-- 添加 op_type 字段到 std_download 表
ALTER TABLE `std_download` 
ADD COLUMN `op_type` INT NULL COMMENT '操作类型：1-下载记录，2-预览记录' AFTER `pdf_file_name`,
ADD COLUMN `std_no` VARCHAR(255) NULL COMMENT '标准号' AFTER `std_info_id`,
ADD COLUMN `std_org_name` VARCHAR(1000) NULL COMMENT '标准名称（原文）' AFTER `std_no`,
ADD COLUMN `std_chinese_name` VARCHAR(1000) NULL COMMENT '标准名称（中文）' AFTER `std_org_name`;

-- 更新现有记录，将 op_type 设置为 1（下载记录）
UPDATE `std_download` SET `op_type` = 1 WHERE `op_type` IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX `idx_std_download_op_type` ON `std_download` (`op_type`);
CREATE INDEX `idx_std_download_user_id` ON `std_download` (`user_id`);
CREATE INDEX `idx_std_download_create_time` ON `std_download` (`create_time`);
