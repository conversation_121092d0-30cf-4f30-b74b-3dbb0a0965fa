<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('标准分类修改')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container" id="classContainer">

            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-update" lay-submit=""
                    type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button class="pear-btn pear-btn-sm" type="reset">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
    <input name="id" th:value="${model.id}" type="hidden" />
</form>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery','laydate','tree'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        var laydate = layui.laydate;
        let tree=layui.tree;

        //渲染
        var inst1 = tree.render({
            elem: '#classContainer'  //绑定元素
            ,data: [{
                title: '江西' //一级菜单
                ,children: [{
                    title: '南昌' //二级菜单
                    ,children: [{
                        title: '高新区' //三级菜单
                        //…… //以此类推，可无限层级
                    }]
                }]
            },{
                title: '陕西' //一级菜单
                ,children: [{
                    title: '西安' //二级菜单
                }]
            }]
        });
    });


</script>
<style>
    .layui-input-block { line-height: 36px; }
    .layui-form-label{
        width:120px;
        text-align: right;
        color:#aaa;
    }
    .layui-input, .layui-textarea
    {
        width:90%;
    }
</style>
</body>
</html>
