<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('体系架构信息')"/>
</head>
<body>

<div class="site-text" style="margin: 5%; display: none" id="addClass" target="test123">
    <form class="layui-form" id="addClassForm" method="post" lay-filter="classInfo">
        <div class="layui-form-item">
            <label class="layui-form-label">分类名称</label>
            <div class="layui-input-block">
                <input type="text" id="name" name="name" lay-verify="name" autocomplete="off" placeholder="请输入分类名称"
                       class="layui-input">
            </div>
        </div>
        <input type="hidden" id="id" name="id"/>
        <input type="hidden" id="pClassType" name="pClassType"/>
        <input type="hidden" id="pClassPath" name="pClassPath"/>
        <input type="hidden" id="pid" name="pid"/>
    </form>
</div>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container" id="classContainer">

            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
        </div>
    </div>
    <input name="hdSystemId" th:value="${system.id}" type="hidden"/>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-update" lay-submit=""
                    type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                关闭
            </button>
        </div>
    </div>
</form>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery', 'laydate', 'tree', 'layer'], function () {
        let form = layui.form;
        var layer = layui.layer;
        let $ = layui.jquery;
        var laydate = layui.laydate;
        let tree = layui.tree;
        let MODULE_PATH_OF_CLASS = '/stdclassification/';
        var treeHandler=null;
        var treeData={};
        form.on('submit(dict-type-update)', function (data) {
            parent.layer.close(parent.layer.getFrameIndex(window.name));
            return false;
        });
        window.loadtree = function () {
            $.ajax({
                type: "get",
                url: MODULE_PATH_OF_CLASS + "treeofsystem?systemId=" + $("input[name='hdSystemId']").val(),
                dataType: 'json',
                success: function (data) {
                    console.log(data);
                    treeData=data;
                    //渲染
                    if(treeHandler)
                    {
                        tree.reload("theTree",{data:treeData});
                    }
                    else {
                        treeHandler = tree.render({
                            id: 'theTree',
                            elem: '#classContainer',  //绑定元素
                            edit: [], //操作节点的图标
                            customOperate: true,
                            accordion: false,
                            onlyIconControl: false,
                            limitNodeAddLevel: 6, // 设置第X级节点不允许添加操作
                            limitNodeDelLevel: 1, // 设置第X级节点不允许删除操作
                            click: function (obj) {

                            },
                            operate: function (obj) {
                                var type = obj.type; //得到操作类型：add、edit、del
                                var data = obj.data; //得到当前节点的数据
                                var elem = obj.elem; //得到当前节点元素
                                //Ajax 操作
                                var id = data.id; //得到节点索引
                                console.log(obj);
                                window.id = id;
                                if (type === 'add') { //增加节点
                                    //给表单赋值
                                    form.val("classInfo", {
                                        "id": null,
                                        "pid": data.classType == -1 ? "-1" : id,
                                        "pClassType": data.classType,
                                        "pClassPath": data.classPath,
                                        "name": null
                                    });
                                    //https://blog.csdn.net/qq_43517653/article/details/93915762
                                    form.render(); //更新全部
                                    window.add("");
                                } else if (type === 'update') {
                                    //var title = elem.find('.layui-tree-txt').html();//得到修改后的内容
                                    //给表单赋值
                                    form.val("classInfo", {
                                        "id": data.id,
                                        "pid": "",
                                        "pClassType": "",
                                        "pClassPath": "",
                                        "name": data.realTitle
                                    });
                                    form.render(); //更新全部
                                    window.update(data.id, elem);
                                } else if (type == 'del') {
                                    window.delete(id, data.realTitle);
                                }
                            },
                            data: treeData
                        });
                    }
                    console.log("treeHandler");
                    console.log(treeHandler);
                }
            });
        }
        window.loadtree();
        window.delete=function(id,name)
        {
            layer.confirm('确认要删除分类['+name+']吗？', {
                btn : [ '确定', '取消' ]//按钮
            }, function(index) {
                layer.close(index);
                $.ajax({
                    type: "post",
                    url: MODULE_PATH_OF_CLASS + "delete",
                    dataType: 'json',
                    contentType: 'application/json',
                    data: JSON.stringify({id:id}),
                    success: function (data) {
                        if (data.success) {
                            layer.msg("已删除!", {icon: 1, time: 1500});
                            window.loadtree();
                        }
                    }
                });
            });
        }
        window.add = function (title) {
            layer.open({
                type: 1,
                title: ['新增分类', 'font-size:18px'],
                shadeClose: true
                , shade: 0, //遮罩透明度
                area: ['80%', '50%'],
                btn: ['确定', '取消'],
                content: $("#addClass"),
                end: function (index) {
                    layer.close(index);//关闭弹出层
                    $("#addClassForm")[0].reset()//重置form
                    $("#addClass").hide();
                },
                yes: function (index) {
                    var data = {
                        className: $("#name").val(),
                        parentFullCode: $("#pClassPath").val(),
                        parentClassType: $("#pClassType").val(),
                        parentId: $("#pid").val(),
                        systemId: $("input[name='hdSystemId']").val(),
                        classCode: "",
                        classCodePath: "",
                        classType: "0",
                        parentName: "",
                        sortCondition: 0,
                        status: 1,
                    };
                    console.log(data);
                    $.ajax({
                        type: "post",
                        url: MODULE_PATH_OF_CLASS + "save",
                        dataType: 'json',
                        contentType: 'application/json',
                        data: JSON.stringify(data),
                        success: function (data) {
                            if (data.success) {
                                layer.close(index);//关闭弹出层
                                layer.msg("添加成功!", {icon: 1, time: 1500});
                                $("#addClassForm")[0].reset()//重置form
                                $("#addClass").hide();
                                window.loadtree();
                            }
                        }
                    });
                }
            });
        }
        window.update=function(id,element)
        {
            layer.open({
                type: 1,
                title: ['编辑分类', 'font-size:18px'],
                shadeClose: true
                , shade: 0, //遮罩透明度
                area: ['80%', '50%'],
                btn: ['确定', '取消'],
                content: $("#addClass"),
                end: function (index) {
                    layer.close(index);//关闭弹出层
                    $("#addClassForm")[0].reset()//重置form
                    $("#addClass").hide();
                },
                yes: function (index) {
                    var data = {
                        className: $("#name").val(),
                        id:id,
                    };
                    console.log(data);
                    $.ajax({
                        type: "post",
                        url: MODULE_PATH_OF_CLASS + "updateName",
                        dataType: 'json',
                        contentType: 'application/json',
                        data: JSON.stringify(data),
                        success: function (res) {
                            if (res.success) {
                                layer.close(index);//关闭弹出层
                                layer.msg("更新成功!", {icon: 1, time: 1500});
                                $("#addClassForm")[0].reset()//重置form
                                $("#addClass").hide();
                                window.loadtree();
                                element.find('.layui-tree-txt').html(data.className);
                            }
                        }
                    });
                }
            });
        }
    });


</script>
<style>
    .layui-input-block {
        line-height: 36px;
    }

    .layui-form-label {
        width: 120px;
        text-align: right;
        color: #aaa;
    }

    .layui-input, .layui-textarea {
        width: 90%;
    }

    .displayMenu {
        display: none;
    }

</style>
</body>
</html>
