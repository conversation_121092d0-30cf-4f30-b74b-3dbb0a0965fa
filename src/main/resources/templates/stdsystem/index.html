<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('体系检索')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="simple-container" style="width:60%;margin:auto;">
                <div class="item" style="width:100%;">
                   <!-- <label class="sub-item search-label">检索：</label>-->
                    <div class="search-cmp">
                        <input class="layui-input sub-item" id="searchKey" name="stdNo" placeholder="请输入体系名称" type="text">
                        <button id="btnSimpleSearch" class="search-button">
                            <i class="layui-icon layui-icon-search"></i>
                        </button>
                    </div>
                    <div class="search-anchor-1" ><a href="javascript:void(0);"  id="clearSimpleCon" style="display: none;" class="search-advance">清空</a><a style="display: none;" href="javascript:void(0);" id="advanceAnchor" class="search-advance">高级检索&gt;&gt;</a></div>
                </div>
            </div>
            <div class="advance-container" style="display: none;">
                <div class="advance-item" id="advanceItem1" style="width:100%;">
                    <div class="ph-div" style="text-align: right;">高级检索：</div>
                    <div class="search-cmp">
                        <div class="con-field-container">
                            <select id="conField1" class="con-field">
                                <option value="std_no">标准号</option>
                                <option value="std_org_name">标准名称（原文）</option>
                                <option value="std_chinese_name">标准名称（中文）</option>
                                <option value="std_english_name">标准名称（英文）</option>
                                <option value="catetory_no">分类号</option>
                                <option value="std_ics">ICS号</option>
                                <option value="pub_date">发布日期</option>
                                <option value="implementation_date">实施日期</option>
                                <option value="advance_dept">提出单位</option>
                                <option value="drafting_unit">起草单位</option>
                                <option value="drafter">起草人</option>
                                <option value="std_status">标准状态</option>
                            </select>
                        </div>
                        <input class="layui-input sub-item con-val" id="conVal1" placeholder="" type="text">
                        <div class="con-type-container">
                            <select id="conQueryType1" class="con-query-type">
                                <option value="fuzzy">模糊</option>
                                <option value="exact">精确</option>
                            </select>
                        </div>
                    </div>
                    <div class="ph-end-div">
                        <a href="javascript:void(0);" id="simpleAnchor" class="search-advance">普通检索&gt;&gt;</a>
                    </div>
                </div>
                <div class="advance-item" id="advanceItem2" style="width:100%;">
                    <div class="ph-div">
                        <select id="conAndOr2" class="conAndOr">
                            <option value="AND">AND</option>
                            <option value="OR">OR</option>
                            <option value="NOT">NOT</option>
                        </select>
                    </div>
                    <div class="search-cmp">
                        <div class="con-field-container">
                            <select id="conField2">
                                <option value="std_no">标准号</option>
                                <option value="std_org_name">标准名称（原文）</option>
                                <option value="std_chinese_name">标准名称（中文）</option>
                                <option value="std_english_name">标准名称（英文）</option>
                                <option value="catetory_no">分类号</option>
                                <option value="std_ics">ICS号</option>
                                <option value="pub_date">发布日期</option>
                                <option value="implementation_date">实施日期</option>
                                <option value="advance_dept">提出单位</option>
                                <option value="drafting_unit">起草单位</option>
                                <option value="drafter">起草人</option>
                                <option value="std_status">标准状态</option>
                            </select>
                        </div>
                        <input class="layui-input sub-item" id="conVal2" placeholder="" type="text">
                        <div class="con-type-container">
                            <select id="conQueryType2">
                                <option value="fuzzy">模糊</option>
                                <option value="exact">精确</option>
                            </select>
                        </div>
                    </div>
                    <div class="ph-end-div">
                        <a href="javascript:void(0);" onclick="delCondition(2)" class="conDel" id="conDel2" style="display: none;">-</a>
                        <a href="javascript:void(0);" onclick="addCondition()" class="conAdd" id="conAdd2">+</a>
                    </div>
                </div>
            </div>
            <div class="item" id="advTool" style="text-align: center;display: none;">
                <button class="pear-btn pear-btn-md sub-item" type="reset" style="margin-left:30px;margin-top:0px;width:80px;padding-left:5px;">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重 置
                </button>
                <button class="pear-btn pear-btn-md pear-btn-primary sub-item" lay-filter="role-query" lay-submit
                        style="margin-left:40px;margin-top:0px;width:80px;padding-left:5px;">
                    <i class="layui-icon layui-icon-search"></i>
                    检 索
                </button>
            </div>
            <div id="resultInfo"></div>
        </form>
    </div>
</div>
<!--<div class="item">
               <label class="sub-item">标准号：</label>
               <input class="layui-input sub-item" name="stdNo" placeholder="" type="text">
           </div>
               <div class="item">
               <label class="sub-item">标准名称：</label>
                   <input class="layui-input sub-item" name="stdOrgName" placeholder="" type="text">
           </div>
               <div class="item">
           <label class="sub-item">主要内容：</label>
                   <input class="layui-input sub-item" name="primaryCoverage" placeholder="" type="text">
           </div>
               <div class="item">
               <label class="sub-item">起草单位：</label>
                   <input class="layui-input sub-item" name="draftingUnit" placeholder="" type="text" >
           </div>
               <div class="item">
           <label class="sub-item">起草人：</label>
                   <input class="layui-input sub-item" name="drafter" placeholder="" type="text">
           </div>
               <div class="item">
                   <button class="pear-btn pear-btn-md pear-btn-primary sub-item" lay-filter="role-query" lay-submit style="margin-left:40px;margin-top:0px;">
                       <i class="layui-icon layui-icon-search"></i>
                       查询
                   </button>
                   <button class="pear-btn pear-btn-md sub-item" type="reset" style="margin-left:30px;margin-top:0px;">
                       <i class="layui-icon layui-icon-refresh"></i>
                       重置
                   </button>
           </div>-->
<div class="layui-card">
    <div class="layui-card-body">
        <table id="role-table" lay-filter="role-table"></table>
    </div>
</div>
</body>

<script id="role-toolbar" type="text/html">
</script>
<!--
<button
        class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
    <i class="layui-icon layui-icon-add-1"></i>
    新增
</button>
<button  class="pear-btn pear-btn-md"
         lay-event="batchRemove">
    <i class="layui-icon layui-icon-delete"></i>
    删除
</button>-->
<script id="role-bar" type="text/html">
    <a class="c-anchor layui-icon layui-icon-form" href="javascript:void(0);" lay-event="detail" title="详情"></a>
    <a class="c-anchor iconfont icon-xiangmutixi" href="javascript:void(0);" lay-event="system-img" title="体系图"></a>
    <a class="c-anchor layui-icon layui-icon-template-1" href="javascript:void(0);" lay-event="std-list" title="明细表"></a>
    <a class="c-anchor layui-icon layui-icon-survey" href="javascript:void(0);" lay-event="opinion" title="意见反馈"></a>
</script>
<!--
<button
        class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>
</button>
<button
        class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>
</button>-->

<th:block th:include="include :: footer"/>

<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common', 'upload', 'layer', 'element'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;

        let MODULE_PATH = "/stdsystem/";
        let conditionArr=[1,2];
        let cols = [
            [
                {field: 'number', title: '序号', align: 'center', type: 'numbers',width:'5%'},
                {title: '体系名称', field: 'name', align: 'left',width:'30%'},
                {title: '级别', field: 'level', align: 'center', width: '10%'},
                {title: '层级数', field: 'layerNumber', align: 'center', width: '8%'},
                {title: '标准数量', field: 'standardNumber', align: 'center', width: '8%'},
                {title: '说明', field: 'description', align: 'left',width:'27%'},
                {title: '操作', toolbar: '#role-bar', align: 'center',width: '12%'}
            ]
        ]

        table.render({
            elem: '#role-table',
            url: MODULE_PATH + 'datapublished',
            page: true,
            limit: 50,
            limits: [10, 15, 20, 30, 50],
            cols: cols,
            skin: 'row,line',
            toolbar: '#role-toolbar',
            cellMinWidth: 'auto',
            defaultToolbar: [],
            done: function (res, curr, count) {
                $("#resultInfo").html('共检索到' + res.count + "个体系");
                var $data = $('div[lay-id="role-table"]').find('.layui-table-body').find('tr').eq(0).find('td');
                var $head = $('div[lay-id="role-table"]').find('.layui-table-header').find('tr').eq(0).find('th');
                for (var i = 0; i < $data.length; i++) {
                    var l1 = $data.eq(i).find('div').width();
                    var l2 = $head.eq(i).find('div').width();
                    if (l1 > l2) {
                        $head.eq(i).find('div').width(l1);
                    } else if (l2 > l1) {
                        $data.eq(i).find('div').width(l2);
                    }
                }
            }
        });

        table.on('tool(role-table)', function (obj) {
            if (obj.event === 'remove') {
                window.remove(obj);
            } else if (obj.event === 'detail') {
                window.detail(obj);
            } else if (obj.event === 'download') {
                window.download(obj);
            }
            else if(obj.event=='std-list')
            {
                window.showStdList(obj);
            }
            else if(obj.event==='system-img') //体系图
            {
                window.showClassStruct(obj);
            }
            else if(obj.event==='opinion')
            {
                window.opinion(obj);
            }
            /*else if (obj.event === 'power') {
                window.power(obj);
            } else if (obj.event === 'dept') {
                window.dept(obj);
            } */
        });

        table.on('toolbar(role-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                window.refresh();
            } else if (obj.event === 'batchRemove') {
                window.batchRemove(obj);
            } else if (obj.event === 'updateIndex') {
                window.updateIndex(obj);
            } else if (obj.event === 'updateAll') {
                window.updateAll(obj);
            } else if (obj.event === 'delete') {
                window.deleteRecordAndIndex(obj);
            } else if (obj.event === 'importExcel') {
                //window.importExcel(obj);
            }
        });

        form.on('submit(role-query)', function (data) {
            console.log(data);
            var fields=[];
            for(var i=0;i<conditionArr.length;i++)
            {
                var obj={};
                obj.logic="AND";
                if(conditionArr[i]>1)
                {
                   obj.logic= $("#conAndOr"+conditionArr[i]).val();
                }
                obj.field=$("#conField"+conditionArr[i]).val();
                obj.val=$("#conVal"+conditionArr[i]).val();
                obj.type=$("#conQueryType"+conditionArr[i]).val();
                fields.push(obj);
            }
            table.reload('role-table', {where: {advSearchKey:JSON.stringify(fields)},page: { curr: 1 }});
            return false;
        });

        window.showStdList=function(obj)
        {
            var id = obj.data['id'];
            console.log(id);
            var exist=parent.getTab(id);
            if(exist == 0){
                var url='/stdsystem/stdofsystem?sysId='+obj.data["id"];
                window.parent.layui.element.tabAdd('content', {
                    title: obj.data["name"]+'【标准明细】',
                    content:'<iframe lay-id="' + id + '" src="'+url+'" frameborder="0" class="admin-iframe" style="width: 100%;height: 100%"></iframe>' //支持传入html
                    ,id: id
                });
            }
            window.parent.layui.element.tabChange('content', id);
            //window.parent.addTab('标准列表','/stdsystem/dataofsystem?systemId='+obj.data["id"],'icon-add');
        }
        //体系图
        window.showClassStruct=function(obj)
        {
           parent.layer.open({
                type: 2,
                title: "【"+obj.data["name"]+"】体系图",
                shade: 0.1,
                area: ['1000px', '650px'],
                content: '/go/index.html?id='+obj.data["id"]
                //content: '/joint/tree.html?id='+obj.data["id"]
            });
        }
        window.opinion=function(obj)
        {
            parent.layer.open({
                type: 2,
                title: "【"+obj.data["name"]+"】意见列表",
                shade: 0.1,
                area: ['1200px', '630px'],
                content: '/sysopinion/main?systemId='+obj.data["id"]
            });
        }

        $("#advanceAnchor").click(function () {
            $(".simple-container").toggle();
            $(".advance-container").toggle();
            $("#advTool").toggle();
        });
        $("#simpleAnchor").click(function () {
            $(".simple-container").toggle();
            $(".advance-container").toggle();
            $("#advTool").toggle();
        });

        $("#searchKey").keydown(function (e) {//当按下按键时
            if (e.which == 13) {//.which属性判断按下的是哪个键，回车键的键位序号为13
                var key = $('#searchKey').val();
                // if (!key) {
                //     layer.alert("请输入关键字", {
                //         title: "提示"
                //     });
                //     return false;
                // } else {
                     table.reload('role-table', {where: {simpleSearchKey:key},page: { curr: 1 }});
                     $("#clearSimpleCon").show();
                    return false;
                    //table.reload('role-table');
                // }
            }
        });
        $("#btnSimpleSearch").click(function(e){
            var key = $('#searchKey').val();
            // if (!key) {
            //     layer.alert("请输入关键字", {
            //         title: "提示"
            //     });
            //     return false;
            // } else {
                table.reload('role-table', {where: {simpleSearchKey:key},page: { curr: 1 }});
               // $('#searchKey').val(key);
                $("#clearSimpleCon").show();
                return false;
                //table.reload('role-table');
            // }
        });
        $("#clearSimpleCon").click(function(){
            $('#searchKey').val("");
            table.reload('role-table', {where: {simpleSearchKey:""},page: { curr: 1 }});
            $("#clearSimpleCon").hide();
        });
        window.addCondition=function(){
            var id=0;
            for(var i=0;i<conditionArr.length;i++)
            {
                if(conditionArr[i]>id)
                {
                    id=conditionArr[i];
                }
            }
            if(id>0)
            {
                id+=1;
                conditionArr[conditionArr.length]=id;
            }
            var html=`<div class="advance-item" id="advanceItem${id}" style="width:100%;">
                    <div class="ph-div">
                        <select id="conAndOr${id}" class="conAndOr">
                            <option value="AND">AND</option>
                            <option value="OR">OR</option>
                            <option value="NOT">NOT</option>
                        </select>
                    </div>
                    <div class="search-cmp">
                        <div class="con-field-container">
                            <select id="conField${id}">
                                <option value="std_no">标准号</option>
                                 <option value="std_org_name">标准名称（原文）</option>
                                <option value="std_chinese_name">标准名称（中文）</option>
                                <option value="std_english_name">标准名称（英文）</option>
                                <option value="catetory_no">分类号</option>
                                <option value="std_ics">ICS号</option>
                                <option value="pub_date">发布日期</option>
                                <option value="implementation_date">实施日期</option>
                                <option value="advance_dept">提出单位</option>
                                <option value="drafting_unit">起草单位</option>
                                <option value="drafter">起草人</option>
                                <option value="std_status">标准状态</option>
                            </select>
                        </div>
                        <input class="layui-input sub-item" id="conVal${id}" placeholder="" type="text">
                        <div class="con-type-container">
                            <select id="conQueryType${id}">
                                <option value="fuzzy">模糊</option>
                                <option value="exact">精确</option>
                            </select>
                        </div>
                    </div>
                    <div class="ph-end-div">
                        <a href="javascript:void(0);" class="conDel" onclick="delCondition(${id})" id="conDel${id}">-</a>
                        <a href="javascript:void(0);" onclick="addCondition()" class="conAdd" id="conAdd${id}">+</a>
                    </div>
                </div>`;
                $(".advance-container").append(html);
                form.render("select");
                $("#conAdd"+(id-1)).toggle();
                for(var i=1;i<conditionArr.length-1;i++)
                {
                    $("#conDel"+conditionArr[i]).show();
                }
        };

        window.delCondition=function(id){
            $("#advanceItem"+id).remove();
            for(var i=0;i<conditionArr.length;i++)
            {
                if(conditionArr[i]==id)
                {
                    if(i==conditionArr.length-1)
                    {
                        $("#conAdd"+conditionArr[i-1]).toggle();
                    }
                    conditionArr.splice(i, 1);
                    break;
                }
            }
            console.log('conditionArr.length:'+conditionArr.length);
            //需要保留至少两行
            if(conditionArr.length==2)
            {
                $("#conDel"+conditionArr[1]).hide();
            }
        };

        window.add = function () {
            layer.open({
                type: 2,
                title: '新增',
                shade: 0.1,
                area: ['500px', '500px'],
                content: MODULE_PATH + 'add'
            });
        }


        window.detail = function (obj) {
            layer.open({
                type: 2,
                title: '【' + obj.data['name'] + '】',
                shade: 0.1,
                area: ['600px', '500px'],
                content: MODULE_PATH + 'detail?id=' + obj.data['id']
            });
        }

        //刷新全部索引
        window.updateAll = function (obj) {
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "updateallsolrindex",
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("更新成功!", {icon: 1, time: 2000});
                    } else {
                        layer.msg("更新失败:" + result.message, {icon: 2, time: 2000});
                    }
                }
            })
        }

        //刷新指定索引
        window.updateIndex = function (obj) {
            let ids = common.checkField(obj, 'id');
            if (common.isEmpty(ids)) {
                popup.warning("请选择要更新的记录！");
                return false;
            }
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "updatesolrindex",
                dataType: 'json',
                type: 'post',
                data: 'ids=' + ids,
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("更新成功!", {icon: 1, time: 2000});
                    } else {
                        layer.msg("更新失败:" + result.message, {icon: 2, time: 2000});
                    }
                }
            })
        }
        //多文件上传
        var uploadListIns = upload.render({
            elem: '#uploadBtn'     // 选择文件按钮
            , elemList: $('#demoList') //列表元素对象
            , url: MODULE_PATH + 'file-upload' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
            , accept: 'file'  //指定允许上传时校验的文件类型，可选值有：images（图片）、file（所有文件）、video（视频）、audio（音频）
            , multiple: false  //是否允许多文件上传。设置 true即可开启。不支持ie8/9
            , number: 5   //设置同时可上传的文件数量，一般配合 multiple 参数出现; 0 不限制
            , auto: true  //是否选完文件后自动上传。如果设定 false，那么需要设置 bindAction 参数来指向一个其它按钮提交上传
            , bindAction: '#testListAction'  //指向一个按钮触发上传，一般配合 auto: false 来使用
            , choose: function (obj) {   //选择文件后的回调函数。返回一个object参数
            }
            , done: function (res, index, upload) { //成功的回调
                var that = this;
                if (res.success) { //上传成功
                    popup.success("导入完成！", function () {
                        table.reload('role-table');
                    });
                    //delete this.files[index]; //删除文件队列已经上传成功的文件
                    return;
                } else {
                    popup.failure(res.message);
                }
            }
            , allDone: function (obj) { //多文件上传完毕后的状态回调
                console.log(obj)
            }
            , error: function (index, upload) { //错误回调
                popup.failure("导入失败！");
            }
            , progress: function (n, elem, e, index) { //注意：index 参数为 layui 2.6.6 新增
                element.progress('progress-demo-' + index, n + '%'); //执行进度条。n 即为返回的进度百分比
            }
        });

        //删除记录 并删除指定索引
        window.deleteRecordAndIndex = function (obj) {
            let ids = common.checkField(obj, 'id');
            if (common.isEmpty(ids)) {
                popup.warning("请选择要删除的记录！");
                return false;
            }
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "deleteRecordAndIndex",
                dataType: 'json',
                type: 'post',
                data: 'ids=' + ids,
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("删除成功!", {icon: 1, time: 2000});
                        window.refresh();
                    } else {
                        layer.msg("删除失败:" + result.message, {icon: 2, time: 2000});
                    }
                }
            })
        }

        window.remove = function (obj) {
            layer.confirm('确定要删除该数据吗', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "remove/" + obj.data['id'],
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                                obj.del();
                            });
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 1000});
                        }
                    }
                })
            });
        }

        window.batchRemove = function (obj) {
            let ids = common.checkField(obj, 'roleId');
            if (common.isEmpty(ids)) {
                popup.warning("未选中数据");
                return false;
            }
            layer.confirm('确定要删除选中角色', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "batchRemove/" + ids,
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            popup.success(result.msg, function () {
                                table.reload('role-table');
                            });
                        } else {
                            popup.failure(result.msg);
                        }
                    }
                })
            });
        }

        window.refresh = function () {
            table.reload('role-table');
        }
        //清空文件列表
        $('#clearList').click(function () {
            $('#demoList').html('');
        });
    });
</script>
<style>
    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }

    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
    }

    .simple-container .item .sub-item button {
        margin-top: 10px;
    }

    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }

    .advance-container {
        width: 70%;
        margin: 0px auto;
    }

    .advance-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-bottom: 15px;
    }
    .layui-table-tool {
        height: 0px !important;
        line-height: 1px !important;
        padding: 0px !important;
        min-height: 0px !important;
    }
    .layui-form-item .layui-form-label, .layui-form-item .layui-input-inline {
        margin-bottom: 7px;
    }
    .layui-form-item {
        padding-bottom: 0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    #resultInfo {
        width: 100%;
        text-align: center;
        color: #999;
        margin-top: 10px;
    }
    .layui-table thead th {
        font-weight: bold;
    }
   /* .c-anchor {
        text-decoration: underline;
        color: #666;
        font-size: 12px;
        display: inline-block;
    }*/
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    .search-label {
        flex-grow: 2;
    }
    .simple-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #ddd;
        border-radius: 5px;
    }
    .simple-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .simple-container .search-cmp button:hover {
        cursor: pointer;
    }
    .simple-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .simple-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .advance-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #efefef;
        border-radius: 5px;
    }
    .advance-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .advance-container .search-cmp button:hover {
        cursor: pointer;
    }
    .advance-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .advance-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 240px;
    }
    .con-type-container {
        width: 110px;
    }
    .search-anchor-1
    {
        flex-grow:1;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }

</style>
</html>
