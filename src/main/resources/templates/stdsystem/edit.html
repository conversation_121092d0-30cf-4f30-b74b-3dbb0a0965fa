<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('体系修改')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">体系名称：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="name"
                               placeholder="请输入体系名称" th:value="${model.name}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">级别：</label>
                    <div class="layui-input-block">
                        <select name="level" id="level" lay-filter="level" lay-verify="required" th:value="${model.level}">
                            <option value="国家级">国家级</option>
                            <option value="行业级">行业级</option>
                            <option value="集团级">集团级</option>
                            <option value="企业级">企业级</option>
                            <option value="型号级">型号级</option>
                            <option value="产品级">产品级</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">层级数：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="layerNumber"
                               placeholder="请输入层级数" th:value="${model.layerNumber}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准数量：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="standardNumber"
                               placeholder="请输入标准数量" th:value="${model.standardNumber}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;">*</span>发布状态</label>
                    <div class="layui-input-block">
                        <select name="isPublish" th:value="${model.isPublish}"  id="isPublish" lay-filter="isPublish" lay-verify="required">
                            <option value="0">编制中</option>
                            <option value="1">已发布</option>
                            <option value="-1">已停用</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">说明：</label>
                    <div class="layui-input-block">
                        <textarea autocomplete="off" class="layui-textarea"  name="description" rows="5"
                               placeholder="请输入说明" th:text="${model.description}" type="text"  style="padding:5px;height:100px;">
                        </textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-update" lay-submit=""
                    type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button class="pear-btn pear-btn-sm" type="reset">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
    <input name="id" th:value="${model.id}" type="hidden" />
</form>
<input name="hdLevel" th:value="${model.level}" type="hidden" />
<input name="hdIsPublish" th:value="${model.isPublish}" type="hidden" />
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery','laydate'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        var laydate = layui.laydate;
        $("#level").val($("input[name='hdLevel']").val());
        $("#isPublish").val($("input[name='hdIsPublish']").val());
        form.render("select");
     /*   var pubDate = laydate.render({
            elem: '#pubDate',
            type:'date',
            theme: 'molv',
            trigger: 'click',
            done: function(value, date) {
            }
        });*/

        form.on('submit(dict-type-update)', function (data) {
            console.log(data.field);
            $.ajax({
                url: '/stdsystem/update',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                            parent.layui.table.reload("role-table");
                            parent.window.setUpload();
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });
    })
</script>
<style>
    .layui-input-block { line-height: 36px; }
    .layui-form-label{
        width:120px;
        text-align: right;
        color:#aaa;
    }
    .layui-input, .layui-textarea
    {
        width:90%;
    }
</style>
</body>
</html>
