<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('岗位修改')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="lauyi-form-item company-select">
                <label class="layui-form-label">所在部门：</label>
                <div class="layui-input-block">
                    <div class="layui-unselect layui-form-select downpanel">
                        <div class="layui-select-title layui-select-tree">
                            <span class="layui-input layui-unselect" id="tree">---请选择所属部门---</span>
                            <i class="layui-edge"></i>
                        </div>
                        <dl class="layui-anim layui-anim-upbit">
                            <dd>
                                <ul>
                                    <div class="main-container" id="classContainer">

                                    </div>
                                </ul>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">岗位名称：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="className"
                               placeholder="请输入岗位名称" th:value="${model.className}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">岗位描述：</label>
                    <div class="layui-input-block">
                        <textarea autocomplete="off" class="layui-textarea"  name="remark" rows="5"
                               placeholder="请输入岗位描述" th:text="${model.remark}" type="text"  style="padding:5px;height:100px;">
                        </textarea>
                    </div>
                </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-update" lay-submit=""
                    type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button class="pear-btn pear-btn-sm" type="reset">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
    <input name="id" th:value="${model.id}" type="hidden" />
    <input type="hidden" id="systemId"  name="systemId" th:value="${model.systemId}" />
    <input type="hidden" id="parentId" name="parentId" th:value="${model.parentId}" />
    <input type="hidden" id="parentName" name="parentName" th:value="${model.parentName}" />
    <input type="hidden" id="parentFullCode" name="parentFullCode"  th:value="${model.parentFullCode}"  />
    <input type="hidden" id="classType" name="classType" value="1" />
</form>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery','laydate','tree'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        var laydate = layui.laydate;
        let tree = layui.tree;
        let MODULE_PATH_OF_CLASS = '/departments/';
        var treeHandler=null;
        var treeData={};

        window.loadtree = function () {
            var currDepartCodePath=$("#hdDepartClassPath").val();
            $.ajax({
                type: "get",
                url: MODULE_PATH_OF_CLASS + "departments",
                dataType: 'json',
                success: function (data) {
                    console.log(data);
                    treeData=data;
                    $("#tree").text($("#parentName").val());
                    //渲染
                    if(treeHandler)
                    {
                        tree.reload("theTree",{data:treeData});
                    }
                    else {
                        treeHandler = tree.render({
                            id: 'theTree',
                            elem: '#classContainer',  //绑定元素
                            edit: [], //操作节点的图标
                            customOperate: true,
                            accordion: false,
                            onlyIconControl: true,
                            limitNodeAddLevel: 6, // 设置第X级节点不允许添加操作
                            limitNodeDelLevel: 1, // 设置第X级节点不允许删除操作
                            click: function (obj) {
                                var type = obj.type; //得到操作类型：add、edit、del
                                var data = obj.data; //得到当前节点的数据
                                var elem = obj.elem; //得到当前节点元素
                                //Ajax 操作
                                var id = data.id; //得到节点索引
                                //所有体系下标准
                                if(id==$("#systemId").val())
                                {
                                    $("#parentId").val(-1);
                                    $("#parentName").val(data.title);
                                    $("#parentFullCode").val("-1");
                                }
                                else
                                {
                                    //当前分类下体系下标准
                                    $("#parentId").val(id);
                                    $("#parentName").val(data.title);
                                    $("#parentFullCode").val(data.classPath);
                                }
                                var othis = $($(this)[0].elem).parents(".layui-form-select");
                                othis.removeClass("layui-form-selected").find(".layui-select-title span").html(data.title).end().find("input:hidden[name='hdClassId']").val(id);
                            },
                            operate: function (obj) {
                            },
                            data: treeData
                        });
                    }
                    console.log("treeHandler");
                    console.log(treeHandler);
                }
            });
        }
        window.loadtree();
        window.registeClassChangeEvent=function() {
            $(".downpanel").on("click", ".layui-select-title", function (e) {
                $(".layui-form-select").not($(this).parents(".layui-form-select")).removeClass("layui-form-selected");
                $(this).parents(".downpanel").toggleClass("layui-form-selected");
                layui.stope(e);
            }).on("click", "dl i", function (e) {
                layui.stope(e);
            });
            $(document).on("click", function (e) {
                $(".main-container").parents(".layui-form-select").removeClass("layui-form-selected");
            });
            //$(".downpanel").find(".layui-select-title span").html($("#hdClassName").val());
        }
        window.registeClassChangeEvent();

        form.on('submit(dict-type-update)', function (data) {
            console.log(data.field);
            $.ajax({
                url: '/stdpost/update',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                            parent.layui.table.reload("role-table");
                            parent.window.setUpload();
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });
    })
</script>
<style>
    .layui-input-block { line-height: 36px; }
    .layui-form-label{
       /* width:120px;*/
        text-align: right;
        color:#aaa;
    }
    .layui-input, .layui-textarea
    {
        width:90%;
    }
    #tree
    {
        padding-top:8px;
    }
</style>
</body>
</html>
