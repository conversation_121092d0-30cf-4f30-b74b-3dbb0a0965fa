<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('标准体系意见')"/>
</head>
<body class="pear-container">
<!--<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="simple-container">
                <div class="item" style="width:100%;">
                    <label class="sub-item search-label"></label>
                    <div class="search-cmp">
                        <input class="layui-input sub-item" id="searchKey" name="name" placeholder="请输入体系名称" type="text">
                        <button id="btnSimpleSearch" class="search-button">
                            <i class="layui-icon layui-icon-search"></i>
                        </button>
                    </div>
                    <div class="search-anchor-1"><a href="javascript:void(0);"  id="clearSimpleCon" style="display: none;" class="search-advance">清空</a>
                        &lt;!&ndash; <a href="javascript:void(0);" id="advanceAnchor" class="search-advance">高级检索&gt;&gt;</a>&ndash;&gt;
                    </div>
                </div>
            </div>
            <div id="resultInfo"></div>
        </form>
    </div>
</div>
-->
<!--<div class="layui-card">
    <div class="layui-card-body" style="padding:10px 10px 5px 10px;">
        <form action="" class="layui-form">
            <div class="search-container">
                <div class="item">
                    <label class="sub-item">标准号：</label>
                    <input class="layui-input sub-item" name="stdNo" placeholder="" type="text">
                </div>
                <div class="item">
                    <label class="sub-item">标准名称：</label>
                    <input class="layui-input sub-item" name="stdOrgName" placeholder="" type="text">
                </div>
                <div class="item">
                    <label class="sub-item">主要内容：</label>
                    <input class="layui-input sub-item" name="primaryCoverage" placeholder="" type="text">
                </div>
                <div class="item">
                    <label class="sub-item">起草单位：</label>
                    <input class="layui-input sub-item" name="draftingUnit" placeholder="" type="text" >
                </div>
                <div class="item">
                    <label class="sub-item">起草人：</label>
                    <input class="layui-input sub-item" name="drafter" placeholder="" type="text">
                </div>
                <div class="item">
                    <button class="pear-btn pear-btn-md pear-btn-primary sub-item" lay-filter="role-query" lay-submit style="margin-left:40px;margin-top:0px;">
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <button class="pear-btn pear-btn-md sub-item" type="reset" style="margin-left:30px;margin-top:0px;">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                </div>
            </div>
            <div id="resultInfo"></div>
        </form>
    </div>
</div>-->
<div class="layui-card">
    <div class="layui-card-body">
        <table id="role-table" lay-filter="role-table"></table>
    </div>
</div>
<input type="hidden" id="hdSystemId" name="hdSystemId" th:value="${system.id}" />
</body>

<script id="role-toolbar" type="text/html">
    <button
            class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        新增
    </button>
   <!-- <button  class="pear-btn pear-btn-primary pear-btn-md" style="margin-left:15px;" lay-event="downloadTemplate">
        <i class="layui-icon layui-icon-upload"></i>
        下载导入模板
    </button>-->
    <!--  <button  class="pear-btn pear-btn-primary pear-btn-md" style="margin-left:15px;" lay-event="downloadTemplate">
          <i class="layui-icon layui-icon-upload"></i>
          下载导入模板
      </button>
      <button  class="pear-btn pear-btn-primary pear-btn-md"  id="uploadBtn">
          <i class="layui-icon layui-icon-upload"></i>
          导入
      </button>-->
   <!-- <div id="uploadElem" style="display: inline-block;"></div>-->
   <!-- <button  class="pear-btn pear-btn-danger pear-btn-md" style="margin-left:15px;" lay-event="delete">
        <i class="layui-icon layui-icon-delete"></i>
        删除
    </button>-->
    <!--    <button  class="pear-btn pear-btn-primary pear-btn-md" lay-event="batchDecryptDoc">
            <i class="layui-icon layui-icon-upload"></i>
            批量解密指导性文件
        </button>-->
</script>
<!--

<button  class="pear-btn pear-btn-md"
         lay-event="batchRemove">
    <i class="layui-icon layui-icon-delete"></i>
    删除
</button>-->
<!--<a class="c-anchor" href="javascript:void(0);" lay-event="detail">详情</a>-->
<script id="role-bar" type="text/html">

</script>
<!--
<button
        class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>
</button>
<button
        class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>
</button>-->

<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common','upload','layer','element'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;

        let MODULE_PATH = "/sysopinion/";
        let MODULE_PATH_OF_CLASS='/stdclassification/';
        let conditionArr=[1,2];
        let cols = [
            [
                {field: 'number', title: '序号', align: 'center', type: 'numbers',width:'6%'},
                {title: '体系位置',  align: 'left', width: '12%',templet:function(rowData){
                     if(rowData.classCodePath=="-1")
                     {
                         return "当前体系";
                     }
                     else
                     {
                         return rowData.classCodePath;
                     }
                    }},
                {title: '节点名称', field: 'className', align: 'left', width: '6%'},
                {title: '修改意见或建议', field: 'suggestion', align: 'left', width: '20%'},
                {title: '修改理由', field: 'reasion', align: 'left', width: '16%'},
                {title: '提出人', field: 'questioner', align: 'left', width: '7%'},
                {title: '提出时间', field: 'createTime', align: 'left',width:'13%'},
            ]
        ]
        /*   //多文件上传
           var uploadListIns = upload.render({
               elem: '#uploadBtn'     // 选择文件按钮
               ,elemList: $('#demoList') //列表元素对象
               ,url: '/standard/file-upload' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
               ,accept: 'file'  //指定允许上传时校验的文件类型，可选值有：images（图片）、file（所有文件）、video（视频）、audio（音频）
               ,multiple: false  //是否允许多文件上传。设置 true即可开启。不支持ie8/9
               ,number: 5   //设置同时可上传的文件数量，一般配合 multiple 参数出现; 0 不限制
               ,auto: true  //是否选完文件后自动上传。如果设定 false，那么需要设置 bindAction 参数来指向一个其它按钮提交上传
               ,bindAction: '#testListAction'  //指向一个按钮触发上传，一般配合 auto: false 来使用
               ,choose: function(obj){   //选择文件后的回调函数。返回一个object参数
               }
               ,done: function(res, index, upload){ //成功的回调
                   var that = this;
                   if(res.success){ //上传成功
                       popup.success("导入完成！", function () {
                           table.reload('role-table');
                       });
                       //delete this.files[index]; //删除文件队列已经上传成功的文件
                       return;
                   }else {
                       popup.failure(res.message);
                   }
                  // window.setUpload();
               }
               ,allDone: function(obj){ //多文件上传完毕后的状态回调
                   console.log(obj)
               }
               ,error: function(index, upload){ //错误回调
                   popup.failure("导入失败！");
               }
               ,progress: function(n, elem, e, index){ //注意：index 参数为 layui 2.6.6 新增
                   element.progress('progress-demo-'+ index, n + '%'); //执行进度条。n 即为返回的进度百分比
               }
           });*/

        table.render({
            elem: '#role-table',
            url: MODULE_PATH + 'data',
            page: true,
            limit:50,
            limits:[10,15,20,30,50],
            cols: cols,
            skin: 'row,line',
            toolbar: '#role-toolbar',
            cellMinWidth: 'auto',
            where:{
                "systemId":$("#hdSystemId").val()
            },
            defaultToolbar: [{
                title: '刷新',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print', 'exports'],
            done: function (res, curr, count) {
                $("#resultInfo").html('共检索到'+res.count+"个体系");
                var $data = $('div[lay-id="role-table"]').find('.layui-table-body').find('tr').eq(0).find('td');
                var $head = $('div[lay-id="role-table"]').find('.layui-table-header').find('tr').eq(0).find('th');
                for (var i = 0; i < $data.length; i++) {
                    var l1 = $data.eq(i).find('div').width();
                    var l2 = $head.eq(i).find('div').width();
                    if (l1 > l2) {
                        $head.eq(i).find('div').width(l1);
                    } else if(l2>l1) {
                        $data.eq(i).find('div').width(l2);
                    }
                }
            }
        });

        table.on('tool(role-table)', function (obj) {
            /*if (obj.event === 'remove') {
                window.remove(obj);
            }
            else if (obj.event === 'edit') {
                window.edit(obj);
            }
            else if(obj.event=='systemclassedit')
            {
                window.editClass(obj);
            }
            else if(obj.event=='publish')
            {
                window.publish(obj);
            }
            else if(obj.event=='stop-publish')
            {
                window.stopPublish(obj);
            }
            else if(obj.event=='stdmanager')
            {
                window.stdmanager(obj);
            }
            else if (obj.event === 'power') {
                window.power(obj);
            } else if (obj.event === 'dept') {
                window.dept(obj);
            } */
        });

        table.on('toolbar(role-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } /*else if (obj.event === 'refresh') {
                window.refresh();
            } else if (obj.event === 'batchRemove') {
                window.batchRemove(obj);
            }
            else if(obj.event=='downloadTemplate')
            {
                window.downloadTemplate();
            }
            else if(obj.event=='batchUpdateDoc')
            {
                window.batchUpdateDocFiles();
            }*/
        });
        form.on('submit(role-query)', function (data) {
            console.log(data);
            table.reload('role-table', {where: {systemId:$("#hdSystemId").val()},page: { curr: 1 }});
            return false;
        });

        window.reload=function()
        {
            table.reload('role-table', {where: {systemId:$("#hdSystemId").val()},page: { curr: 1 }});
            return false;
        }

        //跳转到新页面
        window.add_tab_f = function (title,url,is_refresh) {
            var element=parent.layui.element;
            var id = url.replace("?","_").replace("=","_").replaceAll('/','_');//md5每个url
            //防止重复打开
            console.log('id:'+id);
            for (var i = 0; i < parent.$('.x-iframe').length; i++) {
                console.log('parent.$(\'.x-iframe\').length:'+parent.$('.x-iframe').length);
                console.log(parent.$('.x-iframe'));
                if(parent.$('.x-iframe').eq(i).attr('tab-id')==id){
                    element.tabChange('xbs_tab', id);
                    if(is_refresh)
                        parent.$('.x-iframe').eq(i).attr("src",parent.$('.x-iframe').eq(i).attr('src'));
                    return;
                }
            };
            console.log('element:'+element);
            element.tabAdd('xbs_tab', {
                title: title
                ,content: '<iframe tab-id="'+id+'" frameborder="0" src="'+url+'" scrolling="yes" class="x-iframe"></iframe>'
                ,id: id
            });
            element.tabChange('xbs_tab', id);
        }
        window.add = function () {
            var systemId=$("#hdSystemId").val();
            parent.layer.open({
                type: 2,
                title: '新增体系意见',
                shade: 0.1,
                area: ['650px', '600px'],
                content: MODULE_PATH + 'add?systemId='+systemId,
                end: function(){
                    window.reload();
                }
            });
        }

        window.detail=function(obj){
            layer.open({
                type: 2,
                title: '【'+obj.data['name']+'】',
                shade: 0.1,
                area: ['1000px', '500px'],
                content: MODULE_PATH + 'detail?id=' + obj.data['id']
            });
        }

        window.refresh = function () {
            table.reload('role-table');
            window.setUpload();
        }
    });
</script>
<style>
    body{
        font-size:12px !important;
    }
    .layui-table-tool
    {
        height:70px !important;
        line-height:30px !important;
        padding:10px 0 0 10px !important;
        min-height:0px !important;
    }
    .layui-form-item .layui-form-label, .layui-form-item .layui-input-inline {
        margin-bottom: 7px;
    }
    .layui-form-item {
        padding-bottom: 0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    #resultInfo {
        width: 100%;
        text-align: center;
        color: #999;
        margin-top: 10px;
    }
    .layui-table thead th {
        font-weight: bold;
    }
   /* .c-anchor {
        text-decoration: underline;
        color: #666;
        font-size: 14px;
        display: inline-block;
        white-space: nowrap;
    }*/
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    .search-label {
        flex-grow: 4;
    }

    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 240px;
    }
    .con-type-container {
        width: 110px;
    }
    .search-anchor-1
    {
        flex-grow:5;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }
    /*  .search-container
      {
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
      }
      .search-container .item
      {
          display: flex;
          flex-direction: row;
          margin-top:0.625rem;
      }
      .search-container .item .sub-item button
      {
          margin-top:10px;
      }
      .search-container .item label
      {
          min-width: 80px;
          padding: 8px 1px 8px 10px;
          text-align: right;
      }
      .layui-table-tool
      {
          height:70px !important;
          line-height:30px !important;
          padding:10px 0 0 10px !important;
          min-height:0px !important;
      }
      .layui-card-body .layui-form
      {
          margin-top:0px !important;
      }
      .layui-form-item .layui-form-label,.layui-form-item  .layui-input-inline{
          margin-bottom:7px;
      }
      .layui-form-item{
          padding-bottom:0px !important;
      }
      .layui-table-cell {
          height: auto;
          line-height: 28px;
          overflow:auto;
          white-space: normal;
      }
      #resultInfo{
          width:300px;
          text-align: center;
          color:#999;
          margin:10px auto 0;
      }
      .layui-table thead th {
          font-weight: bold;
      }
      .c-anchor
      {
          text-decoration: underline;
          color: #666;
          font-size: 12px;
          display: inline-block;
      }
      .layui-table thead th .layui-table-cell {
          text-align: center !important;
      }
      .is-show-0{
          display: none;
      }
      .layui-table-page
      {
          text-align: center !important;
      }*/
</style>
</html>
