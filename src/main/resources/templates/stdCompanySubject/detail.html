<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('信息配置修改')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">专题名称：</label>
                    <div class="layui-input-block"  th:text="${model.name}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">级别</label>
                    <div class="layui-input-block">
                        <select name="level" id="level" lay-filter="level" lay-verify="required">
                            <option value="国家级">国家级</option>
                            <option value="行业级">行业级</option>
                            <option value="集团级">集团级</option>
                            <option value="企业级">企业级</option>
                            <option value="型号级">型号级</option>
                            <option value="产品级">产品级</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">层级数：</label>
                    <div class="layui-input-block"  th:text="${model.layerNumber}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准数量：</label>
                    <div class="layui-input-block"  th:text="${model.standardNumber}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;">*</span>发布状态</label>
                    <div class="layui-input-block">
                        <select name="isPublish" id="isPublish" lay-filter="isPublish" lay-verify="required">
                            <option value="0">编制中</option>
                            <option value="1">已发布</option>
                            <option value="-1">已停用</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;"></span>说明</label>
                    <div class="layui-input-block">
                        <textarea rows="5" class="layui-textarea" readonly="readonly" name="description" th:text="${model.description}" ></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-update" lay-submit=""
                    type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                关闭
            </button>
        </div>
    </div>
</form>
<input name="hdLevel" th:value="${model.level}" type="hidden" />
<input name="hdIsPublish" th:value="${model.isPublish}" type="hidden" />
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        $("#level").val($("input[name='hdLevel']").val());
        $("#isPublish").val($("input[name='hdIsPublish']").val());
        form.render("select");
        form.on('submit(dict-type-update)', function (data) {
            parent.layer.close(parent.layer.getFrameIndex(window.name));
            return false;
        });
    })
</script>
<style>
    .layui-form-label{
        width:20%;
        text-align: right;
    }
    .layui-form-label
    {
        color:#aaa;
    }
    .layui-input-block
    {
        line-height: 36px;
        display: inline-block  !important;
        margin-left:0px  !important;
        width:70% !important;
    }
</style>
</body>
</html>
