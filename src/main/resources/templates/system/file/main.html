<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('文件列表')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">资源名称</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="realName" placeholder="" type="text">
                </div>
                <label class="layui-form-label">文件名称</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="username" placeholder="" type="text">
                </div>
                <button class="pear-btn pear-btn-md pear-btn-primary" lay-filter="file-query" lay-submit>
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button class="pear-btn pear-btn-md" type="reset">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>

<div class="layui-card">
    <div class="layui-card-body">
        <table id="file-table" lay-filter="file-table"></table>
    </div>
</div>

</body>

<script id="role-toolbar" type="text/html">
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        上传
    </button>
    <button class="pear-btn pear-btn-md" lay-event="batchRemove">
        <i class="layui-icon layui-icon-delete"></i>
        删除
    </button>
</script>

<script id="role-bar" type="text/html">
    <a class="pear-btn pear-btn-primary pear-btn-sm" href="/system/file/download/{{d.id}}" download="{{d.fileDesc}}">
        <i class="pear-icon pear-icon-direction-down"></i>
    </a>
    <button class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>
    </button>
</script>

<script id="file-image" type="text/html">
    <a href="javascript:void(0);" class="pear-text">查看</a>
</script>

<script id="file-createTime" type="text/html">
    {{layui.util.toDateString(d.createTime,  'yyyy-MM-dd HH:mm:ss')}}
</script>

<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;

        let MODULE_PATH = "/system/file/";

        let cols = [
            [
                {type: 'checkbox'},
                {title: '资源名称', field: 'fileName', align: 'center', width: 200},
                {title: '原名称', field: 'fileDesc', align: 'center', width: 260},
                {title: '类型', field: 'fileType', align: 'center', width: 80},
                {title: '文件大小', field: 'fileSize', align: 'center'},
                {title: '预览', field: 'fileType', align: 'center', templet: "#file-image", event: "file-image"},
                {title: '创建日期', field: 'createTime', align: 'center', templet: "#file-createTime"},
                {title: '操作', toolbar: '#role-bar', align: 'center', width: 150}
            ]
        ]

        table.render({
            elem: '#file-table',
            url: MODULE_PATH + 'data',
            page: true,
            cols: cols,
            skin: 'line',
            height: 'full-148',
            toolbar: '#role-toolbar',
            defaultToolbar: [{
                title: '刷新',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print', 'exports']
        });

        table.on('tool(file-table)', function (obj) {
            if (obj.event === 'remove') {
                window.remove(obj);
            } else if (obj.event === 'file-image') {
                window.show(obj);
            }
        });

        table.on('toolbar(file-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                window.refresh();
            } else if (obj.event === 'batchRemove') {
                window.batchRemove(obj);
            }
        });

        form.on('submit(file-query)', function (data) {
            table.reload('file-table', {where: data.field})
            return false;
        });

        window.add = function () {
            layer.open({
                type: 2,
                title: '新增',
                shade: 0.1,
                area: ['500px', '400px'],
                content: MODULE_PATH + 'add'
            });
        }

        window.show = function (obj) {
            layer.photos({
                photos: {
                    "title": "预览图片",
                    "start": 0,
                    "data": [
                        {"src": "/system/file/download/" + obj.data.id}
                    ]
                }
                , anim: 5
            });
        }

        window.remove = function (obj) {
            layer.confirm('确定要删除该文件', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "remove/" + obj.data['id'],
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            popup.success(result.msg, function () {
                                obj.del();
                            });
                        } else {
                            popup.failure(result.msg);
                        }
                    }
                })
            });
        }

        window.batchRemove = function (obj) {
            let ids = common.checkField(obj, 'id');
            if (common.isEmpty(ids)) {
                popup.warning("未选中数据");
                return false;
            }
            layer.confirm('确定要删除选中文件', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "batchRemove/" + ids,
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            popup.success(result.msg, function () {
                                table.reload('file-table');
                            });
                        } else {
                            popup.failure(result.msg);
                        }
                    }
                })
            });
        }

        window.refresh = function () {
            table.reload('file-table');
        }
    })
</script>
</html>