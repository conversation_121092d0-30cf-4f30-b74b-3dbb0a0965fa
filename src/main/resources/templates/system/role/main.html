<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('角色列表')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">角色名</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="roleName" placeholder="" type="text">
                </div>
                <label class="layui-form-label">角色标识</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="roleCode" placeholder="" type="text">
                </div>
                <button class="pear-btn pear-btn-md pear-btn-primary" lay-filter="role-query" lay-submit>
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button class="pear-btn pear-btn-md" type="reset">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="role-table" lay-filter="role-table"></table>
    </div>
</div>
</body>

<script id="role-toolbar" type="text/html">
    <button sec:authorize="hasPermission('/system/role/add','sys:role:add')"
            class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        新增
    </button>
    <button sec:authorize="hasPermission('/system/role/remove','sys:role:remove')" class="pear-btn pear-btn-md"
            lay-event="batchRemove">
        <i class="layui-icon layui-icon-delete"></i>
        删除
    </button>
</script>

<script id="role-bar" type="text/html">
    <button sec:authorize="hasPermission('/system/role/edit','sys:role:edit')"
            class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>
    </button>
    <button sec:authorize="hasPermission('/system/role/power','sys:role:power')"
            class="pear-btn pear-btn-warming pear-btn-sm" lay-event="power"><i
            class="layui-icon layui-icon-vercode"></i>
    </button>
    <button sec:authorize="hasPermission('/system/role/power','sys:role:power')"
            class="pear-btn pear-btn-warming pear-btn-sm" lay-event="dept"><i
            class="layui-icon layui-icon-password"></i>
    </button>
    <button sec:authorize="hasPermission('/system/role/remove','sys:role:remove')"
            class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>
    </button>
</script>

<script id="role-enable" type="text/html">
    <input type="checkbox" name="enable" value="{{d.roleId}}" lay-skin="switch" lay-text="启用|禁用"
           lay-filter="role-enable" {{d.enable== '1' ? 'checked' : '' }} >
</script>

<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;

        let MODULE_PATH = "/system/role/";

        let cols = [
            [
                {type: 'checkbox'},
                {title: '角色名', field: 'roleName', align: 'center', width: 100},
                {title: 'Key值', field: 'roleCode', align: 'center'},
                {title: '描述', field: 'details', align: 'center'},
                {title: '是否可用', field: 'enable', align: 'center', templet: '#role-enable'},
                {title: '排序', field: 'sort', align: 'center'},
                {title: '操作', toolbar: '#role-bar', align: 'center', width: 195}
            ]
        ]

        table.render({
            elem: '#role-table',
            url: MODULE_PATH + 'data',
            page: true,
            cols: cols,
            skin: 'line',
            toolbar: '#role-toolbar',
            defaultToolbar: [{
                title: '刷新',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print', 'exports']
        });

        table.on('tool(role-table)', function (obj) {
            if (obj.event === 'remove') {
                window.remove(obj);
            } else if (obj.event === 'edit') {
                window.edit(obj);
            } else if (obj.event === 'power') {
                window.power(obj);
            } else if (obj.event === 'dept') {
                window.dept(obj);
            }
        });

        table.on('toolbar(role-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                window.refresh();
            } else if (obj.event === 'batchRemove') {
                window.batchRemove(obj);
            }
        });

        form.on('submit(role-query)', function (data) {
            table.reload('role-table', {where: data.field})
            return false;
        });

        form.on('switch(role-enable)', function (obj) {
            let operate;
            if (obj.elem.checked) {
                operate = "enable";
            } else {
                operate = "disable";
            }
            let loading = layer.load()
            $.ajax({
                url: '/system/role/' + operate,
                data: JSON.stringify({roleId: this.value}),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000});
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
        });

        window.add = function () {
            layer.open({
                type: 2,
                title: '新增',
                shade: 0.1,
                area: ['500px', '500px'],
                content: MODULE_PATH + 'add'
            });
        }

        window.power = function (obj) {
            layer.open({
                type: 2,
                title: '操作权限',
                shade: 0.1,
                area: ['320px', '400px'],
                content: MODULE_PATH + 'power?roleId=' + obj.data["roleId"]
            });
        }

        window.dept = function (obj) {
            layer.open({
                type: 2,
                title: '数据权限',
                shade: 0.1,
                area: ['320px', '400px'],
                content: MODULE_PATH + 'dept?roleId=' + obj.data["roleId"]
            });
        }

        window.edit = function (obj) {
            layer.open({
                type: 2,
                title: '修改',
                shade: 0.1,
                area: ['500px', '500px'],
                content: MODULE_PATH + 'edit?roleId=' + obj.data['roleId']
            });
        }

        window.remove = function (obj) {
            layer.confirm('确定要删除该角色', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "remove/" + obj.data['roleId'],
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                                obj.del();
                            });
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 1000});
                        }
                    }
                })
            });
        }

        window.batchRemove = function (obj) {
            let ids = common.checkField(obj, 'roleId');
            if (common.isEmpty(ids)) {
                popup.warning("未选中数据");
                return false;
            }
            layer.confirm('确定要删除选中角色', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "batchRemove/" + ids,
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            popup.success(result.msg, function () {
                                table.reload('role-table');
                            });
                        } else {
                            popup.failure(result.msg);
                        }
                    }
                })
            });
        }

        window.refresh = function () {
            table.reload('role-table');
        }
    })
</script>
</html>