<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('数据权限')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="layui-form-item">
                <select id="dataScope" name="dataScope" lay-filter="dataScope">
                    <option th:selected="${sysRole.dataScope.name() eq 'ALL'}" value="ALL">全部</option>
                    <option th:selected="${sysRole.dataScope.name() eq 'SELF'}" value="SELF">仅自己</option>
                    <option th:selected="${sysRole.dataScope.name() eq 'DEPT'}" value="DEPT">所在部门</option>
                    <option th:selected="${sysRole.dataScope.name() eq 'DEPT_CHILD'}" value="DEPT_CHILD">所在部门及下级部门</option>
                    <option th:selected="${sysRole.dataScope.name() eq 'CUSTOM'}" value="CUSTOM">自定义数据</option>
                </select>
            </div>
            <div id="deptTree" class="layui-form-item" th:classappend="${sysRole.dataScope.name() eq 'CUSTOM'} ? '' : 'layui-hide' ">
                <ul class="dtree" data-id="0" id="role-dept"></ul>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dataScope-save" lay-submit="" type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button class="pear-btn pear-btn-sm" type="reset">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    layui.use(['dtree', 'form', 'jquery'], function () {
        let dtree = layui.dtree;
        let form = layui.form;
        let $ = layui.jquery;

        dtree.render({
            elem: "#role-dept",
            method: "get",
            url: "/system/role/getRoleDept?roleId=" + [[${roleId}]],
            dataFormat: "list",
            checkbar: true,
            skin: "layui",
            initLevel: "1",
            checkbarType: "self",
            response: {treeId: "deptId", parentId: "parentId", title: "deptName"},
        });

        form.on('select(dataScope)', function(data){
            if(data.value === "CUSTOM") {
                $("#deptTree").removeClass("layui-hide");
            } else {
                $("#deptTree").addClass("layui-hide");
            }
        })

        form.on('submit(dataScope-save)', function (data) {
            let param = dtree.getCheckbarNodesParam("role-dept");
            let ids = '';
            for (let i = 0; i < param.length; i++) {
                let id = param[i].nodeId;
                ids += id + ',';
            }
            ids = ids.substr(0, ids.length - 1);
            data.field.roleId = [[${roleId}]];
            data.field.deptIds = ids;
            if(data.field.dataScope != "CUSTOM") {
                data.field.deptIds = [];
            }
            $.ajax({
                url: '/system/role/saveRoleDept',
                data: data.field,
                dataType: 'json',
                type: 'put',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name));
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });

    })
</script>
</body>
</html>