<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('dataSource列表')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <div class="col-sm-12 search-collapse">
            <form class="layui-form" id="formId">
                <div class="layui-form-item">
                    <label class="layui-form-label">标题：</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" lay-required name="title" placeholder="请输入标题" type="text"/>
                    </div>

                    <label class="layui-form-label">类型：</label>
                    <div class="layui-input-inline">
                        <select dict-code="sys_notice_type" lay-required name="type">
                            <option value="">所有</option>
                        </select>
                    </div>
                    <button class="pear-btn pear-btn-md pear-btn-primary" lay-filter="dataSource-query" lay-submit>
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <button class="pear-btn pear-btn-md" type="reset">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="dataSource-table" lay-filter="dataSource-table"></table>

        <script id="dataSource-toolbar" type="text/html">
            <button sec:authorize="hasPermission('/system/dataSource/add','system:dataSource:add')"
                    class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
                <i class="layui-icon layui-icon-add-1"></i>
                新增
            </button>
            <button sec:authorize="hasPermission('/system/dataSource/remove','system:dataSource:remove')"
                    class="pear-btn pear-btn-md" lay-event="batchRemove">
                <i class="layui-icon layui-icon-delete"></i>
                删除
            </button>
        </script>

        <script id="dataSource-bar" type="text/html">
            <button sec:authorize="hasPermission('/system/dataSource/edit','system:dataSource:edit')"
                    class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i
                    class="layui-icon layui-icon-edit"></i>
            </button>
            <button sec:authorize="hasPermission('/system/dataSource/remove','system:dataSource:remove')"
                    class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i
                    class="layui-icon layui-icon-delete"></i>
            </button>
        </script>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'form', 'jquery', 'dictionary', 'common'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let common = layui.common;

        let prefix = "/system/dataSource/";

        let cols = [
            [
                { type: 'checkbox' },
                {
                    field: 'name',
                    title: '数据库名称'
                },
                {
                    field: 'username',
                    title: '账户'
                },
                {
                    field: 'password',
                    title: '密码'
                },
                {
                    field: 'url',
                    title: '链接'
                },
                {
                    field: 'driver',
                    title: '驱动'
                },
                {title: '操作', toolbar: '#dataSource-bar', align: 'center', width: 130}
            ]
        ]

        table.render({
            elem: '#dataSource-table',
            url: prefix + 'data',
            page: true,
            cols: cols,
            skin: 'line',
            toolbar: '#dataSource-toolbar',
            defaultToolbar: [{
                title: '刷新',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print', 'exports']
        });

        table.on('tool(dataSource-table)', function (obj) {
            if (obj.event === 'remove') {
                window.remove(obj);
            } else if (obj.event === 'edit') {
                window.edit(obj);
            }
        });

        table.on('toolbar(dataSource-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                window.refresh();
            } else if (obj.event === 'batchRemove') {
                window.batchRemove(obj);
            }
        });

        form.on('submit(dataSource-query)', function (data) {
            table.reload('dataSource-table', {where: data.field})
            return false;
        });

        window.add = function () {
            layer.open({
                type: 2,
                title: '新增数据源',
                shade: 0.1,
                area: ['550px', '500px'],
                content: prefix + 'add'
            });
        }

        window.edit = function (obj) {
            layer.open({
                type: 2,
                title: '修改数据源',
                shade: 0.1,
                area: ['550px', '500px'],
                content: prefix + 'edit?id=' + obj.data['id']
            });
        }

        window.remove = function (obj) {
            layer.confirm('确定要删除该数据源', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: prefix + "remove/" + obj.data['id'],
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                                obj.del();
                            });
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 1000});
                        }
                    }
                })
            });
        }

        window.batchRemove = function (obj) {
            let data = table.checkStatus(obj.dataSource.id).data;
            if (data.length === 0) {
                layer.msg("未选中数据", {icon: 3, time: 1000});
                return false;
            }
            let ids = common.checkField(obj,'id');
            layer.confirm('确定要删除这些数据源', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: prefix + "batchRemove",
                    dataType: 'json',
                    data: {"ids": ids},
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                                table.reload('dataSource-table');
                            });
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 1000});
                        }
                    }
                })
            });
        }

        window.refresh = function (param) {
            table.reload('dataSource-table', {where: param});
        }
    })
</script>
</body>
</html>