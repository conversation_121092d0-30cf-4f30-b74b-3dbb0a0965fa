<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('部門修改')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item layui-hide">
                    <label class="layui-form-label"><span style="color: red;">*</span>编号</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="deptId"
                               placeholder="请输入标题" th:value="${sysDept.deptId}" type="text">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;">*</span>名称</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="deptName"
                               placeholder="请输入标题" th:value="${sysDept.deptName}" type="text">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;">*</span>管理</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="leader"
                               placeholder="请输入标题" th:value="${sysDept.leader}"
                               type="text">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;">*</span>邮箱</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="email" name="email"
                               placeholder="请输入标题" th:value="${sysDept.email}"
                               type="text">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;">*</span>电话</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="phone" name="phone"
                               placeholder="请输入标题" th:value="${sysDept.phone}"
                               type="text">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <input name="status" th:checked="${sysDept.status eq '1'}" title="开启" type="radio" value="1" />
                        <input checked name="status" th:checked="${sysDept.status eq '0'}" title="关闭" type="radio"
                               value="0" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">排序</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="title" name="sort" placeholder="请输入标题"
                               th:value="${sysDept.sort}"
                               type="text">
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">描述</label>
                    <div class="layui-input-block">
                        <textarea class="layui-textarea" name="address" placeholder="请输入详细地址"
                                  th:text="${sysDept.address}"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dept-update" lay-submit=""
                    type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button class="pear-btn pear-btn-sm" type="reset">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery'], function () {
        let form = layui.form;
        let $ = layui.jquery;

        form.on('submit(dept-update)', function (data) {
            $.ajax({
                url: '/system/dept/update',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                            parent.layui.treetable.reload("#dept-table");
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });
    })
</script>
<script>
</script>
</body>
</html>