<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('部门列表')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">部门名称</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="deptName" placeholder="" type="text">
                </div>
                <button class="pear-btn pear-btn-md pear-btn-primary" lay-filter="dept-query" lay-submit>
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button class="pear-btn pear-btn-md" type="reset">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>

<div class="layui-card">
    <div class="layui-card-body">
        <table id="dept-table" lay-filter="dept-table"></table>
    </div>
</div>

<script id="dept-toolbar" type="text/html">
    <button sec:authorize="hasPermission('/system/dept/add','sys:dept:add')"
            class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        新增
    </button>
</script>

<script id="dept-bar" type="text/html">
    <button sec:authorize="hasPermission('/system/dept/edit','sys:dept:edit')"
            class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>
    </button>
    <button sec:authorize="hasPermission('/system/dept/remove','sys:dept:remove')"
            class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>
    </button>
</script>

<script id="dept-status" type="text/html">
    <input type="checkbox" name="enable" value="{{d.deptId}}" lay-skin="switch" lay-text="启用|禁用"
           lay-filter="dept-enable" {{ d.status == '1' ? 'checked' : '' }}>
</script>

</body>

<th:block th:include="include :: footer"/>

<script>
    layui.use(['table', 'form', 'jquery', 'treetable', 'popup'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let treetable = layui.treetable;
        let popup = layui.popup;

        let MODULE_PATH = "/system/dept/";

        treetable.render({
            treeColIndex: 1,
            treeSpid: 0,
            treeIdName: 'deptId',
            treePidName: 'parentId',
            skin: 'line',
            method: 'get',
            treeDefaultClose: false,
            toolbar: '#dept-toolbar',
            elem: '#dept-table',
            url: '/system/dept/data',
            page: false,
            defaultToolbar: [{
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
                title: '刷新'
            }, 'filter', 'print', 'exports'],
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'deptName', minWidth: 200, title: '部门名称'},
                    {field: 'leader', title: '负责人'},
                    {field: 'phone', title: '联系方式'},
                    {field: 'email', title: '邮箱'},
                    {field: 'address', title: '详细地址'},
                    {field: 'status', title: '状态', templet: '#dept-status'},
                    {field: 'sort', title: '排序', width: 100},
                    {title: '操作', templet: '#dept-bar', width: 120, align: 'center'}
                ]
            ]
        });

        form.on('submit(dept-query)', function (data) {
            treetable.search("#dept-table", data.field.deptName);
            return false;
        });

        table.on('tool(dept-table)', function (obj) {
            if (obj.event === 'remove') {
                window.remove(obj);
            } else if (obj.event === 'edit') {
                window.edit(obj);
            }
        })

        table.on('toolbar(dept-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                window.refresh();
            }
        });

        form.on('switch(dept-enable)', function (obj) {
            let operate;
            if (obj.elem.checked) {
                operate = "enable";
            } else {
                operate = "disable";
            }
            let loading = layer.load();
            $.ajax({
                url: '/system/dept/' + operate,
                data: JSON.stringify({deptId: this.value}),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        popup.success(result.msg);
                    } else {
                        popup.failure(result.msg);
                    }
                }
            })
        });

        window.add = function () {
            layer.open({
                type: 2,
                title: '新增',
                shade: 0.1,
                area: ['450px', '500px'],
                content: MODULE_PATH + 'add'
            });
        }

        window.edit = function (obj) {
            layer.open({
                type: 2,
                title: '修改',
                shade: 0.1,
                area: ['450px', '500px'],
                content: MODULE_PATH + 'edit?deptId=' + obj.data['deptId']
            });
        }

        window.remove = function (obj) {
            layer.confirm('确定要删除该部门', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "remove/" + obj.data['deptId'],
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            popup.success(result.msg, function () {
                                obj.del();
                            });
                        } else {
                            popup.failure(result.msg);
                        }
                    }
                })
            });
        }

        window.refresh = function () {
            treetable.reload('#dept-table');
        }
    })
</script>
</html>