<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('标准修改')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;">*</span>文本存放盘符</label>
                        <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" id="configValue"
                               placeholder="请输入文本存放盘符" value="D" type="text">
                    </div>
                    <button class="my-btn pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-update" lay-submit=""
                            type="submit">
                        <i class="layui-icon layui-icon-ok"></i>
                        保存
                    </button>
                </div>
               <div class="layui-form-item">
                   <div class="layui-input-block" style="margin-top:10px">
                       <table class="m-table">
                           <tr><th>文本存放路径</th></tr>
                           <tr>
                               <td style="word-break: break-all;word-wrap: break-word;"><i id="disk4" style="line-height:30px;">D</i></td>
                           </tr>
                       </table>
                   </div>
                </div>
            </div>
            <div class="main-container">
                <div class="layui-form-item">
                    <div class="button-container">

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">

    </div>
</form>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        var sysConfig={configId:''};
        $.ajax({
            url: '/system/config/encryptdisk',
            type:'get',
            dataType: 'json',
            contentType: 'application/json',
            success: function (result) {
                if (result.success) {
                    sysConfig.configId=result.data.configId;
                    sysConfig.configValue=result.data.configValue;
                    $("#configValue").val(sysConfig.configValue);
                    console.log(sysConfig);
                    // $("#disk1").text(sysConfig.configValue);
                    // $("#disk2").text(sysConfig.configValue);
                    // $("#disk3").text(sysConfig.configValue);
                    var arr=sysConfig.configValue.split(",");
                    var text=[];
                    arr.forEach(item=>{
                        text.push(`${item}:\\EncryptSources`);
                        text.push(`${item}:\\EncryptSources0`);
                        text.push(`${item}:\\EncryptSources1`);
                        text.push(`${item}:\\EncryptSources2`);
                    });
                    $("#disk4").html(text.join("<br/>"));
                }
            }
        });
        form.on('submit(dict-type-update)', function (data) {
            sysConfig.configValue=$("#configValue").val();
            $.ajax({
                url: '/system/config/saveencryptdisk',
                data: JSON.stringify(sysConfig),
                dataType: 'json',
                contentType: 'application/json',
                type: 'post',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                           // parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                           // parent.layui.table.reload("config-table");
                           //  $("#disk1").text(sysConfig.configValue);
                           //  $("#disk2").text(sysConfig.configValue);
                           //  $("#disk3").text(sysConfig.configValue);
                           // $("#disk4").text(sysConfig.configValue);
                            var arr=sysConfig.configValue.split(",");
                            var text=[];
                            arr.forEach(item=>{
                                text.push(`${item}:\\EncryptSources`);
                                text.push(`${item}:\\EncryptSources0`);
                                text.push(`${item}:\\EncryptSources1`);
                                text.push(`${item}:\\EncryptSources2`);
                            });
                            $("#disk4").html(text.join("<br/>"));
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });
    })
</script>
<script>
</script>
<style>
    .layui-form-label
    {
        width:300px;
    }
    .layui-input-block
    {
        margin-left:330px;
        width:50%;
        margin-top:50px;
    }
    .my-btn{
        margin-left: 330px !important;
        margin-top: 15px !important;
        width: 80px !important;
    }
    .m-table
    {
        color:#888;
        width:80%;
        border-collapse:collapse;
        border: solid 1px #bbb;
        border-radius: 3px;

    }
    .m-table th,td{
        border: solid 1px #bbb;
        padding:3px 5px 3px 10px;
    }
    .m-table th{
       text-align: center;
    }
    .m-table td {
        text-align: left;
    }
</style>
</body>
</html>
