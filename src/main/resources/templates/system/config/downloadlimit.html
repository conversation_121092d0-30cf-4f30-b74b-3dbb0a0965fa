<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('下载限制')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;">*</span>用户下载次数限制</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" id="configValue"
                               placeholder="请输入每日最大下载次数" value="50" type="number">
                    </div>
                    <div class="layui-input-block" style="margin-top:10px;color:#888;">
                        请输入用户每日最大下载次数,用户每日下载量超过该值后不能继续下载。
                    </div>
                    <button class="my-btn pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-update" lay-submit=""
                            type="submit">
                        <i class="layui-icon layui-icon-ok"></i>
                        保存
                    </button>
                </div>
            </div>
            <div class="main-container">
                <div class="layui-form-item">
                    <div class="button-container">

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">

    </div>
</form>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        var sysConfig={configId:''};
        $.ajax({
            url: '/system/config/downloadlimitperday',
            type:'get',
            dataType: 'json',
            contentType: 'application/json',
            success: function (result) {
                if (result.success) {
                    sysConfig.configId=result.data.configId;
                    sysConfig.configValue=result.data.configValue;
                    $("#configValue").val(sysConfig.configValue);
                    console.log(sysConfig);
                }
            }
        });
        form.on('submit(dict-type-update)', function (data) {
            sysConfig.configValue=$("#configValue").val();
            $.ajax({
                url: '/system/config/savedownloadlimit',
                data: JSON.stringify(sysConfig),
                dataType: 'json',
                contentType: 'application/json',
                type: 'post',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });
    })
</script>
<script>
</script>
<style>
    .layui-form-label
    {
        width:300px;
    }
    .layui-input-block
    {
        margin-left:330px;
        width:50%;
        margin-top:100px;
    }
    .my-btn{
        margin-left: 330px !important;
        margin-top: 15px !important;
        width: 80px !important;
    }
    .m-table
    {
        color:#888;
        width:80%;
        border-collapse:collapse;
        border: solid 1px #bbb;
        border-radius: 3px;

    }
    .m-table th,td{
        border: solid 1px #bbb;
        padding:3px 5px 3px 10px;
    }
    .m-table th{
        text-align: center;
    }
    .m-table td {
        text-align: left;
    }
</style>
</body>
</html>
