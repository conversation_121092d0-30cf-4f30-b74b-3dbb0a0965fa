<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改消息')"/>
</head>
<body>
<form class="layui-form" th:object="${sysNotice}">
    <div class="mainBox">
        <div class="main-container">
            <input name="id" th:field="*{id}" type="hidden">
            <div class="layui-form-item">
                <label class="layui-form-label">标题：</label>
                <div class="layui-input-block">
                    <input class="layui-input" lay-verify="required" name="title" placeholder="请输入标题" th:field="*{title}"
                           type="text"/>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">内容：</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" lay-verify="required" name="content" placeholder="请输入内容"
                              th:field="*{content}" type="text"></textarea>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="notice-update" lay-submit=""
                    type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button class="pear-btn pear-btn-sm" type="reset">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
</body>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    layui.use(['form', 'jquery'], function () {
        let form = layui.form;
        let $ = layui.jquery;

        let prefix = "/system/notice/";

        form.on('submit(notice-update)', function (data) {
            $.ajax({
                url: prefix + 'update',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name));
                            parent.layui.table.reload("notice-table");
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });
    });
</script>
</html>