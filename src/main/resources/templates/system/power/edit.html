<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('权限修改')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item layui-hide">
                    <label class="layui-form-label">编号</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="powerId"
                               th:value="${sysPower.powerId}" type="text">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">父级</label>
                    <div class="layui-input-block">
                        <ul class="dtree" data-id="-1" id="selectParent" name="parentId"></ul>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="powerName"
                               placeholder="权限名称" th:value="${sysPower.powerName}" type="text">
                    </div>
                </div>
                <div class="layui-form-item" id="powerCodeItem">
                    <label class="layui-form-label">标识</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" id="powerCode" name="powerCode"
                               placeholder="权限标识" th:value="${sysPower.powerCode}" type="text">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <input lay-filter="powerType" name="powerType" th:checked="${sysPower.powerType eq '0'}"
                               title="目录" type="radio" value="0">
                        <input lay-filter="powerType" name="powerType" th:checked="${sysPower.powerType eq '1'}"
                               title="菜单" type="radio" value="1">
                        <input lay-filter="powerType" name="powerType" th:checked="${sysPower.powerType eq '2'}"
                               title="按钮" type="radio" value="2">
                    </div>
                </div>
                <div class="layui-form-item" id="powerUrlItem">
                    <label class="layui-form-label">路径</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" id="powerUrl" name="powerUrl"
                               placeholder="菜单路径" th:value="${sysPower.powerUrl}" type="text">
                    </div>
                </div>
                <div class="layui-form-item" id="openTypeItem">
                    <label class="layui-form-label">打开</label>
                    <div class="layui-input-block">
                        <select id="openType" name="openType">
                            <option value=""></option>
                            <option th:selected="${sysPower.openType eq '_iframe'}" value="_iframe">框架</option>
                            <option th:selected="${sysPower.openType eq '_blank'}" value="_blank">签页</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">图标</label>
                    <div class="layui-input-block">
                        <input class="layui-input" id="icon" lay-filter="icon" name="icon" th:value="${sysPower.icon}"
                               type="text">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">排序</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" name="sort" placeholder="排序"
                               th:value="${sysPower.sort}" type="text">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="power-save" lay-submit="" type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button class="pear-btn pear-btn-sm" type="reset">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
<th:block th:include="include :: footer"/>
<script th:inline="javascript">
    layui.use(['form', 'jquery', 'iconPicker', 'dtree'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        let iconPicker = layui.iconPicker;
        let dtree = layui.dtree;

        iconPicker.render({
            elem: '#icon',
            type: 'fontClass',
            search: true,
            page: true,
            limit: 16
        });

        dtree.renderSelect({
            elem: "#selectParent",
            url: "/system/power/selectParent",
            method: 'get',
            selectInputName: {nodeId: "parentId", context: "powerName"},
            skin: "layui",
            dataFormat: "list",
            response: {treeId: "powerId", parentId: "parentId", title: "powerName"},
            selectInitVal: [[${sysPower.parentId}]]
        });

        form.on("radio(powerType)", function (data) {
            if (this.value == '0') {
                $("#powerUrlItem").hide();
                $("#powerCodeItem").hide();
                $("#openTypeItem").hide();
                $("#powerUrl").val("");
                $("#powerCode").val("");
                $("#openType").val("");
            } else if (this.value == '1') {
                $("#powerUrlItem").show();
                $("#powerCodeItem").show();
                $("#openTypeItem").show();
            } else if (this.value == '2') {
                $("#powerUrlItem").hide();
                $("#openTypeItem").hide();
                $("#powerCodeItem").show();
                $("#powerUrl").val("");
                $("#openType").val("");
            }
        });

        form.on('submit(power-save)', function (data) {
            data.field.icon = "layui-icon " + data.field.icon;
            data.field.selectParent_select_input = undefined;
            $.ajax({
                url: '/system/power/update',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                            parent.layui.treetable.reload("#power-table");
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });

        window.init = function (type) {
            if (type == '0') {
                $("#powerUrlItem").hide();
                $("#powerCodeItem").hide();
                $("#openTypeItem").hide();
                $("#powerUrl").val("");
                $("#powerCode").val("");
                $("#openType").val("");
            } else if (type == '1') {
                $("#powerUrlItem").show();
                $("#powerCodeItem").show();
                $("#openTypeItem").show();
            } else if (type == '2') {
                $("#powerUrlItem").hide();
                $("#openTypeItem").hide();
                $("#powerCodeItem").show();
                $("#powerUrl").val("");
                $("#openType").val("");
            }
        }

        window.init([[${sysPower.powerType}]]);
    })
</script>
<script>
</script>
</body>
</html>