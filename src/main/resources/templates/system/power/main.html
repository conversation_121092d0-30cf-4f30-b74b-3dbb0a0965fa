<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('权限列表')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">关键字</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="powerName" placeholder="" type="text">
                </div>
                <button class="pear-btn pear-btn-md pear-btn-primary" lay-filter="power-query" lay-submit>
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button class="pear-btn pear-btn-md" type="reset">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="power-table" lay-filter="power-table"></table>
    </div>
</div>

<script id="power-toolbar" type="text/html">
    <button sec:authorize="hasPermission('/system/power/add','sys:power:add')"
            class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        新增
    </button>
</script>

<script id="power-bar" type="text/html">
    <button sec:authorize="hasPermission('/system/power/edit','sys:power:edit')"
            class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>
    </button>
    <button sec:authorize="hasPermission('/system/power/remove','sys:power:remove')"
            class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>
    </button>
</script>

<script id="power-type" type="text/html">
    {{#if (d.powerType == '0') { }}
    <span>目录</span>
    {{# }else if(d.powerType == '1'){ }}
    <span>菜单</span>
    {{# }else if(d.powerType == '2'){ }}
    <span>按钮</span>
    {{# } }}
</script>

<script id="power-enable" type="text/html">
    <input type="checkbox" name="enable" value="{{d.powerId}}" lay-skin="switch" lay-text="启用|禁用"
           lay-filter="power-enable" {{ d.enable== true ? 'checked' : '' }}>
</script>

<script id="icon" type="text/html">
    <i class="layui-icon {{d.icon}}"></i>
</script>

</body>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'form', 'jquery', 'treetable', 'popup'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let treetable = layui.treetable;
        let popup = layui.popup;

        let MODULE_PATH = "/system/power/";

        treetable.render({
            treeColIndex: 1,
            treeSpid: 0,
            treeIdName: 'powerId',
            treePidName: 'parentId',
            skin: 'line',
            method: 'get',
            treeDefaultClose: false,
            toolbar: '#power-toolbar',
            elem: '#power-table',
            url: '/system/power/data',
            page: false,
            defaultToolbar: [{
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
                title: '刷新'
            }, 'filter', 'print', 'exports'],
            cols: [
                [
                    {type: 'checkbox'},
                    {field: 'powerName', minWidth: 200, title: '权限名称'},
                    {field: 'icon', title: '图标', templet: '#icon'},
                    {field: 'powerType', title: '权限类型', templet: '#power-type'},
                    {field: 'enable', title: '是否可用', templet: '#power-enable'},
                    {field: 'sort', title: '排序'},
                    {title: '操作', templet: '#power-bar', width: 150, align: 'center'}
                ]
            ]
        });

        form.on('submit(power-query)', function (data) {
            treetable.search("#power-table", data.field.powerName)
            return false;
        });

        table.on('tool(power-table)', function (obj) {
            if (obj.event === 'remove') {
                window.remove(obj);
            } else if (obj.event === 'edit') {
                window.edit(obj);
            }
        })

        table.on('toolbar(power-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                window.refresh();
            }
        });

        form.on('switch(power-enable)', function (obj) {
            let operate;
            if (obj.elem.checked) {
                operate = "enable";
            } else {
                operate = "disable";
            }
            let loading = layer.load();
            $.ajax({
                url: '/system/power/' + operate,
                data: JSON.stringify({powerId: this.value}),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        popup.success(result.msg);
                    } else {
                        popup.failure(result.msg);
                    }
                }
            })
        });

        window.add = function () {
            layer.open({
                type: 2,
                title: '新增',
                shade: 0.1,
                area: ['450px', '500px'],
                content: MODULE_PATH + 'add'
            });
        }

        window.edit = function (obj) {
            layer.open({
                type: 2,
                title: '修改',
                shade: 0.1,
                area: ['450px', '500px'],
                content: MODULE_PATH + 'edit?powerId=' + obj.data['powerId']
            });
        }

        window.remove = function (obj) {
            layer.confirm('确定要删除该权限', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "remove/" + obj.data['powerId'],
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            popup.success(result.msg, function () {
                                obj.del();
                            })
                        } else {
                            popup.failure(result.msg);
                        }
                    }
                })
            });
        }

        window.refresh = function () {
            treetable.reload('#power-table');
        }
    })
</script>
</html>