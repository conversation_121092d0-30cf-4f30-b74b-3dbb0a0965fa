<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('预览限制配置')"/>
</head>
<body>
<form class="layui-form" action="">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">每日预览次数</label>
                    <div class="layui-input-block">
                        <input type="text" name="configValue" th:value="${config.configValue}" lay-verify="required|number" autocomplete="off" placeholder="请输入每日预览次数" class="layui-input">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button type="submit" class="pear-btn pear-btn-primary pear-btn-sm" lay-submit="" lay-filter="save">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button type="reset" class="pear-btn pear-btn-sm">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery', 'popup'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;

        form.on('submit(save)', function (data) {
            $.ajax({
                url: '/system/previewlimit/save',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'post',
                success: function (result) {
                    if (result.success) {
                        popup.success(result.msg, function () {
                        });
                    } else {
                        popup.failure(result.msg);
                    }
                }
            })
            return false;
        });
    })
</script>
</body>
</html>
