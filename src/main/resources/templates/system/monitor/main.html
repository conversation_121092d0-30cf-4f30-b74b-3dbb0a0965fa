<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('监控')"/>
    <link href="/admin/css/other/console2.css" rel="stylesheet"/>
</head>
<body class="pear-container">
<div class="layui-row layui-col-space10">
    <div class="layui-col-md8">
        <div class="layui-row layui-col-space10">
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        主机信息
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6 layui-col-sm6 layui-col-xs6">
                                <div class="pear-card2">
                                    <div class="title">核心数</div>
                                    <div class="count pear-text" th:text="${cpu.cpuNum}">21</div>
                                </div>
                            </div>
                            <div class="layui-col-md6 layui-col-sm6 layui-col-xs6">
                                <div class="pear-card2">
                                    <div class="title">空闲率</div>
                                    <div class="count pear-text" th:text="${cpu.free + '%'}">32</div>
                                </div>
                            </div>
                            <div class="layui-col-md6 layui-col-sm6 layui-col-xs6">
                                <div class="pear-card2">
                                    <div class="title">等待率</div>
                                    <div class="count pear-text" th:text="${cpu.wait + '%'}"></div>
                                </div>
                            </div>
                            <div class="layui-col-md6 layui-col-sm6 layui-col-xs6">
                                <div class="pear-card2">
                                    <div class="title">使用率</div>
                                    <div class="count pear-text" th:text="${cpu.used + '%'}"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md6">
                <div class="layui-card">
                    <div class="layui-card-header">
                        内存监控
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6 layui-col-sm6 layui-col-xs6">
                                <div class="pear-card2">
                                    <div class="title">空闲内存</div>
                                    <div class="count pear-text" th:text="${cpu.memInfo.free + 'G'}"></div>
                                </div>
                            </div>
                            <div class="layui-col-md6 layui-col-sm6 layui-col-xs6">
                                <div class="pear-card2">
                                    <div class="title">最大内存</div>
                                    <div class="count pear-text" th:text="${cpu.memInfo.total + 'G'}"></div>
                                </div>
                            </div>
                            <div class="layui-col-md6 layui-col-sm6 layui-col-xs6">
                                <div class="pear-card2">
                                    <div class="title">已用内存</div>
                                    <div class="count pear-text" th:text="${cpu.memInfo.used + 'G'}"></div>
                                </div>
                            </div>
                            <div class="layui-col-md6 layui-col-sm6 layui-col-xs6">
                                <div class="pear-card2">
                                    <div class="title">内存使用</div>
                                    <div class="count pear-text"
                                         th:text="${#numbers.formatDecimal(cpu.memInfo.used / cpu.memInfo.total * 100, 2, 2)+ '%'}"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-col-md12">
                <div class="layui-card">
                    <div class="layui-card-header">访客流量</div>
                    <div class="layui-card-body">
                        <div class="layui-tab custom-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                            <div id="echarts-records" style="background-color:#ffffff;min-height:400px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-col-md4">
        <div class="layui-card">
            <div class="layui-card-header">磁盘信息</div>
            <div class="layui-card-body">
                <ul class="pear-card-status">
                    <li th:each="sysFile:${cpu.sysFiles}">
                        <p th:text="${sysFile.typeName}"></p>
                        磁盘大小&nbsp;<span th:text="${sysFile.total}"></span>&nbsp;&nbsp;
                        空闲大小&nbsp;<span th:text="${sysFile.free}"></span>&nbsp;&nbsp;
                        <br/>
                        <br/>
                        已经使用&nbsp;<span th:text="${sysFile.used}"></span>&nbsp;&nbsp;
                        使用概率&nbsp;<span th:text="${sysFile.usage + '%'}"></span>
                        <br/>
                    </li>
                </ul>
            </div>
        </div>
        <div class="layui-card">
            <div class="layui-card-header">主机信息</div>
            <div class="layui-card-body">
                <table class="layui-table" lay-skin="line">
                    <thead>
                    <tr>
                        <th>属性</th>
                        <th>值</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>名称</td>
                        <td th:text="${cpu.sysInfoComputerName}"></td>
                    </tr>
                    <tr>
                        <td>地址</td>
                        <td th:text="${cpu.sysInfoComputerIp}"></td>
                    </tr>
                    <tr>
                        <td>系统</td>
                        <td th:text="${cpu.sysInfoOsName}"></td>
                    </tr>
                    <tr>
                        <td>模型</td>
                        <td th:text="${cpu.sysInfoOsArch}"></td>
                    </tr>
                    <tr>
                        <td>JDK</td>
                        <td th:text="${'JDK' + cpu.jvmInfoVersion}"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['layer', 'echarts'], function () {
        var $ = layui.jquery,
            echarts = layui.echarts;

        var echartsRecords = echarts.init(document.getElementById('echarts-records'), 'walden');

        $("body").on("click", "[data-url]", function () {
            parent.layui.tab.addTabOnlyByElem("content", {
                id: $(this).attr("data-id"),
                title: $(this).attr("data-title"),
                url: $(this).attr("data-url"),
                close: true
            })
        })


        let bgColor = "#fff";
        let color = [
            "#0090FF",
            "#36CE9E",
            "#FFC005",
            "#FF515A",
            "#8B5CFF",
            "#00CA69"
        ];
        let echartData = [{
            name: "1",
            value1: 100,
            value2: 233
        },
            {
                name: "2",
                value1: 138,
                value2: 233
            },
            {
                name: "3",
                value1: 350,
                value2: 200
            },
            {
                name: "4",
                value1: 173,
                value2: 180
            },
            {
                name: "5",
                value1: 180,
                value2: 199
            },
            {
                name: "6",
                value1: 150,
                value2: 233
            },
            {
                name: "7",
                value1: 180,
                value2: 210
            },
            {
                name: "8",
                value1: 230,
                value2: 180
            }
        ];

        let xAxisData = echartData.map(v => v.name);
        //  ["1", "2", "3", "4", "5", "6", "7", "8"]
        let yAxisData1 = echartData.map(v => v.value1);
        // [100, 138, 350, 173, 180, 150, 180, 230]
        let yAxisData2 = echartData.map(v => v.value2);
        // [233, 233, 200, 180, 199, 233, 210, 180]
        const hexToRgba = (hex, opacity) => {
            let rgbaColor = "";
            let reg = /^#[\da-f]{6}$/i;
            if (reg.test(hex)) {
                rgbaColor =
                    `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
                        "0x" + hex.slice(3, 5)
                    )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
            }
            return rgbaColor;
        }

        option = {
            backgroundColor: bgColor,
            color: color,
            legend: {
                right: 10,
                top: 10
            },
            tooltip: {
                trigger: "axis",
                formatter: function (params) {
                    let html = '';
                    params.forEach(v => {
                        console.log(v)
                        html +=
                            `<div style="color: #666;font-size: 14px;line-height: 24px">
					                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color[v.componentIndex]};"></span>
					                ${v.seriesName}.${v.name}
					                <span style="color:${color[v.componentIndex]};font-weight:700;font-size: 18px">${v.value}</span>
					                人`;
                    })


                    return html
                },
                extraCssText: 'background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;',
                axisPointer: {
                    type: 'shadow',
                    shadowStyle: {
                        color: '#ffffff',
                        shadowColor: 'rgba(225,225,225,1)',
                        shadowBlur: 5
                    }
                }
            },
            grid: {
                top: 100,
                containLabel: true
            },
            xAxis: [{
                type: "category",
                boundaryGap: false,
                axisLabel: {
                    formatter: '{value}月',
                    textStyle: {
                        color: "#333"
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: "#D9D9D9"
                    }
                },
                data: xAxisData
            }],
            yAxis: [{
                type: "value",
                name: '单位：人',
                axisLabel: {
                    textStyle: {
                        color: "#666"
                    }
                },
                nameTextStyle: {
                    color: "#666",
                    fontSize: 12,
                    lineHeight: 40
                },
                splitLine: {
                    lineStyle: {
                        type: "dashed",
                        color: "#E9E9E9"
                    }
                },
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                }
            }],
            series: [{
                name: "2018",
                type: "line",
                smooth: true,
                symbolSize: 8,
                zlevel: 3,
                lineStyle: {
                    normal: {
                        color: color[0],
                        shadowBlur: 3,
                        shadowColor: hexToRgba(color[0], 0.5),
                        shadowOffsetY: 8
                    }
                },
                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(
                            0,
                            0,
                            0,
                            1,
                            [{
                                offset: 0,
                                color: hexToRgba(color[0], 0.3)
                            },
                                {
                                    offset: 1,
                                    color: hexToRgba(color[0], 0.1)
                                }
                            ],
                            false
                        ),
                        shadowColor: hexToRgba(color[0], 0.1),
                        shadowBlur: 10
                    }
                },
                data: yAxisData1
            }, {
                name: "2019",
                type: "line",
                smooth: true,
                symbolSize: 8,
                zlevel: 3,
                lineStyle: {
                    normal: {
                        color: color[1],
                        shadowBlur: 3,
                        shadowColor: hexToRgba(color[1], 0.5),
                        shadowOffsetY: 8
                    }
                },
                areaStyle: {
                    normal: {
                        color: new echarts.graphic.LinearGradient(
                            0,
                            0,
                            0,
                            1,
                            [{
                                offset: 0,
                                color: hexToRgba(color[1], 0.3)
                            },
                                {
                                    offset: 1,
                                    color: hexToRgba(color[1], 0.1)
                                }
                            ],
                            false
                        ),
                        shadowColor: hexToRgba(color[1], 0.1),
                        shadowBlur: 10
                    }
                },
                data: yAxisData2
            }]
        };

        echartsRecords.setOption(option);

        window.onresize = function () {
            echartsRecords.resize();
        }

    });
</script>
</body>
</html>