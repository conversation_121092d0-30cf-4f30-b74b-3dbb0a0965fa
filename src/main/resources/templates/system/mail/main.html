<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('邮件列表')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">发件人</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="createBy" type="text">
                </div>
                <label class="layui-form-label">收件人</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="receiver" type="text">
                </div>
                <label class="layui-form-label">主题</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="subject" type="text">
                </div>
                <button class="pear-btn pear-btn-md pear-btn-primary" lay-filter="mail-query" lay-submit>
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button class="pear-btn pear-btn-md" type="reset">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>

<div class="layui-card">
    <div class="layui-card-body">
        <table id="mail-table" lay-filter="mail-table"></table>
    </div>
</div>

</body>

<script id="mail-toolbar" type="text/html">
    <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        发送
    </button>
    <button class="pear-btn pear-btn-md" lay-event="batchRemove">
        <i class="layui-icon layui-icon-delete"></i>
        删除
    </button>
</script>

<script id="mail-bar" type="text/html">
    <button class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove">
        <i class="layui-icon layui-icon-delete"></i>
    </button>
</script>

<script id="mail-createTime" type="text/html">
    {{layui.util.toDateString(d.createTime,  'yyyy-MM-dd HH:mm:ss')}}
</script>

<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;

        let MODULE_PATH = "/system/mail/";

        let cols = [
            [
                {type: 'checkbox'},
                {title: '主题', field: 'subject'},
                {title: '正文', field: 'content'},
                {title: '收件人', field: 'receiver', align: 'center'},
                {title: '发件人', field: 'createBy', align: 'center'},
                {title: '创建时间', field: 'createTime', align: 'center', templet: "#file-createTime"},
                {title: '操作', toolbar: '#mail-bar', align: 'center', width: 100}
            ]
        ]

        table.render({
            elem: '#mail-table',
            url: MODULE_PATH + 'data',
            page: true,
            cols: cols,
            skin: 'line',
            height: 'full-148',
            toolbar: '#mail-toolbar',
            defaultToolbar: [{
                title: '刷新',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh'
            }, 'filter', 'print', 'exports']
        });

        table.on('tool(mail-table)', function (obj) {
            if (obj.event === 'remove') {
                window.remove(obj);
            }
        });

        table.on('toolbar(mail-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                window.refresh();
            } else if (obj.event === 'batchRemove') {
                window.batchRemove(obj);
            }
        });

        form.on('submit(mail-query)', function (data) {
            table.reload('mail-table', {where: data.field})
            return false;
        });

        window.add = function () {
            layer.open({
                type: 2,
                title: '发邮件',
                shade: 0.1,
                area: ['600px', '600px'],
                content: MODULE_PATH + 'add'
            });
        }

        window.remove = function (obj) {
            layer.confirm('确定要删除该文件', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "remove/" + obj.data['mailId'],
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            popup.success(result.msg, function () {
                                obj.del();
                            });
                        } else {
                            popup.failure(result.msg);
                        }
                    }
                })
            });
        }

        window.batchRemove = function (obj) {
            let ids = common.checkField(obj, 'mailId');
            if (common.isEmpty(ids)) {
                popup.warning("未选中数据");
                return false;
            }
            layer.confirm('确定要删除选中文件', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "batchRemove/" + ids,
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            popup.success(result.msg, function () {
                                table.reload('mail-table');
                            });
                        } else {
                            popup.failure(result.msg);
                        }
                    }
                })
            });
        }

        window.refresh = function () {
            table.reload('mail-table');
        }
    })
</script>
</html>