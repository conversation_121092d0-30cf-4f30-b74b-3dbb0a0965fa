<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('发送邮件')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">
                        目标
                    </label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="receiver" placeholder="多个收件人用(英文分号;)隔开"
                               type="text">
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">
                        主题
                    </label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" name="subject" lay-verify="required" placeholder="请输入邮件主题，可不填写"
                               type="text">
                    </div>
                </div>

                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">正文</label>
                    <div class="layui-input-block">
                        <textarea class="layui-textarea" name="content" placeholder="请输入邮件正文"
                                  style="height: 350px"></textarea>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="mail-save" lay-submit=""
                    type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                发送
            </button>
            <button class="pear-btn pear-btn-sm" type="reset">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery'], function () {
        let form = layui.form;
        let $ = layui.jquery;

        form.on('submit(mail-save)', function (data) {
            $.ajax({
                url: '/system/mail/save',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'post',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                            parent.layui.table.reload("mail-table");
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });
    })
</script>
<script>
</script>
</body>
</html>