<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>标准搜索</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../component/pear/css/pear.css" />
    <link rel="stylesheet" href="../../admin/css/other/console1.css" />
    <!-- 主 题 更 换 -->
    <style id="pearadmin-bg-color"></style>
    <style>
        a.pdf-title
        {
            color:rgb(36, 64, 179);
        }
        #summary {
            font-size:15px;
            color:#999;
            height: 30px;
            text-align: center;
            line-height: 30px;
            margin-top:10px;
        }
    </style>
</head>
<body class="pear-container">
<div>
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body" style="min-height: 1000px;">
                    <div>
                        <div style="text-align: center">
                            <div><label for="txtKey">
                                <span style="font-size:16px;font-weight: bold;margin-top:20px;color:#666;">全文检索：</span><input type="text" id="txtKey" placeholder="请输入关键字"  class="layui-input" style="width:500px;display:inline-block;border-width: 1px;border-color:#555;"></label><span style="width:15px;"> </span><button type="button" class="pear-btn pear-btn-primary" id="btnSearch">搜索</button>
                            </div>
                        </div>
                        <div id="summary"></div>
                        <ul id="search_ul">

                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!--</div>-->
<script src="../../component/layui/layui.js"></script>
<script src="../../component/pear/pear.js"></script>
<script>

    layui.use(['layer', 'echarts', 'element', 'count'], function() {
        var $ = layui.jquery,
            layer = layui.layer,
            element = layui.element,
            count = layui.count,
            echarts = layui.echarts;
        let MODULE_PATH = "/standard/";
        //绑定事件
        $(document).on('click', '#btnSearch', function(data) {
            var key=$('#txtKey').val();
            if(!key) {
                layer.alert("请输入关键字", {
                    title: "提示"
                });
                return;
            }
            $.ajax({
                url:"/chinastd/solrsearch?key="+key,
                data:{},
                type:"get",
                dataType:"json",
                headers : {'Content-Type' : 'application/json;charset=utf-8'}, //接口json格式
                success:function(data){
                    console.log(data);
                    $("#search_ul").empty();
                    $("#summary").text("共搜索到"+data.result.datalist.length+"个标准");
                    for(var i=0;i<data.result.datalist.length;i++) {
                        var line = data.result.datalist[i];
                        var highlight=[];
                        var matchCount=0;
                        if(data.result.highlight)
                        {
                            for(var key in data.result.highlight)
                            {
                                if(key==line.id)
                                {
                                    var itemLight=data.result.highlight[key];
                                    console.log(itemLight);
                                    if(itemLight)
                                    {
                                        console.log(itemLight.primaryCoverage);
                                        if(itemLight.primaryCoverage) {
                                            highlight.push(...itemLight.primaryCoverage);
                                        }
                                        if(itemLight.content) {
                                            highlight.push(...itemLight.content);
                                        }
                                        console.log(highlight);
                                    }
                                    break;
                                }
                            }
                            if(highlight.length==0)
                            {
                                highlight.push(line.primaryCoverage);
                            }
                            //匹配数量求和
                            for(var j=0;j<highlight.length;j++)
                            {
                                var count=highlight[j].split('<em>').length-1;
                                if(count>0)
                                {
                                    matchCount+=count;
                                }
                            }
                            if(matchCount==0)
                            {
                                matchCount=1;
                            }
                        }
                        $("#search_ul").append('<li class="std-title"><span><a class="pdf-title" title="点击查看详情" target="_self" onclick="window.edit(\''+line.stdNo+'\',\''+line.stdChineseName+'\')" href="javascript:void(0);">' + line.stdChineseName + '</a></span><span style="color:gray;font-size:13px;padding-left:10px;">[<a target="_blank"  title="下载"  style="color:#bbb;" href="/source/downloadStandard?fileName=' + encodeURIComponent(line.pdfFileName) + '">下载</a>]</span></li>')
                        $("#search_ul").append('<li class="std-summary"><span class="item-title">[标准号]</span><span class="std-no">' + line.stdNo + '</span><span class="item-title match-count" style="width:auto !important;">匹配次数:<span style="color:red;padding-left:3px !important;">' + matchCount + '</span>次</span></li>')
                        $("#search_ul").append('<li class="std-summary"><span class="item-title">[起草单位]</span><span class="std-ellipsis">' + line.draftingUnit + '</span></li>')
                        $("#search_ul").append('<li class="std-summary"><span class="item-title">[起草人]</span><span class="std-ellipsis">' + line.drafter + '</span></li>')
                        $("#search_ul").append('<li class="std-primary">' + (highlight.length>0 ? highlight[0] : '') +(highlight.length>1 ? '<a style="margin-left:10px;color:red;" href="javascript:void(0);" onclick="window.showMore(\''+line.id+'\')">【点击查看更多匹配结果】</a>' : '')+'</li>');
                        if(highlight.length>1) {
                            var html=[];
                            for (var j = 1; j < highlight.length; j++) {
                                html.push('<div class="std-more">' + highlight[j] + '</div>');
                            }
                            $("#search_ul").append('<li class="std-primary std-content-more" id="light_' + line.id + '">'+html.join("")+'</li>');
                        }
                        $("#search_ul").append('<li class="std-split"></li>')
                    }
                    $(".std-content-more").slideUp();
                },
                error:function(data){
                    layer.alert(JSON.stringify(data), {
                        title: data
                    });
                }
            });
        });
        $("#txtKey").keydown(function (e) {//当按下按键时
            if (e.which == 13) {//.which属性判断按下的是哪个键，回车键的键位序号为13
                var key=$('#txtKey').val();
                if(!key) {
                    layer.alert("请输入关键字", {
                        title: "提示"
                    });
                    return;
                }
                $.ajax({
                    url:"/chinastd/solrsearch?key="+key,
                    data:{},
                    type:"get",
                    dataType:"json",
                    headers : {'Content-Type' : 'application/json;charset=utf-8'}, //接口json格式
                    success:function(data){
                        console.log(data);
                        $("#search_ul").empty();
                        $("#summary").text("共搜索到"+data.result.datalist.length+"个标准");
                        for(var i=0;i<data.result.datalist.length;i++) {
                            var line = data.result.datalist[i];
                            var highlight=[];
                            var matchCount=0;
                            if(data.result.highlight)
                            {
                                for(var key in data.result.highlight)
                                {
                                    if(key==line.id)
                                    {
                                        var itemLight=data.result.highlight[key];
                                        if(itemLight)
                                        {
                                            if(itemLight.primaryCoverage) {
                                                highlight.push(...itemLight.primaryCoverage);
                                            }
                                            if(itemLight.content) {
                                                highlight.push(...itemLight.content);
                                            }
                                            console.log(highlight);
                                        }
                                        break;
                                    }
                                }
                                //匹配数量求和
                                for(var j=0;j<highlight.length;j++)
                                {
                                    var count=highlight[j].split('<em>').length-1;
                                    if(count>0)
                                    {
                                        matchCount+=count;
                                    }
                                }
                                if(highlight.length==0)
                                {
                                    highlight.push(line.primaryCoverage);
                                }
                                if(matchCount==0)
                                {
                                    matchCount=1;
                                }
                            }
                            $("#search_ul").append('<li class="std-title"><span><a class="pdf-title" title="点击查看详情" target="_self" onclick="window.edit(\''+line.stdNo+'\',\''+line.stdChineseName+'\')" href="javascript:void(0);">' + line.stdChineseName + '</a></span><span style="color:gray;font-size:13px;padding-left:10px;">[<a target="_blank"  title="下载"  style="color:#bbb;" href="/source/downloadStandard?fileName=' + encodeURIComponent(line.pdfFileName) + '">下载</a>]</span></li>')
                            $("#search_ul").append('<li class="std-summary"><span class="item-title">[标准号]</span><span class="std-no">' + line.stdNo + '</span><span class="item-title match-count" style="width:auto !important;">匹配次数:<span style="color:red;padding-left:3px !important;">' + matchCount + '</span>次</span></li>')
                            $("#search_ul").append('<li class="std-summary"><span class="item-title">[起草单位]</span><span class="std-ellipsis">' + line.draftingUnit + '</span></li>')
                            $("#search_ul").append('<li class="std-summary"><span class="item-title">[起草人]</span><span class="std-ellipsis">' + line.drafter + '</span></li>')
                            $("#search_ul").append('<li class="std-primary">' + (highlight.length>0 ? highlight[0] : '') +(highlight.length>1 ? '<a style="margin-left:10px;color:red;" href="javascript:void(0);" onclick="window.showMore(\''+line.id+'\')">【点击查看更多匹配结果】</a>' : '')+'</li>');
                            if(highlight.length>1) {
                                var html=[];
                                for (var j = 1; j < highlight.length; j++) {
                                    html.push('<div class="std-more">' + highlight[j] + '</div>');
                                }
                                $("#search_ul").append('<li class="std-primary std-content-more" id="light_' + line.id + '">'+html.join("")+'</li>');
                            }
                            $("#search_ul").append('<li class="std-split"></li>')
                        }
                        $(".std-content-more").slideUp();
                    },
                    error:function(data){
                        layer.alert(JSON.stringify(data), {
                            title: data
                        });
                    }
                });
            }
        });
        window.showMore = function (id) {
            $('#light_'+id).slideToggle();
        }
        window.edit = function (stdNo,name) {
            layer.open({
                type: 2,
                title: '【'+name+'】',
                shade: 0.1,
                area: ['1000px', '500px'],
                content: MODULE_PATH + 'detailbystdno?stdNo=' + stdNo
            });
        }
    });

</script>
</body>
<style>
    .std-content-more {
        display: block;
    }
    em {
        color:red;
    }
    .pdf-title{
        font-family: 宋体;
        font-weight: bold;
        font-size:17px;
    }

    .item-title{
        color:#999 !important;
        font-size:13px;
        padding:0 0 0 30px;
        font-style: normal;
        display: block;
        float:left;
        width:80px;
        height: 30px;
        line-height: 30px;
    }
    .std-summary span{
        color:#333;
        font-size:14px;
    }
    .std-summary span:first-child{
        padding-left:0px !important;
    }
    .std-summary{
        font-size:17px;padding:5px 10px;color:#999;
        background-color: #fff;
        font-style: normal;
    }
    .std-summary:after{
        content: '';
        clear: both;
        display: block;
    }
    .std-primary{
        padding:10px 10px 10px 10px;
        background-color: #fff;
    }
    .std-more
    {
        padding:10px 10px 10px 0;
        background-color: #fff;
    }
    .std-title{
        background-color: #fff;
        padding:15px 0 0 10px;
        margin-top:10px;
    }
    .std-split{
        height: 20px;
        background-color:#f9f9f9;
    }
    .std-summary:hover,.std-primary:hover{
        background-color:#f9f9f9;
    }
    .std-no{
        font-weight: bold;
        color:#333 !important;
        font-style: normal !important;
        line-height: 30px;
        height: 30px;
    }
    .match-count{
        display: inline-block;
        float:right;
    }
    .std-ellipsis{
        width:89%;
        float:left;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 30px;
        height: 30px;
    }
</style>
</html>
<!--
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>标准搜索</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../component/pear/css/pear.css" />
    <link rel="stylesheet" href="../../admin/css/other/console1.css" />
    &lt;!&ndash; 主 题 更 换 &ndash;&gt;
    <style id="pearadmin-bg-color"></style>
    <style>
        a.pdf-title
        {
            color:rgb(36, 64, 179);
        }
        #summary {
            font-size:15px;
            color:#999;
            height: 30px;
            text-align: center;
            line-height: 30px;
            margin-top:10px;
        }
    </style>
</head>
<body class="pear-container">
<div>
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-body" style="min-height: 1000px;">
                    <div>
                        <div style="text-align: center">
                            <div><label for="txtKey">
                                <span style="font-size:16px;font-weight: bold;margin-top:20px;color:#666;">标准搜索：</span><input type="text" id="txtKey" placeholder="请输入关键字"  class="layui-input" style="width:500px;display:inline-block;border-width: 1px;border-color:#555;"></label><span style="width:15px;"> </span><button type="button" class="pear-btn pear-btn-primary" id="btnSearch">搜索</button>
                            </div>
                        </div>
                        <div id="summary"></div>
                        <ul id="search_ul">

                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
&lt;!&ndash;</div>&ndash;&gt;
<script src="../../component/layui/layui.js"></script>
<script src="../../component/pear/pear.js"></script>
<script>

    layui.use(['layer', 'echarts', 'element', 'count'], function() {
        var $ = layui.jquery,
            layer = layui.layer,
            element = layui.element,
            count = layui.count,
            echarts = layui.echarts;
        //绑定事件
        $(document).on('click', '#btnSearch', function(data) {
            var key=$('#txtKey').val();
            if(!key) {
                layer.alert("请输入关键字", {
                    title: "提示"
                });
                return;
            }
            $.ajax({
                url:"/standard/solrsearch?key="+key,
                data:{},
                type:"get",
                dataType:"json",
                headers : {'Content-Type' : 'application/json;charset=utf-8'}, //接口json格式
                success:function(data){
                    console.log(data);
                    $("#search_ul").empty();
                    $("#summary").text("共搜索到"+data.result.datalist.length+"个标准");
                    for(var i=0;i<data.result.datalist.length;i++) {
                        var line = data.result.datalist[i];
                        var highlight=[];
                        var matchCount=0;
                        if(data.result.highlight)
                        {
                            for(var key in data.result.highlight)
                            {
                                if(key==line.id)
                                {
                                    var itemLight=data.result.highlight[key];
                                    if(itemLight)
                                    {
                                        highlight=itemLight.content;
                                        console.log(highlight);
                                    }
                                    break;
                                }
                            }
                        }
                        //匹配数量求和
                        for(var j=0;j<highlight.length;j++)
                        {
                            var count=highlight[j].split('<em>').length-1;
                            if(count>0)
                            {
                                matchCount+=count;
                            }
                        }
                        $("#search_ul").append('<li class="std-title"><span><a class="pdf-title" title="点击下载" target="_blank" href="http://localhost:8080/source/download?fileName=' + encodeURIComponent(line.pdfFileName) + '">' + line.stdChineseName + '</a></span><span style="color:gray;font-size:13px;padding-left:10px;">[<a target="_blank" style="color:#bbb;" href="http://localhost:8080/source/download?fileName=' + encodeURIComponent(line.pdfFileName) + '">下载</a>]</span></li>')
                        $("#search_ul").append('<li class="std-summary"><span class="item-title">[标准号]</span><span>' + line.stdNo + '</span><span class="item-title">[密级]</span><span>' + line.securityClass + '</span><span class="item-title">[起草人]</span><span>' + line.drafter + ' ' + line.draftingUnit + '</span><span class="item-title">[发布时间]</span><span>' + line.pubDate + '</span><span class="item-title">[匹配次数]</span><span style="color:red;">' + matchCount + '</span></li>')
                        $("#search_ul").append('<li class="std-primary">' + (highlight.length>0 ? highlight[0] : '') +(highlight.length>1 ? '<a style="margin-left:10px;" href="javascript:void(0);" onclick="window.showMore(\''+line.id+'\')">【查看更多结果】</a>' : '')+'</li>');
                        if(highlight.length>1) {
                            var html=[];
                            for (var j = 1; j < highlight.length; j++) {
                                html.push('<div class="std-primary">' + highlight[j] + '</div>');
                            }
                            $("#search_ul").append('<li class="std-primary std-content-more" id="light_' + line.id + '">'+html.join("")+'</li>');
                        }
                        $("#search_ul").append('<li class="std-split"></li>')
                    }
                    $(".std-content-more").slideUp();
                },
                error:function(data){
                    layer.alert(JSON.stringify(data), {
                        title: data
                    });
                }
            });
        });
        $("#txtKey").keydown(function (e) {//当按下按键时
            if (e.which == 13) {//.which属性判断按下的是哪个键，回车键的键位序号为13
                var key=$('#txtKey').val();
                if(!key) {
                    layer.alert("请输入关键字", {
                        title: "提示"
                    });
                    return;
                }
                $.ajax({
                    url:"/standard/solrsearch?key="+key,
                    data:{},
                    type:"get",
                    dataType:"json",
                    headers : {'Content-Type' : 'application/json;charset=utf-8'}, //接口json格式
                    success:function(data){
                        console.log(data);
                        $("#search_ul").empty();
                        $("#summary").text("共搜索到"+data.result.datalist.length+"个标准");
                        for(var i=0;i<data.result.datalist.length;i++) {
                            var line = data.result.datalist[i];
                            var highlight=[];
                            var matchCount=0;
                            if(data.result.highlight)
                            {
                                for(var key in data.result.highlight)
                                {
                                    if(key==line.id)
                                    {
                                        var itemLight=data.result.highlight[key];
                                        if(itemLight)
                                        {
                                            highlight=itemLight.content;
                                            console.log(highlight);
                                        }
                                        break;
                                    }
                                }
                            }
                            //匹配数量求和
                            for(var j=0;j<highlight.length;j++)
                            {
                                var count=highlight[j].split('<em>').length-1;
                                if(count>0)
                                {
                                    matchCount+=count;
                                }
                            }
                            $("#search_ul").append('<li class="std-title"><span><a class="pdf-title" title="点击下载" target="_blank" href="http://localhost:8080/source/download?fileName=' + encodeURIComponent(line.pdfFileName) + '">' + line.stdChineseName + '</a></span><span style="color:gray;font-size:13px;padding-left:10px;">[<a target="_blank" style="color:#bbb;" href="http://localhost:8080/source/download?fileName=' + encodeURIComponent(line.pdfFileName) + '">下载</a>]</span></li>')
                            $("#search_ul").append('<li class="std-summary"><span class="item-title">[标准号]</span><span>' + line.stdNo + '</span><span class="item-title">[密级]</span><span>' + line.securityClass + '</span><span class="item-title">[起草人]</span><span>' + line.drafter + ' ' + line.draftingUnit + '</span><span class="item-title">[发布时间]</span><span>' + line.pubDate + '</span><span class="item-title">[匹配次数]</span><span style="color:red;">' + matchCount + '</span></li>')
                            $("#search_ul").append('<li class="std-primary">' + (highlight.length>0 ? highlight[0] : '') +(highlight.length>1 ? '<a style="margin-left:10px;" href="javascript:void(0);" onclick="window.showMore(\''+line.id+'\')">【查看更多结果】</a>' : '')+'</li>');
                            if(highlight.length>1) {
                                var html=[];
                                for (var j = 1; j < highlight.length; j++) {
                                    html.push('<div class="std-primary">' + highlight[j] + '</div>');
                                }
                                $("#search_ul").append('<li class="std-primary std-content-more" id="light_' + line.id + '">'+html.join("")+'</li>');
                            }
                            $("#search_ul").append('<li class="std-split"></li>')
                        }
                        $(".std-content-more").slideUp();
                    },
                    error:function(data){
                        layer.alert(JSON.stringify(data), {
                            title: data
                        });
                    }
                });
            }
        });
        window.showMore = function (id) {
            $('#light_'+id).slideToggle();

        }
    });

</script>
</body>
<style>
    .std-content-more {
        display: block;
    }
    em {
        color:red;
    }
    .pdf-title{
        font-family: 宋体;
        font-weight: bold;
        font-size:17px;
    }
    .item-title{
        color:#eee;
        font-size:13px;
        padding:0 15px 0 30px;
        font-style: normal;
    }
    .std-summary span{
        color:#999;
        font-size:14px;
    }
    .std-summary span:first-child{
        padding-left:0px !important;
    }
    .std-summary{
        font-size:17px;padding:5px 10px;color:#999;
        background-color: #fff;
        font-style: italic;
    }
    .std-primary{
        padding:10px;
        background-color: #fff;
    }
    .std-title{
        background-color: #fff;
        padding:15px 0 0 10px;
        margin-top:10px;
    }
    .std-split{
        height: 20px;
        background-color:#f9f9f9;
    }
    .std-summary:hover,.std-primary:hover{
        background-color:#f9f9f9;
    }
</style>
</html>
-->
