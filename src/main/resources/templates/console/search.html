<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>标准搜索</title>
		<meta name="renderer" content="webkit">
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<link rel="stylesheet" href="../../component/pear/css/pear.css" />
		<link rel="stylesheet" href="../../admin/css/other/console1.css" />
		<!-- 主 题 更 换 -->
		<style id="pearadmin-bg-color"></style>
		<style>
			a.pdf-title
			{
				color:rgb(36, 64, 179);
			}
			#summary {
				font-size:15px;
				color:#999;
				height: 30px;
				text-align: center;
				line-height: 30px;
				margin-top:10px;
			}
		</style>
	</head>
	<body class="pear-container">
		<div>
			<div class="layui-row layui-col-space10">
				<div class="layui-col-md12">
					<div class="layui-card">
						<div class="layui-card-body" style="min-height: 1000px;">
							<div>
								<div style="text-align: center">
									<div><label for="txtKey">
									<span style="font-size:16px;font-weight: bold;margin-top:20px;color:#666;">标准搜索：</span><input type="text" id="txtKey" placeholder="请输入关键字"  class="layui-input" style="width:500px;display:inline-block;border-width: 1px;border-color:#555;"></label><span style="width:15px;"> </span><button type="button" class="pear-btn pear-btn-primary" id="btnSearch">搜索</button>
									</div>
								</div>
								   <div id="summary"></div>
								   <ul id="search_ul">

								   </ul>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--</div>-->
		<script src="../../component/layui/layui.js"></script>
		<script src="../../component/pear/pear.js"></script>
		<script>

			layui.use(['layer', 'echarts', 'element', 'count'], function() {
				var $ = layui.jquery,
					layer = layui.layer,
					element = layui.element,
					count = layui.count,
					echarts = layui.echarts;
				//绑定事件
				$(document).on('click', '#btnSearch', function(data) {
					var key=$('#txtKey').val();
					if(!key) {
						layer.alert("请输入关键字", {
							title: "提示"
						});
						return;
					}
					$.ajax({
						url:"/standard/search?key="+key,
						data:{},
						type:"get",
						dataType:"json",
						headers : {'Content-Type' : 'application/json;charset=utf-8'}, //接口json格式
						success:function(data){
							console.log(data);
							$("#search_ul").empty();
							$("#summary").text("共搜索到"+data.result.length+"个标准");
							for(var i=0;i<data.result.length;i++)
							{
							/*	$("#search_ul").append('<li style="font-size:20px;padding:30px 0 0 10px"><span><a class="pdf-title" title="点击下载" target="_blank" href="/source/download?fileName='+ encodeURIComponent(data.result[i].title)+'">'+data.result[i].title.replace('.pdf','')+'</a></span><span style="color:gray;font-size:13px;padding-left:10px;">[<a target="_blank" style="color:#bbb;" href="/source/download?fileName='+ encodeURIComponent(data.result[i].title)+'">下载</a>]</span></li>')
								$("#search_ul").append('<li style="font-size:17px;padding:10px">'+data.result[i].content+'</li>')*/
								var line=data.result[i];
								$("#search_ul").append('<li class="std-title"><span><a class="pdf-title" title="下载" target="_blank" href="/source/download?fileName='+encodeURIComponent(line.pdfFileName)+'">'+line.stdChineseName+'</a></span><span style="color:gray;font-size:13px;padding-left:10px;">[<a target="_blank" style="color:#bbb;" href="/source/download?fileName='+ encodeURIComponent(line.pdfFileName)+'">下载</a>]</span></li>')
								$("#search_ul").append('<li class="std-summary"><span class="item-title">[标准号]</span><span>'+line.stdNo+'</span><span class="item-title">[密级]</span><span>'+line.securityClass+'</span><span class="item-title">[起草人]</span><span>'+line.drafter+' '+line.draftingUnit+'</span><span class="item-title">[发布时间]</span><span>'+line.pubDate+'</span><span class="item-title">[匹配次数]</span><span style="color:red;">'+line.matchTimes+'</span></li>')
								$("#search_ul").append('<li class="std-primary">'+(line.primaryCoverage?line.primaryCoverage:'')+'</li>')
								$("#search_ul").append('<li class="std-split"></li>')
							}
						},
						error:function(data){
							layer.alert(JSON.stringify(data), {
								title: data
							});
						}
					});
				});
				$("#txtKey").keydown(function (e) {//当按下按键时
					if (e.which == 13) {//.which属性判断按下的是哪个键，回车键的键位序号为13
						var key=$('#txtKey').val();
						if(!key) {
							layer.alert("请输入关键字", {
								title: "提示"
							});
							return;
						}
						$.ajax({
							url:"/standard/search?key="+key,
							data:{},
							type:"get",
							dataType:"json",
							headers : {'Content-Type' : 'application/json;charset=utf-8'}, //接口json格式
							success:function(data){
								console.log(data);
								$("#search_ul").empty();
								$("#summary").text("共搜索到"+data.result.length+"个标准");
								for(var i=0;i<data.result.length;i++) {
									var line = data.result[i];
									$("#search_ul").append('<li class="std-title"><span><a class="pdf-title" title="下载" target="_blank" href="/source/download?fileName=' + encodeURIComponent(line.pdfFileName) + '">' + line.stdChineseName + '</a></span><span style="color:gray;font-size:13px;padding-left:10px;">[<a target="_blank" style="color:#bbb;" href="/source/download?fileName=' + encodeURIComponent(line.pdfFileName) + '">下载</a>]</span></li>')
									$("#search_ul").append('<li class="std-summary"><span class="item-title">[标准号]</span><span>' + line.stdNo + '</span><span class="item-title">[密级]</span><span>' + line.securityClass + '</span><span class="item-title">[起草人]</span><span>' + line.drafter + ' ' + line.draftingUnit + '</span><span class="item-title">[发布时间]</span><span>' + line.pubDate + '</span><span class="item-title">[匹配次数]</span><span style="color:red;">' + line.matchTimes + '</span></li>')
									$("#search_ul").append('<li class="std-primary">' + (line.primaryCoverage ? line.primaryCoverage : '') + '</li>')
									$("#search_ul").append('<li class="std-split"></li>')
								}
							},
							error:function(data){
								layer.alert(JSON.stringify(data), {
									title: data
								});
							}
						});
					}
				});
			});

		</script>
	</body>
	<style>
		.pdf-title{
			font-family: 宋体;
			font-weight: bold;
			font-size:17px;
		}
		.item-title{
			color:#eee;
			font-size:13px;
			padding:0 15px 0 30px;
			font-style: normal;
		}
		.std-summary span{
			color:#999;
			font-size:14px;
		}
		.std-summary span:first-child{
			padding-left:0px !important;
		}
		.std-summary{
			font-size:17px;padding:5px 10px;color:#999;
			background-color: #fff;
			font-style: italic;
		}
		.std-primary{
			padding:10px;
			background-color: #fff;
		}
		.std-title{
			background-color: #fff;
			padding:15px 0 0 10px;
			margin-top:10px;
		}
		.std-split{
			height: 20px;
			background-color:#f9f9f9;
		}
		.std-summary:hover,.std-primary:hover{
			background-color:#f9f9f9;
		}
	</style>
</html>
