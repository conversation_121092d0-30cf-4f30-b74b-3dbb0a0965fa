<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('体系标准管理')"/>
</head>
<body class="pear-container" style="padding:0 20px 20px 20px;">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="c-tool">
                <div class="class-container">
                    <div class="layui-form-item company-select" style="margin-top: 10px;">
                        <label class="layui-form-label" style="width:100px;overflow: hidden">体系位置：</label>
                        <div class="layui-input-block">
                            <div class="layui-unselect layui-form-select downpanel">
                                <div class="layui-select-title layui-select-tree">
                                    <span class="layui-input layui-unselect" id="tree">---请选择体系位置---</span>
                                    <i class="layui-edge"></i>
                                </div>
                                <dl class="layui-anim layui-anim-upbit">
                                    <dd>
                                        <ul>
                                            <div class="main-container" id="classContainer">

                                            </div>
                                        </ul>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="search-container">
                    <div class="simple-container">
                        <div class="item" style="width:100%;">
                            <label class="sub-item search-label">标准号/标准名称(多关键词用空格隔开)：</label>
                            <div class="search-cmp">
                                <input class="layui-input sub-item" id="searchKey" name="stdNo" placeholder="" type="text">
                                <button id="btnSimpleSearch" class="search-button">
                                    <i class="layui-icon layui-icon-search"></i>
                                </button>
                            </div>
                            <div class="search-anchor-1"><a href="javascript:void(0);"  id="clearSimpleCon" style="display: none;" class="search-advance">清空</a><a href="javascript:void(0);" id="advanceAnchor" class="search-advance">高级检索&gt;&gt;</a></div>
                        </div>
                    </div>
                    <div class="advance-container" style="display: none;margin-top:10px;">
                        <div class="advance-item" id="advanceItem1" style="width:100%;">
                            <div class="ph-div" style="text-align: right;">高级检索：</div>
                            <div class="search-cmp">
                                <div class="con-field-container">
                                    <select id="conField1" class="con-field">
                                        <option value="std_no">标准号</option>
                                        <option value="std_org_name">标准名称（原文）</option>
                                        <option value="std_chinese_name">标准名称（中文）</option>
                                        <option value="std_english_name">标准名称（英文）</option>
                                        <option value="catetory_no">分类号</option>
                                        <option value="std_ics">ICS号</option>
                                        <option value="pub_date">发布日期</option>
                                        <option value="implementation_date">实施日期</option>
                                        <option value="advance_dept">提出单位</option>
                                        <option value="drafting_unit">起草单位</option>
                                        <option value="drafter">起草人</option>
                                        <option value="std_status">标准状态</option>
                                    </select>
                                </div>
                                <input class="layui-input sub-item con-val" id="conVal1" placeholder="" type="text">
                                <div class="con-type-container">
                                    <select id="conQueryType1" class="con-query-type">
                                        <option value="fuzzy">模糊</option>
                                        <option value="exact">精确</option>
                                    </select>
                                </div>
                            </div>
                            <div class="ph-end-div">
                                <a href="javascript:void(0);" id="simpleAnchor" class="search-advance">普通检索&gt;&gt;</a>
                            </div>
                        </div>
                        <div class="advance-item" id="advanceItem2" style="width:100%;">
                            <div class="ph-div">
                                <select id="conAndOr2" class="conAndOr">
                                    <option value="AND">AND</option>
                                    <option value="OR">OR</option>
                                    <option value="NOT">NOT</option>
                                </select>
                            </div>
                            <div class="search-cmp">
                                <div class="con-field-container">
                                    <select id="conField2">
                                        <option value="std_no">标准号</option>
                                        <option value="std_org_name">标准名称（原文）</option>
                                        <option value="std_chinese_name">标准名称（中文）</option>
                                        <option value="std_english_name">标准名称（英文）</option>
                                        <option value="catetory_no">分类号</option>
                                        <option value="std_ics">ICS号</option>
                                        <option value="pub_date">发布日期</option>
                                        <option value="implementation_date">实施日期</option>
                                        <option value="advance_dept">提出单位</option>
                                        <option value="drafting_unit">起草单位</option>
                                        <option value="drafter">起草人</option>
                                        <option value="std_status">标准状态</option>
                                    </select>
                                </div>
                                <input class="layui-input sub-item" id="conVal2" placeholder="" type="text">
                                <div class="con-type-container">
                                    <select id="conQueryType2">
                                        <option value="fuzzy">模糊</option>
                                        <option value="exact">精确</option>
                                    </select>
                                </div>
                            </div>
                            <div class="ph-end-div">
                                <a href="javascript:void(0);" onclick="delCondition(2)" class="conDel" id="conDel2" style="display: none;">-</a>
                                <a href="javascript:void(0);" onclick="addCondition()" class="conAdd" id="conAdd2">+</a>
                            </div>
                        </div>
                    </div>
                    <div class="item" id="advTool" style="text-align: center;display: none;">
                        <button class="pear-btn pear-btn-md sub-item" type="reset" style="margin-left:30px;margin-top:0px;width:80px;padding-left:5px;">
                            <i class="layui-icon layui-icon-refresh"></i>
                            重 置
                        </button>
                        <button class="pear-btn pear-btn-md pear-btn-primary sub-item" lay-filter="role-query" lay-submit
                                style="margin-left:40px;margin-top:0px;width:80px;padding-left:5px;">
                            <i class="layui-icon layui-icon-search"></i>
                            检 索
                        </button>
                    </div>

                </div>
            </div>
            <div id="resultInfo"></div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="role-table" lay-filter="role-table"></table>
    </div>
</div>
</body>

<script id="role-toolbar" type="text/html">
    <div class="layui-col-xs6">
        <button
                class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
            <i class="layui-icon layui-icon-add-1"></i>
            添加库中标准
        </button>
        <button class="pear-btn pear-btn-primary pear-btn-md custom-blue" lay-event="addStandard">
            <i class="layui-icon layui-icon-add-1"></i>
            新增标准(仅信息)
        </button>
        <button  class="pear-btn pear-btn-danger pear-btn-md" style="margin-left:15px;" lay-event="batchRemove">
            <i class="layui-icon layui-icon-delete"></i>
            删除
        </button>
    </div>
    <!--<button  class="pear-btn pear-btn-primary pear-btn-md" style="margin-left:15px;" lay-event="downloadTemplate">
        <i class="layui-icon layui-icon-upload"></i>
        下载导入模板
    </button>
    <div id="uploadElem" style="display: inline-block;"></div>-->

</script>

<!--<a class="c-anchor" href="javascript:void(0);" lay-event="detail">详情</a>-->
<script id="role-bar" type="text/html">
    <a class="c-anchor layui-icon {{d.isReal=='1'?'custom-stdinfo-hidden':'custom-stdinfo-empty'}}" href="javascript:void(0);"  title="标准在库中不存在">空</a>
    <a class="c-anchor  iconfont icon-bianji1 {{d.isReal=='1'?'custom-stdinfo-hidden':''}}" href="javascript:void(0);" lay-event="editStdInfoNull" title="编辑"></a>
    <a class="c-anchor iconfont icon-chakan {{d.isReal=='1'?'':'custom-stdinfo-hidden'}}" href="javascript:void(0);" lay-event="onlineRead" title="在线查看"></a>
    <a href="javascript:void(0);" lay-event="download" class="c-anchor iconfont icon-xiazaidaoru {{d.isReal=='1'?'':'custom-stdinfo-hidden'}}" title="下载"></a>
    <a class="c-anchor layui-icon layui-icon-{{d.isCollected=='1'?'rate-solid':'rate'}} {{d.isReal=='1'?'':'custom-stdinfo-hidden'}}" href="javascript:void(0);" title="{{d.isCollected=='1'?'取消收藏':'收藏'}}" lay-event="collect"></a>
    <a class="c-anchor layui-icon layui-icon-survey {{d.isReal=='1'?'':'custom-stdinfo-hidden'}}" href="javascript:void(0);" lay-event="opinion" title="意见反馈"></a>
    <a class="c-anchor layui-icon layui-icon-delete" href="javascript:void(0);" lay-event="remove" title="删除"></a>
</script>
<!--
<button
        class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>
</button>
<button
        class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>
</button>-->
<input type="hidden" id="hdSystemId"  th:value="${system.id}" />
<input type="hidden" id="hdSystemName" th:value="${system.name}" />
<input type="hidden" id="hdClassId" name="hdClassId" />
<input type="hidden" id="hdClassName" name="hdClassName" />
<input type="hidden" id="hdClassPath" name="hdClassPath" />
<input type="hidden" id="hdPostName" name="hdPostName"  />
<th:block th:include="include :: footer"/>

<!-- 添加标准的模态框 -->
<div class="layui-form" id="addStandardModal" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="addStandardForm">
        <div class="layui-form-item">
            <label class="layui-form-label">体系位置<span style="padding-left:2px;color:red;">*</span></label>
            <div class="layui-input-block">
                <div class="layui-unselect layui-form-select downpanel">
                    <div class="layui-select-title layui-select-tree">
                        <span class="layui-input layui-unselect" id="modalTree">---请选择体系位置---</span>
                        <i class="layui-edge"></i>
                    </div>
                    <dl class="layui-anim layui-anim-upbit">
                        <dd>
                            <ul>
                                <div class="main-container" id="modalClassContainer">
                                </div>
                            </ul>
                        </dd>
                    </dl>
                </div>
                <div class="layui-form-mid layui-word-aux">选择要添加标准的体系位置</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">标准号<span style="padding-left:2px;color:red;">*</span></label>
            <div class="layui-input-block">
                <div class="layui-input-group">
                    <input type="text" name="stdNo" required lay-verify="required" placeholder="请输入标准号" autocomplete="off" class="layui-input" id="stdNoInput">
                </div>
                <div class="layui-form-mid layui-word-aux">请输入标准号</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">标准名称<span style="padding-left:2px;color:red;">*</span></label>
            <div class="layui-input-block">
                <input type="text" name="stdName" id="stdNameInput" required lay-verify="required" placeholder="请输入标准名称" autocomplete="off" class="layui-input">
                <div class="layui-form-mid layui-word-aux">请输入标准的完整名称</div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button class="layui-btn" lay-submit lay-filter="addStandardSubmit">立即提交</button>
                <button type="reset" class="layui-btn layui-btn-primary">重置</button>
            </div>
        </div>
    </form>
</div>
<!-- 隐藏字段用于存储选中的节点信息 -->
<input type="hidden" id="modalClassId" name="modalClassId" />
<input type="hidden" id="modalClassName" name="modalClassName" />
<input type="hidden" id="modalClassPath" name="modalClassPath" />
<input type="hidden" id="hdStdInfoNullId" name="hdStdInfoNullId" />

<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common','upload','layer','element','tree'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;
        let tree = layui.tree;
        let MODULE_PATH = "/classjoinstd/";
        let conditionArr=[1,2];
        let MODULE_PATH_OF_CLASS = '/stdclassification/';
        var treeHandler=null;
        var treeData={};
        let systemId=$("#hdSystemId").val();
        let searchKeyMap=[];
        window.loadtree = function () {
            $.ajax({
                type: "get",
                url: MODULE_PATH_OF_CLASS + "treeofsystem?systemId=" + $("#hdSystemId").val(),
                dataType: 'json',
                success: function (data) {
                    console.log(data);
                    treeData=data;
                    initModalTree();
                    //$("#tree").text($("#hdPostName").val());
                    //渲染
                    if(treeHandler)
                    {
                        tree.reload("theTree",{data:treeData});
                    }
                    else {
                        treeHandler = tree.render({
                            id: 'theTree',
                            elem: '#classContainer',  //绑定元素
                            edit: [], //操作节点的图标
                            customOperate: true,
                            accordion: false,
                            onlyIconControl: true,
                            limitNodeAddLevel: 6, // 设置第X级节点不允许添加操作
                            limitNodeDelLevel: 1, // 设置第X级节点不允许删除操作
                            click: function (obj) {
                                var type = obj.type; //得到操作类型：add、edit、del
                                var data = obj.data; //得到当前节点的数据
                                var elem = obj.elem; //得到当前节点元素
                                //Ajax 操作
                                var id = data.id; //得到节点索引
                                //所有体系下标准
                                if(id==$("#hdSystemId").val())
                                {
                                    $("#hdClassId").val(-1);
                                    $("#hdClassName").val(data.title);
                                    $("#hdClassPath").val("");
                                }
                                else
                                {
                                    //当前分类下体系下标准
                                    $("#hdClassId").val(id);
                                    $("#hdClassName").val(data.title);
                                    $("#hdClassPath").val(data.classPath);
                                }
                                var othis = $($(this)[0].elem).parents(".layui-form-select");
                                othis.removeClass("layui-form-selected").find(".layui-select-title span").html(data.title).end().find("input:hidden[name='hdClassId']").val(id);
                                window.smartSearch(false);
                            },
                            operate: function (obj) {
                            },
                            data: treeData
                        });
                    }
                    console.log("treeHandler");
                    console.log(treeHandler);
                }
            });
        }
        window.loadtree();
        let cols = [
            [
                {type: 'checkbox',width:'5%'},
                {field: 'number', title: '序号', align: 'center', type: 'numbers',width:'5%'},
                {title: '体系位置', field: 'classCodePath', align: 'left', width: '8%'},
                {title: '节点名称', field: 'className', align: 'left', width: '15%'},
                {title: '标准号', align: 'left', width: '11%',templet:function(rowData){
                        return window.highlight("stdNo",rowData,searchKeyMap)+(rowData["isReal"]=="1"?"":"<span style='color:red'>[无文本]</span>");
                    }},
                {title: '标准名称',  align: 'left', width: '31%',templet:function(rowData){
                        return window.highlight("stdOrgName",rowData,searchKeyMap);
                    }},
                {title: '标准状态', field: 'stdStatus', align: 'center', width: '8%'},
                {title: '操作', toolbar: '#role-bar', align: 'center',width: '13%'}
            ]
        ]
        // 原来获取数据的路径： url: MODULE_PATH + 'dataformanage',
        table.render({
            elem: '#role-table',
            url:   '/chinastd/dataofsystem',
            where:{
                "systemId":$("#hdSystemId").val()
            },
            page: true,
            limit:50,
            limits:[10,15,20,30,50],
            cols: cols,
            skin: 'row,line',
            toolbar: '#role-toolbar',
            cellMinWidth: 'auto',
            defaultToolbar: [{
                title: '刷新',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print', 'exports'],
            done: function (res, curr, count) {
                $("#resultInfo").html('共检索到'+res.count+"条记录");
                var $data = $('div[lay-id="role-table"]').find('.layui-table-body').find('tr').eq(0).find('td');
                var $head = $('div[lay-id="role-table"]').find('.layui-table-header').find('tr').eq(0).find('th');
                for (var i = 0; i < $data.length; i++) {
                    var l1 = $data.eq(i).find('div').width();
                    var l2 = $head.eq(i).find('div').width();
                    if (l1 > l2) {
                        $head.eq(i).find('div').width(l1);
                    } else if(l2>l1) {
                        $data.eq(i).find('div').width(l2);
                    }
                }
            }
        });
        /*  $(".downpanel").click(function(){
              $(".layui-form-select dl").toggle();
          });*/
        window.registeClassChangeEvent=function() {
            $(".downpanel").on("click", ".layui-select-title", function (e) {
                $(".layui-form-select").not($(this).parents(".layui-form-select")).removeClass("layui-form-selected");
                $(this).parents(".downpanel").toggleClass("layui-form-selected");
                layui.stope(e);
            }).on("click", "dl i", function (e) {
                layui.stope(e);
            });
            $(document).on("click", function (e) {
                $(".main-container").parents(".layui-form-select").removeClass("layui-form-selected");
            });
            //$(".downpanel").find(".layui-select-title span").html($("#hdClassName").val());
        }
        window.registeClassChangeEvent();

        table.on('tool(role-table)', function (obj) {
            if (obj.event === 'editStdInfoNull') {
                window.editStdInfoNull(obj);
            }
            else if (obj.event === 'remove') {
                window.remove(obj);
            }
            else if (obj.event === 'download') {
                window.download(obj);
            }
            else if(obj.event==='onlineRead')
            {
                window.onlineRead(obj,'Standard_Online_Read',$("#hdSystemId").val(),$("#hdSystemName").val());
            }
            else if(obj.event==='collect')
            {
                window.collect(obj,'StdSystem_Standard_Collect',$("#hdSystemId").val(),$("#hdSystemName").val(),function(){
                    window.smartSearch(false);
                });
            }
            else if(obj.event==='opinion')
            {
                window.opinion(obj);
            }
        });

        table.on('toolbar(role-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'addStandard') {
                // 打开模态框
                layer.open({
                    type: 1,
                    title: '新增标准(仅信息)',
                    area: ['500px', '450px'],
                    content: $('#addStandardModal'),
                    success: function(layero, index) {
                        // 重置表单
                        var $form = $("#addStandardForm");

                            // 重置表单字段
                           // $form[0].reset();
                            // 重置隐藏字段
                            $("#modalClassId").val("");
                            $("#modalClassName").val("");
                            $("#modalClassPath").val("");
                            $("#modalTree").text("---请选择体系位置---");

                            // 重新渲染表单
                          //  form.render(null, 'addStandardForm');
                            // 绑定表单提交事件
                            form.on('submit(addStandardSubmit)', function(data) {
                                var formData = data.field;
                                formData.systemId = systemId;
                                formData.classId = $("#modalClassId").val();

                                // 检查是否选择了体系位置
                                if (!formData.classId) {
                                    layer.msg("请选择体系位置", {icon: 2});
                                    return false;
                                }

                                // 验证标准号格式
                                if(!formData.stdNo) {
                                    layer.msg("请输入标准号", {icon: 2});
                                    return false;
                                }

                                // 显示加载中
                                var loadingIndex = layer.load(1, {
                                    shade: [0.1,'#fff']
                                });

                                $.ajax({
                                    url: MODULE_PATH + 'addSingleStandardToSystem',
                                    type: 'POST',
                                    contentType: 'application/json',
                                    data: JSON.stringify(formData),
                                    success: function(res) {
                                        layer.close(loadingIndex);
                                        if(res.success) {
                                            layer.closeAll();
                                            popup.success("添加成功");
                                            table.reload('role-table');
                                        } else {
                                            popup.failure(res.msg || "添加失败");
                                        }
                                    },
                                    error: function() {
                                        layer.close(loadingIndex);
                                        popup.failure("添加失败，请稍后重试");
                                    }
                                });
                                return false;
                            });

                    }
                });
            } else if (obj.event === 'refresh') {
                window.refresh();
            } else if (obj.event === 'batchRemove') {
                window.batchRemove(obj);
            }
            else if(obj.event=='downloadTemplate')
            {
                window.downloadTemplate();
            }
            else if(obj.event=='batchUpdateDoc')
            {
                window.batchUpdateDocFiles();
            }
        });
        form.on('submit(role-query)', function (data) {
            window.createKeywords(conditionArr,function(data){searchKeyMap=data;});
            return window.advanceSearch();
        });
        $("#advanceAnchor").click(function () {
            $(".simple-container").toggle();
            $(".advance-container").toggle();
            $("#advTool").toggle();
        });
        $("#simpleAnchor").click(function () {
            $(".simple-container").toggle();
            $(".advance-container").toggle();
            $("#advTool").toggle();
        });
        $("#btnSimpleSearch").click(function(e)
        {
            var clickSearchBtn = true;
            return window.simpleSearch(clickSearchBtn);
        });
        $("#clearSimpleCon").click(function(){
            $('#searchKey').val("");
            var classPathVal=$("#hdClassPath").val();
            table.reload('role-table', {where: {systemId:$("#hdSystemId").val(),classCodePath:classPathVal,simpleSearchKey:""},page: { curr: 1 }});
            $("#clearSimpleCon").hide();
            window.createKeywords(conditionArr,function(data){searchKeyMap=data;});
        });
        window.addCondition=function(){
            var id=0;
            for(var i=0;i<conditionArr.length;i++)
            {
                if(conditionArr[i]>id)
                {
                    id=conditionArr[i];
                }
            }
            if(id>0)
            {
                id+=1;
                conditionArr[conditionArr.length]=id;
            }
            var html=`<div class="advance-item" id="advanceItem${id}" style="width:100%;">
                    <div class="ph-div">
                        <select id="conAndOr${id}" class="conAndOr">
                            <option value="AND">AND</option>
                            <option value="OR">OR</option>
                            <option value="NOT">NOT</option>
                        </select>
                    </div>
                    <div class="search-cmp">
                        <div class="con-field-container">
                            <select id="conField${id}">
                                <option value="std_no">标准号</option>
                                  <option value="std_org_name">标准名称（原文）</option>
                                <option value="std_chinese_name">标准名称（中文）</option>
                                <option value="std_english_name">标准名称（英文）</option>
                                <option value="catetory_no">分类号</option>
                                <option value="std_ics">ICS号</option>
                                <option value="pub_date">发布日期</option>
                                <option value="implementation_date">实施日期</option>
                                <option value="advance_dept">提出单位</option>
                                <option value="drafting_unit">起草单位</option>
                                <option value="drafter">起草人</option>
                                <option value="std_status">标准状态</option>
                            </select>
                        </div>
                        <input class="layui-input sub-item" id="conVal${id}" placeholder="" type="text">
                        <div class="con-type-container">
                            <select id="conQueryType${id}">
                                <option value="fuzzy">模糊</option>
                                <option value="exact">精确</option>
                            </select>
                        </div>
                    </div>
                    <div class="ph-end-div">
                        <a href="javascript:void(0);" class="conDel" onclick="delCondition(${id})" id="conDel${id}">-</a>
                        <a href="javascript:void(0);" onclick="addCondition()" class="conAdd" id="conAdd${id}">+</a>
                    </div>
                </div>`;
            $(".advance-container").append(html);
            form.render("select");
            $("#conAdd"+(id-1)).toggle();
            for(var i=1;i<conditionArr.length-1;i++)
            {
                $("#conDel"+conditionArr[i]).show();
            }
        };

        window.delCondition=function(id){
            $("#advanceItem"+id).remove();
            for(var i=0;i<conditionArr.length;i++)
            {
                if(conditionArr[i]==id)
                {
                    if(i==conditionArr.length-1)
                    {
                        $("#conAdd"+conditionArr[i-1]).toggle();
                    }
                    conditionArr.splice(i, 1);
                    break;
                }
            }
            console.log('conditionArr.length:'+conditionArr.length);
            //需要保留至少两行
            if(conditionArr.length==2)
            {
                $("#conDel"+conditionArr[1]).hide();
            }
        };
        window.opinion=function(obj)
        {
            parent.layer.open({
                type: 2,
                title: "【"+obj.data["stdOrgName"]+"】意见反馈列表",
                shade: 0.1,
                area: ['1200px', '630px'],
                content: '/stdopinion/main?stdId='+obj.data["id"]
            });
        }
        // 回车检索
        $(document).on('keydown', 'input', function(e) {
            if (e.key === 'Enter') {
                const isSimpleVisible = $('.simple-container').is(':visible');
                const isAdvanceVisible = $('.advance-container').is(':visible');
                console.log("isSimpleSearch:"+isSimpleVisible);
                console.log("isAdvanceVisible:"+isAdvanceVisible);
                if (isSimpleVisible && !isAdvanceVisible) {
                    // 执行普通检索
                    window.simpleSearch(true);
                } else if (isAdvanceVisible) {
                    // 执行高级检索
                    //console.log("高级检索");
                    // 阻止表单默认提交行为，避免触发普通检索
                    e.preventDefault();
                    window.advanceSearch();
                }
            }
        });

        window.smartSearch=function(isClickSearchBtn)
        {
            if($(".simple-container")[0].style.display!='none')
            {
                //简单搜索
                window.simpleSearch(isClickSearchBtn);
            }
            else
            {
                //高级检索
                window.advanceSearch();
            }
            return false;
        }
        window.advanceSearch=function()
        {
            var fields=[];
            for(var i=0;i<conditionArr.length;i++)
            {
                var obj={};
                obj.logic="AND";
                if(conditionArr[i]>1)
                {
                    obj.logic= $("#conAndOr"+conditionArr[i]).val();
                }
                obj.field=$("#conField"+conditionArr[i]).val();
                obj.val=$("#conVal"+conditionArr[i]).val();
                obj.type=$("#conQueryType"+conditionArr[i]).val();
                fields.push(obj);
            }
            var classPathVal=$("#hdClassPath").val();
            table.reload('role-table', {where: {systemId:$("#hdSystemId").val(),classCodePath:classPathVal,advSearchKey:JSON.stringify(fields)},page: { curr: 1 }});
            window.setUpload();
            window.createKeywords(conditionArr,function(data){searchKeyMap=data;});
            // window.loadtree();
            // window.registeClassChangeEvent();
            return false;
        }
        window.simpleSearch=function(isClickSearchBtn)
        {
            var key = $('#searchKey').val();
            if(isClickSearchBtn) {
                // if (!key) {
                //     layer.alert("请输入关键字", {
                //         title: "提示"
                //     });
                //     return false;
                // }
            }
            //选中的分类
            var classPathVal=$("#hdClassPath").val();
            table.reload('role-table', {where: {systemId:$("#hdSystemId").val(),classCodePath:classPathVal, simpleSearchKey:key},page: { curr: 1 }});
            // $('#searchKey').val(key);
            $("#clearSimpleCon").show();
            window.setUpload();
            // window.loadtree();
            // window.registeClassChangeEvent();
            window.createKeywords(conditionArr,function(data){searchKeyMap=data;});
            return false;
            //table.reload('role-table');
        }
        window.add = function () {
            parent.layer.open({
                type: 2,
                title: '['+$("#hdSystemName").val()+']体系-添加库中标准',
                shade: 0.1,
                maxmin: true,
                area: ['90%', '80%'],
                content: MODULE_PATH + 'add?sysId='+$("#hdSystemId").val(),
                cancel: function(){
                    window.smartSearch(false);
                }
            });
        }
        window.batchRemove = function (obj) {
            let ids = common.checkField(obj, 'id');
            if (common.isEmpty(ids)) {
                popup.warning("请选择要删除的记录！");
                return false;
            }
            layer.confirm('确定要删除选中记录吗', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "batchRemove",
                    dataType: 'json',
                    contentType:'application/json',
                    type: 'post',
                    data:JSON.stringify({ids:ids,classId:$("#hdClassId").val(),systemId:$("#hdSystemId").val()}),
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            layer.msg("删除成功!", {icon: 1, time: 2000});
                            table.reload('role-table');
                        } else {
                            layer.msg("删除失败:" + result.message, {icon: 2, time: 2000});
                        }
                    }
                });
            });
        }
        window.setUpload=function(){
            $('#uploadElem').html(`<div id="uploadBox"><button type="button" class="pear-btn pear-btn-primary pear-btn-md" id="uploadBtn"><i class="layui-icon">&#xe67c;</i>导入</button><span id="selected"></span></div>`);
            upload.render({
                elem: '#uploadBtn'     // 选择文件按钮
                ,elemList: $('#demoList') //列表元素对象
                ,url:  MODULE_PATH+ 'file-upload' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
                ,accept: 'file'  //指定允许上传时校验的文件类型，可选值有：images（图片）、file（所有文件）、video（视频）、audio（音频）
                ,multiple: false  //是否允许多文件上传。设置 true即可开启。不支持ie8/9
                ,number: 1   //设置同时可上传的文件数量，一般配合 multiple 参数出现; 0 不限制
                ,auto: true  //是否选完文件后自动上传。如果设定 false，那么需要设置 bindAction 参数来指向一个其它按钮提交上传
                ,bindAction: '#testListAction'  //指向一个按钮触发上传，一般配合 auto: false 来使用
                ,choose: function(obj){   //选择文件后的回调函数。返回一个object参数
                }
                ,done: function(res, index, upload){ //成功的回调
                    var that = this;
                    if(res.success){ //上传成功
                        layer.msg("导入完成！", {icon: 1, time: 1500}, function () {
                            table.reload('role-table');
                            window.setUpload();
                        });
                        //delete this.files[index]; //删除文件队列已经上传成功的文件
                        return;
                    }else {
                        layer.msg("导入失败！"+res.message, {icon: 2, time: 10000}, function () {
                            table.reload('role-table');
                            window.setUpload();
                        });
                    }

                }
                ,allDone: function(obj){ //多文件上传完毕后的状态回调
                    console.log(obj)
                    window.setUpload();
                }
                ,error: function(index, upload){ //错误回调
                    popup.failure("导入失败！");
                    window.setUpload();
                }
                ,progress: function(n, elem, e, index){ //注意：index 参数为 layui 2.6.6 新增
                    element.progress('progress-demo-'+ index, n + '%'); //执行进度条。n 即为返回的进度百分比
                }
            });
        }

        window.setUpload();

        //删除记录 并删除指定索引
        window.deleteRecordAndIndex = function (obj) {
            let ids = common.checkField(obj, 'id');
            if (common.isEmpty(ids)) {
                popup.warning("请选择要删除的记录！");
                return false;
            }
            layer.confirm('确定要删除选中记录吗', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "deleteRecordAndIndex",
                    dataType: 'json',
                    type: 'post',
                    data: {data: ids},
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            layer.msg("删除成功!", {icon: 1, time: 2000});
                            table.reload('role-table');
                            window.setUpload();
                        } else {
                            layer.msg("删除失败:" + result.message, {icon: 2, time: 2000});
                        }
                    }
                });
            });
        }

        //删除记录 并删除指定索引
        window.deleteAll = function (obj) {
            layer.confirm('确定要删除选所有标准吗', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "deleteAll",
                    dataType: 'json',
                    type: 'post',
                    data: {},
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            layer.msg("删除成功!", {icon: 1, time: 2000});
                            table.reload('role-table');
                            window.setUpload();
                        } else {
                            layer.msg("删除失败:" + result.message, {icon: 2, time: 2000});
                        }
                    }
                });
            });
        }

        window.remove = function (obj) {
            var ids=obj.data['id'];
            layer.confirm('确定要删除选中记录吗', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "batchRemove",
                    dataType: 'json',
                    contentType:'application/json',
                    type: 'post',
                    data:JSON.stringify({ids:ids,classId:$("#hdClassId").val(),systemId:$("#hdSystemId").val()}),
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            layer.msg("删除成功!", {icon: 1, time: 2000});
                            table.reload('role-table');
                        } else {
                            layer.msg("删除失败:" + result.message, {icon: 2, time: 2000});
                        }
                    }
                });
            });
        }

        // 初始化模态框中的体系位置树
        function initModalTree() {
            // var $container = $('#modalClassContainer');
            // if (!$container.length) return;
            var treeHandler = tree.render({
                id: 'modalTree',
                elem: '#modalClassContainer',
                edit: [],
                customOperate: true,
                accordion: false,
                onlyIconControl: true,
                click: function (obj) {
                    var data = obj.data;
                    var id = data.id;
                    var title = data.title;

                    // 更新选中显示
                    $("#modalTree").text(title);
                    $(".layui-form-select").removeClass("layui-form-selected");

                    // 保存选中的节点信息
                    $("#modalClassId").val(id);
                    $("#modalClassName").val(title);
                    $("#modalClassPath").val(data.classPath);

                    // 添加选中样式
                    $(".layui-tree-txt").removeClass("layui-tree-selected");
                    $(obj.elem).find(".layui-tree-txt").addClass("layui-tree-selected");

                    // 自动关闭下拉框
                    setTimeout(function() {
                        $(".layui-form-select").removeClass("layui-form-selected");
                    }, 200);
                },
                data: treeData,
            });
        }
        window.editStdInfoNull=function(obj)
        {
            // 打开模态框
            layer.open({
                type: 1,
                title: '编辑标准',
                area: ['500px', '450px'],
                content: $('#addStandardModal'),
                success: function(layero, index) {
                    // 重置表单
                    var $form = $("#addStandardForm");
                    //从treeData中通过classCodePath找到对应的节点
                    var classId=null;
                    if(treeData.length>0)
                    {
                        var aimClass=window.findClass(treeData[0],obj.data.classCodePath);
                        if(aimClass)
                        {
                            classId=aimClass.id;
                        }
                    }
                    // 重置表单字段
                    $("#stdNoInput").val(obj.data.stdNo);
                    $("#stdNameInput").val(obj.data.stdOrgName);
                    $("#hdStdInfoNullId").val(obj.data.id);
                    // 重置隐藏字段
                    $("#modalClassId").val(classId);
                    $("#modalClassName").val(obj.data.className);
                    $("#modalClassPath").val(obj.data.classCodePath);
                    $("#modalTree").text(obj.data.className);

                    // 重新渲染表单
                    //  form.render(null, 'addStandardForm');
                    // 绑定表单提交事件
                    form.on('submit(addStandardSubmit)', function(data) {
                        var formData = data.field;
                        formData.systemId = systemId;
                        formData.classId = $("#modalClassId").val();
                        formData.id=obj.data.id;
                        formData.orgClassPath=obj.data.classCodePath;
                        // 检查是否选择了体系位置
                        if (!formData.classId) {
                            layer.msg("请选择体系位置", {icon: 2});
                            return false;
                        }

                        // 验证标准号格式
                        if(!formData.stdNo) {
                            layer.msg("请输入标准号", {icon: 2});
                            return false;
                        }

                        // 显示加载中
                        var loadingIndex = layer.load(1, {
                            shade: [0.1,'#fff']
                        });

                        $.ajax({
                            url: MODULE_PATH + 'editSingleStandardOfSystem',
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify(formData),
                            success: function(res) {
                                layer.close(loadingIndex);
                                if(res.success) {
                                    layer.closeAll();
                                    popup.success("编辑成功");
                                    table.reload('role-table');
                                } else {
                                    popup.failure(res.msg || "编辑失败");
                                }
                            },
                            error: function() {
                                layer.close(loadingIndex);
                                popup.failure("编辑失败，请稍后重试");
                            }
                        });
                        return false;
                    });

                }
            });

        }
        window.findClass=function(obj,classCodePath)
        {
            if(obj.children && obj.children.length>0)
            {
                for(var i=0;i<obj.children.length;i++)
                {
                    if(obj.children[i].classPath==classCodePath)
                    {
                        return obj.children[i];
                    }
                    else
                    {
                        var result=window.findClass(obj.children[i],classCodePath);
                        if(result)
                        {
                            return result;
                        }
                    }
                }

            }
        }
        window.refresh = function () {
            table.reload('role-table');
            window.setUpload();
        }
        //清空文件列表
        $('#clearList').click(function(){
            $('#demoList').html('');
        });

    });
</script>
<style>
    .c-tool
    {
        display: flex;
        flex-wrap: nowrap;
        align-items: flex-start;
    }
    .c-tool .class-container{
        flex-grow: 1;
        align-items: flex-start;
    }
    .c-tool  .search-container {
        flex-grow: 3;
    }
    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        flex-grow: 4;
    }


    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
    }

    .simple-container .item .sub-item button {
        margin-top: 10px;
    }

    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }

    .advance-container {
        width: 90%;
        margin: 0px auto;
    }

    .advance-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-bottom: 15px;
    }
    .layui-table-tool
    {
        height:70px !important;
        line-height:30px !important;
        padding:10px 0 0 10px !important;
        min-height:0px !important;
    }
    .layui-form-item .layui-form-label, .layui-form-item .layui-input-inline {
        margin-bottom: 7px;
    }
    .layui-form-item {
        padding-bottom: 0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    #resultInfo {
        width: 100%;
        text-align: center;
        color: #999;
    }
    .layui-table thead th {
        font-weight: bold;
    }
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    .search-label {
        flex-grow: 2;
    }
    .simple-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #ddd;
        border-radius: 5px;
    }
    .simple-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .simple-container .search-cmp button:hover {
        cursor: pointer;
    }
    .simple-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .simple-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .advance-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #efefef;
        border-radius: 5px;
    }
    .advance-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .advance-container .search-cmp button:hover {
        cursor: pointer;
    }
    .advance-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .advance-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 240px;
    }
    .con-type-container {
        width: 110px;
    }
    .search-anchor-1
    {
        flex-grow:5;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }
    .layui-input-block{
        margin-left:115px;
    }
    .layui-form-select .layui-input
    {
        line-height: 36px;
    }
    .custom-stdinfo-hidden{
        display:none;
    }
    .custom-stdinfo-empty{
        margin-right: 5px;
        color: #777;
        font-size: 15px !important;
        font-weight: bold;
        position: relative;
        top: -3px;
    }
</style>
</html>