<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增体系标准')"/>
</head>
<body>
<div class="layui-row whole-ui">
    <div class="layui-col-xs4">
        <form action="" class="layui-form">
            <div class="mainBox">
                <div class="main-container" id="classContainer">
                </div>
            </div>
        </form>
    </div>
    <div class="layui-col-xs8">
        <div class="layui-card" style="margin-bottom:0px;">
            <div class="layui-card-body">
                <div>当前选中分类：【<span id="dvChooseClassify">未选择</span>】</div>
                <form action="" class="layui-form">
                    <div class="simple-container">
                        <div class="item" style="width:100%;">
                            <label class="sub-item search-label">标准号/标准名称：</label>
                            <div class="search-cmp">
                                <input class="layui-input sub-item" id="searchKey" name="stdNo" placeholder=""
                                       type="text">
                                <button id="btnSimpleSearch" class="search-button">
                                    <i class="layui-icon layui-icon-search"></i>
                                </button>
                            </div>
                            <div class="search-anchor-1"><a href="javascript:void(0);" id="clearSimpleCon"
                                                            style="display: none;" class="search-advance">清空</a><a
                                    href="javascript:void(0);" id="advanceAnchor"
                                    class="search-advance">高级检索&gt;&gt;</a></div>
                        </div>
                    </div>
                    <div class="advance-container" style="display: none;">
                        <div class="advance-item" id="advanceItem1" style="width:100%;">
                            <div class="ph-div" style="text-align: right;">高级检索：</div>
                            <div class="search-cmp">
                                <div class="con-field-container">
                                    <select id="conField1" class="con-field">
                                        <option value="std_no">标准号</option>
                                        <option value="std_org_name">标准名称（原文）</option>
                                        <option value="std_chinese_name">标准名称（中文）</option>
                                        <option value="std_english_name">标准名称（英文）</option>
                                        <option value="std_class">标准类别</option>
                                        <option value="catetory_no">分类号</option>
                                        <option value="std_ics">ICS号</option>
                                        <option value="pub_date">发布日期</option>
                                        <option value="implementation_date">实施日期</option>
                                        <option value="advance_dept">提出单位</option>
                                        <option value="drafting_unit">起草单位</option>
                                        <option value="drafter">起草人</option>
                                        <option value="std_status">标准状态</option>
                                    </select>
                                </div>
                                <input class="layui-input sub-item con-val" id="conVal1" placeholder="" type="text">
                                <div class="con-type-container">
                                    <select id="conQueryType1" class="con-query-type">
                                        <option value="fuzzy">模糊</option>
                                        <option value="exact">精确</option>
                                    </select>
                                </div>
                            </div>
                            <div class="ph-end-div">
                                <a href="javascript:void(0);" id="simpleAnchor" class="search-advance">普通检索&gt;&gt;</a>
                            </div>
                        </div>
                        <div class="advance-item" id="advanceItem2" style="width:100%;">
                            <div class="ph-div">
                                <select id="conAndOr2" class="conAndOr">
                                    <option value="AND">AND</option>
                                    <option value="OR">OR</option>
                                    <option value="NOT">NOT</option>
                                </select>
                            </div>
                            <div class="search-cmp">
                                <div class="con-field-container">
                                    <select id="conField2">
                                        <option value="std_no">标准号</option>
                                        <option value="std_org_name">标准名称（原文）</option>
                                        <option value="std_chinese_name">标准名称（中文）</option>
                                        <option value="std_english_name">标准名称（英文）</option>
                                        <option value="std_class">标准类别</option>
                                        <option value="catetory_no">分类号</option>
                                        <option value="std_ics">ICS号</option>
                                        <option value="pub_date">发布日期</option>
                                        <option value="implementation_date">实施日期</option>
                                        <option value="advance_dept">提出单位</option>
                                        <option value="drafting_unit">起草单位</option>
                                        <option value="drafter">起草人</option>
                                        <option value="std_status">标准状态</option>
                                    </select>
                                </div>
                                <input class="layui-input sub-item" id="conVal2" placeholder="" type="text">
                                <div class="con-type-container">
                                    <select id="conQueryType2">
                                        <option value="fuzzy">模糊</option>
                                        <option value="exact">精确</option>
                                    </select>
                                </div>
                            </div>
                            <div class="ph-end-div">
                                <a href="javascript:void(0);" onclick="delCondition(2)" class="conDel" id="conDel2"
                                   style="display: none;">-</a>
                                <a href="javascript:void(0);" onclick="addCondition()" class="conAdd" id="conAdd2">+</a>
                            </div>
                        </div>
                    </div>
                    <div class="item" style="width:100%;justify-content: center;align-items: center;margin-bottom: 0.625rem;">
                        <input type="checkbox" name="chkClass" id="chkAll" value="0" title="全部" lay-skin="primary" lay-filter="i-all" />
                        <input type="checkbox" name="chkClass" id="chkAQ" value="AQ" title="AQ" lay-skin="primary" lay-filter="i-aq" />
                        <input type="checkbox" name="chkClass" id="chkCB" value="CB" title="CB" lay-skin="primary" lay-filter="i-cb" />
                        <input type="checkbox" name="chkClass" id="chkDB11" value="DB11" title="DB11" lay-skin="primary" lay-filter="i-db11" />
                        <input type="checkbox" name="chkClass" id="chkDB13" value="DB13" title="DB13" lay-skin="primary" lay-filter="i-db13" />
                        <input type="checkbox" name="chkClass" id="chkDB31" value="DB31" title="DB31" lay-skin="primary" lay-filter="i-db31" />
                        <input type="checkbox" name="chkClass" id="chkDB37" value="DB37" title="DB37" lay-skin="primary" lay-filter="i-db37" />
                        <input type="checkbox" name="chkClass" id="chkDB50" value="DB50" title="DB50" lay-skin="primary" lay-filter="i-db50" />
                        <input type="checkbox" name="chkClass" id="chkDB61" value="DB61" title="DB61" lay-skin="primary" lay-filter="i-db61" />
                        <input type="checkbox" name="chkClass" id="chkDL" value="DL" title="DL" lay-skin="primary" lay-filter="i-dl" />
                        <input type="checkbox" name="chkClass" id="chkGB" value="GB" title="GB" lay-skin="primary" lay-filter="i-gb" />
                        <input type="checkbox" checked="checked" name="chkClass" id="chkGJB" value="GJB" title="GJB" lay-skin="primary" lay-filter="i-gjb" />
                        <input type="checkbox" name="chkClass" id="chkHB" value="HB" title="HB" lay-skin="primary" lay-filter="i-hb" />
                        <input type="checkbox" name="chkClass" id="chkHG" value="HG" title="HG" lay-skin="primary" lay-filter="i-hg" />
                        <input type="checkbox" name="chkClass" id="chkHJ" value="HJ" title="HJ" lay-skin="primary" lay-filter="i-hj" />
                        <input type="checkbox" name="chkClass" id="chkJB" value="JB" title="JB" lay-skin="primary" lay-filter="i-jb" />
                        <input type="checkbox" name="chkClass" id="chkJJF" value="JJF" title="JJF" lay-skin="primary" lay-filter="i-jjf" />
                        <input type="checkbox" name="chkClass" id="chkJJG" value="JJG" title="JJG" lay-skin="primary" lay-filter="i-jjg" />
                        <input type="checkbox" name="chkClass" id="chkJR" value="JR" title="JR" lay-skin="primary" lay-filter="i-jr" />
                        <input type="checkbox" name="chkClass" id="chkKA" value="KA" title="KA" lay-skin="primary" lay-filter="i-ka" />
                        <input type="checkbox" name="chkClass" id="chkMT" value="MT" title="MT" lay-skin="primary" lay-filter="i-mt" />
                        <input type="checkbox" checked="checked" name="chkClass" id="chkQCNG" value="Q/CNG" title="Q/CNG" lay-skin="primary" lay-filter="i-qcng" />
                        <input type="checkbox" name="chkClass" id="chkQC" value="QC" title="QC" lay-skin="primary" lay-filter="i-qc" />
                        <input type="checkbox" name="chkClass" id="chkQJ" value="QJ" title="QJ" lay-skin="primary" lay-filter="i-qj" />
                        <input type="checkbox" name="chkClass" id="chkQX" value="QX" title="QX" lay-skin="primary" lay-filter="i-qx" />
                        <input type="checkbox" name="chkClass" id="chkSJ" value="SJ" title="SJ" lay-skin="primary" lay-filter="i-sj" />
                        <input type="checkbox" name="chkClass" id="chkSY" value="SY" title="SY" lay-skin="primary" lay-filter="i-sy" />
                        <input type="checkbox" name="chkClass" id="chkTB" value="TB" title="TB" lay-skin="primary" lay-filter="i-tb" />
                        <input type="checkbox" checked="checked" name="chkClass" id="chkWJ" value="WJ" title="WJ" lay-skin="primary" lay-filter="i-wj" />
                        <input type="checkbox" name="chkClass" id="chkWS" value="WS" title="WS" lay-skin="primary" lay-filter="i-ws" />
                        <input type="checkbox" name="chkClass" id="chkYD" value="YD" title="YD" lay-skin="primary" lay-filter="i-yd" />
                        <input type="checkbox" name="chkClass" id="chkYZ" value="YZ" title="YZ" lay-skin="primary" lay-filter="i-yz" />
                    </div>
                    <div class="item" id="advTool" style="text-align: center;display: none;">
                        <button class="pear-btn pear-btn-md sub-item" type="reset"
                                style="margin-left:30px;margin-top:0px;width:80px;padding-left:5px;">
                            <i class="layui-icon layui-icon-refresh"></i>
                            重 置
                        </button>
                        <button class="pear-btn pear-btn-md pear-btn-primary sub-item" lay-filter="role-query"
                                lay-submit
                                style="margin-left:40px;margin-top:0px;width:80px;padding-left:5px;">
                            <i class="layui-icon layui-icon-search"></i>
                            检 索
                        </button>
                    </div>
                    <div id="resultInfo"></div>
                </form>
            </div>
        </div>
        <div class="layui-card">
            <div class="layui-card-body">
                <table id="role-table" lay-filter="role-table"></table>
            </div>
        </div>
      <!--  <div class="bottom">
            <div class="button-container">
                <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-save" lay-submit=""
                        type="submit">
                    <i class="layui-icon layui-icon-ok"></i>
                    保存
                </button>
                <button class="pear-btn pear-btn-sm" type="reset">
                    <i class="layui-icon layui-icon-refresh"></i>
                    关闭
                </button>
            </div>
        </div>-->
    </div>
</div>
<th:block th:include="include :: footer"/>
<input name="hdSystemId" th:value="${system.id}" type="hidden"/>
<input name="hdSystemName" th:value="${system.name}" type="hidden"/>

<script id="role-toolbar" type="text/html">
    <button  class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        添加到分类
    </button>
   <!-- <button  class="pear-btn pear-btn-primary pear-btn-md" lay-event="del">
        <i class="layui-icon layui-icon-delete"></i>
        从分类中删除
    </button>-->
</script>
<script id="role-bar" type="text/html">
</script>
<script>
    layui.use(['tree', 'table', 'form', 'jquery', 'popup', 'common', 'upload', 'layer', 'element'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;
        let tree = layui.tree;
        let MODULE_PATH = "/chinastd/";
        let MODULE_PATH_OF_CLASS = '/stdclassification/';
        let MODULE_PATH_JOIN='/classjoinstd/';
        var treeHandler = null;
        var treeData = {};
        var choosenClassify={id:0,name:""};
        let searchKeyMap=[];
        let conditionArr = [1, 2];
        var hasQueryCondtion = false;
        window.loadtree = function () {
            $.ajax({
                type: "get",
                url: MODULE_PATH_OF_CLASS + "treeofsystem?systemId=" + $("input[name='hdSystemId']").val(),
                dataType: 'json',
                success: function (data) {
                    console.log(data);
                    treeData = data;
                    //渲染
                    if (treeHandler) {
                        tree.reload("theTree", {data: treeData});
                    } else {
                        treeHandler = tree.render({
                            id: 'theTree',
                            elem: '#classContainer',  //绑定元素
                            edit: [], //操作节点的图标
                            customOperate: true,
                            showCheckbox:false,
                            accordion: false,
                            onlyIconControl: true,
                            limitNodeAddLevel: 6, // 设置第X级节点不允许添加操作
                            limitNodeDelLevel: 1, // 设置第X级节点不允许删除操作
                            click: function (obj) {
                                   //体系下不能直接挂标准
                                   if(obj.data.id==$("input[name='hdSystemId']").val())
                                   {
                                       return;
                                   }
                                // if(obj.data.children.length==0)
                                // {
                                    var needReload=false;
                                    if(choosenClassify.id)
                                    {
                                        needReload=true;
                                    }
                                    choosenClassify={id:obj.data.id,name:obj.data.title};
                                    $("#dvChooseClassify").html(choosenClassify.name);
                                    tree.setChecked('theTree', obj.data.id);

                                    // 1. 自动进行一次简单检索，显示一页标准数据
                                    window.loadStandardsForClassify();
                                // }
                                // else
                                // {
                                //     layer.msg("无效分类，请选择一个叶子分类（没有子级的分类）！",{
                                //         icon: 2,
                                //         time: 1000
                                //     });
                                // }

                            },
                            operate: function (obj) {
                                var type = obj.type; //得到操作类型：add、edit、del
                                var data = obj.data; //得到当前节点的数据
                                var elem = obj.elem; //得到当前节点元素
                                //Ajax 操作
                                var id = data.id; //得到节点索引
                                console.log(obj);
                                window.id = id;
                                if (type === 'add') { //增加节点
                                    //给表单赋值
                                    form.val("classInfo", {
                                        "id": null,
                                        "pid": data.classType == -1 ? "-1" : id,
                                        "pClassType": data.classType,
                                        "pClassPath": data.classPath,
                                        "name": null
                                    });
                                    //https://blog.csdn.net/qq_43517653/article/details/93915762
                                    form.render(); //更新全部
                                    window.add("");
                                } else if (type === 'update') {
                                    //var title = elem.find('.layui-tree-txt').html();//得到修改后的内容
                                    //给表单赋值
                                    form.val("classInfo", {
                                        "id": data.id,
                                        "pid": "",
                                        "pClassType": "",
                                        "pClassPath": "",
                                        "name": data.title
                                    });
                                    form.render(); //更新全部
                                    window.update(data.id, elem);
                                } else if (type == 'del') {
                                    window.delete(id, data.title);
                                }
                            },
                            data: treeData
                        });
                    }
                    console.log("treeHandler");
                    console.log(treeHandler);
                }
            });
        }
        window.loadtree();


        // 获取选中的标准类别
        window.getClassCond = function() {
            var chkList = [];
            $("input[name='chkClass']:checked").each(function() {
                if($(this).val() != "0") { // 排除"全部"选项
                    chkList.push($(this).val());
                }
            });
            return chkList.join(",");
        };

        // 为选中的分类加载标准数据并勾选已有标准
        window.loadStandardsForClassify = function() {
            if(!choosenClassify.id) {
                return;
            }

            // 1. 先进行一次简单检索，显示一页标准数据
           window.smartSearch();

            // 2. 获取该分类下已有的标准，并在加载完成后勾选
            window.loadExistingStandards();
        };

        window.smartSearch=function(isClickSearchBtn)
        {
            const isSimpleVisible = $('.simple-container').is(':visible');
            const isAdvanceVisible = $('.advance-container').is(':visible');
            console.log("isSimpleSearch:"+isSimpleVisible);
            console.log("isAdvanceVisible:"+isAdvanceVisible);
            if (isSimpleVisible && !isAdvanceVisible) {
                // 执行普通检索
                window.simpleSearch(false);
            } else if (isAdvanceVisible) {
                // 执行高级检索
                //console.log("高级检索");
                // 阻止表单默认提交行为，避免触发普通检索
                window.advanceSearch();
            }
            return false;
        }

        // 获取分类下已有的标准并勾选
        window.loadExistingStandards = function() {
            if(!choosenClassify.id) {
                console.log("没有选中的分类");
                return;
            }

            console.log("开始获取分类下已有标准，分类ID：", choosenClassify.id);

            $.ajax({
                url: MODULE_PATH_JOIN + "getStandardsByClassId",
                dataType: 'json',
                type: 'get',
                data: { classId: choosenClassify.id },
                success: function (result) {
                    console.log("获取已有标准的响应：", result);

                    if (result.success && result.data) {
                        // 存储已有标准的ID列表
                        window.existingStandardIds = result.data.map(function(std) {
                            return std.id;
                        });

                        console.log("已有标准ID列表：", window.existingStandardIds);

                        // 延迟执行勾选操作，确保表格已经渲染完成
                        setTimeout(function() {
                            window.checkExistingStandards();
                        }, 800);
                    } else {
                        window.existingStandardIds = [];
                        console.log("没有获取到已有标准数据");
                    }
                },
                error: function(xhr, status, error) {
                    window.existingStandardIds = [];
                    console.log("获取分类下已有标准失败：", error);
                }
            });
        };

        // 勾选已有的标准
        window.checkExistingStandards = function() {
            if(!window.existingStandardIds || window.existingStandardIds.length === 0) {
                console.log("没有已有标准需要勾选");
                return;
            }

            console.log("开始勾选已有标准，ID列表：", window.existingStandardIds);

            // 获取表格数据
            var tableData = table.cache['role-table'] || [];
            console.log("表格数据：", tableData);

            // 遍历表格数据，找到需要勾选的行
            tableData.forEach(function(row, index) {
                if(window.existingStandardIds.indexOf(row.id) !== -1) {
                    console.log("找到需要勾选的标准：", row.stdNo, row.id);

                    // 方法1：直接操作DOM
                    var $tr = $('div[lay-id="role-table"] tbody tr').eq(index);
                    var $checkbox = $tr.find('input[type="checkbox"]').first();

                    if($checkbox.length > 0) {
                        $checkbox.prop('checked', true);

                        // 同时更新layui的样式
                        var $formCheckbox = $checkbox.next('.layui-form-checkbox');
                        if($formCheckbox.length > 0) {
                            $formCheckbox.addClass('layui-form-checked');
                        }

                        console.log("已勾选标准：", row.stdNo);
                    } else {
                        console.log("未找到复选框，行索引：", index);
                    }
                }
            });

            // 重新渲染表格的复选框状态
            form.render('checkbox');

            console.log("勾选操作完成");

            // 备用方法：如果上面的方法不起作用，尝试使用事件触发
            setTimeout(function() {
                window.checkExistingStandardsBackup();
            }, 200);
        };

        // 备用勾选方法
        window.checkExistingStandardsBackup = function() {
            if(!window.existingStandardIds || window.existingStandardIds.length === 0) {
                return;
            }

            console.log("使用备用勾选方法");

            // 方法3：使用更直接的DOM操作
            window.existingStandardIds.forEach(function(stdId) {
                // 查找包含该ID的行
                $('div[lay-id="role-table"] tbody tr').each(function(index, tr) {
                    var $tr = $(tr);
                    var tableData = table.cache['role-table'] || [];
                    var rowData = tableData[index];

                    if(rowData && rowData.id === stdId) {
                        // 找到对应的行，勾选复选框
                        var $checkbox = $tr.find('input[type="checkbox"]').first();
                        if($checkbox.length > 0) {
                            // 设置选中状态
                            $checkbox.prop('checked', true);

                            // 更新layui样式
                            var $layuiCheckbox = $checkbox.next('.layui-form-checkbox');
                            $layuiCheckbox.addClass('layui-form-checked');

                            // 如果还是不行，尝试直接修改class
                            if(!$layuiCheckbox.hasClass('layui-form-checked')) {
                                $layuiCheckbox.attr('class', 'layui-form-checkbox layui-form-checked');
                            }

                            console.log("备用方法勾选成功：", rowData.stdNo);
                        }
                    }
                });
            });

            // 最后再次渲染
            form.render('checkbox');
        };

        // 格式化数字，添加千分位分隔符
        window.formatNumber = function(num) {
            if (!num) return '0';
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        };
        let cols = [
            [
                {type: 'checkbox',width:'8%'},
                {field: 'number', title: '序号', align: 'center', type: 'numbers',width:'6%'},
                {title: '标准<br/>类别', field: 'stdClass', align: 'center', width: '10%'},
                {title: '标准号', align: 'left', width: '20%',templet:function(rowData){
                        return window.highlight("stdNo",rowData,searchKeyMap)+(rowData["isReal"]=="1"?"":"<span style='color:red'>[无文本]</span>");
                    }},
                {title: '标准名称',  align: 'left', width: '48%',templet:function(rowData){
                        return window.highlight("stdOrgName",rowData,searchKeyMap);
                    }},
                {title: '标准<br/>状态', field: 'stdStatus', align: 'center', width: '8%'},
            ]
        ]
         table.render({
             elem: '#role-table',
             url: MODULE_PATH + 'data',
             page: true,
             limit:50,
             limits:[10,15,20,30,50],
             cols: cols,
             skin: 'row,line',
             toolbar: '#role-toolbar',
             cellMinWidth: 'auto',
             defaultToolbar: [],
             done: function (res, curr, count) {
                 $("#resultInfo").html('共检索到'+formatNumber(res.count)+"个标准");
                 var $data = $('div[lay-id="role-table"]').find('.layui-table-body').find('tr').eq(0).find('td');
                 var $head = $('div[lay-id="role-table"]').find('.layui-table-header').find('tr').eq(0).find('th');
                 for (var i = 0; i < $data.length; i++) {
                     var l1 = $data.eq(i).find('div').width();
                     var l2 = $head.eq(i).find('div').width();
                     if (l1 > l2) {
                         $head.eq(i).find('div').width(l1);
                     } else if(l2>l1) {
                         $data.eq(i).find('div').width(l2);
                     }
                 }

                 // 在表格渲染完成后，自动勾选已有的标准
                 if(choosenClassify.id && window.existingStandardIds && window.existingStandardIds.length > 0) {
                     console.log("表格渲染完成，准备勾选已有标准");
                     setTimeout(function() {
                         window.checkExistingStandards();
                     }, 300);
                 }
             }
         });
        $(document).on('keydown', 'input', function(e) {
            if (e.key === 'Enter') {
                const isSimpleVisible = $('.simple-container').is(':visible');
                const isAdvanceVisible = $('.advance-container').is(':visible');
                console.log("isSimpleSearch:"+isSimpleVisible);
                console.log("isAdvanceVisible:"+isAdvanceVisible);
                if (isSimpleVisible && !isAdvanceVisible) {
                    // 执行普通检索
                    window.simpleSearch(false);
                } else if (isAdvanceVisible) {
                    // 执行高级检索
                    //console.log("高级检索");
                    // 阻止表单默认提交行为，避免触发普通检索
                    e.preventDefault();
                    window.advanceSearch();
                }
            }
        });
        window.advanceSearch=function()
        {
            var fields=[];
            for(var i=0;i<conditionArr.length;i++)
            {
                var obj={};
                obj.logic="AND";
                if(conditionArr[i]>1)
                {
                    obj.logic= $("#conAndOr"+conditionArr[i]).val();
                }
                obj.field=$("#conField"+conditionArr[i]).val();
                obj.val=$("#conVal"+conditionArr[i]).val();
                obj.type=$("#conQueryType"+conditionArr[i]).val();
                fields.push(obj);
            }
            hasQueryCondtion = fields.length > 0;
            table.reload('role-table', {where: {advSearchKey:JSON.stringify(fields),queryIdenti:getClassCond()},page: { curr: 1 }});
            window.createKeywords(conditionArr,function(data){searchKeyMap=data;});
            return false;
        }
        window.simpleSearch=function(isClickSearchBtn)
        {
            var key = $('#searchKey').val();
            if(isClickSearchBtn) {
                // if (!key) {
                //     layer.alert("请输入关键字", {
                //         title: "提示"
                //     });
                //     return false;
                // }
            }
            if(!key)
            {
                hasQueryCondtion=false;
            }
            else
            {
                hasQueryCondtion=true;
            }
            var classes=getClassCond();
            table.reload('role-table', {where: {simpleSearchKey:key,queryIdenti:classes},page: { curr: 1 }});
            $("#clearSimpleCon").show();
            window.createKeywords(conditionArr,function(data){searchKeyMap=data;});
            return false;
        }
        table.on('toolbar(role-table)', function (obj) {
            if (obj.event === 'add') {
                window.add(obj);
            } else if (obj.event === 'del') {
                window.del(obj);
            }
        });
        window.add=function(obj)
        {
             if(!choosenClassify.id)
             {
                 layer.msg("请先在左侧分类树中选择一个分类!", {icon: 2, time: 2000});
                 return;
             }
            let ids = common.checkField(obj, 'id');
            if (common.isEmpty(ids)) {
                //layer.msg("请先选择一个分类!", {icon: 2, time: 2000});
                popup.warning("请在列表中勾选要添加的标准！");
                return false;
            }
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH_JOIN + "addOrUpdate",
                dataType: 'json',
                type: 'post',
                data: JSON.stringify({classId:choosenClassify.id,stdIds:ids}),
                contentType: 'application/json',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("添加成功!", {icon: 1, time: 2000});
                        // 添加成功后重新加载已有标准列表，更新勾选状态
                        window.loadExistingStandards();
                    } else {
                        layer.msg("添加失败:"+result.message, {icon: 2, time: 2000});
                    }
                }
            })
        }

        form.on('submit(role-query)', function (e) {
            console.log(e);
            window.advanceSearch();
            return false;
        });

        form.on('checkbox(i-all)',function(data){
            let checkboxes =  ['chkAll',
                'chkAQ','chkCB','chkDB11','chkDB13','chkDB31','chkDB37','chkDB50','chkDB61', 'chkDL', 'chkGB',
                'chkGJB', 'chkHB', 'chkHG','chkHJ','chkJB','chkJJF', 'chkJJG', 'chkJR','chkKA','chkMT',
                'chkQCNG', 'chkQC', 'chkQJ','chkQX','chkSJ', 'chkSY', 'chkTB', 'chkWJ','chkWS', 'chkYD','chkYZ'];
            checkboxes.forEach(id => {
                if((!data.elem.checked) && (id=='chkGJB' || id=='chkQCNG' || id=='chkWJ'))
                {
                    $(`input[id=${id}]`).prop("checked", !data.elem.checked);
                }
                else{
                    $(`input[id=${id}]`).prop("checked", data.elem.checked);
                }
            });
            form.render();
        });
        
        // 其他复选框事件
        form.on('checkbox', function(data){
            if(data.elem.name === 'chkClass' && data.elem.id !== 'chkAll'){
                var allChecked = true;
                $("input[name='chkClass']:not(#chkAll)").each(function(){
                    if(!$(this).prop("checked")){
                        allChecked = false;
                        return false;
                    }
                });
                $("#chkAll").prop("checked", allChecked);
                form.render('checkbox');
            }
        });
        $("#advanceAnchor").click(function () {
            $(".simple-container").toggle();
            $(".advance-container").toggle();
            $("#advTool").toggle();
        });
        $("#simpleAnchor").click(function () {
            $(".simple-container").toggle();
            $(".advance-container").toggle();
            $("#advTool").toggle();
        });

        $("#btnSimpleSearch").click(function(e){
            e.preventDefault();
            window.simpleSearch(false);
        });
        $("#clearSimpleCon").click(function(){
            $('#searchKey').val("");
            var classes=getClassCond();
            table.reload('role-table', {where: {simpleSearchKey:"",classes},page: { curr: 1 }});
            $("#clearSimpleCon").hide();
            window.createKeywords(conditionArr,function(data){searchKeyMap=data;});
        });
        window.addCondition=function(){
            var id=0;
            for(var i=0;i<conditionArr.length;i++)
            {
                if(conditionArr[i]>id)
                {
                    id=conditionArr[i];
                }
            }
            if(id>0)
            {
                id+=1;
                conditionArr[conditionArr.length]=id;
            }
            var html=`<div class="advance-item" id="advanceItem${id}" style="width:100%;">
                    <div class="ph-div">
                        <select id="conAndOr${id}" class="conAndOr">
                            <option value="AND">AND</option>
                            <option value="OR">OR</option>
                            <option value="NOT">NOT</option>
                        </select>
                    </div>
                    <div class="search-cmp">
                        <div class="con-field-container">
                            <select id="conField${id}">
                                <option value="std_no">标准号</option>
                                <option value="std_org_name">标准名称（原文）</option>
                                <option value="std_chinese_name">标准名称（中文）</option>
                                <option value="std_english_name">标准名称（英文）</option>
                                <option value="std_class">标准类别</option>
                                <option value="catetory_no">分类号</option>
                                <option value="std_ics">ICS号</option>
                                <option value="pub_date">发布日期</option>
                                <option value="implementation_date">实施日期</option>
                                <option value="advance_dept">提出单位</option>
                                <option value="drafting_unit">起草单位</option>
                                <option value="drafter">起草人</option>
                                <option value="std_status">标准状态</option>
                            </select>
                        </div>
                        <input class="layui-input sub-item" id="conVal${id}" placeholder="" type="text">
                        <div class="con-type-container">
                            <select id="conQueryType${id}">
                                <option value="fuzzy">模糊</option>
                                <option value="exact">精确</option>
                            </select>
                        </div>
                    </div>
                    <div class="ph-end-div">
                        <a href="javascript:void(0);" class="conDel" onclick="delCondition(${id})" id="conDel${id}">-</a>
                        <a href="javascript:void(0);" onclick="addCondition()" class="conAdd" id="conAdd${id}">+</a>
                    </div>
                </div>`;
            $(".advance-container").append(html);
            form.render("select");
            $("#conAdd"+(id-1)).toggle();
            for(var i=1;i<conditionArr.length-1;i++)
            {
                $("#conDel"+conditionArr[i]).show();
            }
        };

        window.delCondition=function(id){
            $("#advanceItem"+id).remove();
            for(var i=0;i<conditionArr.length;i++)
            {
                if(conditionArr[i]==id)
                {
                    if(i==conditionArr.length-1)
                    {
                        $("#conAdd"+conditionArr[i-1]).toggle();
                    }
                    conditionArr.splice(i, 1);
                    break;
                }
            }
            console.log('conditionArr.length:'+conditionArr.length);
            //需要保留至少两行
            if(conditionArr.length==2)
            {
                $("#conDel"+conditionArr[1]).hide();
            }
        };
    })
</script>
</body>
<style>
    .whole-ui
    {
        display: flex;
        flex-direction:row;
    }
    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }

    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
    }

    .simple-container .item .sub-item button {
        margin-top: 10px;
    }

    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }

    .advance-container {
        width: 70%;
        margin: 0px auto;
    }

    .advance-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-bottom: 15px;
    }
    .layui-table-tool
    {
        height:70px !important;
        line-height:30px !important;
        padding:10px 0 0 10px !important;
        min-height:0px !important;
    }
    .layui-form-item .layui-form-label, .layui-form-item .layui-input-inline {
        margin-bottom: 7px;
    }
    .layui-form-item {
        padding-bottom: 0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    #resultInfo {
        width: 100%;
        text-align: center;
        color: #999;
        margin-top: 10px;
    }
    .layui-table thead th {
        font-weight: bold;
    }
   /* .c-anchor {
        text-decoration: underline;
        color: #666;
        font-size: 12px;
        display: inline-block;
    }*/
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    .search-label {
        flex-grow: 2;
    }
    .simple-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #ddd;
        border-radius: 5px;
    }
    .simple-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .simple-container .search-cmp button:hover {
        cursor: pointer;
    }
    .simple-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .simple-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .advance-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #efefef;
        border-radius: 5px;
    }
    .advance-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .advance-container .search-cmp button:hover {
        cursor: pointer;
    }
    .advance-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .advance-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 240px;
    }
    .con-type-container {
        width: 120px;
    }
    .search-anchor-1
    {
        flex-grow:5;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }
    .layui-table-cell
    {
        height:auto !important;
    }
    .layui-card-body .layui-form
    {
        margin-top:0px;
    }
    .layui-card
    {
        margin-bottom:1px;
    }
    #dvChooseClassify
    {
        font-weight: bold;
    }
</style>
<link rel="stylesheet" href="/component/layui/css/layui.css"/>
</html>
