<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('岗位下新增标准')"/>
</head>
<body>
<div class="layui-row whole-ui">
    <div class="layui-col-xs12">
        <div class="layui-card" style="margin-bottom:0px;">
            <div class="layui-card-body">
                <div>当前选中岗位：【<span id="dvChooseClassify"></span>】</div>
                <form action="" class="layui-form">
                    <div class="simple-container">
                        <div class="item" style="width:100%;">
                            <label class="sub-item search-label">标准号/标准名称：</label>
                            <div class="search-cmp">
                                <input class="layui-input sub-item" id="searchKey" name="stdNo" placeholder=""
                                       type="text">
                                <button id="btnSimpleSearch" class="search-button">
                                    <i class="layui-icon layui-icon-search"></i>
                                </button>
                            </div>
                            <div class="search-anchor-1"><a href="javascript:void(0);" id="clearSimpleCon"
                                                            style="display: none;" class="search-advance">清空</a><a
                                    href="javascript:void(0);" id="advanceAnchor"
                                    class="search-advance">高级检索&gt;&gt;</a></div>
                        </div>
                    </div>
                    <div class="advance-container" style="display: none;">
                        <div class="advance-item" id="advanceItem1" style="width:100%;">
                            <div class="ph-div" style="text-align: right;">高级检索：</div>
                            <div class="search-cmp">
                                <div class="con-field-container">
                                    <select id="conField1" class="con-field">
                                        <option value="std_no">标准号</option>
                                        <option value="std_org_name">标准名称（原文）</option>
                                        <option value="std_chinese_name">标准名称（中文）</option>
                                        <option value="std_english_name">标准名称（英文）</option>
                                        <option value="std_class">标准类别</option>
                                        <option value="catetory_no">分类号</option>
                                        <option value="std_ics">ICS号</option>
                                        <option value="pub_date">发布日期</option>
                                        <option value="implementation_date">实施日期</option>
                                        <option value="advance_dept">提出单位</option>
                                        <option value="drafting_unit">起草单位</option>
                                        <option value="drafter">起草人</option>
                                        <option value="std_status">标准状态</option>
                                    </select>
                                </div>
                                <input class="layui-input sub-item con-val" id="conVal1" placeholder="" type="text">
                                <div class="con-type-container">
                                    <select id="conQueryType1" class="con-query-type">
                                        <option value="fuzzy">模糊</option>
                                        <option value="exact">精确</option>
                                    </select>
                                </div>
                            </div>
                            <div class="ph-end-div">
                                <a href="javascript:void(0);" id="simpleAnchor" class="search-advance">普通检索&gt;&gt;</a>
                            </div>
                        </div>
                        <div class="advance-item" id="advanceItem2" style="width:100%;">
                            <div class="ph-div">
                                <select id="conAndOr2" class="conAndOr">
                                    <option value="AND">AND</option>
                                    <option value="OR">OR</option>
                                    <option value="NOT">NOT</option>
                                </select>
                            </div>
                            <div class="search-cmp">
                                <div class="con-field-container">
                                    <select id="conField2">
                                        <option value="std_no">标准号</option>
                                        <option value="std_org_name">标准名称</option>
                                        <option value="catetory_no">分类号</option>
                                        <option value="std_ics">ICS号</option>
                                        <option value="pub_date">发布日期</option>
                                        <option value="implementation_date">实施日期</option>
                                        <option value="advance_dept">提出单位</option>
                                        <option value="drafting_unit">起草单位</option>
                                        <option value="drafter">起草人</option>
                                        <option value="std_status">标准状态</option>
                                    </select>
                                </div>
                                <input class="layui-input sub-item" id="conVal2" placeholder="" type="text">
                                <div class="con-type-container">
                                    <select id="conQueryType2">
                                        <option value="fuzzy">模糊</option>
                                        <option value="exact">精确</option>
                                    </select>
                                </div>
                            </div>
                            <div class="ph-end-div">
                                <a href="javascript:void(0);" onclick="delCondition(2)" class="conDel" id="conDel2"
                                   style="display: none;">-</a>
                                <a href="javascript:void(0);" onclick="addCondition()" class="conAdd" id="conAdd2">+</a>
                            </div>
                        </div>
                    </div>
                    <div class="item" id="advTool" style="text-align: center;display: none;">
                        <button class="pear-btn pear-btn-md sub-item" type="reset"
                                style="margin-left:30px;margin-top:0px;width:80px;padding-left:5px;">
                            <i class="layui-icon layui-icon-refresh"></i>
                            重 置
                        </button>
                        <button class="pear-btn pear-btn-md pear-btn-primary sub-item" lay-filter="role-query"
                                lay-submit
                                style="margin-left:40px;margin-top:0px;width:80px;padding-left:5px;">
                            <i class="layui-icon layui-icon-search"></i>
                            检 索
                        </button>
                    </div>
                    <div id="resultInfo"></div>
                </form>
            </div>
        </div>
        <div class="layui-card">
            <div class="layui-card-body">
                <table id="role-table" lay-filter="role-table"></table>
            </div>
        </div>
      <!--  <div class="bottom">
            <div class="button-container">
                <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-save" lay-submit=""
                        type="submit">
                    <i class="layui-icon layui-icon-ok"></i>
                    保存
                </button>
                <button class="pear-btn pear-btn-sm" type="reset">
                    <i class="layui-icon layui-icon-refresh"></i>
                    关闭
                </button>
            </div>
        </div>-->
    </div>
</div>
<th:block th:include="include :: footer"/>
<input id="hdSystemId" name="hdSystemId" th:value="${system.id}" type="hidden"/>
<input id="hdSystemName"  name="hdSystemName" th:value="${system.name}" type="hidden"/>
<input id="hdPostId"  name="hdPostId" th:value="${post.id}" type="hidden"/>
<input id="hdPostName"  name="hdPostName" th:value="${post.className}" type="hidden" />
<script id="role-toolbar" type="text/html">
    <button  class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        添加到岗位
    </button>
   <!-- <button  class="pear-btn pear-btn-primary pear-btn-md" lay-event="del">
        <i class="layui-icon layui-icon-delete"></i>
        从分类中删除
    </button>-->
</script>
<script id="role-bar" type="text/html">
</script>
<script>
    layui.use(['tree', 'table', 'form', 'jquery', 'popup', 'common', 'upload', 'layer', 'element'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;
        let tree = layui.tree;
        let MODULE_PATH = "/chinastd/";
        let MODULE_PATH_OF_CLASS = '/stdclassification/';
        let MODULE_PATH_JOIN='/classjoinstd/';
        var treeHandler = null;
        var treeData = {};
        var choosenClassify={id:0,name:""};
        let conditionArr = [1, 2];
        window.initData=function()
        {
            $("#dvChooseClassify").text($("#hdPostName").val());
            choosenClassify.id=$("#hdPostId").val();
        };
        window.initData();
        let cols = [
            [
                {type: 'checkbox',width:'8%'},
                {field: 'number', title: '序号', align: 'center', type: 'numbers',width:'6%'},
                {title: '标准<br/>类别', field: 'stdClass', align: 'center', width: '10%'},
                {title: '标准号', field: 'stdNo', align: 'left', width: '20%'},
                {title: '标准名称', field: 'stdOrgName', align: 'left', width: '48%'},
                {title: '标准<br/>状态', field: 'stdStatus', align: 'center', width: '8%'},
            ]
        ]
         table.render({
             elem: '#role-table',
             url: MODULE_PATH + 'data',
             page: true,
             limit:50,
             limits:[10,15,20,30,50],
             where: {
                 queryIdenti: "GJB,Q/CNG,WJ" // 默认显示这些标准类别
             },
             cols: cols,
             skin: 'row,line',
             toolbar: '#role-toolbar',
             cellMinWidth: 'auto',
             defaultToolbar: [],
             done: function (res, curr, count) {
                 $("#resultInfo").html('共检索到'+formatNumber(res.count)+"个标准");
                 var $data = $('div[lay-id="role-table"]').find('.layui-table-body').find('tr').eq(0).find('td');
                 var $head = $('div[lay-id="role-table"]').find('.layui-table-header').find('tr').eq(0).find('th');
                 for (var i = 0; i < $data.length; i++) {
                     var l1 = $data.eq(i).find('div').width();
                     var l2 = $head.eq(i).find('div').width();
                     if (l1 > l2) {
                         $head.eq(i).find('div').width(l1);
                     } else if(l2>l1) {
                         $data.eq(i).find('div').width(l2);
                     }
                 }

                 // 在表格渲染完成后，自动勾选岗位已关联的标准
                 if(choosenClassify.id && window.existingStandardIds && window.existingStandardIds.length > 0) {
                     setTimeout(function() {
                         window.checkExistingStandards();
                     }, 300);
                 }
             }
         });
        table.on('toolbar(role-table)', function (obj) {
            if (obj.event === 'add') {
                window.add(obj);
            } else if (obj.event === 'del') {
                window.del(obj);
            }
        });
        window.add=function(obj)
        {
             if(!choosenClassify.id)
             {
                 layer.msg("请先在左侧分类树中选择一个分类!", {icon: 2, time: 2000});
                 return;
             }
            let ids = common.checkField(obj, 'id');
            if (common.isEmpty(ids)) {
                //layer.msg("请先选择一个分类!", {icon: 2, time: 2000});
                popup.warning("请在列表中勾选要添加的标准！");
                return false;
            }
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH_JOIN + "addOrUpdate",
                dataType: 'json',
                type: 'post',
                data: JSON.stringify({classId:choosenClassify.id,stdIds:ids}),
                contentType: 'application/json',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("添加成功!", {icon: 1, time: 2000});
                    } else {
                        layer.msg("添加失败:"+result.message, {icon: 2, time: 2000});
                    }
                }
            })

        }

        form.on('submit(role-query)', function (data) {
            console.log(data);
            var fields=[];
            for(var i=0;i<conditionArr.length;i++)
            {
                var obj={};
                obj.logic="AND";
                if(conditionArr[i]>1)
                {
                    obj.logic= $("#conAndOr"+conditionArr[i]).val();
                }
                obj.field=$("#conField"+conditionArr[i]).val();
                obj.val=$("#conVal"+conditionArr[i]).val();
                obj.type=$("#conQueryType"+conditionArr[i]).val();
                fields.push(obj);
            }
            table.reload('role-table', {where: {advSearchKey:JSON.stringify(fields)},page: { curr: 1 }});
            return false;
        });
        $("#advanceAnchor").click(function () {
            $(".simple-container").toggle();
            $(".advance-container").toggle();
            $("#advTool").toggle();
        });
        $("#simpleAnchor").click(function () {
            $(".simple-container").toggle();
            $(".advance-container").toggle();
            $("#advTool").toggle();
        });
        $("#searchKey").keydown(function (e) {//当按下按键时
            if (e.which == 13) {//.which属性判断按下的是哪个键，回车键的键位序号为13
                var key = $('#searchKey').val();
                if (!key) {
                    layer.alert("请输入关键字", {
                        title: "提示"
                    });
                    return false;
                } else {
                    table.reload('role-table', {where: {simpleSearchKey:key},page: { curr: 1 }});
                    $("#clearSimpleCon").show();
                    return false;
                    //table.reload('role-table');
                }
            }
        });
        $("#btnSimpleSearch").click(function(e){
            var key = $('#searchKey').val();
            if (!key) {
                layer.alert("请输入关键字", {
                    title: "提示"
                });
                return false;
            } else {
                table.reload('role-table', {where: {simpleSearchKey:key},page: { curr: 1 }});
                // $('#searchKey').val(key);
                $("#clearSimpleCon").show();
                return false;
                //table.reload('role-table');
            }
        });
        $("#clearSimpleCon").click(function(){
            $('#searchKey').val("");
            table.reload('role-table', {where: {simpleSearchKey:""},page: { curr: 1 }});
            $("#clearSimpleCon").hide();
        });
        window.addCondition=function(){
            var id=0;
            for(var i=0;i<conditionArr.length;i++)
            {
                if(conditionArr[i]>id)
                {
                    id=conditionArr[i];
                }
            }
            if(id>0)
            {
                id+=1;
                conditionArr[conditionArr.length]=id;
            }
            var html=`<div class="advance-item" id="advanceItem${id}" style="width:100%;">
                    <div class="ph-div">
                        <select id="conAndOr${id}" class="conAndOr">
                            <option value="AND">AND</option>
                            <option value="OR">OR</option>
                            <option value="NOT">NOT</option>
                        </select>
                    </div>
                    <div class="search-cmp">
                        <div class="con-field-container">
                            <select id="conField${id}">
                                <option value="std_no">标准号</option>
                                <option value="std_org_name">标准名称</option>
                                <option value="catetory_no">分类号</option>
                                <option value="std_ics">ICS号</option>
                                <option value="pub_date">发布日期</option>
                                <option value="implementation_date">实施日期</option>
                                <option value="advance_dept">提出单位</option>
                                <option value="drafting_unit">起草单位</option>
                                <option value="drafter">起草人</option>
                                <option value="std_status">标准状态</option>
                            </select>
                        </div>
                        <input class="layui-input sub-item" id="conVal${id}" placeholder="" type="text">
                        <div class="con-type-container">
                            <select id="conQueryType${id}">
                                <option value="fuzzy">模糊</option>
                                <option value="exact">精确</option>
                            </select>
                        </div>
                    </div>
                    <div class="ph-end-div">
                        <a href="javascript:void(0);" class="conDel" onclick="delCondition(${id})" id="conDel${id}">-</a>
                        <a href="javascript:void(0);" onclick="addCondition()" class="conAdd" id="conAdd${id}">+</a>
                    </div>
                </div>`;
            $(".advance-container").append(html);
            form.render("select");
            $("#conAdd"+(id-1)).toggle();
            for(var i=1;i<conditionArr.length-1;i++)
            {
                $("#conDel"+conditionArr[i]).show();
            }
        };

        window.delCondition=function(id){
            $("#advanceItem"+id).remove();
            for(var i=0;i<conditionArr.length;i++)
            {
                if(conditionArr[i]==id)
                {
                    if(i==conditionArr.length-1)
                    {
                        $("#conAdd"+conditionArr[i-1]).toggle();
                    }
                    conditionArr.splice(i, 1);
                    break;
                }
            }
            console.log('conditionArr.length:'+conditionArr.length);
            //需要保留至少两行
            if(conditionArr.length==2)
            {
                $("#conDel"+conditionArr[1]).hide();
            }
        };
    })
</script>
</body>
<style>
    .whole-ui
    {
        display: flex;
        flex-direction:row;
    }
    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }

    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
    }

    .simple-container .item .sub-item button {
        margin-top: 10px;
    }

    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }

    .advance-container {
        width: 70%;
        margin: 0px auto;
    }

    .advance-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-bottom: 15px;
    }
    .layui-table-tool
    {
        height:70px !important;
        line-height:30px !important;
        padding:10px 0 0 10px !important;
        min-height:0px !important;
    }
    .layui-form-item .layui-form-label, .layui-form-item .layui-input-inline {
        margin-bottom: 7px;
    }
    .layui-form-item {
        padding-bottom: 0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    #resultInfo {
        width: 100%;
        text-align: center;
        color: #999;
        margin-top: 10px;
    }
    .layui-table thead th {
        font-weight: bold;
    }
   /* .c-anchor {
        text-decoration: underline;
        color: #666;
        font-size: 12px;
        display: inline-block;
    }*/
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    .search-label {
        flex-grow: 2;
    }
    .simple-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #ddd;
        border-radius: 5px;
    }
    .simple-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .simple-container .search-cmp button:hover {
        cursor: pointer;
    }
    .simple-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .simple-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .advance-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #efefef;
        border-radius: 5px;
    }
    .advance-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .advance-container .search-cmp button:hover {
        cursor: pointer;
    }
    .advance-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .advance-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 140px;
    }
    .con-type-container {
        width: 100px;
    }
    .search-anchor-1
    {
        flex-grow:5;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }
    .layui-table-cell
    {
        height:auto !important;
    }
    .layui-card-body .layui-form
    {
        margin-top:0px;
    }
    .layui-card
    {
        margin-bottom:1px;
    }
    #dvChooseClassify
    {
        font-weight: bold;
    }
</style>
<link rel="stylesheet" href="/component/layui/css/layui.css"/>
</html>
