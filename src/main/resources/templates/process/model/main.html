<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('流程模型列表')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="javascript:void(0);" class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">模型名称</label>
                <div class="layui-input-inline">
                    <input class="layui-input" name="modelName" placeholder="" type="text">
                </div>
                <button class="pear-btn pear-btn-md pear-btn-primary" lay-filter="model-query" lay-submit>
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button class="pear-btn pear-btn-md" type="reset">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="model-table" lay-filter="model-table"></table>
        <script id="barDemo" type="text/html">
            <a sec:authorize="hasPermission('/process/model/publish','pro:model:publish')"
               class="pear-btn pear-btn-sm pear-btn-primary" lay-event="publish"><i
                    class="pear-icon pear-icon-print"></i></a>
            <a sec:authorize="hasPermission('/process/model/edit','pro:model:edit')"
               class="pear-btn pear-btn-sm pear-btn-warming" lay-event="design"><i
                    class="layui-icon layui-icon-edit"></i></a>
            <a sec:authorize="hasPermission('/process/model/remove','pro:model:remove')"
               class="pear-btn pear-btn-sm pear-btn-danger" lay-event="del"><i
                    class="layui-icon layui-icon-delete"></i></a>
        </script>
    </div>
</div>
<div class="createModel" sec:authorize="hasPermission('/process/model/add','pro:model:add')">
    <i class="layui-icon layui-icon-add-1"></i>
</div>
<style>

    .createModel {
        position: absolute;
        right: 30px;
        bottom: 30px;
        width: 50px;
        height: 50px;
        border-radius: 50px;
        background-color: #5FB878;
        text-align: center;
        font-size: 22px;
        line-height: 50px;
    }

    .createModel i {
        font-size: 23px;
        color: white;
    }

</style>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'layer', 'jquery', 'form'], function () {
        let layer = layui.layer;
        let table = layui.table;
        let $ = layui.jquery;
        let form = layui.form;

        table.render({
            elem: '#model-table'
            , method: 'get'
            , url: '/process/model/data'
            , title: '模型列表'
            , skin: 'line'
            , toolbar: false
            , page: true
            , cols: [
                [
                    {type: 'checkbox'}
                    , {field: 'id', title: '流程编号', sort: true}
                    , {field: 'name', title: '流程名称'}
                    , {field: 'key', title: '流程标识', sort: true}
                    , {field: 'version', title: '流程版本', sort: true}
                    , {title: '操作', width: 220, align: 'center', toolbar: '#barDemo'}
                ]
            ]
        });

        $(".createModel").click(function () {
            layer.open({
                type: 2,
                title: '新增',
                shade: 0.1,
                area: ['550px', '500px'],
                content: '/process/model/add'
            });
        })

        table.on('tool(model-table)', function (obj) {
            let layEvent = obj.event;
            if (layEvent === 'del') {
                window.remove(obj);
            } else if (layEvent === 'design') {
                window.design(obj);
            } else if (layEvent === 'publish') {
                window.publish(obj);
            }
        });

        form.on('submit(model-query)', function (data) {
            console.log(data);
            table.reload('model-table', {where: data.field})
            return false;
        });

        window.design = function (obj) {
            location.href = "/process/model/editor?modelId=" + obj.data.id;
        }

        window.publish = function (obj) {
            let loading = layer.load();
            $.ajax({
                url: '/process/model/publish',
                data: {modelId: obj.data.id},
                dataType: 'json',
                type: 'post',
                success: function (data) {
                    layer.close(loading);
                    if (data.success) {
                        layer.msg(data.msg, {icon: 1, time: 1000});
                    } else {
                        layer.msg(data.msg, {icon: 2, time: 1000});
                    }
                }
            })
        }

        window.remove = function (obj) {
            layer.confirm('确认删除该流程图吗', function (index) {
                $.ajax({
                    url: '/process/model/deleteById',
                    data: {modelId: obj.data.id},
                    dataType: 'json',
                    type: 'post',
                    success: function (data) {
                        if (data.success) {
                            layer.msg(data.msg, {icon: 1, time: 1000}, function () {
                                obj.del();
                                layer.close(index);
                            })
                        } else {
                            layer.msg(data.msg, {icon: 2, time: 1000});
                        }
                    }
                })
            });
        }
    });
</script>
</body>
</html>