<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('流程模型新增')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="title" name="name" placeholder="请输入标题"
                               type="text">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标识</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="title" name="key" placeholder="请输入标题"
                               type="text">
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">描述</label>
                    <div class="layui-input-block">
                        <textarea class="layui-textarea" name="description" placeholder="请输入描述"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="role-save" lay-submit="" type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button class="pear-btn pear-btn-sm" type="reset">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
</form>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery', 'layer'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        let layer = layui.layer;

        form.on('submit(role-save)', function (data) {
            $.ajax({
                url: '/process/model/create',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'post',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            top.layui.tab.addTabOnlyByElem("content", {
                                id: result.data,
                                title: "流程编辑器",
                                url: "/process/model/editor?modelId=" + result.data,
                                close: true
                            })
                            parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                            parent.layui.table.reload("model-table");
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });
    })
</script>
<script>
</script>
</body>
</html>