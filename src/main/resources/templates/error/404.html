<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('404')"/>
    <link rel="stylesheet" th:href="@{/admin/css/other/error.css}"/>
</head>
<body>
<div class="content">
    <img alt="" src="../../admin/images/404.svg">
    <div class="content-r">
        <h1>404</h1>
        <p>抱歉，你访问的页面不存在或仍在开发中</p>
        <button class="pear-btn pear-btn-primary">返回首页</button>
    </div>
</div>
<th:block th:include="include :: footer"/>
</body>
</html>
