<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('编辑下载配置')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="">
            <input type="hidden" name="id" th:value="${stdDownloadConfig?.id}">
            <div class="layui-form-item">
                <label class="layui-form-label required">用户名</label>
                <div class="layui-input-block">
                    <input type="text" name="userName" lay-verify="required" placeholder="请输入用户名" 
                           class="layui-input" th:value="${stdDownloadConfig?.userName}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label required">下载限制</label>
                <div class="layui-input-block">
                    <input type="number" name="downloadLimit" lay-verify="required|number" placeholder="请输入下载限制数量" 
                           class="layui-input" min="0" th:value="${stdDownloadConfig?.downloadLimit}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label required">预览限制</label>
                <div class="layui-input-block">
                    <input type="number" name="previewLimit" lay-verify="required|number" placeholder="请输入预览限制数量" 
                           class="layui-input" min="0" th:value="${stdDownloadConfig?.previewLimit}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" th:checked="${stdDownloadConfig?.status == 1}">
                    <input type="radio" name="status" value="0" title="禁用" th:checked="${stdDownloadConfig?.status == 0}">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea" th:text="${stdDownloadConfig?.remark}"></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="update">更新</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery', 'layer'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        let layer = layui.layer;
        let MODULE_PATH = "/system/stdDownloadConfig/";

        // 表单提交
        form.on('submit(update)', function (data) {
            $.ajax({
                url: MODULE_PATH + 'update',
                type: 'PUT',
                contentType: 'application/json',
                data: JSON.stringify(data.field),
                success: function (res) {
                    if (res.success) {
                        layer.msg('更新成功', {icon: 1}, function () {
                            parent.layer.closeAll();
                            parent.layui.table.reload('config-table');
                        });
                    } else {
                        layer.msg(res.msg || '更新失败', {icon: 2});
                    }
                },
                error: function () {
                    layer.msg('网络错误', {icon: 2});
                }
            });
            return false;
        });

        // 自定义验证规则
        form.verify({
            number: function(value) {
                if (value && (isNaN(value) || parseInt(value) < 0)) {
                    return '请输入有效的数字（大于等于0）';
                }
            }
        });
    });
</script>

<style>
    .required::before {
        content: "*";
        color: red;
        margin-right: 4px;
    }
    .layui-form-item {
        margin-bottom: 20px;
    }
    .layui-form-label {
        width: 120px;
    }
    .layui-input-block {
        margin-left: 150px;
    }
</style>
</body>
</html>
