<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('快速配置用户权限')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="">
            <div class="layui-form-item" style="display: none;">
                <label class="layui-form-label required">用户名</label>
                <div class="layui-input-block">
                    <input type="text" name="userName" lay-verify="required" placeholder="请输入用户名" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label required">下载次数</label>
                <div class="layui-input-block">
                    <input type="number" name="downloadLimit" lay-verify="required|number" placeholder="请输入下载次数" class="layui-input" min="0">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">开始时间</label>
                <div class="layui-input-block">
                    <input type="text" name="downloadStartTime" id="downloadStartTime" placeholder="请选择开始时间" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">结束时间</label>
                <div class="layui-input-block">
                    <input type="text" name="downloadEndTime" id="downloadEndTime" placeholder="请选择结束时间" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" checked>
                    <input type="radio" name="status" value="0" title="禁用">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea"></textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="save">保存配置</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery', 'layer', 'laydate'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        let layer = layui.layer;
        let laydate = layui.laydate;
        let MODULE_PATH = "/system/stdDownloadConfig/";

        // 从URL参数获取用户信息
        function getUrlParam(name) {
            let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            let r = window.location.search.substr(1).match(reg);
            if (r != null) return decodeURIComponent(r[2]);
            return null;
        }

        // 获取用户ID和用户名
        let userId = getUrlParam('userId');
        let userName = getUrlParam('userName');

        // 如果有用户信息，预填并设为只读
        if (userId) {
            $('input[name="userName"]').val(userId);
            $('input[name="userName"]').prop('readonly', true);
            $('input[name="userName"]').css('background-color', '#f5f5f5');
        }

        // 初始化时间选择器
        laydate.render({
            elem: '#downloadStartTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss'
        });

        laydate.render({
            elem: '#downloadEndTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss'
        });

        // 表单提交
        form.on('submit(save)', function (data) {
            let formData = data.field;

            // 验证必填字段
            if (!formData.downloadLimit) {
                layer.msg('请输入下载次数', {icon: 2});
                return false;
            }

            // 保存下载配置
            $.ajax({
                url: MODULE_PATH + 'saveOrUpdateByType',
                type: 'POST',
                data: {
                    userName: formData.userName,
                    configType: '1',
                    configValue: formData.downloadLimit,
                    startTime: formData.downloadStartTime,
                    endTime: formData.downloadEndTime,
                    remark: formData.remark
                },
                success: function(res) {
                    if (res.success) {
                        layer.msg('配置保存成功', {icon: 1}, function () {
                            // 关闭当前弹窗
                            let index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        });
                    } else {
                        layer.msg(res.msg || '保存失败', {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('保存失败', {icon: 2});
                }
            });

            return false;
        });

        // 自定义验证规则
        form.verify({
            number: function(value) {
                if (value && (isNaN(value) || parseInt(value) < 0)) {
                    return '请输入有效的数字（大于等于0）';
                }
            }
        });
    });
</script>

<style>
    .required::before {
        content: "*";
        color: red;
        margin-right: 4px;
    }
    .layui-form-item {
        margin-bottom: 20px;
    }
    .layui-form-label {
        width: 120px;
    }
    .layui-input-block {
        margin-left: 150px;
    }
</style>
</body>
</html>
