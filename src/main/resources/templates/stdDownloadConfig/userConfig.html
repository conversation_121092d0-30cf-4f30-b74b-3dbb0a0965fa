<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('用户下载配置管理')"/>
    <style>
        .config-container {
            display: flex;
            height: calc(100vh - 120px);
            gap: 10px;
        }
        .user-panel {
            width: 350px;
            min-width: 350px;
            background: #fff;
            border-radius: 2px;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        }
        .config-panel {
            flex: 1;
            background: #fff;
            border-radius: 2px;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        }
        .panel-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            font-weight: bold;
            color: #333;
        }
        .panel-body {
            padding: 15px 20px;
            height: calc(100% - 60px);
            overflow: auto;
        }
        .user-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .user-item:hover {
            background-color: #f8f8f8;
        }
        .user-item.active {
            background-color: #e6f7ff;
            border-left: 3px solid #1890ff;
        }
        .user-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .user-info {
            font-size: 12px;
            color: #666;
            line-height: 1.5;
        }
        .no-user-selected {
            text-align: center;
            color: #999;
            padding: 50px 20px;
            font-size: 14px;
        }
        .user-search-container {
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        .user-search-container .layui-form-item {
            margin-bottom: 8px;
        }
        .user-search-row {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .user-search-input {
            flex: 1;
            min-width: 0;
        }
        .user-search-btn, .user-clear-btn {
            flex-shrink: 0;
            width: 40px;
            padding: 0;
            text-align: center;
        }
        .config-form-container {
            max-width: 800px;
        }
        .config-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
        }
        .config-section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
    </style>
</head>
<body class="pear-container">
<div class="config-container">
    <!-- 左侧用户列表 -->
    <div class="user-panel">
        <div class="panel-header">
            用户列表
        </div>
        <div class="panel-body">
            <!-- 用户搜索框 -->
            <div class="user-search-container">
                <form class="layui-form">
                    <div class="layui-form-item" style="margin-bottom: 10px;">
                        <div class="user-search-row">
                            <input type="text" id="userSearchKey" placeholder="输入姓名或邮箱搜索" class="layui-input user-search-input">
                            <button type="button" id="userSearchBtn" class="layui-btn layui-btn-sm layui-btn-primary user-search-btn">
                                <i class="layui-icon layui-icon-search"></i>
                            </button>
                            <button type="button" id="userClearBtn" class="layui-btn layui-btn-sm layui-btn-primary user-clear-btn">
                                <i class="layui-icon layui-icon-refresh"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            
            <div id="userList">
                <div class="no-user-selected">加载中...</div>
            </div>
            <div id="userPagination" style="text-align: center; margin-top: 15px;"></div>
        </div>
    </div>
    
    <!-- 右侧配置表单 -->
    <div class="config-panel">
        <div class="panel-header">
            <span id="configPanelTitle">用户配置</span>
        </div>
        <div class="panel-body">
            <div id="configContent">
                <div class="no-user-selected">请选择左侧用户进行配置</div>
            </div>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery', 'layer', 'laydate', 'laypage'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        let layer = layui.layer;
        let laydate = layui.laydate;
        let laypage = layui.laypage;
        
        // 全局变量
        let currentUserId = null;
        let currentUserName = '';
        let userPageInfo = { current: 1, size: 10, total: 0 };
        let userSearchKey = '';
        
        // 加载用户列表
        function loadUserList(page = 1, searchKey = null) {
            console.log("Loading user list, page:", page, "searchKey:", searchKey);
            
            if (searchKey === null) {
                searchKey = userSearchKey;
            }
            
            var requestData = {
                page: page,
                limit: userPageInfo.size
            };
            
            if (searchKey && searchKey.trim()) {
                requestData.searchKey = searchKey.trim();
            }
            
            $.ajax({
                url: '/stddownload/adminUsers',
                type: 'GET',
                data: requestData,
                success: function(res) {
                    console.log("User list response:", res);
                    if (res.code === 0 && res.data) {
                        renderUserList(res.data);
                        userPageInfo.total = res.count || 0;
                        userPageInfo.current = page;
                        renderUserPagination();
                    } else {
                        $('#userList').html('<div class="no-user-selected">暂无用户数据</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error loading user list:", error);
                    $('#userList').html('<div class="no-user-selected">加载用户列表失败</div>');
                }
            });
        }
        
        // 渲染用户列表
        function renderUserList(users) {
            let html = '';
            if (users && users.length > 0) {
                users.forEach(function(user) {
                    html += '<div class="user-item" data-user-id="' + user.userId + '" data-username="' + (user.realName || user.username) + '">';
                    html += '<div class="user-name">' + (user.realName || user.username) + '</div>';
                    html += '<div class="user-info">';
                    html += '用户名: ' + user.username + '<br>';
                    if (user.email) {
                        html += '邮箱: ' + user.email + '<br>';
                    }
                    html += '下载次数: <span style="color: #1890ff; font-weight: bold;">' + user.downloadCount + '</span><br>';
                    html += '最后下载: ' + (user.lastDownloadTime || '无');
                    html += '</div>';
                    html += '</div>';
                });
            } else {
                html = '<div class="no-user-selected">暂无用户数据</div>';
            }
            $('#userList').html(html);
            
            // 绑定用户点击事件
            $('.user-item').click(function() {
                $('.user-item').removeClass('active');
                $(this).addClass('active');
                currentUserId = $(this).data('user-id');
                currentUserName = $(this).data('username');
                $('#configPanelTitle').text(currentUserName + ' 的配置管理');
                loadUserConfig();
            });
        }
        
        // 渲染用户分页
        function renderUserPagination() {
            if (userPageInfo.total > 0) {
                laypage.render({
                    elem: 'userPagination',
                    count: userPageInfo.total,
                    limit: userPageInfo.size,
                    curr: userPageInfo.current,
                    limits: [10, 20, 50],
                    layout: ['count', 'prev', 'page', 'next', 'limit'],
                    jump: function(obj, first) {
                        if (!first) {
                            userPageInfo.size = obj.limit;
                            loadUserList(obj.curr, userSearchKey);
                        }
                    }
                });
            } else {
                $('#userPagination').empty();
            }
        }
        
        // 用户搜索功能
        function searchUsers() {
            userSearchKey = $('#userSearchKey').val().trim();
            console.log("Searching users with key:", userSearchKey);
            
            userPageInfo.current = 1;
            currentUserId = null;
            currentUserName = '';
            $('#configPanelTitle').text('用户配置');
            $('#configContent').html('<div class="no-user-selected">请选择左侧用户进行配置</div>');
            
            loadUserList(1, userSearchKey);
        }
        
        // 清除用户搜索条件
        function clearUserSearch() {
            $('#userSearchKey').val('');
            userSearchKey = '';
            console.log("Clearing user search");
            
            userPageInfo.current = 1;
            currentUserId = null;
            currentUserName = '';
            $('#configPanelTitle').text('用户配置');
            $('#configContent').html('<div class="no-user-selected">请选择左侧用户进行配置</div>');
            
            loadUserList(1);
        }
        
        // 加载用户配置表单
        function loadUserConfig() {
            if (!currentUserId) {
                $('#configContent').html('<div class="no-user-selected">请选择左侧用户进行配置</div>');
                return;
            }
            
            let configHtml = `
                <div class="config-form-container">
                    <form class="layui-form" lay-filter="configForm">
                        <input type="hidden" name="userName" value="${currentUserId}">
                        
                        <!-- 下载配置 -->
                        <div class="config-section">
                            <div class="config-section-title">
                                <i class="layui-icon layui-icon-download-circle" style="color: #1890ff;"></i>
                                下载配置
                            </div>
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">下载次数</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="downloadLimit" placeholder="允许下载次数" class="layui-input" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">状态</label>
                                        <div class="layui-input-block">
                                            <input type="checkbox" name="downloadEnabled" title="启用下载" lay-skin="switch">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">开始时间</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="downloadStartTime" id="downloadStartTime" placeholder="请选择开始时间" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">结束时间</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="downloadEndTime" id="downloadEndTime" placeholder="请选择结束时间" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 预览配置 -->
                        <div class="config-section">
                            <div class="config-section-title">
                                <i class="layui-icon layui-icon-survey" style="color: #52c41a;"></i>
                                预览配置
                            </div>
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">预览次数</label>
                                        <div class="layui-input-block">
                                            <input type="number" name="previewLimit" placeholder="允许预览次数" class="layui-input" min="0">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">状态</label>
                                        <div class="layui-input-block">
                                            <input type="checkbox" name="previewEnabled" title="启用预览" lay-skin="switch">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">开始时间</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="previewStartTime" id="previewStartTime" placeholder="请选择开始时间" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">结束时间</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="previewEndTime" id="previewEndTime" placeholder="请选择结束时间" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 备注 -->
                        <div class="config-section">
                            <div class="config-section-title">
                                <i class="layui-icon layui-icon-note" style="color: #faad14;"></i>
                                备注信息
                            </div>
                            <div class="layui-form-item layui-form-text">
                                <div class="layui-input-block">
                                    <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="layui-form-item" style="text-align: center; margin-top: 30px;">
                            <button class="layui-btn layui-btn-lg" lay-submit lay-filter="saveConfig">
                                <i class="layui-icon layui-icon-ok"></i> 保存配置
                            </button>
                            <button type="button" class="layui-btn layui-btn-primary layui-btn-lg" id="loadCurrentConfig">
                                <i class="layui-icon layui-icon-refresh"></i> 加载当前配置
                            </button>
                        </div>
                    </form>
                </div>
            `;
            
            $('#configContent').html(configHtml);
            
            // 重新渲染表单
            form.render();
            
            // 初始化时间选择器
            laydate.render({
                elem: '#downloadStartTime',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss'
            });
            
            laydate.render({
                elem: '#downloadEndTime',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss'
            });
            
            laydate.render({
                elem: '#previewStartTime',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss'
            });
            
            laydate.render({
                elem: '#previewEndTime',
                type: 'datetime',
                format: 'yyyy-MM-dd HH:mm:ss'
            });
            
            // 绑定加载当前配置按钮事件
            $('#loadCurrentConfig').click(function() {
                loadCurrentUserConfig();
            });
            
            // 自动加载当前配置
            loadCurrentUserConfig();
        }
        
        // 加载用户当前配置
        function loadCurrentUserConfig() {
            if (!currentUserId) return;
            
            // 加载下载配置
            $.ajax({
                url: '/system/stdDownloadConfig/getConfigValue',
                type: 'GET',
                data: {
                    userName: currentUserId,
                    configType: '1'
                },
                success: function(res) {
                    if (res.success && res.data) {
                        $('input[name="downloadLimit"]').val(res.data);
                        $('input[name="downloadEnabled"]').prop('checked', true);
                        form.render('checkbox');
                    }
                }
            });
            
            // 加载预览配置
            $.ajax({
                url: '/system/stdDownloadConfig/getConfigValue',
                type: 'GET',
                data: {
                    userName: currentUserId,
                    configType: '2'
                },
                success: function(res) {
                    if (res.success && res.data) {
                        $('input[name="previewLimit"]').val(res.data);
                        $('input[name="previewEnabled"]').prop('checked', true);
                        form.render('checkbox');
                    }
                }
            });
        }
        
        // 表单提交
        form.on('submit(saveConfig)', function(data) {
            let formData = data.field;
            let promises = [];
            
            // 保存下载配置
            if (formData.downloadEnabled && formData.downloadLimit) {
                let downloadPromise = $.ajax({
                    url: '/system/stdDownloadConfig/saveOrUpdateByType',
                    type: 'POST',
                    data: {
                        userName: currentUserId,
                        configType: '1',
                        configValue: formData.downloadLimit,
                        startTime: formData.downloadStartTime,
                        endTime: formData.downloadEndTime,
                        remark: formData.remark
                    }
                });
                promises.push(downloadPromise);
            }
            
            // 保存预览配置
            if (formData.previewEnabled && formData.previewLimit) {
                let previewPromise = $.ajax({
                    url: '/system/stdDownloadConfig/saveOrUpdateByType',
                    type: 'POST',
                    data: {
                        userName: currentUserId,
                        configType: '2',
                        configValue: formData.previewLimit,
                        startTime: formData.previewStartTime,
                        endTime: formData.previewEndTime,
                        remark: formData.remark
                    }
                });
                promises.push(previewPromise);
            }
            
            if (promises.length === 0) {
                layer.msg('请至少启用一项配置', {icon: 2});
                return false;
            }
            
            Promise.all(promises).then(function(results) {
                let allSuccess = results.every(function(res) {
                    return res.success;
                });
                
                if (allSuccess) {
                    layer.msg('配置保存成功', {icon: 1});
                } else {
                    layer.msg('部分配置保存失败', {icon: 2});
                }
            }).catch(function(error) {
                layer.msg('保存失败', {icon: 2});
                console.error('Save error:', error);
            });
            
            return false;
        });
        
        // 初始化页面
        $(function() {
            console.log("Initializing user config page...");
            
            // 绑定用户搜索事件
            $('#userSearchBtn').click(function() {
                searchUsers();
            });
            
            $('#userClearBtn').click(function() {
                clearUserSearch();
            });
            
            // 绑定回车键搜索
            $('#userSearchKey').keypress(function(e) {
                if (e.which == 13) {
                    e.preventDefault();
                    searchUsers();
                }
            });
            
            // 加载用户列表
            loadUserList(1);
        });
    });
</script>
</body>
</html>
