<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增下载配置')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form class="layui-form" action="">
            <div class="layui-form-item">
                <label class="layui-form-label required">用户名</label>
                <div class="layui-input-block">
                    <input type="text" name="userName" lay-verify="required" placeholder="请输入用户名" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label required">配置类型</label>
                <div class="layui-input-block">
                    <select name="configType" lay-verify="required">
                        <option value="">请选择配置类型</option>
                        <option value="1">下载限制</option>
                        <option value="2">预览限制</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label required">配置值</label>
                <div class="layui-input-block">
                    <input type="text" name="configValue" lay-verify="required" placeholder="请输入配置值" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">开始时间</label>
                <div class="layui-input-block">
                    <input type="text" name="startTime" id="startTime" placeholder="请选择开始时间" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">结束时间</label>
                <div class="layui-input-block">
                    <input type="text" name="endTime" id="endTime" placeholder="请选择结束时间" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-block">
                    <input type="radio" name="status" value="1" title="启用" checked>
                    <input type="radio" name="status" value="0" title="禁用">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" placeholder="请输入备注信息" class="layui-textarea"></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="save">保存</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery', 'layer', 'laydate'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        let layer = layui.layer;
        let laydate = layui.laydate;
        let MODULE_PATH = "/system/stdDownloadConfig/";

        // 初始化时间选择器
        laydate.render({
            elem: '#startTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss'
        });

        laydate.render({
            elem: '#endTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss'
        });

        // 表单提交
        form.on('submit(save)', function (data) {
            $.ajax({
                url: MODULE_PATH + 'save',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data.field),
                success: function (res) {
                    if (res.success) {
                        layer.msg('保存成功', {icon: 1}, function () {
                            // 关闭当前弹窗
                            let index = parent.layer.getFrameIndex(window.name);
                            parent.layer.close(index);
                        });
                    } else {
                        layer.msg(res.msg || '保存失败', {icon: 2});
                    }
                },
                error: function () {
                    layer.msg('网络错误', {icon: 2});
                }
            });
            return false;
        });

        // 自定义验证规则
        form.verify({
            number: function(value) {
                if (value && (isNaN(value) || parseInt(value) < 0)) {
                    return '请输入有效的数字（大于等于0）';
                }
            }
        });
    });
</script>

<style>
    .required::before {
        content: "*";
        color: red;
        margin-right: 4px;
    }
    .layui-form-item {
        margin-bottom: 20px;
    }
    .layui-form-label {
        width: 120px;
    }
    .layui-input-block {
        margin-left: 150px;
    }
</style>
</body>
</html>
