<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('下载配置管理')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="simple-container">
                <div class="item" style="width:100%;">
                    <label class="sub-item search-label">用户名/备注：</label>
                    <div class="search-cmp">
                        <input class="layui-input sub-item" id="searchKey" name="simpleSearchKey" placeholder="输入用户名或备注搜索" type="text">
                        <button id="btnSimpleSearch" class="search-button">
                            <i class="layui-icon layui-icon-search"></i>
                        </button>
                    </div>
                    <div class="search-anchor-1">
                        <a href="javascript:void(0);" id="clearSimpleCon" style="display: none;" class="search-advance">清空</a>
                    </div>
                </div>
            </div>
            <div id="resultInfo"></div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="config-table" lay-filter="config-table"></table>
    </div>
</div>

<script id="config-toolbar" type="text/html">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm" lay-event="add">
            <i class="layui-icon layui-icon-add-1"></i>新增配置
        </button>
        <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="batchRemove">
            <i class="layui-icon layui-icon-delete"></i>批量删除
        </button>
    </div>
</script>

<script id="config-bar" type="text/html">
    <a class="c-anchor layui-icon layui-icon-edit" href="javascript:void(0);" title="编辑" lay-event="edit"></a>
    <a class="c-anchor layui-icon layui-icon-delete" href="javascript:void(0);" title="删除" lay-event="remove"></a>
</script>

<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'layer'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let layer = layui.layer;
        let MODULE_PATH = "/system/stdDownloadConfig/";

        // 表格列定义
        let cols = [
            [
                {type: 'checkbox', fixed: 'left'},
                {field: 'userName', title: '用户名', align: 'center', width: 150},
                {field: 'downloadLimit', title: '下载限制', align: 'center', width: 120},
                {field: 'previewLimit', title: '预览限制', align: 'center', width: 120},
                {field: 'status', title: '状态', align: 'center', width: 100, templet: function(d) {
                    return d.status === 1 ? '<span class="layui-badge layui-bg-green">启用</span>' : '<span class="layui-badge layui-bg-gray">禁用</span>';
                }},
                {field: 'remark', title: '备注', align: 'left', minWidth: 200},
                {field: 'createTime', title: '创建时间', align: 'center', width: 160},
                {field: 'updateTime', title: '更新时间', align: 'center', width: 160},
                {title: '操作', toolbar: '#config-bar', align: 'center', width: 120, fixed: 'right'}
            ]
        ];

        // 渲染表格
        table.render({
            elem: '#config-table',
            url: MODULE_PATH + 'data',
            page: true,
            cols: cols,
            skin: 'row,line',
            toolbar: '#config-toolbar',
            cellMinWidth: 80,
            defaultToolbar: ['filter', 'print', 'exports'],
            done: function(res, curr, count) {
                $("#resultInfo").html('共 ' + count + " 条配置记录");
            }
        });

        // 工具栏事件
        table.on('toolbar(config-table)', function(obj) {
            if (obj.event === 'add') {
                popup.open("新增配置", MODULE_PATH + "add", "800px", "600px");
            } else if (obj.event === 'batchRemove') {
                let checkStatus = table.checkStatus('config-table');
                if (checkStatus.data.length === 0) {
                    layer.msg('请选择要删除的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定删除选中的配置吗？', {icon: 3, title: '提示'}, function(index) {
                    let ids = checkStatus.data.map(item => item.id).join(',');
                    $.ajax({
                        url: MODULE_PATH + 'remove',
                        type: 'DELETE',
                        data: {ids: ids},
                        success: function(res) {
                            if (res.success) {
                                layer.msg('删除成功', {icon: 1});
                                table.reload('config-table');
                            } else {
                                layer.msg(res.msg || '删除失败', {icon: 2});
                            }
                        }
                    });
                    layer.close(index);
                });
            }
        });

        // 行工具事件
        table.on('tool(config-table)', function(obj) {
            if (obj.event === 'edit') {
                popup.open("编辑配置", MODULE_PATH + "edit?id=" + obj.data.id, "800px", "600px");
            } else if (obj.event === 'remove') {
                layer.confirm('确定删除该配置吗？', {icon: 3, title: '提示'}, function(index) {
                    $.ajax({
                        url: MODULE_PATH + 'remove',
                        type: 'DELETE',
                        data: {ids: obj.data.id},
                        success: function(res) {
                            if (res.success) {
                                layer.msg('删除成功', {icon: 1});
                                table.reload('config-table');
                            } else {
                                layer.msg(res.msg || '删除失败', {icon: 2});
                            }
                        }
                    });
                    layer.close(index);
                });
            }
        });

        // 搜索功能
        $("#btnSimpleSearch").click(function() {
            let searchKey = $('#searchKey').val();
            table.reload('config-table', {
                where: {simpleSearchKey: searchKey},
                page: {curr: 1}
            });
            if (searchKey) {
                $("#clearSimpleCon").show();
            }
        });

        // 清空搜索
        $("#clearSimpleCon").click(function() {
            $('#searchKey').val("");
            table.reload('config-table', {
                where: {simpleSearchKey: ""},
                page: {curr: 1}
            });
            $("#clearSimpleCon").hide();
        });

        // 回车搜索
        $('#searchKey').keypress(function(e) {
            if (e.which == 13) {
                $("#btnSimpleSearch").click();
            }
        });

        // 刷新表格
        window.refresh = function() {
            table.reload('config-table');
        };
    });
</script>

<style>
    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: flex-start;
        flex-direction: column;
    }
    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
        margin-bottom: 0.625rem;
    }
    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }
    .search-label {
        flex-grow: 2;
    }
    .simple-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #ddd;
        border-radius: 5px;
    }
    .simple-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .simple-container .search-cmp button:hover {
        cursor: pointer;
    }
    .simple-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .simple-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-anchor-1 {
        flex-grow: 5;
        padding-left: 10px;
        line-height: 38px;
    }
    #resultInfo {
        width: 100%;
        text-align: center;
        color: #999;
        margin-top: 10px;
    }
</style>
</body>
</html>
