<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('下载配置管理')"/>
    <style>
        .config-main-container {
            display: flex;
            height: calc(100vh - 120px);
            gap: 10px;
        }
        .user-panel {
            width: 350px;
            min-width: 350px;
            background: #fff;
            border-radius: 2px;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        }
        .config-panel {
            flex: 1;
            background: #fff;
            border-radius: 2px;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        }
        .panel-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            font-weight: bold;
            color: #333;
            display: flex;
            /*justify-content: space-between;*/
            align-items: center;
        }
        .panel-body {
            padding: 15px 20px;
            height: calc(100% - 60px);
            overflow: auto;
        }
        .user-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.3s;
            position: relative;
        }
        .user-item:hover {
            background-color: #f8f8f8;
        }
        .user-item.active {
            background-color: #e6f7ff;
            border-left: 3px solid #1890ff;
        }
        .user-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .user-info {
            font-size: 12px;
            color: #666;
            line-height: 1.5;
        }
        .user-config-status {
            position: absolute;
            top: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
        }
        .config-badge {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
        }
        .config-badge.download {
            background-color: #1890ff;
        }
        .no-user-selected {
            text-align: center;
            color: #999;
            padding: 50px 20px;
            font-size: 14px;
        }
        .user-search-container {
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        .user-search-container .layui-form-item {
            margin-bottom: 8px;
        }
        .user-search-row {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .user-search-input {
            flex: 1;
            min-width: 0;
        }
        .user-search-btn, .user-clear-btn {
            flex-shrink: 0;
            width: 40px;
            padding: 0;
            text-align: center;
        }
        .config-search-container {
            margin-bottom: 15px;
        }
        .config-search-row {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .config-search-input {
            flex: 1;
            min-width: 0;
        }
        .config-search-btn, .config-clear-btn {
            flex-shrink: 0;
            width: 40px;
            padding: 0;
            text-align: center;
        }
    </style>
</head>
<body class="pear-container">
<div class="config-main-container">
    <!-- 左侧用户列表 -->
    <div class="user-panel">
        <div class="panel-header">
            <span>用户列表</span>
            <button class="layui-btn layui-btn-xs layui-btn-primary" id="refreshUsers">
                <i class="layui-icon layui-icon-refresh"></i>
            </button>
        </div>
        <div class="panel-body">
            <!-- 用户搜索框 -->
            <div class="user-search-container">
                <form class="layui-form">
                    <div class="layui-form-item" style="margin-bottom: 10px;">
                        <div class="user-search-row">
                            <input type="text" id="userSearchKey" placeholder="搜索用户" class="layui-input user-search-input">
                            <button type="button" id="userSearchBtn" class="layui-btn layui-btn-sm layui-btn-primary user-search-btn">
                                <i class="layui-icon layui-icon-search"></i>
                            </button>
                            <button type="button" id="userClearBtn" class="layui-btn layui-btn-sm layui-btn-primary user-clear-btn">
                                <i class="layui-icon layui-icon-refresh"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <div id="userList">
                <div class="no-user-selected">加载中...</div>
            </div>
            <div id="userPagination" style="text-align: center; margin-top: 15px;"></div>
        </div>
    </div>

    <!-- 右侧配置列表 -->
    <div class="config-panel">
        <div class="panel-header" style="">
            <span id="configPanelTitle">配置列表</span>
            <div style="margin-left:20px;">
                <button class="layui-btn layui-btn-xs layui-btn-normal" id="addConfigForUser" style="display: none;">
                    <i class="layui-icon layui-icon-add-1"></i> 添加下载配额
                </button>
            </div>
        </div>
        <div class="panel-body">
            <!-- 配置搜索框 -->
            <div class="config-search-container">
                <form class="layui-form">
                    <div class="layui-form-item" style="margin-bottom: 10px;">
                        <div class="config-search-row">
                            <input type="text" id="configSearchKey" placeholder="搜索配置" class="layui-input config-search-input">
                            <button type="button" id="configSearchBtn" class="layui-btn layui-btn-sm layui-btn-primary config-search-btn">
                                <i class="layui-icon layui-icon-search"></i>
                            </button>
                            <button type="button" id="configClearBtn" class="layui-btn layui-btn-sm layui-btn-primary config-clear-btn">
                                <i class="layui-icon layui-icon-refresh"></i>
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <div id="configContent">
                <table id="config-table" lay-filter="config-table"></table>
                <div id="resultInfo" style="text-align: center; color: #999; margin-top: 10px;"></div>
            </div>
        </div>
    </div>
</div>

<script id="config-toolbar" type="text/html">
    <div class="layui-btn-container">
        <button class="layui-btn layui-btn-sm layui-btn-normal" lay-event="userConfig">
            <i class="layui-icon layui-icon-user"></i>用户配置管理
        </button>
        <button class="layui-btn layui-btn-sm layui-btn-warm" lay-event="quickConfig">
            <i class="layui-icon layui-icon-set"></i>快速配置
        </button>
        <button class="layui-btn layui-btn-sm" lay-event="add">
            <i class="layui-icon layui-icon-add-1"></i>新增配置
        </button>
        <button class="layui-btn layui-btn-sm layui-btn-danger" lay-event="batchRemove">
            <i class="layui-icon layui-icon-delete"></i>批量删除
        </button>
    </div>
</script>

<script id="config-bar" type="text/html">
    <a class="c-anchor layui-icon layui-icon-edit" href="javascript:void(0);" title="编辑" lay-event="edit"></a>
    <a class="c-anchor layui-icon layui-icon-delete" href="javascript:void(0);" title="删除" lay-event="remove"></a>
</script>

<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'form', 'jquery', 'layer', 'laypage'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let layer = layui.layer;
        let laypage = layui.laypage;
        let MODULE_PATH = "/system/stdDownloadConfig/";

        // 全局变量
        let currentUserId = null;
        let currentUserName = '';
        let userPageInfo = { current: 1, size: 10, total: 0 };
        let userSearchKey = '';
        let configSearchKey = '';

        // 表格列定义
        let cols = [
            [
                //{type: 'checkbox', fixed: 'left'},
                //{field: 'userName', title: '用户名', align: 'center', width: 120},
                // {field: 'configType', title: '配置类型', align: 'center', width: 120, templet: function(d) {
                //     if (d.configType === '1') {
                //         return '<span class="layui-badge layui-bg-blue">下载限制</span>';
                //     } else if (d.configType === '2') {
                //         return '<span class="layui-badge layui-bg-green">预览限制</span>';
                //     } else {
                //         return d.configType;
                //     }
                // }},
                {field: 'configValue', title: '允许下载次数', align: 'center', width: 100},
                {field: 'downloadCount', title: '已下载次数', align: 'center', width: 100},
                {field: 'startTime', title: '开始生效时间', align: 'center', width: 160},
                {field: 'endTime', title: '结束时间', align: 'center', width: 160},
                {field: 'status', title: '状态', align: 'center', width: 80, templet: function(d) {
                    if (d.status === 1) {
                        return '<span class="layui-badge layui-bg-green">启用</span>';
                    } else {
                        return '<span class="layui-badge layui-bg-red">禁用</span>';
                    }
                }},
                {field: 'remark', title: '备注', align: 'left', minWidth: 150},
                {field: 'createTime', title: '创建时间', align: 'center', width: 160},
                {field: 'updateTime', title: '更新时间', align: 'center', width: 160},
                {title: '操作', toolbar: '#config-bar', align: 'center', width: 120, fixed: 'right'}
            ]
        ];

        // 加载用户列表
        function loadUserList(page = 1, searchKey = null) {
            console.log("Loading user list, page:", page, "searchKey:", searchKey);

            if (searchKey === null) {
                searchKey = userSearchKey;
            }

            // 调用我们新创建的用户列表接口
            $.ajax({
                url: MODULE_PATH + 'users',
                type: 'GET',
                data: {
                    page: page,
                    limit: userPageInfo.size,
                    realName: searchKey,
                    username: searchKey
                },
                success: function(res) {
                    console.log("User list response:", res);
                    if (res.code === 0 && res.data) {
                        renderUserList(res.data);
                        userPageInfo.total = res.count || 0;
                        userPageInfo.current = page;
                        renderUserPagination();
                    } else {
                        $('#userList').html('<div class="no-user-selected">暂无用户数据</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error loading user list:", error);
                    // 如果用户接口不可用，使用下载用户接口作为备选
                    loadDownloadUserList(page, searchKey);
                }
            });
        }

        // 备选方案：加载有下载记录的用户列表
        function loadDownloadUserList(page = 1, searchKey = null) {
            $.ajax({
                url: '/stddownload/adminUsers',
                type: 'GET',
                data: {
                    page: page,
                    limit: userPageInfo.size,
                    searchKey: searchKey
                },
                success: function(res) {
                    console.log("Download user list response:", res);
                    if (res.code === 0 && res.data) {
                        renderUserList(res.data);
                        userPageInfo.total = res.count || 0;
                        userPageInfo.current = page;
                        renderUserPagination();
                    } else {
                        $('#userList').html('<div class="no-user-selected">暂无用户数据</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error loading download user list:", error);
                    $('#userList').html('<div class="no-user-selected">加载用户列表失败</div>');
                }
            });
        }

        // 渲染用户列表
        function renderUserList(users) {
            let html = '';
            if (users && users.length > 0) {
                users.forEach(function(user) {
                    // 兼容不同的用户数据结构
                    let userId = user.userId || user.id;
                    let userName = user.realName || user.username;
                    let userAccount = user.username || user.account;

                    // 确保userId是字符串类型
                    userId = String(userId);

                    html += '<div class="user-item" data-user-id="' + userId + '" data-username="' + userName + '">';
                    html += '<div class="user-config-status" id="status-' + userId.replace(/[^a-zA-Z0-9]/g, '_') + '"></div>';
                    html += '<div class="user-name">' + userName + '</div>';
                    html += '<div class="user-info">';
                    html += '用户名: ' + userAccount + '<br>';
                    if (user.email) {
                        html += '邮箱: ' + user.email + '<br>';
                    }
                    if (user.downloadCount !== undefined) {
                        html += '下载次数: <span style="color: #1890ff; font-weight: bold;">' + user.downloadCount + '</span>';
                    }
                    html += '</div>';
                    html += '</div>';
                });
            } else {
                html = '<div class="no-user-selected">暂无用户数据</div>';
            }
            $('#userList').html(html);

            // 绑定用户点击事件
            $('.user-item').click(function() {
                $('.user-item').removeClass('active');
                $(this).addClass('active');
                currentUserId = $(this).data('user-id');
                currentUserName = $(this).data('username');
                $('#configPanelTitle').text(currentUserName + ' 的配置');
                $('#addConfigForUser').show();
                loadUserConfigs();
                loadUserConfigStatus(currentUserId);
            });

            // 加载所有用户的配置状态
            users.forEach(function(user) {
                let userId = String(user.userId || user.id);
                loadUserConfigStatus(userId);
            });
        }

        // 加载用户配置状态
        function loadUserConfigStatus(userId) {
            let statusElementId = 'status-' + userId.replace(/[^a-zA-Z0-9]/g, '_');

            // 检查下载配置
            $.ajax({
                url: MODULE_PATH + 'getConfigValue',
                type: 'GET',
                data: {
                    userName: userId,
                    configType: '1'
                },
                success: function(res) {
                    if (res.success && res.data) {
                        $('#' + statusElementId).append('<span class="config-badge download" title="已配置下载权限"></span>');
                    }
                }
            });
        }

        // 渲染用户分页
        function renderUserPagination() {
            if (userPageInfo.total > 0) {
                laypage.render({
                    elem: 'userPagination',
                    count: userPageInfo.total,
                    limit: userPageInfo.size,
                    curr: userPageInfo.current,
                    limits: [10, 20, 50],
                    layout: ['count', 'prev', 'page', 'next', 'limit'],
                    jump: function(obj, first) {
                        if (!first) {
                            userPageInfo.size = obj.limit;
                            loadUserList(obj.curr, userSearchKey);
                        }
                    }
                });
            } else {
                $('#userPagination').empty();
            }
        }

        // 加载用户配置列表
        function loadUserConfigs() {
            if (!currentUserId) {
                table.render({
                    elem: '#config-table',
                    data: [],
                    cols: cols,
                    skin: 'row,line',
                    cellMinWidth: 80,
                    text: {
                        none: '请选择用户查看配置'
                    }
                });
                return;
            }

            // 构建搜索条件：用户名 + 配置搜索关键字
            let searchCondition = 'userName:' + currentUserId;
            if (configSearchKey && configSearchKey.trim()) {
                searchCondition += ' ' + configSearchKey.trim();
            }

            // 渲染表格
            table.render({
                elem: '#config-table',
                url: MODULE_PATH + 'data',
                page: true,
                cols: cols,
                skin: 'row,line',
                cellMinWidth: 80,
                where: {
                    simpleSearchKey: searchCondition
                },
                done: function(res, curr, count) {
                    $("#resultInfo").html('共 ' + count + " 条配置记录");
                }
            });
        }

        // 初始渲染空表格
        table.render({
            elem: '#config-table',
            data: [],
            cols: cols,
            skin: 'row,line',
            cellMinWidth: 80,
            text: {
                none: '请选择用户查看配置'
            }
        });

        // 工具栏事件
        table.on('toolbar(config-table)', function(obj) {
            if (obj.event === 'userConfig') {
                // 打开用户配置管理页面
                parent.layer.open({
                    type: 2,
                    title: "用户配置管理页面",
                    shade: 0.1,
                    area: ['1200px', '630px'],
                    content: MODULE_PATH + "userConfig"
                });
            } else if (obj.event === 'quickConfig') {
                parent.layer.open({
                    type: 2,
                    title: "快速配置用户权限",
                    shade: 0.3,
                    maxmin: true,
                    area: ['900px', '600px'],
                    content: MODULE_PATH + "quickConfig",
                    end: function() {
                        // 弹窗关闭后刷新配置列表
                        if (currentUserId) {
                            loadUserConfigs();
                            loadUserConfigStatus(currentUserId);
                        } else {
                            table.reload('config-table');
                        }
                    }
                });
            } else if (obj.event === 'add') {
                parent.layer.open({
                    type: 2,
                    title: "新增配置",
                    shade: 0.3,
                    maxmin: true,
                    area: ['800px', '600px'],
                    content: MODULE_PATH + "add",
                    end: function() {
                        // 弹窗关闭后刷新配置列表
                        if (currentUserId) {
                            loadUserConfigs();
                            loadUserConfigStatus(currentUserId);
                        } else {
                            table.reload('config-table');
                        }
                    }
                });
            } else if (obj.event === 'batchRemove') {
                let checkStatus = table.checkStatus('config-table');
                if (checkStatus.data.length === 0) {
                    layer.msg('请选择要删除的数据', {icon: 2});
                    return;
                }
                layer.confirm('确定删除选中的配置吗？', {icon: 3, title: '提示'}, function(index) {
                    let ids = checkStatus.data.map(item => item.id).join(',');
                    $.ajax({
                        url: MODULE_PATH + 'remove',
                        type: 'DELETE',
                        data: {ids: ids},
                        success: function(res) {
                            if (res.success) {
                                layer.msg('删除成功', {icon: 1});
                                // 刷新配置列表
                                if (currentUserId) {
                                    loadUserConfigs();
                                    loadUserConfigStatus(currentUserId);
                                } else {
                                    table.reload('config-table');
                                }
                            } else {
                                layer.msg(res.msg || '删除失败', {icon: 2});
                            }
                        }
                    });
                    layer.close(index);
                });
            }
        });

        // 行工具事件
        table.on('tool(config-table)', function(obj) {
            if (obj.event === 'edit') {
                parent.layer.open({
                    type: 2,
                    title: "编辑配置",
                    shade: 0.3,
                    maxmin: true,
                    area: ['800px', '600px'],
                    content: MODULE_PATH + "edit?id=" + obj.data.id,
                    end: function() {
                        // 弹窗关闭后刷新配置列表
                        if (currentUserId) {
                            loadUserConfigs();
                            loadUserConfigStatus(currentUserId);
                        } else {
                            table.reload('config-table');
                        }
                    }
                });
            } else if (obj.event === 'remove') {
                layer.confirm('确定删除该配置吗？', {icon: 3, title: '提示'}, function(index) {
                    $.ajax({
                        url: MODULE_PATH + 'remove',
                        type: 'DELETE',
                        data: {ids: obj.data.id},
                        success: function(res) {
                            if (res.success) {
                                layer.msg('删除成功', {icon: 1});
                                // 刷新配置列表
                                if (currentUserId) {
                                    loadUserConfigs();
                                    loadUserConfigStatus(currentUserId);
                                } else {
                                    table.reload('config-table');
                                }
                            } else {
                                layer.msg(res.msg || '删除失败', {icon: 2});
                            }
                        }
                    });
                    layer.close(index);
                });
            }
        });

        // 用户搜索功能
        function searchUsers() {
            userSearchKey = $('#userSearchKey').val().trim();
            console.log("Searching users with key:", userSearchKey);

            userPageInfo.current = 1;
            currentUserId = null;
            currentUserName = '';
            $('#configPanelTitle').text('配置列表');
            $('#addConfigForUser').hide();

            // 重新渲染空表格
            table.render({
                elem: '#config-table',
                data: [],
                cols: cols,
                skin: 'row,line',
                cellMinWidth: 80,
                text: {
                    none: '请选择用户查看配置'
                }
            });

            loadUserList(1, userSearchKey);
        }

        // 清除用户搜索条件
        function clearUserSearch() {
            $('#userSearchKey').val('');
            userSearchKey = '';
            console.log("Clearing user search");

            userPageInfo.current = 1;
            currentUserId = null;
            currentUserName = '';
            $('#configPanelTitle').text('配置列表');
            $('#addConfigForUser').hide();

            // 重新渲染空表格
            table.render({
                elem: '#config-table',
                data: [],
                cols: cols,
                skin: 'row,line',
                cellMinWidth: 80,
                text: {
                    none: '请选择用户查看配置'
                }
            });

            loadUserList(1);
        }

        // 配置搜索功能
        function searchConfigs() {
            configSearchKey = $('#configSearchKey').val().trim();
            console.log("Searching configs with key:", configSearchKey);

            if (currentUserId) {
                loadUserConfigs();
            }
        }

        // 清除配置搜索条件
        function clearConfigSearch() {
            $('#configSearchKey').val('');
            configSearchKey = '';
            console.log("Clearing config search");

            if (currentUserId) {
                loadUserConfigs();
            }
        }

        // 刷新表格
        window.refresh = function() {
            if (currentUserId) {
                loadUserConfigs();
            }
        };

        // 初始化页面
        $(function() {
            console.log("Initializing config management page...");

            // 绑定用户搜索事件
            $('#userSearchBtn').click(function() {
                searchUsers();
            });

            $('#userClearBtn').click(function() {
                clearUserSearch();
            });

            // 绑定配置搜索事件
            $('#configSearchBtn').click(function() {
                searchConfigs();
            });

            $('#configClearBtn').click(function() {
                clearConfigSearch();
            });

            // 绑定刷新用户列表事件
            $('#refreshUsers').click(function() {
                loadUserList(userPageInfo.current, userSearchKey);
            });

            // 绑定为用户添加配置事件
            $('#addConfigForUser').click(function() {
                if (!currentUserId) {
                    layer.msg('请先选择用户', {icon: 2});
                    return;
                }

                // 使用 parent.layer.open 打开快速配置弹窗，通过URL参数传递用户信息
                let url = MODULE_PATH + "quickConfig?userId=" + encodeURIComponent(currentUserId) + "&userName=" + encodeURIComponent(currentUserName);
                let index = parent.layer.open({
                    type: 2,
                    title: "为 " + currentUserName + " 添加配置",
                    shade: 0.3,
                    maxmin: true,
                    area: ['900px', '600px'],
                    content: url,
                    end: function() {
                        // 弹窗关闭后刷新配置列表
                        if (currentUserId) {
                            loadUserConfigs();
                            loadUserConfigStatus(currentUserId);
                        }
                    }
                });
            });

            // 绑定回车键搜索
            $('#userSearchKey').keypress(function(e) {
                if (e.which == 13) {
                    e.preventDefault();
                    searchUsers();
                }
            });

            $('#configSearchKey').keypress(function(e) {
                if (e.which == 13) {
                    e.preventDefault();
                    searchConfigs();
                }
            });

            // 加载用户列表
            loadUserList(1);
        });
    });
</script>


</body>
</html>
