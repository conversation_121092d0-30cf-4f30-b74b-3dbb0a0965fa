<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('标准列表')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body" style="padding:10px 10px 5px 10px;">
        <form action="" class="layui-form">
            <div class="search-container">
                <div class="item">
                    <label class="sub-item">标准号：</label>
                    <input class="layui-input sub-item" name="stdNo" placeholder="" type="text">
                </div>
                <div class="item">
                    <label class="sub-item">标准名称：</label>
                    <input class="layui-input sub-item" name="stdOrgName" placeholder="" type="text">
                </div>
                <div class="item">
                    <label class="sub-item">主要内容：</label>
                    <input class="layui-input sub-item" name="primaryCoverage" placeholder="" type="text">
                </div>
                <div class="item">
                    <label class="sub-item">起草单位：</label>
                    <input class="layui-input sub-item" name="draftingUnit" placeholder="" type="text" >
                </div>
                <div class="item">
                    <label class="sub-item">起草人：</label>
                    <input class="layui-input sub-item" name="drafter" placeholder="" type="text">
                </div>
                <div class="item">
                    <button class="pear-btn pear-btn-md pear-btn-primary sub-item" lay-filter="role-query" lay-submit style="margin-left:40px;margin-top:0px;">
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <button class="pear-btn pear-btn-md sub-item" type="reset" style="margin-left:30px;margin-top:0px;">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                </div>
            </div>
            <div id="resultInfo"></div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="role-table" lay-filter="role-table"></table>
    </div>
</div>
</body>

<script id="role-toolbar" type="text/html">
    <button  class="pear-btn pear-btn-primary pear-btn-md custom-blue" style="margin-left:15px;" lay-event="downloadTemplate">
        <i class="layui-icon layui-icon-download-circle"></i>
        下载模板
    </button>
    <!--  <button  class="pear-btn pear-btn-primary pear-btn-md"  id="uploadBtn">
          <i class="layui-icon layui-icon-upload"></i>
          导入
      </button>-->
    <div id="uploadElem" style="display: inline-block;"></div>
    <button  class="pear-btn pear-btn-primary pear-btn-md custom-blue" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        新增
    </button>
    <button  class="pear-btn pear-btn-primary pear-btn-md custom-dark-blue" style="margin-left:15px;" lay-event="batchUpdateDoc">
        <i class="layui-icon layui-icon-password"></i>
        明转密(全部)
    </button>
    <button  class="pear-btn pear-btn-primary pear-btn-md custom-dark-green" lay-event="statisticsPdfCount">
        <i class="layui-icon layui-icon-component"></i>
        文本数送显
    </button>
    <button  class="pear-btn pear-btn-primary pear-btn-md custom-green"  style="margin-left:15px;" lay-event="updateIndex">
        <i class="layui-icon layui-icon-note"></i>
        创建索引
    </button>
    <button  class="pear-btn pear-btn-primary pear-btn-md custom-green" lay-event="updateAll">
        <i class="layui-icon layui-icon-note"></i>
        全部索引
    </button>
    <button  class="pear-btn pear-btn-danger pear-btn-md custom-red" style="margin-left:15px;" lay-event="delete">
        <i class="layui-icon layui-icon-delete"></i>
        删除数据
    </button>
    <button  class="pear-btn pear-btn-danger pear-btn-md custom-red"  lay-event="delete-all">
        <i class="layui-icon layui-icon-delete"></i>
        全部删除
    </button>
</script>
<!--
<button
        class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
    <i class="layui-icon layui-icon-add-1"></i>
    新增
</button>
<button  class="pear-btn pear-btn-md"
         lay-event="batchRemove">
    <i class="layui-icon layui-icon-delete"></i>
    删除
</button> <a class="c-anchor" href="javascript:void(0);" lay-event="detail">详情</a>-->
<script id="role-bar" type="text/html">
    <a class="c-anchor iconfont icon-bianji1" href="javascript:void(0);" lay-event="edit" title="编辑"></a>
    <a class="c-anchor  iconfont icon-chakan" href="javascript:void(0);" lay-event="onlineRead" title="在线查看"></a>
    <a href="javascript:void(0);" lay-event="download" class="c-anchor iconfont icon-xiazaidaoru is-show-{{d.pdfIsExists}}" title="下载"></a>
    <a href="javascript:void(0);" lay-event="remove" class="c-anchor layui-icon layui-icon-delete" title="删除"></a>
</script>
<!--
<button
        class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>
</button>
<button
        class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>
</button>-->
<div style="height:1px;overflow: hidden;">
<div id="uploadProcess" class="layui-progress layui-progress-big" lay-showPercent="yes" lay-filter="progressBar">
    <div class="layui-progress-bar layui-bg-green" lay-percent="0%"></div>
</div>
</div>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common','upload','layer','element'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;

        let MODULE_PATH = "/foreignstd/";

        let cols = [
            [
                {type: 'checkbox',width:'5%'},
                {field:'number', title: '序号' , align:'center',type:'numbers',width:'5%'},
                {title: '标准类别', field: 'stdClass', align: 'center',width:'8%'},
                {title: '标准号', field: 'stdNo', align: 'left',width:'12%'},
                {title: '标准名称(原文)', field: 'stdOrgName', align: 'left',width:'20%'},
                {title: '标准名称(对应中文)', field: 'stdChineseName', align: 'left',width:'20%'},
                {title: '实施日期', field: 'implementationDate', align: 'left',width:'10%'},
                {title: '标准状态', field: 'stdStatus', align: 'center',width:'8%'},
                {title: '操作', toolbar: '#role-bar', align: 'center',width:'12%'}
            ]
        ]
     /*   //多文件上传
        var uploadListIns = upload.render({
            elem: '#uploadBtn'     // 选择文件按钮
            ,elemList: $('#demoList') //列表元素对象
            ,url: '/standard/file-upload' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
            ,accept: 'file'  //指定允许上传时校验的文件类型，可选值有：images（图片）、file（所有文件）、video（视频）、audio（音频）
            ,multiple: false  //是否允许多文件上传。设置 true即可开启。不支持ie8/9
            ,number: 5   //设置同时可上传的文件数量，一般配合 multiple 参数出现; 0 不限制
            ,auto: true  //是否选完文件后自动上传。如果设定 false，那么需要设置 bindAction 参数来指向一个其它按钮提交上传
            ,bindAction: '#testListAction'  //指向一个按钮触发上传，一般配合 auto: false 来使用
            ,choose: function(obj){   //选择文件后的回调函数。返回一个object参数
            }
            ,done: function(res, index, upload){ //成功的回调
                var that = this;
                if(res.success){ //上传成功
                    popup.success("导入完成！", function () {
                        table.reload('role-table');
                    });
                    //delete this.files[index]; //删除文件队列已经上传成功的文件
                    return;
                }else {
                    popup.failure(res.message);
                }
               // window.setUpload();
            }
            ,allDone: function(obj){ //多文件上传完毕后的状态回调
                console.log(obj)
            }
            ,error: function(index, upload){ //错误回调
                popup.failure("导入失败！");
            }
            ,progress: function(n, elem, e, index){ //注意：index 参数为 layui 2.6.6 新增
                element.progress('progress-demo-'+ index, n + '%'); //执行进度条。n 即为返回的进度百分比
            }
        });*/

        table.render({
            elem: '#role-table',
            url: MODULE_PATH + 'data',
            page: true,
            limit:50,
            limits:[10,15,20,30,50],
            cols: cols,
            skin: 'row,line',
            toolbar: '#role-toolbar',
            cellMinWidth: 'auto',
            defaultToolbar: [{
                title: '刷新',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print', 'exports'],
            done: function (res, curr, count) {
                $("#resultInfo").html('共检索到'+res.count+"个标准，"+res.extData+"个可下载");
                var $data = $('div[lay-id="role-table"]').find('.layui-table-body').find('tr').eq(0).find('td');
                var $head = $('div[lay-id="role-table"]').find('.layui-table-header').find('tr').eq(0).find('th');
                for (var i = 0; i < $data.length; i++) {
                    var l1 = $data.eq(i).find('div').width();
                    var l2 = $head.eq(i).find('div').width();
                    if (l1 > l2) {
                        $head.eq(i).find('div').width(l1);
                    } else if(l2>l1) {
                        $data.eq(i).find('div').width(l2);
                    }
                }
            }
        });

        table.on('tool(role-table)', function (obj) {
            if (obj.event === 'remove') {
                window.remove(obj);
            }
            else if (obj.event === 'detail') {
                window.detail(obj);
            }
            else if (obj.event === 'edit') {
                window.edit(obj);
            }
            else if(obj.event==='download')
            {
                window.download(obj);
            }
            else if(obj.event==='onlineRead')
            {
                window.onlineRead(obj);
            }
            /*else if (obj.event === 'power') {
                window.power(obj);
            } else if (obj.event === 'dept') {
                window.dept(obj);
            } */
        });

        table.on('toolbar(role-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                window.refresh();
            } else if (obj.event === 'batchRemove') {
                window.batchRemove(obj);
            }
            else if (obj.event === 'updateIndex') {
                window.updateIndex(obj);
            }
            else if (obj.event === 'updateAll') {
                window.updateAll(obj);
            }
            else if (obj.event === 'delete') {
                window.deleteRecordAndIndex(obj);
            }
            else if (obj.event === 'delete-all') {
                window.deleteAll(obj);
            }
           /* else if (obj.event === 'uploadExcel') {
                window.setUpload();
            }*/
            else if(obj.event=='downloadTemplate')
            {
                window.downloadTemplate();
            }
            else if(obj.event=='batchUpdateDoc')
            {
                window.batchUpdateDocFiles();
            }
            else if(obj.event=='statisticsPdfCount')
            {
                window.statisticsPdfCount();
            }
   /*         else if(obj.event=='batchDecryptDoc')
            {
                window.batchDecryptDoc();
            }*/
        });

        form.on('submit(role-query)', function (data) {
            console.log(data);
            table.reload('role-table', {where: data.field})
            window.setUpload();
            return false;
        });

        window.add = function () {
            parent.layer.open({
                type: 2,
                title: '新增标准',
                shade: 0.1,
                area: ['1000px', '600px'],
                content: MODULE_PATH + 'add'
            });
        }

        window.edit = function (obj) {
            layer.open({
                type: 2,
                title: '【'+obj.data['stdOrgName']+'】编辑',
                shade: 0.1,
                area: ['1000px', '600px'],
                content: MODULE_PATH + 'edit?stdID=' + obj.data['id']
            });
        }

        window.detail=function(obj){
            layer.open({
                type: 2,
                title: '【'+obj.data['stdOrgName']+'】',
                shade: 0.1,
                area: ['1000px', '500px'],
                content: MODULE_PATH + 'detail?stdID=' + obj.data['id']
            });
            window.writelog('Standard_OverView',obj.data["stdNo"],obj.data["stdOrgName"],"","","");
        }

        window.statisticsPdfCount=function(){
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "testpdfstatus",
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("统计完毕!", {icon: 1, time: 2000});
                        window.setUpload();
                    } else {
                        layer.msg("操作失败:"+result.message, {icon: 2, time: 2000});
                        window.setUpload();
                    }
                }
            })
        }

        window.batchUpdateDocFiles=function(){
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "batchUpdateDoc",
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("更新成功!", {icon: 1, time: 2000});
                        window.setUpload();
                    } else {
                        layer.msg("更新失败:"+result.message, {icon: 2, time: 2000});
                        window.setUpload();
                    }
                }
            })
        }

     /*   window.batchDecryptDoc=function()
        {
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "batchDecryptDoc",
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("更新成功!", {icon: 1, time: 2000});
                        window.setUpload();
                    } else {
                        layer.msg("更新失败:"+result.message, {icon: 2, time: 2000});
                        window.setUpload();
                    }
                }
            })
        }*/
        //downloadTemplateBtn
        window.downloadTemplate=function(){
            window.open("/source/downloadStandardTemplate");
        }

        //刷新全部索引
        window.updateAll = function (obj) {
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "updateallsolrindex",
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("更新成功!", {icon: 1, time: 2000});
                    } else {
                        layer.msg("更新失败:"+result.message, {icon: 2, time: 2000});
                    }
                }
            })
        }

        //刷新指定索引
        window.updateIndex = function (obj) {
            let ids = common.checkField(obj, 'id');
            if (common.isEmpty(ids)) {
                popup.warning("请选择要更新的记录！");
                return false;
            }
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "updatesolrindex",
                dataType: 'json',
                type: 'post',
                data: 'ids='+ids,
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("更新成功!", {icon: 1, time: 2000});
                    } else {
                        layer.msg("更新失败:"+result.message, {icon: 2, time: 2000});
                    }
                }
            })
        }

        window.fetchUploadStatus=function(){
            $.ajax({
                url: "/chinastd/uploadProcess",
                dataType: 'json',
                type: 'get',
                data: {customSessionId:uploadSessionId},
                success: function (result) {
                    console.log(result);
                    if (result.success) {
                        //  $("#uploadProcess_dealed").html(result.data.dealedCount);
                        //  $("#uploadProcess_total").html(result.data.totalCount);
                        if(result.data.dealedCount*1>0) {
                            var percent = Math.round(result.data.dealedCount * 100.0 / result.data.totalCount * 1.0) + '%' //获取进度百分比
                            element.progress('progressBar', percent); //可配合 layui 进度条元素使用
                            // console.log(elem); //得到当前触发的元素 DOM 对象。可通过该元素定义的属性值匹配到对应的进度条。
                            // console.log(res); //得到 progress 响应信息
                            //  console.log(index); //得到当前上传文件的索引，多文件上传时的进度条控制，如：
                            //  element.progress('progressBar-'+ index, n + '%'); //进度条
                            $(".layui-layer-title").html('上传中[已处理' + result.data.dealedCount + '条数据/共' + result.data.totalCount + '条数据]');
                            if (result.data.dealedCount >= result.data.totalCount) {
                                window.clearInterval(uploadIntervalHandle);
                                layer.open({
                                    type: 1,
                                    title: '上传完成',
                                    shade: 0.1,
                                    area: ['500px', '300px'],
                                    content: '上传已完成，共导入' + result.data.totalCount + '条数据',
                                    end: function () {
                                        layer.closeAll();
                                        element.progress('progressBar', "0%");
                                    }
                                });
                            }
                        }

                    } else {
                        window.clearInterval(uploadIntervalHandle);
                        layer.open({
                            type: 1,
                            title: '发生错误',
                            shade: 0.1,
                            area: ['500px', '300px'],
                            content: result.data.err,
                            end : function() {
                                layer.closeAll();
                            }
                        });
                    }
                }
            })
        }
        window.getPercent=function(num) {
            //5.四会五入保留2位小数《不够位救，则用昔补)
            var result = parseFloat(num);
            if (isNaN(result)) {
                return 0;
            }
            result = Math.round(num * 100) / 100;
            var s_x = result.toString(); //将数学转热为字符串
            var pos_decimal = s_x.index0f('.');//小教点的家引值
            //pos decimal=-L 自动补@(pos decimal <)
            if (pos_decimal < 0) {
                pos_decimal = s_x.length;
                s_x += '.';
            }
            //当教字的长度《小教点宝引2时
            while (s_x.length <= pos_decimal + 2) {
                s_x += '0';
            }
            return s_x;
        }
        window.setUpload=function(){
            $('#uploadElem').html(`<div id="uploadBox"><button type="button" class="pear-btn pear-btn-primary pear-btn-md custom-blue" id="uploadBtn"><i class="layui-icon">&#xe67c;</i> 数据导入</button><span id="selected"></span></div>`);
            uploadSessionId=Date.parse(new Date());
            upload.render({
                elem: '#uploadBtn'     // 选择文件按钮
                ,elemList: $('#demoList') //列表元素对象
                ,data:{customSessionId:uploadSessionId}
                ,url:  MODULE_PATH+ 'file-upload' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
                ,accept: 'file'  //指定允许上传时校验的文件类型，可选值有：images（图片）、file（所有文件）、video（视频）、audio（音频）
                ,multiple: false  //是否允许多文件上传。设置 true即可开启。不支持ie8/9
                ,number: 1   //设置同时可上传的文件数量，一般配合 multiple 参数出现; 0 不限制
                ,auto: true  //是否选完文件后自动上传。如果设定 false，那么需要设置 bindAction 参数来指向一个其它按钮提交上传
                ,bindAction: '#testListAction'  //指向一个按钮触发上传，一般配合 auto: false 来使用
                ,choose: function(obj){   //选择文件后的回调函数。返回一个object参数
                },
                before: function(obj){ //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
                    layer.open({
                        type: 1,
                        title: '上传中...',
                        shade: 0.1,
                        area: ['500px', '100px'],
                        content: $("#uploadProcess"),
                        end : function() {
                            window.clearInterval(uploadIntervalHandle);
                        }
                    });
                    uploadIntervalHandle=window.setInterval("window.fetchUploadStatus()",1000);
                }
                ,done: function(res, index, upload){ //成功的回调
                    var that = this;
                    if(res.success){ //上传成功
                        layer.msg("导入完成！", {icon: 1, time: 1500}, function () {
                            table.reload('role-table');
                            window.setUpload();
                        });
                        //delete this.files[index]; //删除文件队列已经上传成功的文件
                        return;
                    }else {
                        layer.msg("导入失败！"+res.message, {icon: 2, time: 10000}, function () {
                            table.reload('role-table');
                            window.setUpload();
                        });
                    }
                }
                ,allDone: function(obj){ //多文件上传完毕后的状态回调
                    console.log(obj)
                    window.setUpload();
                }
                ,error: function(index, upload){ //错误回调
                    popup.failure("导入失败！");
                    window.setUpload();
                    window.clearInterval(uploadIntervalHandle);
                }
                ,progress: function(n, elem, e, index){ //注意：index 参数为 layui 2.6.6 新增
                    /*var percent = n + '%' //获取进度百分比
                    element.progress('progressBar', percent); //可配合 layui 进度条元素使用
                    console.log(elem); //得到当前触发的元素 DOM 对象。可通过该元素定义的属性值匹配到对应的进度条。
                    console.log(res); //得到 progress 响应信息
                    console.log(index); //得到当前上传文件的索引，多文件上传时的进度条控制，如：
                    element.progress('progressBar-'+ index, n + '%'); //进度条*/
                }
            });
        }

        window.setUpload();

        //删除记录 并删除指定索引
        window.deleteRecordAndIndex = function (obj) {
            let ids = common.checkField(obj, 'id');
            if (common.isEmpty(ids)) {
                popup.warning("请选择要删除的记录！");
                return false;
            }
            layer.confirm('确定要删除选中记录吗', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "deleteRecordAndIndex",
                    dataType: 'json',
                    type: 'post',
                    data: {data: ids},
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            layer.msg("删除成功!", {icon: 1, time: 2000});
                            table.reload('role-table');
                            window.setUpload();
                        } else {
                            layer.msg("删除失败:" + result.message, {icon: 2, time: 2000});
                        }
                    }
                });
            });
        }

        //删除记录 并删除指定索引
        window.deleteAll = function (obj) {
            layer.confirm('确定要删除选所有标准吗', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "deleteAll",
                    dataType: 'json',
                    type: 'post',
                    data: {},
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            layer.msg("删除成功!", {icon: 1, time: 2000});
                            table.reload('role-table');
                            window.setUpload();
                        } else {
                            layer.msg("删除失败:" + result.message, {icon: 2, time: 2000});
                        }
                    }
                });
            });
        }

        window.remove = function (obj) {
            var ids=obj.data['id'];
            layer.confirm('确定要删除选中记录吗', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "deleteRecordAndIndex",
                    dataType: 'json',
                    type: 'post',
                    data: {data: ids},
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            layer.msg("删除成功!", {icon: 1, time: 2000});
                            table.reload('role-table');
                            window.setUpload();
                        } else {
                            layer.msg("删除失败:" + result.message, {icon: 2, time: 2000});
                        }
                    }
                });
            });
        }

        window.batchRemove = function (obj) {
            let ids = common.checkField(obj, 'roleId');
            if (common.isEmpty(ids)) {
                popup.warning("未选中数据");
                return false;
            }
            layer.confirm('确定要删除选中的数据吗', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "batchRemove/" + ids,
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            popup.success(result.msg, function () {
                                table.reload('role-table');
                                window.setUpload();
                            });
                        } else {
                            popup.failure(result.msg);
                        }
                    }
                })
            });
        }

        window.refresh = function () {
            table.reload('role-table');
            window.setUpload();
        }
        //清空文件列表
        $('#clearList').click(function(){
            $('#demoList').html('');
        });

    });
</script>
<style>
    .search-container
    {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }
    .search-container .item
    {
        display: flex;
        flex-direction: row;
        margin-top:0.625rem;
    }
    .search-container .item .sub-item button
    {
        margin-top:10px;
    }
    .search-container .item label
    {
        min-width: 80px;
        padding: 8px 1px 8px 10px;
        text-align: right;
    }
    .layui-table-tool
    {
        height:70px !important;
        line-height:30px !important;
        padding:10px 0 0 10px !important;
        min-height:0px !important;
    }
    .layui-card-body .layui-form
    {
        margin-top:0px !important;
    }
    .layui-form-item .layui-form-label,.layui-form-item  .layui-input-inline{
        margin-bottom:7px;
    }
    .layui-form-item{
        padding-bottom:0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow:auto;
        white-space: normal;
    }
    #resultInfo{
        width:300px;
        text-align: center;
        color:#999;
        margin:10px auto 0;
    }
    .layui-table thead th {
        font-weight: bold;
    }
 /*   .c-anchor
    {
        text-decoration: underline;
        color: #666;
        font-size: 12px;
        display: inline-block;
    }*/
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .is-show-0{
        display: none;
    }
    .layui-table-page
    {
        text-align: center !important;
    }
</style>
</html>
