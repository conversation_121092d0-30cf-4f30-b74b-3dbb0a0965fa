<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('标准查询')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body" style="background-color:#f9f9f9;">
       <!-- <form action="javascript:void(0);" class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">标准名称</label>
                <div class="layui-input-inline">
                    <input class="layui-input" id="txtStdName" placeholder="" type="text">
                </div>
                <label class="layui-form-label">标准号</label>
                <div class="layui-input-inline">
                    <input class="layui-input" id="txtStdNo" placeholder="" type="text">
                </div> <label class="layui-form-label">起草单位</label>
                <div class="layui-input-inline">
                    <input class="layui-input" id="txtDraftingUnit" placeholder="" type="text">
                </div> <label class="layui-form-label">起草人</label>
                <div class="layui-input-inline">
                    <input class="layui-input" id="txtDrafter" placeholder="" type="text">
                </div> <label class="layui-form-label">概要内容</label>
                <div class="layui-input-inline">
                    <input class="layui-input" id="txtPrimary" placeholder="" type="text">
                </div>
                <button class="pear-btn pear-btn-md pear-btn-primary" id="btnSearch">
                    <i class="layui-icon layui-icon-search"></i>
                    查询
                </button>
                <button class="pear-btn pear-btn-md" type="reset">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重置
                </button>
            </div>
        </form>-->
        <div>
            <div id="summary"></div>
            <ul id="search_ul">

            </ul>
        </div>
        <table id="role-table" lay-filter="role-table"></table>
    </div>
</div>
</body>

<script id="role-bar" type="text/html">
    <button
            class="pear-btn pear-btn-primary pear-btn-sm" lay-event="download"><i class="layui-icon layui-icon-download-circle"></i>
    </button>
</script>

<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;

        let MODULE_PATH = "/searchdb/";

        $.ajax({
            url:"/standard/list",
            data:{},
            type:"get",
            dataType:"json",
            headers : {'Content-Type' : 'application/json;charset=utf-8'}, //接口json格式
            success:function(data){
                console.log(data);
                $("#search_ul").empty();
                for(var i=0;i<data.length;i++)
                {
                    var line=data[i];
                    $("#search_ul").append('<li class="std-title"><span><a class="pdf-title" title="点击下载" target="_blank" href="/source/download?fileName='+encodeURIComponent(line.pdfFileName)+'">'+line.stdChineseName+'</a></span><span style="color:gray;font-size:13px;padding-left:10px;">[<a target="_blank" style="color:#bbb;" href="/source/download?fileName='+ encodeURIComponent(line.pdfFileName)+'">下载</a>]</span></li>')
                    $("#search_ul").append('<li class="std-summary"><span class="item-title">[标准号]</span><span>'+line.stdNo+'</span><span class="item-title">[密级]</span><span>'+line.securityClass+'</span><span class="item-title">[起草人]</span><span>'+line.drafter+' '+line.draftingUnit+'</span><span class="item-title">[发布时间]</span><span>'+line.pubDate+'</span></li>')
                    $("#search_ul").append('<li class="std-primary">'+(line.primaryCoverage?line.primaryCoverage:'')+'</li>')
                    $("#search_ul").append('<li class="std-split"></li>')
                }
            },
            error:function(data){
                layer.alert(JSON.stringify(data), {
                    title: data
                });
            }
        });

        form.on('submit(role-query)', function (data) {
            table.reload('role-table', {where: data.field})
            return false;
        });
        window.refresh = function () {
            table.reload('role-table');
        }

        //绑定检索时间
        $(document).on('click', '#btnSearch', function(data) {
            var stdName=$('#txtStdName').val();
            var stdNo=$('#txtStdNo').val();
            var draftingUnit=$('#txtDraftingUnit').val();
            var drafter=$('#txtDrafter').val();
            var primary=$('#txtPrimary').val();

            $.ajax({
                url:"/standard/list",
                data:{stdChineseName:stdName,stdNo:stdNo,draftingUnit:draftingUnit,drafter:drafter,primaryCoverage:primary},
                type:"get",
                dataType:"json",
                headers : {'Content-Type' : 'application/json;charset=utf-8'}, //接口json格式
                success:function(data){
                    console.log(data);
                    $("#search_ul").empty();
                    $("#summary").text("共搜索到"+((data && data.length)?data.length:0)+"个标准");
                    for(var i=0;i<data.length;i++) {
                        var line = data[i];
                            $("#search_ul").append('<li class="std-title"><span><a class="pdf-title" title="点击下载" target="_blank" href="/source/download?fileName='+encodeURIComponent(line.pdfFileName)+'">'+line.stdChineseName+'</a></span><span style="color:gray;font-size:13px;padding-left:10px;">[<a target="_blank" style="color:#bbb;" href="/source/download?fileName='+ encodeURIComponent(line.pdfFileName)+'">下载</a>]</span></li>')
                            $("#search_ul").append('<li class="std-summary"><span class="item-title">[标准号]</span><span>'+line.stdNo+'</span><span class="item-title">[密级]</span><span>'+line.securityClass+'</span><span class="item-title">[起草人]</span><span>'+line.drafter+' '+line.draftingUnit+'</span><span class="item-title">[发布时间]</span><span>'+line.pubDate+'</span></li>')
                            $("#search_ul").append('<li class="std-primary">'+(line.primaryCoverage?line.primaryCoverage:'')+'</li>')
                            $("#search_ul").append('<li class="std-split"></li>')
                    }
                },
                error:function(data){
                    layer.alert(JSON.stringify(data), {
                        title: data
                    });
                }
            });
        });
    })
</script>
<style>
    .std-summary
    {
        padding-left:10px;
    }
    .layui-form-label
    {
        padding:9px !important;
        width:60px !important;
    }
    .pdf-title{
        font-family: 宋体;
        font-weight: bold;
        font-size:17px;
    }
    .item-title{
        color:#eee;
        font-size:13px;
        padding:0 15px 0 30px;
        font-style: normal;
    }
    .std-summary span{
        color:#999;
        font-size:14px;
    }
    .std-summary span:first-child{
        padding-left:0px !important;
    }
    .std-summary{
        font-size:17px;padding:5px 10px;color:#999;
        background-color: #fff;
        font-style: italic;
    }
    .std-primary{
        padding:10px;
        background-color: #fff;
    }
    .std-title{
        background-color: #fff;
        padding:15px 0 0 10px;
        margin-top:10px;
    }
    .std-split{
        height: 20px;
        background-color:#f9f9f9;
    }
    .std-summary:hover,.std-primary:hover{
        background-color:#f9f9f9;
    }
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
</style>
</html>
