<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('登录')"/>
    <link rel="stylesheet" th:href="@{/admin/css/other/login.css}"/>
</head>
<!-- 代 码 结 构 -->
<body class="login-body">
<form action="javascript:void(0);" class="layui-form">
    <div class="layui-form-item1">
        <img class="logo" src="admin/images/logo.png"/>
        <div class="title">集团公司标准资源服务平台</div>
        <div class="desc">
            （2025年版）
        </div>
        <div class="desc" style="font-size: 13px !important;line-height: 20px !important;height: 20px !important;" >
            Build:2025.4.24
        </div>
    </div>
    <div class="layui-form-item">
        <input class="layui-input" hover name="username" placeholder="账 户 : user1 " type="text" value="user1"/>
    </div>
    <div class="layui-form-item">
        <input class="layui-input" hover name="password" placeholder="密 码 : 请输入密码 " type="password" value="123456"/>
    </div>
   <!-- <div class="layui-form-item">
        <input class="code layui-input layui-input-inline" hover name="captcha" placeholder="验证码 : "/>
        <img class="codeImage" id="captchaImage" src="/system/captcha/generate"/>
    </div>-->
    <div class="layui-form-item">
        <input lay-skin="primary" name="remember-me" title="记住密码" type="checkbox">
    </div>
    <div class="layui-form-item">
        <button class="pear-btn pear-btn-primary login" lay-filter="login" lay-submit>
            登 入
        </button>
    </div>
</form>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery', 'layer', 'button', 'popup'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        let layer = layui.layer;
        let button = layui.button;
        let popup = layui.popup;
        let captchaPath = "/system/captcha/generate";
        // 登录
        form.on('submit(login)', function (data) {
            let loader = layer.load();
            console.log(data.field);
            console.log(data.field.username);
            $.ajax({
                url: '/customauth/l?username='+data.field.username,
                type: "get",
                success: function (result) {
                    //layer.close(loader);
                    if(result.success) {
                        if (result.data) {
                            realLogin(data, loader);
                        } else
                        {
                            layer.close(loader);
                            //有登陆信息，是否要踢掉当前用户登录信息并继续登录?
                            layer.confirm('当前用户名已有登录信息，是否要继续登录？', {icon: 3, title: '提示'}, function (index) {
                                layer.close(index);
                                let loading = layer.load();
                                $.ajax({
                                    url:'/customauth/r?username='+data.field.username,
                                    type: 'get',
                                    success: function (result) {

                                        if (result.success) {
                                            if(result.data)
                                            {
                                                realLogin(data, loading);
                                            }
                                            else
                                            {
                                                layer.close(loading);
                                            }
                                        } else {
                                            popup.failure(result.msg);
                                        }
                                    }
                                })
                            });
                        }
                    }
                    else
                    {
                        //发生异常
                        console.log(result.msg);
                    }
                }
            });

            return false;
        });

        $("#captchaImage").click(function () {
           // document.getElementById("captchaImage").src = captchaPath + "?" + Math.random();
        })

        setInterval(function () {
            //document.getElementById("captchaImage").src = captchaPath + "?" + Math.random();
        }, 30 * 1000);

        validateKickout();
        window.realLogin=function(data,loader)
        {
            let btn = button.load({elem: '.login'});
            $.ajax({
                url: '/login',
                data: data.field,
                type: "post",
                dataType: 'json',
                success: function (result) {
                    layer.close(loader);
                    btn.stop(function () {
                        if (result.success) {
                            popup.success(result.msg, function () {
                                location.href = "/index";
                            })
                        } else {
                            popup.failure(result.msg, function () {
                               // document.getElementById("captchaImage").src = captchaPath + "?" + Math.random();
                            });
                        }
                    })
                }
            });
        }

    })
</script>
<script>
    if (window != top) {
        top.location.href = location.href;
    }

    function validateKickout() {
        if (getParam("abnormalout") == 1) {
            layer.alert("<em color='red'>您已在别处登录，请您修改密码或重新登录</em>", {
                    icon: 0,
                    title: "系统提示"
                },
                function (index) {
                    layer.close(index);
                });
        } else if (getParam("sessionout") == 1) {
            layer.alert("<em color='red'>登录已过期，请重新登录</em>", {
                    icon: 0,
                    title: "系统提示"
                },
                function (index) {
                    layer.close(index);
                });
        }
    }

    function getParam(paramName) {
        var reg = new RegExp("(^|&)" + paramName + "=([^&]*)(&|$)");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return decodeURI(r[2]);
        return null;
    }
</script>
</body>
<style>
    .layui-form
    {
        width:520px !important;
    }
    .desc
    {
        line-height: 40px !important;
        height:40px !important;
        font-size:18px !important;
    }
    .layui-form-item
    {
        width:320px !important;
        margin:15px auto !important;
    }
</style>
</html>
