<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增部门')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
                <div class="lauyi-form-item company-select">
                        <label class="layui-form-label">上级部门：</label>
                        <div class="layui-input-block">
                            <div class="layui-unselect layui-form-select downpanel">
                                <div class="layui-select-title layui-select-tree">
                                    <span class="layui-input layui-unselect" id="tree">---请选择上级部门---</span>
                                    <i class="layui-edge"></i>
                                </div>
                                <dl class="layui-anim layui-anim-upbit">
                                    <dd>
                                        <ul>
                                            <div class="main-container" id="classContainer">

                                            </div>
                                        </ul>
                                    </dd>
                                </dl>
                            </div>
                        </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;">*</span>部门名称</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="className" placeholder="请输入部门名称"
                               type="text">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;"></span>部门描述</label>
                    <div class="layui-input-block">
                       <textarea rows="5" class="layui-textarea" name="remark" ></textarea>
                    </div>
                </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-save" lay-submit=""
                    type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button class="pear-btn pear-btn-sm" type="reset">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
    <input type="hidden" id="systemId"  th:value="${system.id}" />
    <input type="hidden" id="parentId" name="parentId" />
    <input type="hidden" id="parentName" name="parentName" />
    <input type="hidden" id="parentFullCode" name="parentFullCode" />
    <input type="hidden" id="classType" name="classType" value="0" />
</form>
<th:block th:include="include :: footer"/>

<script>
    layui.use(['form', 'jquery','tree'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        let tree = layui.tree;
        let MODULE_PATH_OF_CLASS = '/stddepartment/';
        var treeHandler=null;
        var treeData={};
        window.loadtree = function () {
            $.ajax({
                type: "get",
                url: MODULE_PATH_OF_CLASS + "departments",
                dataType: 'json',
                success: function (data) {
                    console.log(data);
                    treeData=data;
                    //渲染
                    if(treeHandler)
                    {
                        tree.reload("theTree",{data:treeData});
                    }
                    else {
                        treeHandler = tree.render({
                            id: 'theTree',
                            elem: '#classContainer',  //绑定元素
                            edit: [], //操作节点的图标
                            customOperate: true,
                            accordion: false,
                            onlyIconControl: true,
                            limitNodeAddLevel: 6, // 设置第X级节点不允许添加操作
                            limitNodeDelLevel: 1, // 设置第X级节点不允许删除操作
                            click: function (obj) {
                                var type = obj.type; //得到操作类型：add、edit、del
                                var data = obj.data; //得到当前节点的数据
                                var elem = obj.elem; //得到当前节点元素
                                //Ajax 操作
                                var id = data.id; //得到节点索引
                                //所有体系下标准
                                if(id==$("#systemId").val())
                                {
                                    $("#parentId").val(-1);
                                    $("#parentName").val(data.title);
                                    $("#parentFullCode").val("-1");
                                }
                                else
                                {
                                    //当前分类下体系下标准
                                    $("#parentId").val(id);
                                    $("#parentName").val(data.title);
                                    $("#parentFullCode").val(data.classPath);
                                }
                                var othis = $($(this)[0].elem).parents(".layui-form-select");
                                othis.removeClass("layui-form-selected").find(".layui-select-title span").html(data.title).end().find("input:hidden[name='hdClassId']").val(id);
                            },
                            operate: function (obj) {
                            },
                            data: treeData
                        });
                    }
                    console.log("treeHandler");
                    console.log(treeHandler);
                }
            });
        }
        window.loadtree();
        window.registeClassChangeEvent=function() {
            $(".downpanel").on("click", ".layui-select-title", function (e) {
                $(".layui-form-select").not($(this).parents(".layui-form-select")).removeClass("layui-form-selected");
                $(this).parents(".downpanel").toggleClass("layui-form-selected");
                layui.stope(e);
            }).on("click", "dl i", function (e) {
                layui.stope(e);
            });
            $(document).on("click", function (e) {
                $(".main-container").parents(".layui-form-select").removeClass("layui-form-selected");
            });
            //$(".downpanel").find(".layui-select-title span").html($("#hdClassName").val());
        }
        window.registeClassChangeEvent();
        form.on('submit(dict-type-save)', function (data) {
            $.ajax({
                url: '/stddepartment/save',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'post',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                            parent.layui.table.reload("role-table");
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });
    })
</script>
</body>
<style>
    .layui-input-block #tree{
        padding:8px;
    }
    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }

    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
    }

    .simple-container .item .sub-item button {
        margin-top: 10px;
    }

    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }

    .advance-container {
        width: 70%;
        margin: 0px auto;
    }

    .advance-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-bottom: 15px;
    }
    .layui-table-tool
    {
        height:70px !important;
        line-height:30px !important;
        padding:10px 0 0 10px !important;
        min-height:0px !important;
    }
    .layui-form-item .layui-form-label, .layui-form-item .layui-input-inline {
        margin-bottom: 7px;
    }
    .layui-form-item {
        padding-bottom: 0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    #resultInfo {
        width: 100%;
        text-align: center;
        color: #999;
        margin-top: 10px;
    }
    .layui-table thead th {
        font-weight: bold;
    }
    /*.c-anchor {
        text-decoration: underline;
        color: #666;
        font-size: 14px;
        display: inline-block;
        white-space: nowrap;
    }*/
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    .search-label {
        flex-grow: 2;
    }
    .simple-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #ddd;
        border-radius: 5px;
    }
    .simple-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .simple-container .search-cmp button:hover {
        cursor: pointer;
    }
    .simple-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .simple-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .advance-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #efefef;
        border-radius: 5px;
    }
    .advance-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .advance-container .search-cmp button:hover {
        cursor: pointer;
    }
    .advance-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .advance-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 240px;
    }
    .con-type-container {
        width: 110px;
    }
    .search-anchor-1
    {
        flex-grow:5;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }
</style>
</html>
