<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('部门管理')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="c-tool">
            <div class="class-container">
                <div class="layui-form-item company-select" style="margin-top: 10px;">
                    <label class="layui-form-label" style="width:250px;overflow: hidden;">上级部门：</label>
                    <div class="layui-input-block" style="margin-left:250px;">
                        <div style="margin-left:25px;" class="layui-unselect layui-form-select downpanel">
                            <div class="layui-select-title layui-select-tree">
                                <span class="layui-input layui-unselect" id="tree" style="line-height: 36px;">---请选择所属部门---</span>
                                <i class="layui-edge"></i>
                            </div>
                            <dl class="layui-anim layui-anim-upbit">
                                <dd>
                                    <ul>
                                        <div class="main-container" id="classContainer">
                                        </div>
                                    </ul>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
            <div class="search-container simple-container" style="padding-left:30px;">
                <div class="item" style="width:100%;">
                   <!-- <label class="sub-item search-label"></label>-->
                    <div class="search-cmp">
                        <input class="layui-input sub-item" id="searchKey" name="name" placeholder="请输入部门名称" type="text">
                        <button id="btnSimpleSearch" class="search-button">
                            <i class="layui-icon layui-icon-search"></i>
                        </button>
                    </div>
                    <div class="search-anchor-1"><a href="javascript:void(0);"  id="clearSimpleCon" style="display: none;" class="search-advance">清空</a>
                        <!-- <a href="javascript:void(0);" id="advanceAnchor" class="search-advance">高级检索&gt;&gt;</a>-->
                    </div>
                </div>
            </div>
            </div>
            <div id="resultInfo"></div>
        </form>
    </div>
</div>
<!--<div class="layui-card">
    <div class="layui-card-body" style="padding:10px 10px 5px 10px;">
        <form action="" class="layui-form">
            <div class="search-container">
                <div class="item">
                    <label class="sub-item">标准号：</label>
                    <input class="layui-input sub-item" name="stdNo" placeholder="" type="text">
                </div>
                <div class="item">
                    <label class="sub-item">标准名称：</label>
                    <input class="layui-input sub-item" name="stdOrgName" placeholder="" type="text">
                </div>
                <div class="item">
                    <label class="sub-item">主要内容：</label>
                    <input class="layui-input sub-item" name="primaryCoverage" placeholder="" type="text">
                </div>
                <div class="item">
                    <label class="sub-item">起草单位：</label>
                    <input class="layui-input sub-item" name="draftingUnit" placeholder="" type="text" >
                </div>
                <div class="item">
                    <label class="sub-item">起草人：</label>
                    <input class="layui-input sub-item" name="drafter" placeholder="" type="text">
                </div>
                <div class="item">
                    <button class="pear-btn pear-btn-md pear-btn-primary sub-item" lay-filter="role-query" lay-submit style="margin-left:40px;margin-top:0px;">
                        <i class="layui-icon layui-icon-search"></i>
                        查询
                    </button>
                    <button class="pear-btn pear-btn-md sub-item" type="reset" style="margin-left:30px;margin-top:0px;">
                        <i class="layui-icon layui-icon-refresh"></i>
                        重置
                    </button>
                </div>
            </div>
            <div id="resultInfo"></div>
        </form>
    </div>
</div>-->
<div class="layui-card">
    <div class="layui-card-body">
        <table id="role-table" lay-filter="role-table"></table>
    </div>
</div>
</body>
<script id="role-toolbar" type="text/html">
    <button
            class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
        <i class="layui-icon layui-icon-add-1"></i>
        新增
    </button>
    <!-- <button  class="pear-btn pear-btn-primary pear-btn-md custom-blue" style="margin-left:15px;" lay-event="downloadTemplate">
         <i class="layui-icon layui-icon-download-circle"></i>
         下载专题模板
     </button>
       <button  class="pear-btn pear-btn-primary pear-btn-md" style="margin-left:15px;" lay-event="downloadTemplate">
           <i class="layui-icon layui-icon-upload"></i>
           下载导入模板
       </button>
       <button  class="pear-btn pear-btn-primary pear-btn-md"  id="uploadBtn">
           <i class="layui-icon layui-icon-upload"></i>
           导入
       </button>
    <div id="uploadElem" style="display: inline-block;"></div>-->
   <!-- <button  class="pear-btn pear-btn-danger pear-btn-md" style="margin-left:15px;" lay-event="delete">
        <i class="layui-icon layui-icon-delete"></i>
        删除
    </button>-->
    <!--    <button  class="pear-btn pear-btn-primary pear-btn-md" lay-event="batchDecryptDoc">
            <i class="layui-icon layui-icon-upload"></i>
            批量解密指导性文件
        </button>-->
</script>
<!--

<button  class="pear-btn pear-btn-md"
         lay-event="batchRemove">
    <i class="layui-icon layui-icon-delete"></i>
    删除
</button>-->
<!--<a class="c-anchor" href="javascript:void(0);" lay-event="detail">详情</a>-->
<script id="role-bar" type="text/html">
    <a class="c-anchor iconfont icon-bianji1" href="javascript:void(0);" lay-event="edit" title="编辑"></a>
    <a class="c-anchor layui-icon layui-icon-delete"  href="javascript:void(0);" lay-event="delete"  title="删除"></a>
    <a class="c-anchor layui-icon layui-icon-up"  href="javascript:void(0);" lay-event="up"  title="上移"></a>
    <a class="c-anchor layui-icon layui-icon-down"  href="javascript:void(0);" lay-event="down"  title="下移"></a>
</script>
<!--
<button
        class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>
</button>
<button
        class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>
</button>-->
<div style="height:1px;overflow: hidden;">
<div id="uploadProcess" class="layui-progress layui-progress-big" lay-showPercent="yes" lay-filter="progressBar">
    <div class="layui-progress-bar layui-bg-green" lay-percent="0%"></div>
</div>
</div>
<input type="hidden" id="hdSystemId"  th:value="${system.id}" />
<input type="hidden" id="hdSystemName" th:value="${system.name}" />
<input type="hidden" id="hdClassName" name="hdClassName" />
<input type="hidden" id="hdClassPath" name="hdClassPath"  />
<input type="hidden" id="hdDepartmentName" name="hdDepartmentName"  />
<input type="hidden" id="hdDepartmentId" name="hdDepartmentId" />
<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common','upload','layer','element','tree'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;

        let MODULE_PATH = "/stddepartment/";
        let conditionArr=[1,2];
        let uploadSessionId="";
        var uploadIntervalHandle=null;
        let tree = layui.tree;
        var treeHandler=null;
        var treeData={};
        window.loadtree = function () {
            $.ajax({
                type: "get",
                url: MODULE_PATH + "departments",
                dataType: 'json',
                success: function (data) {
                    console.log(data);
                    treeData=data;
                    //$("#tree").text($("#hdPostName").val());
                    //渲染
                    if(treeHandler)
                    {
                        tree.reload("theTree",{data:treeData});
                    }
                    else {
                        treeHandler = tree.render({
                            id: 'theTree',
                            elem: '#classContainer',  //绑定元素
                            edit: [], //操作节点的图标
                            customOperate: true,
                            accordion: false,
                            onlyIconControl: true,
                            limitNodeAddLevel: 6, // 设置第X级节点不允许添加操作
                            limitNodeDelLevel: 1, // 设置第X级节点不允许删除操作
                            click: function (obj) {
                                var type = obj.type; //得到操作类型：add、edit、del
                                var data = obj.data; //得到当前节点的数据
                                var elem = obj.elem; //得到当前节点元素
                                //Ajax 操作
                                var id = data.id; //得到节点索引
                                //所有岗位下标准
                                if(id==$("#hdSystemId").val())
                                {
                                    $("#hdDepartmentId").val(-1);
                                    $("#hdClassName").val(data.title);
                                    $("#hdClassPath").val("-1");
                                }
                                else
                                {
                                    //当前分类下岗位下标准
                                    $("#hdDepartmentId").val(id);
                                    $("#hdClassName").val(data.title);
                                    $("#hdClassPath").val(data.classPath);
                                }
                                var othis = $($(this)[0].elem).parents(".layui-form-select");
                                othis.removeClass("layui-form-selected").find(".layui-select-title span").html(data.title).end();//.find("input:hidden[name='hdDepartmentId']").val(id);
                                window.searchDepartment(false);
                            },
                            operate: function (obj) {
                            },
                            data: treeData
                        });
                    }
                    console.log("treeHandler");
                    console.log(treeHandler);
                }
            });
        }
        window.loadtree();
        window.registeClassChangeEvent=function() {
            $(".downpanel").on("click", ".layui-select-title", function (e) {
                $(".layui-form-select").not($(this).parents(".layui-form-select")).removeClass("layui-form-selected");
                $(this).parents(".downpanel").toggleClass("layui-form-selected");
                layui.stope(e);
            }).on("click", "dl i", function (e) {
                layui.stope(e);
            });
            $(document).on("click", function (e) {
                $(".main-container").parents(".layui-form-select").removeClass("layui-form-selected");
            });
            //$(".downpanel").find(".layui-select-title span").html($("#hdClassName").val());
        }
        window.registeClassChangeEvent();
        let cols = [
            [
                {type: 'checkbox',width:'4%'},
                {field: 'number', title: '序号', align: 'center', type: 'numbers',width:'5%'},
                {title: '部门名称', field: 'className', align: 'left', width: '25%'},
                {title: '所属部门', field: 'parentName', align: 'center', width: '15%'},
                {title: '部门描述', field: 'remark', align: 'left',width:'18%'},
                {title: '状态', templet:function(d){return d.status=="1"?"正常":"停用"}, align: 'center', width: '8%'},
                {title: '操作', toolbar: '#role-bar', align: 'center',width: '17%'}
            ]
        ]
        table.render({
            elem: '#role-table',
            url: MODULE_PATH + 'directSubOfDeparts',
            page: true,
            limit:50,
            limits:[10,15,20,30,50],
            cols: cols,
            skin: 'row,line',
            toolbar: '#role-toolbar',
            cellMinWidth: 'auto',
            where:{
                "name":$("#searchKey").val(),
                "classCodePath":$("#hdClassPath").val()
            },
            defaultToolbar: [{
                title: '刷新',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print', 'exports'],
            done: function (res, curr, count) {
                $("#resultInfo").html('共检索到'+res.count+"个部门");
                var $data = $('div[lay-id="role-table"]').find('.layui-table-body').find('tr').eq(0).find('td');
                var $head = $('div[lay-id="role-table"]').find('.layui-table-header').find('tr').eq(0).find('th');
                for (var i = 0; i < $data.length; i++) {
                    var l1 = $data.eq(i).find('div').width();
                    var l2 = $head.eq(i).find('div').width();
                    if (l1 > l2) {
                        $head.eq(i).find('div').width(l1);
                    } else if(l2>l1) {
                        $data.eq(i).find('div').width(l2);
                    }
                }
            }
        });

        table.on('tool(role-table)', function (obj) {
            if (obj.event === 'delete') {
                window.remove(obj);
            }
            else if (obj.event === 'edit') {
                window.edit(obj);
            }
            else if(obj.event=='systemclassedit')
            {
                window.editClass(obj);
            }
            else if(obj.event=='systemclassinfo')
            {
                window.showClass(obj);
            }
            else if(obj.event=='publish')
            {
                window.publish(obj);
            }
            else if(obj.event=='stop-publish')
            {
                window.stopPublish(obj);
            }
            else if(obj.event=='to-edit-status')
            {
                window.toEditStatus(obj);
            }
            else if(obj.event=='stdmanager')
            {
                window.stdmanager(obj);
            }
            else if(obj.event=='stdinfo')
            {
                window.stdinfo(obj);
            }
            else if(obj.event=='show-system-info')
            {
                window.systemInfo(obj);
            }
            else if(obj.event=='up')
            {
                window.moveUp(obj);
            }
            else if(obj.event=='down')
            {
                window.moveDown(obj);
            }
            /*else if (obj.event === 'power') {
                window.power(obj);
            } else if (obj.event === 'dept') {
                window.dept(obj);
            } */
        });

        table.on('toolbar(role-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                window.refresh();
            }
            else if(obj.event=='batchUpdateDoc')
            {
                window.batchUpdateDocFiles();
            }
        });
        $("#searchKey").keydown(function (e) {//当按下按键时
            if (e.which == 13) {//.which属性判断按下的是哪个键，回车键的键位序号为13
                var key = $('#searchKey').val();
                if(true) {
                    if (!key) {
                        layer.alert("请输入关键字", {
                            title: "提示"
                        });
                        return false;
                    }
                }
                table.reload('role-table', {where: {"classCodePath":$("#hdClassPath").val(),className:key},page: { curr: 1 }});
                if(key) {
                    $("#clearSimpleCon").show();
                }
                return false;
            }
        });
        $("#btnSimpleSearch").click(function(e){
            var key = $('#searchKey').val();
            if(true) {
                if (!key) {
                    layer.alert("请输入关键字", {
                        title: "提示"
                    });
                    return false;
                }
            }
            table.reload('role-table', {where: {"classCodePath":$("#hdClassPath").val(),className:key},page: { curr: 1 }});
            if(key) {
                $("#clearSimpleCon").show();
            }
            return false;
        });
        $("#clearSimpleCon").click(function(){
            $('#searchKey').val("");
            $("#hdClassPath").val("");
            $("#hdClassName").val("");
            $("#hdDepartmentName").val("");
            $("#hdDepartmentId").val("");
            $("#tree").text("---请选择所属部门---");
            table.reload('role-table', {where: {"classCodePath":"",className:""},page: { curr: 1 }});
            $("#clearSimpleCon").hide();
        });
        window.systemInfo=function(obj)
        {
            layer.open({
                type: 2,
                title: '【'+obj.data['name']+'】',
                shade: 0.1,
                area: ['600px', '550px'],
                content: MODULE_PATH + 'detail?id=' + obj.data['id']
            });
        }
        //跳转到新页面
        window.add_tab_f = function (title,url,is_refresh) {
            var element=parent.layui.element;
            var id = url.replace("?","_").replace("=","_").replaceAll('/','_');//md5每个url
            //防止重复打开
            console.log('id:'+id);
            for (var i = 0; i < parent.$('.x-iframe').length; i++) {
                console.log('parent.$(\'.x-iframe\').length:'+parent.$('.x-iframe').length);
                console.log(parent.$('.x-iframe'));
                if(parent.$('.x-iframe').eq(i).attr('tab-id')==id){
                    element.tabChange('xbs_tab', id);
                    if(is_refresh)
                        parent.$('.x-iframe').eq(i).attr("src",parent.$('.x-iframe').eq(i).attr('src'));
                    return;
                }
            };
            console.log('element:'+element);
            element.tabAdd('xbs_tab', {
                title: title
                ,content: '<iframe tab-id="'+id+'" frameborder="0" src="'+url+'" scrolling="yes" class="x-iframe"></iframe>'
                ,id: id
            });
            element.tabChange('xbs_tab', id);
        }
        window.moveUp=function(obj)
        {
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "moveup",
                dataType: 'json',
                type: 'post',
                contentType: 'application/json',
                data:JSON.stringify({id: obj.data['id']}),
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("上移完成!", {icon: 1, time: 2000});
                        window.searchDepartment(false);
                    } else {
                        layer.msg("上移操作失败:"+result.message, {icon: 2, time: 2000});
                    }
                }
            })
        }
        window.moveDown=function(obj)
        {
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "movedown",
                dataType: 'json',
                type: 'post',
                contentType: 'application/json',
                data:JSON.stringify({id: obj.data['id']}),
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("下移完成!", {icon: 1, time: 2000});
                        window.searchDepartment(false);
                    } else {
                        layer.msg("下移操作失败:"+result.message, {icon: 2, time: 2000});
                    }
                }
            })
        }
        //明细表维护
        window.stdmanager=function(obj)
        {
            parent.layer.open({
                type: 2,
                title: '【'+obj.data["name"]+'】明细表维护',
                shade: 0.1,
                maxmin: true,
                area: ['85%', '85%'],
                content:"/classjoinstd/mainofsys?sysId="+obj.data["id"]
            });
            // window.add_tab_f("明细表维护","/classjoinstd/mainofsys?sysId="+obj.data["id"])
            /* if (top.layui.index) {
                 top.layui.index.openTabsPage("/classjoinstd/mainofsys?sysId="+obj.data["id"], "明细表维护");
             } else {
                 window.open("/classjoinstd/mainofsys?sysId="+obj.data["id"])
             }*/
        }
        window.stdinfo=function(obj)
        {
            parent.layer.open({
                type: 2,
                title: '【'+obj.data["name"]+'】明细表信息',
                shade: 0.1,
                maxmin: true,
                area: ['85%', '85%'],
                content:"/classjoinstd/detailofsys?sysId="+obj.data["id"]
            });
        }
        window.toEditStatus=function(obj)
        {
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "toEditStatus",
                dataType: 'json',
                type: 'post',
                contentType: 'application/json',
                data:JSON.stringify({id: obj.data['id']}),
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("已下架!", {icon: 1, time: 2000});
                        window.searchDepartment(false);
                    } else {
                        layer.msg("下架操作失败:"+result.message, {icon: 2, time: 2000});
                    }
                }
            })
        }
        window.stopPublish=function(obj)
        {
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "stopPublish",
                dataType: 'json',
                type: 'post',
                contentType: 'application/json',
                data:JSON.stringify({id: obj.data['id']}),
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("已停用!", {icon: 1, time: 2000});
                        window.searchDepartment(false);
                    } else {
                        layer.msg("停用操作失败:"+result.message, {icon: 2, time: 2000});
                    }
                }
            })
        }
        window.publish=function(obj)
        {
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "publish",
                dataType: 'json',
                contentType: 'application/json',
                type: 'post',
                data:JSON.stringify({id: obj.data['id']}),
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("发布成功!", {icon: 1, time: 2000});
                        window.searchDepartment(false);
                    } else {
                        layer.msg("发布操作失败:"+result.message, {icon: 2, time: 2000});
                    }
                }
            })
        }
        window.add = function () {
            layer.open({
                type: 2,
                title: '新增部门',
                shade: 0.1,
                area: ['650px', '600px'],
                content: MODULE_PATH + 'add'
            });
        }

        window.remove=function(obj)
        {
            layer.confirm('确定要删除['+obj.data["className"]+']吗？', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "softDelete",
                    dataType: 'json',
                    type: 'post',
                    data: {id:obj.data["id"]},
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            layer.msg("删除成功!", {icon: 1, time: 2000});
                            window.searchDepartment(false);
                            window.setUpload();
                        } else {
                            layer.msg("删除失败:" + result.message, {icon: 2, time: 2000});
                        }
                    }
                });
            });
        }

        window.editClass=function(obj)
        {
            layer.open({
                type: 2,
                title: '【'+obj.data['className']+'】专题结构管理',
                shade: 0.1,
                area: ['1000px', '600px'],
                content: MODULE_PATH_OF_CLASS + 'editofsys?systemId=' + obj.data['id']
            });
        }
        window.showClass=function(obj)
        {
            layer.open({
                type: 2,
                title: '【'+obj.data['className']+'】部门信息编辑',
                shade: 0.1,
                area: ['1000px', '600px'],
                content: MODULE_PATH_OF_CLASS + 'detailofsys?systemId=' + obj.data['id']
            });
        }

        window.edit = function (obj) {
            layer.open({
                type: 2,
                title: '【'+obj.data['className']+'】编辑',
                shade: 0.1,
                area: ['600px', '550px'],
                content: MODULE_PATH + 'edit?id=' + obj.data['id']
            });
        }

        window.statisticsPdfCount=function(){
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "testpdfstatus",
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("统计完毕!", {icon: 1, time: 2000});
                        window.setUpload();
                    } else {
                        layer.msg("操作失败:"+result.message, {icon: 2, time: 2000});
                        window.setUpload();
                    }
                }
            })
        }

        window.batchUpdateDocFiles=function(){
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "batchUpdateDoc",
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("更新成功!", {icon: 1, time: 2000});
                        window.setUpload();
                    } else {
                        layer.msg("更新失败:"+result.message, {icon: 2, time: 2000});
                        window.setUpload();
                    }
                }
            })
        }

        window.batchDecryptDoc=function()
        {
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "batchDecryptDoc",
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("更新成功!", {icon: 1, time: 2000});
                        window.setUpload();
                    } else {
                        layer.msg("更新失败:"+result.message, {icon: 2, time: 2000});
                        window.setUpload();
                    }
                }
            })
        }
        //downloadTemplateBtn
        window.downloadTemplate=function(){
            window.open("/source/downloadSubjectTemplate");
        }

        window.setUpload=function(){
            $('#uploadElem').html(`<div id="uploadBox"><button type="button" class="pear-btn pear-btn-primary pear-btn-md custom-blue" id="uploadBtn"><i class="layui-icon">&#xe67c;</i>导入</button><span id="selected"></span></div>`);
            uploadSessionId=Date.parse(new Date());
            upload.render({
                elem: '#uploadBtn'     // 选择文件按钮
                ,elemList: $('#demoList') //列表元素对象
                ,data:{customSessionId:uploadSessionId}
                ,url:  MODULE_PATH+ 'file-upload' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
                ,accept: 'file'  //指定允许上传时校验的文件类型，可选值有：images（图片）、file（所有文件）、video（视频）、audio（音频）
                ,multiple: false  //是否允许多文件上传。设置 true即可开启。不支持ie8/9
                ,number: 1   //设置同时可上传的文件数量，一般配合 multiple 参数出现; 0 不限制
                ,auto: true  //是否选完文件后自动上传。如果设定 false，那么需要设置 bindAction 参数来指向一个其它按钮提交上传
                ,bindAction: '#testListAction'  //指向一个按钮触发上传，一般配合 auto: false 来使用
                ,choose: function(obj){   //选择文件后的回调函数。返回一个object参数
                },
                before: function(obj){ //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
                    layer.open({
                        type: 1,
                        title: '上传中...',
                        shade: 0.1,
                        area: ['500px', '100px'],
                        content: $("#uploadProcess"),
                        end : function() {
                            window.clearInterval(uploadIntervalHandle);
                        }
                    });
                    uploadIntervalHandle=window.setInterval("window.fetchUploadStatus()",1000);
                }
                ,done: function(res, index, upload){ //成功的回调
                    var that = this;
                    if(res.success){ //上传成功
                        layer.msg("导入完成！", {icon: 1, time: 1500}, function () {
                            window.searchDepartment(false);
                            window.setUpload();
                        });
                        //delete this.files[index]; //删除文件队列已经上传成功的文件
                        return;
                    }else {
                        if(res.message.indexOf(".xlsx")!=-1)
                        {
                            layer.msg("导入失败！</br> <a class='d-anchor' href='/source/downloadUploadErr?fileName="+res.message+"' target='_blank'>下载错误明细</a> </br>", {
                                icon: 2,
                                time: 100000
                            }, function () {
                                window.searchDepartment(false);
                                window.setUpload();
                            });
                        }
                        else {
                            layer.msg("导入失败！ </br>" + res.message.replaceAll(";;", "</br>"), {
                                icon: 2,
                                time: 100000
                            }, function () {
                                window.searchDepartment(false);
                                window.setUpload();
                            });
                        }
                    }
                }
                ,allDone: function(obj){ //多文件上传完毕后的状态回调
                    console.log(obj)
                    window.setUpload();
                }
                ,error: function(index, upload){ //错误回调
                    popup.failure("导入失败！");
                    window.setUpload();
                }
                ,progress: function(n, elem, e, index){ //注意：index 参数为 layui 2.6.6 新增
                    element.progress('progress-demo-'+ index, n + '%'); //执行进度条。n 即为返回的进度百分比
                }
            });
        }

        window.setUpload();
        window.fetchUploadStatus=function(){
            $.ajax({
                url: "/chinastd/uploadProcess",
                dataType: 'json',
                type: 'get',
                data: {customSessionId:uploadSessionId},
                success: function (result) {
                    console.log(result);
                    if (result.success) {
                        //  $("#uploadProcess_dealed").html(result.data.dealedCount);
                        //  $("#uploadProcess_total").html(result.data.totalCount);

                        if(result.data.dealedCount*1>0) {
                            var percent = Math.round(result.data.dealedCount * 100.0 / result.data.totalCount * 1.0) + '%' //获取进度百分比
                            element.progress('progressBar', percent); //可配合 layui 进度条元素使用
                            // console.log(elem); //得到当前触发的元素 DOM 对象。可通过该元素定义的属性值匹配到对应的进度条。
                            // console.log(res); //得到 progress 响应信息
                            //  console.log(index); //得到当前上传文件的索引，多文件上传时的进度条控制，如：
                            //  element.progress('progressBar-'+ index, n + '%'); //进度条
                            $(".layui-layer-title").html('上传中[已处理' + result.data.dealedCount + '条数据/共' + result.data.totalCount + '条数据]');
                            if (result.data.dealedCount >= result.data.totalCount) {
                                window.clearInterval(uploadIntervalHandle);
                                layer.open({
                                    type: 1,
                                    title: '上传完成',
                                    shade: 0.1,
                                    area: ['500px', '300px'],
                                    content: '上传已完成，共导入' + result.data.totalCount + '条数据',
                                    end: function () {
                                        layer.closeAll();
                                        element.progress('progressBar', "0%");
                                    }
                                });
                            }
                        }

                    } else {
                        window.clearInterval(uploadIntervalHandle);
                        layer.open({
                            type: 1,
                            title: '发生错误',
                            shade: 0.1,
                            area: ['500px', '300px'],
                            content: result.data.err,
                            end : function() {
                                layer.closeAll();
                            }
                        });
                    }
                }
            })
        }
        window.getPercent=function(num) {
            //5.四会五入保留2位小数《不够位救，则用昔补)
            var result = parseFloat(num);
            if (isNaN(result)) {
                return 0;
            }
            result = Math.round(num * 100) / 100;
            var s_x = result.toString(); //将数学转热为字符串
            var pos_decimal = s_x.index0f('.');//小教点的家引值
            //pos decimal=-L 自动补@(pos decimal <)
            if (pos_decimal < 0) {
                pos_decimal = s_x.length;
                s_x += '.';
            }
            //当教字的长度《小教点宝引2时
            while (s_x.length <= pos_decimal + 2) {
                s_x += '0';
            }
            return s_x;
        }
        window.batchRemove = function (obj) {
            let ids = common.checkField(obj, 'roleId');
            if (common.isEmpty(ids)) {
                popup.warning("未选中数据");
                return false;
            }
            layer.confirm('确定要删除选中的数据吗', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "batchRemove/" + ids,
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            popup.success(result.msg, function () {
                                window.searchDepartment(false);
                                window.setUpload();
                            });
                        } else {
                            popup.failure(result.msg);
                        }
                    }
                })
            });
        }

        window.refresh = function () {
            window.searchDepartment(false);
            window.setUpload();
        }
        //清空文件列表
        $('#clearList').click(function(){
            $('#demoList').html('');
        });
        window.searchDepartment=function(isClickSearchBtn)
        {
            var key = $('#searchKey').val();
            if(isClickSearchBtn) {
                if (!key) {
                    layer.alert("请输入关键字", {
                        title: "提示"
                    });
                    return false;
                }
            }
            table.reload('role-table', {where: {"classCodePath":$("#hdClassPath").val(),className:key},page: { curr: 1 }});
            if(!key) {
                $("#clearSimpleCon").show();
            }
            return false;
        }
    });
</script>
<style>
    .c-tool
    {
        display: flex;
        flex-wrap: nowrap;
        align-items: flex-start;
    }
    .c-tool .class-container{
        flex-grow: 1;
        align-items: flex-start;
    }
    .c-tool  .search-container {
        flex-grow: 3;
    }
    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }

    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
    }

    .simple-container .item .sub-item button {
        margin-top: 10px;
    }

    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }

    .advance-container {
        width: 70%;
        margin: 0px auto;
    }

    .advance-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-bottom: 15px;
    }
    .layui-table-tool
    {
        height:70px !important;
        line-height:30px !important;
        padding:10px 0 0 10px !important;
        min-height:0px !important;
    }
    .layui-form-item .layui-form-label, .layui-form-item .layui-input-inline {
        margin-bottom: 7px;
    }
    .layui-form-item {
        padding-bottom: 0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    #resultInfo {
        width: 100%;
        text-align: center;
        color: #999;
        margin-top: 10px;
    }
    .layui-table thead th {
        font-weight: bold;
    }
   /* .c-anchor {
        text-decoration: underline;
        color: #666;
        font-size: 14px;
        display: inline-block;
        white-space: nowrap;
    }*/
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    .search-label {
        flex-grow: 5;
    }
    .simple-container .search-cmp {
        flex-grow: 5;
        display: flex;
        border: solid 1px #ddd;
        border-radius: 5px;
    }
    .simple-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .simple-container .search-cmp button:hover {
        cursor: pointer;
    }
    .simple-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .simple-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .advance-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #efefef;
        border-radius: 5px;
    }
    .advance-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .advance-container .search-cmp button:hover {
        cursor: pointer;
    }
    .advance-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .advance-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 140px;
    }
    .con-type-container {
        width: 100px;
    }
    .search-anchor-1
    {
        flex-grow:5;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }
    .layui-progress-big, .layui-progress-big .layui-progress-bar
    {
        margin:0 2px !important;
    }
    /*  .search-container
      {
          display: flex;
          flex-wrap: nowrap;
          align-items: center;
      }
      .search-container .item
      {
          display: flex;
          flex-direction: row;
          margin-top:0.625rem;
      }
      .search-container .item .sub-item button
      {
          margin-top:10px;
      }
      .search-container .item label
      {
          min-width: 80px;
          padding: 8px 1px 8px 10px;
          text-align: right;
      }
      .layui-table-tool
      {
          height:70px !important;
          line-height:30px !important;
          padding:10px 0 0 10px !important;
          min-height:0px !important;
      }
      .layui-card-body .layui-form
      {
          margin-top:0px !important;
      }
      .layui-form-item .layui-form-label,.layui-form-item  .layui-input-inline{
          margin-bottom:7px;
      }
      .layui-form-item{
          padding-bottom:0px !important;
      }
      .layui-table-cell {
          height: auto;
          line-height: 28px;
          overflow:auto;
          white-space: normal;
      }
      #resultInfo{
          width:300px;
          text-align: center;
          color:#999;
          margin:10px auto 0;
      }
      .layui-table thead th {
          font-weight: bold;
      }
      .c-anchor
      {
          text-decoration: underline;
          color: #666;
          font-size: 12px;
          display: inline-block;
      }
      .layui-table thead th .layui-table-cell {
          text-align: center !important;
      }
      .is-show-0{
          display: none;
      }
      .layui-table-page
      {
          text-align: center !important;
      }*/
</style>
</html>
