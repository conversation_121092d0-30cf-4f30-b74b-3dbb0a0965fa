<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('信息配置修改')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">标准标识<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="stdIdentification"  lay-verify="required"
                               placeholder="请输入标准标识" th:value="${stdInfo.stdIdentification}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准类别<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="stdClass"  lay-verify="required"
                               placeholder="请输入标准类别" th:value="${stdInfo.stdClass}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准号<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="stdNo"
                               placeholder="请输入标准号" th:value="${stdInfo.stdNo}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准名称(原文)<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="stdOrgName"
                               placeholder="请输入标准名称" th:value="${stdInfo.stdOrgName}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准名称(中文)：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="stdChineseName"
                               placeholder="请输入中文名称" th:value="${stdInfo.stdChineseName}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准名称(英文)：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="stdEnglishName"
                               placeholder="请输入英文名称" th:value="${stdInfo.stdEnglishName}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">文本语言<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="stdLangugage"  lay-verify="required"
                               placeholder="请输入文本语言" th:value="${stdInfo.stdLangugage}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">密级：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" readonly style="width:100px;" class="layui-input"  name="securityClass"
                               placeholder="请输入密级" th:value="${stdInfo.securityClass}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">分类号：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="catetoryNo"
                               placeholder="请输入分类号" th:value="${stdInfo.catetoryNo}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">ICS号：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="stdIcs"
                               placeholder="请输入ICS号" th:value="${stdInfo.stdIcs}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">发布机构：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="pubDept"
                               placeholder="请输入发布机构" th:value="${stdInfo.pubDept}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">发布日期：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" style="width:120px;" class="layui-input"  name="pubDate" id="pubDate"
                               placeholder="请输入发布日期" th:value="${stdInfo.pubDate}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">实施日期：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" style="width:120px;" class="layui-input"  name="implementationDate" id="implementationDate"
                               placeholder="请输入实施日期" th:value="${stdInfo.implementationDate}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">提出单位：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="advanceDept"
                               placeholder="请输入提出单位" th:value="${stdInfo.advanceDept}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">起草单位：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="draftingUnit"
                               placeholder="请输入起草单位" th:value="${stdInfo.draftingUnit}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">起草人：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="drafter"
                               placeholder="请输入起草人" th:value="${stdInfo.drafter}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准状态：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" name="stdStatus"
                               placeholder="请输入标准状态" th:value="${stdInfo.stdStatus}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">代替标准号（老标准）：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" name="alternateStdNo"
                               placeholder="请输入代替标准号（老标准）" th:value="${stdInfo.alternateStdNo}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">被替代的标准号（新标准）：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" name="supersededStdNo"
                               placeholder="请输入被替代的标准号（新标准）" th:value="${stdInfo.supersededStdNo}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">页数<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" style="width:80px;"  class="layui-input"  name="pageCount"  lay-verify="required|isValidPageNo"
                               placeholder="请输入页数" th:value="${stdInfo.pageCount}" type="number" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准年代：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" style="width:120px;"  class="layui-input"  name="stdOcr"  lay-verify="isFourDigits"
                               placeholder="请输入标准年代" th:value="${stdInfo.stdOcr}" type="number" />
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">pdf文件名<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="pdfFileName"
                               placeholder="请输入pdf文件名" th:value="${stdInfo.pdfFileName}" type="text" />
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">主要内容：</label>
                    <div class="layui-input-block">
                        <textarea autocomplete="off"  class="layui-input"  name="primaryCoverage" style="padding:5px;height:100px;"
                                  placeholder="请输入主要内容" th:text="${stdInfo.primaryCoverage}" type="text" ></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-update" lay-submit=""
                    type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button class="pear-btn pear-btn-sm" type="reset">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
    <input name="id" th:value="${stdInfo.id}" type="hidden" />
</form>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery','laydate'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        var laydate = layui.laydate;

        laydate.render({
            elem: '#implementationDate',
            type:'date',
            theme: 'molv',
            trigger: 'click',
            done: function(value, date) {
            }
        });

        laydate.render({
            elem: '#pubDate',
            type:'date',
            theme: 'molv',
            trigger: 'click',
            done: function(value, date) {
            }
        });

        laydate.render({
            elem: '#stdOcr',
            type:'year',
            theme: 'molv',
            trigger: 'click',
            done: function(value, date) {
            }
        });

        form.on('submit(dict-type-update)', function (data) {
            $.ajax({
                url: '/chinastd/update',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                            parent.layui.table.reload("role-table");
                            parent.window.setUpload();
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });
        // 添加自定义验证规则
        form.verify({
            isFourDigits: function(value){
                if(value) {
                    if (!new RegExp("^[0-9]{4}$").test(value)) {
                        return '请输入4位数字';
                    }
                }
            },
            isValidPageNo: function(value){
                if(value*1<=0){
                    return '请输入有效的页数';
                }
            }
        });
    })
</script>
<style>
    .layui-input-block { line-height: 36px; }
    .layui-form-label{
        width:120px;
        text-align: right;
        color:#aaa;
    }
    .layui-input, .layui-textarea
    {
        width:90%;
    }
</style>
</body>
</html>
