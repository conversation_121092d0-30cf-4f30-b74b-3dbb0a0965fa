<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>标准搜索</title>
		<meta name="renderer" content="webkit">
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<link rel="stylesheet" href="../../component/pear/css/pear.css" />
		<link rel="stylesheet" href="../../admin/css/other/console1.css" />
		<!-- 主 题 更 换 -->
		<style id="pearadmin-bg-color"></style>
		<style>
			a.pdf-title
			{
				color:rgb(36, 64, 179);
			}
			#summary {
				font-size:15px;
				color:#999;
				height: 30px;
				text-align: right;
				line-height: 30px;
				margin-top:10px;
				margin-right:20px;
			}
		</style>
	</head>
	<body class="pear-container">
		<div>
			<div class="layui-row layui-col-space10">
				<div class="layui-col-md12">
					<div class="layui-card">
						<div class="layui-card-body" style="min-height: 1000px;">
							<div>
								<div style="text-align: center">
									<div><label for="txtKey">
									<span style="font-size:16px;font-weight: bold;margin-top:20px;color:#666;">全文检索：</span><input type="text" id="txtKey" placeholder="请输入关键字"  class="layui-input" style="width:500px;display:inline-block;border-width: 1px;border-color:#555;"></label><span style="width:15px;"> </span><button type="button" class="pear-btn pear-btn-primary" id="btnSearch">搜索</button>
									</div>
									<div class="tip-info">全文检索仅支持 GJB、WJ 和 Q/CNG。</div>
								</div>
								<div id="summary">每页30条记录</div>
								<div id="matchTab" class="layui-tab layui-tab-brief" style="display: none;">
									<ul class="layui-tab-title">
										<li class="layui-this">标准内容匹配<span class="match-count" id="spAllMatch">（0）</span></li>
										<li>标准名称匹配<span class="match-count" id="spTitleMatch">（0）</span></li>
									</ul>
									<div class="layui-tab-content">

										<div class="layui-tab-item layui-show">
											<div id="topPager"></div>
											<ul id="search_ul">
											</ul>
											<div id="bottomPager"></div>
										</div>
										<div class="layui-tab-item">
											<div id="titleTopPager"></div>
											<ul id="search_ul_title_match">
										    </ul>
											<div id="titleBottomPager"></div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--</div>-->
		<script src="../../component/layui/layui.js"></script>
		<script src="../../component/pear/pear.js"></script>
		<script>
			layui.use(['layer', 'echarts', 'element', 'count','laypage'], function() {
				var $ = layui.jquery,
					layer = layui.layer,
					element = layui.element,
					count = layui.count,
					echarts = layui.echarts,
					laypage= layui.laypage;
				let MODULE_PATH = "/chinastd/";
				let loadingHandler=null;
				let sType="china";

				var currentPage1 = 1;
				var currentPage2 = 1;
				var allTotal=100;

				var titlePage1=1;
				var titlePage2=1;
				var titleTotal=100;

				var pageSize=30;

				// =====================1 标准内容匹配分页函数========================================
				window.onPageChange1=function(page,isFirst) {
					// Update the current page value
					currentPage1 = page.curr;
					p = page.curr;//改变当前页码
					l = page.limit;
					if (!isFirst) {
						var key = $('#txtKey').val();
						if (!key) {
							layer.alert("请输入关键字", {
								title: "提示"
							});
							return;
						}
						window.search("search_ul", (p - 1) * l, l, key);
						// Manually set the page for pagination2
						laypage.render({
							elem: 'bottomPager',
							count: allTotal,
							limit: pageSize,
							curr: currentPage1,
							jump: onPageChange2
						});
					}
				}
				// Function to handle page change events for pagination2
				window.onPageChange2=function(page,isFirst) {
					// Update the current page value
					currentPage1 = page.curr;
					currentPage2=currentPage1;
					p = page.curr;//改变当前页码
					l = page.limit;
					if (!isFirst) {
						var key = $('#txtKey').val();
						if (!key) {
							layer.alert("请输入关键字", {
								title: "提示"
							});
							return;
						}
						window.search("search_ul", (p - 1) * pageSize, pageSize, key);
						// Manually set the page for pagination2
						laypage.render({
							elem: 'topPager',
							count: allTotal,
							limit: pageSize,
							curr: currentPage1,
							jump: onPageChange1
						});
					}
				}
				//分页
				window.initPager=function(total)
				{
					currentPage1=1;
					currentPage2=1;
					allTotal=total;
					laypage.render({
						elem:'topPager',
						curr:1,
						count:allTotal,
						limit:pageSize,
						jump:onPageChange1,
					});
					laypage.render({
						elem: 'bottomPager',
						curr: 1,
						count: allTotal,
						limit: pageSize,
						jump: onPageChange2,
					});
				}
				// ===================== 2 标准标题匹配分页函数========================================
				window.onTitlePageChange1=function(page,isFirst) {
					titlePage1 = page.curr;
					titlePage2=titlePage1;
					var p = page.curr;//改变当前页码
					var l = page.limit;
					if (!isFirst) {
						var key = $('#txtKey').val();
						if (!key) {
							layer.alert("请输入关键字", {
								title: "提示"
							});
							return;
						}
						window.search("search_ul_title_match",(p-1)*pageSize,pageSize,key);
						laypage.render({
							elem: 'titleBottomPager',
							count: titleTotal,
							limit: pageSize,
							curr: titlePage1,
							jump: onTitlePageChange2
						});
					}
				}
				// Function to handle page change events for pagination2
				window.onTitlePageChange2=function(page,isFirst) {
					titlePage1 = page.curr;
					titlePage2=titlePage1;
					var p = page.curr;//改变当前页码
					var l = page.limit;
					if (!isFirst) {
						var key = $('#txtKey').val();
						if (!key) {
							layer.alert("请输入关键字", {
								title: "提示"
							});
							return;
						}
						window.search("search_ul_title_match",(p-1)*pageSize,pageSize,key);
						laypage.render({
							elem: 'titleTopPager',
							count: titleTotal,
							limit: pageSize,
							curr: titlePage1,
							jump: onTitlePageChange1
						});
					}
				}
				//分页
				window.initTitlePager=function(total)
				{
					titlePage1=1;
					titlePage2=1;
					titleTotal=total;
					laypage.render({
						elem:'titleTopPager',
						curr:1,
						count:titleTotal,
						limit:pageSize,
						jump:onTitlePageChange1,
					});
					laypage.render({
						elem:'titleBottomPager',
						curr:1,
						count:titleTotal,
						limit:pageSize,
						jump:onTitlePageChange2,
					});
				}
				//绑定事件
				$(document).on('click', '#btnSearch', function(data) {
					var key=$('#txtKey').val();
					if(!key) {
						layer.alert("请输入关键字", {
							title: "提示"
						});
						return;
					}
					layer.msg("加载中", {icon: 16, time: 500000});
					window.writelog("Solr_Query",'','','','',key);
					$("#matchTab").show();
					$.ajax({
						url: MODULE_PATH+"solrsearch?start=0&rows="+pageSize+"&key=content&value="+key,
						data:{},
						type:"get",
						dataType:"json",
						headers : {'Content-Type' : 'application/json;charset=utf-8'}, //接口json格式
						success:function(data){
							layer.closeAll();
							console.log(data);
							$("#search_ul").empty();
							$("#spAllMatch").text("（"+data.result.total+"）");
							window.initPager(data.result.total);
							window.fillDataList("search_ul",data,1);
						},
						error:function(data){
							layer.msg(JSON.stringify(data), {icon: 1, time: 5000}, function () {
								layer.closeAll();
							});
							// layer.alert(JSON.stringify(data), {
							// 	title: data
							// });
						}
					});
					//填充标准名称匹配的列表
					$.ajax({
						url: MODULE_PATH+"solrtitlesearch?start=0&rows="+pageSize+"&key=stdOrgName&value="+key,
						data:{},
						type:"get",
						dataType:"json",
						headers : {'Content-Type' : 'application/json;charset=utf-8'}, //接口json格式
						success:function(data){
							console.log(data);
							$("#search_ul_title_match").empty();
							$("#spTitleMatch").text("（"+data.result.total+"）");
							window.initTitlePager(data.result.total);
							window.fillDataList("search_ul_title_match",data,1);
						},
						error:function(data){
							layer.alert(JSON.stringify(data), {
								title: data
							});
						}
					});
				});
				$("#txtKey").keydown(function (e) {//当按下按键时
					if (e.which == 13) {//.which属性判断按下的是哪个键，回车键的键位序号为13
						var key=$('#txtKey').val();
						if(!key) {
							layer.alert("请输入关键字", {
								title: "提示"
							});
							return;
						}
						layer.msg("加载中", {icon: 16, time: 500000});
						window.writelog("Solr_Query",'','','','',key);
						$("#matchTab").show();
						$.ajax({
							url: MODULE_PATH+"solrsearch?start=0&rows="+pageSize+"&key=content&value="+key,
							data:{},
							type:"get",
							dataType:"json",
							headers : {'Content-Type' : 'application/json;charset=utf-8'}, //接口json格式
							success:function(data){
								layer.closeAll();
								console.log(data);
								$("#search_ul").empty();
								//$("#summary").text("共搜索到"+data.result.total+"个标准");
								$("#spAllMatch").text("（"+data.result.total+"）");
								window.initPager(data.result.total);
								window.fillDataList("search_ul",data,1);
							},
							error:function(data){
								layer.msg(JSON.stringify(data), {icon: 1, time: 5000}, function () {
									layer.closeAll();
								});
							}
						});
						//填充标准名称匹配的列表
						$.ajax({
							url: MODULE_PATH+"solrtitlesearch?start=0&rows="+pageSize+"&key=stdOrgName&value="+key,
							data:{},
							type:"get",
							dataType:"json",
							headers : {'Content-Type' : 'application/json;charset=utf-8'}, //接口json格式
							success:function(data){
								console.log(data);
								$("#search_ul_title_match").empty();
								$("#spTitleMatch").text("（"+data.result.total+"）");
								window.initTitlePager(data.result.total);
								window.fillDataList("search_ul_title_match",data,1);
							},
							error:function(data){
								layer.alert(JSON.stringify(data), {
									title: data
								});
							}
						});
					}
				});
				//搜索结果填充到列表中
				window.fillDataList=function(container,data,startIndex)
				{
					for(var i=0;i<data.result.datalist.length;i++) {
						var line = data.result.datalist[i];
						var highlight=[];
						var matchCount=0;
						if(data.result.highlight)
						{
							for(var key in data.result.highlight)
							{
								if(key==line.id)
								{
									var itemLight=data.result.highlight[key];
									console.log(itemLight);
									if(itemLight)
									{
										console.log(itemLight.primaryCoverage);
										if(itemLight.primaryCoverage) {
											highlight.push(...itemLight.primaryCoverage);
										}
										if(itemLight.content) {
											highlight.push(...itemLight.content);
										}
										console.log(highlight);
									}
									break;
								}
							}
							if(highlight.length==0)
							{
								highlight.push(line.primaryCoverage);
							}
							//匹配数量求和
							for(var j=0;j<highlight.length;j++)
							{
								var count=highlight[j].split('<em>').length-1;
								if(count>0)
								{
									matchCount+=count;
								}
							}
							if(matchCount==0)
							{
								matchCount=1;
							}
						}
						//$("#"+container).append('<li class="std-title"><span><a class="pdf-title" title="点击查看详情" target="_self" onclick="window.edit(\''+line.stdNo+'\',\''+line.stdOrgName+'\')" href="javascript:void(0);">' + line.stdOrgName + '</a></span><span style="color:gray;font-size:13px;padding-left:10px;"><a target="_blank"  title="下载"  class="d-anchor iconfont icon-xiazaidaoru"  href="/source/downloadStandard?stype='+sType+'&fileName=' + encodeURIComponent(line.pdfFileName) + '"></a><a style="margin-left:10px;"  title="在线查看"  class="d-anchor iconfont icon-chakan" href="javascript:void(0);" onclick="onlineread(\''+line.id+'\')"></a></span></li>')
						//$("#"+container).append('<li class="std-title"><span><a class="pdf-title" title="查看详情" target="_self" onclick="window.edit(\''+line.stdNo+'\',\''+line.stdOrgName+'\')" href="javascript:void(0);">' + line.stdOrgName + '</a></span><span style="color:gray;font-size:13px;padding-left:10px;"><a target="_blank"  title="下载" class="d-anchor" href="/source/downloadStandard?stype='+sType+'&fileName=' + encodeURIComponent(line.pdfFileName) + '">[下载]</a><a title="在线查看" style="margin-left:10px;"  class="d-anchor"  href="javascript:void(0);" onclick="onlineread(\''+line.id+'\')">[查看]</a></span></li>')
						$("#"+container).append('<li class="std-title">');
						$("#"+container).append('<span class="item-idx">'+startIndex+++'.</span>');
						$("#"+container).append('<span><a class="pdf-title" title="详情查看" target="_self" onclick="window.edit(\''+line.stdNo+'\',\''+line.stdOrgName+'\')" href="javascript:void(0);">' + line.stdOrgName + '</a></span>');
						$("#"+container).append('<span style="color:gray;font-size:13px;padding-left:10px;">');
						//$("#"+container).append('<a target="_blank"  title="下载" class="d-anchor" href="/source/downloadStandard?stype='+sType+'&fileName=' + encodeURIComponent(line.pdfFileName) + '">[下载]</a>');

						//$("#"+container).append('<a target="_blank"  title="下载" class="d-anchor" href="/source/downloadStandard?stype='+sType+'&fileName=' + encodeURIComponent(line.pdfFileName) + '">[下载]</a>');
						$("#"+container).append('<a title="在线阅读" style="margin-left:10px;"  class="d-anchor"  href="javascript:void(0);" onclick="onlineread(\''+line.id+'\')">[在线阅读]</a>')
						if(line.isCollected=='1')
						{
							$("#"+container).append('<a title="点击取消收藏" style="margin-left:10px;"  class="d-anchor"  href="javascript:void(0);" onclick="window.discollectWrap(\''+container+'\',\''+line.id+'\',\''+line.stdOrgName+'\')">[已收藏]</a>')
						}
						else
						{
							$("#"+container).append('<a title="点击收藏" style="margin-left:10px;"  class="d-anchor"  href="javascript:void(0);" onclick="window.collectWrap(\''+container+'\',\''+line.id+'\',\''+line.stdOrgName+'\')">[未收藏]</a>')
						}
						$("#"+container).append('<a title="意见反馈" style="margin-left:10px;"  class="d-anchor"  href="javascript:void(0);" onclick="window.opinion(\''+line.id+'\',\''+line.stdOrgName+'\')">[意见反馈]</a>')
						$("#"+container).append('</span></li>');
						$("#"+container).append('<li class="std-summary"><span class="item-title">[标准号]</span><span class="std-no">' + line.stdNo + '</span><span class="item-title match-count" style="width:auto !important;">匹配次数:<span style="color:red;padding-left:3px !important;">' + matchCount + '</span>次</span></li>')
						$("#"+container).append('<li class="std-summary"><span class="item-title">[起草单位]</span><span class="std-ellipsis">' + line.draftingUnit + '</span></li>')
						$("#"+container).append('<li class="std-summary"><span class="item-title">[起草人]</span><span class="std-ellipsis">' + line.drafter + '</span></li>')
						$("#"+container).append('<li class="std-primary">' + (highlight.length>0 ? highlight[0] : '')+'</li>');
						if(highlight.length>1)
						{
							$("#"+container).append('<li class="std-primary"><a style="color:red;" href="javascript:void(0);" onclick="window.showMore(\''+line.id+'\',\''+line.stdNo+'\',\''+line.stdOrgName+'\')">[折叠显示/隐藏更多结果]</a></li>');
							var html=[];
							for (var j = 1; j < highlight.length; j++) {
								html.push('<div class="std-more">' + highlight[j] + '</div>');
							}
							html.push('<div class="std-more"><a style="color:red;" href="javascript:void(0);" onclick="window.showMore(\''+line.id+'\',\''+line.stdNo+'\',\''+line.stdOrgName+'\')">[折叠显示/隐藏更多结果]</a></div>');
							$("#"+container).append('<li class="std-primary std-content-more" id="light_' + line.id + '">'+html.join("")+'</li>');
						}
						$("#"+container).append('<li class="std-split"></li>')
					}
					$(".std-content-more").slideUp();
				}
				window.edit = function (stdNo,name) {
					layer.open({
						type: 2,
						title: '【'+name+'】',
						shade: 0.1,
						area: ['1000px', '500px'],
						content: MODULE_PATH + 'detailbystdno?stdNo=' + stdNo
					});
				};
				window.onlineread=function(id)
				{
					$.ajax({
						url: "/source/isBigFile?id=" + id,
						dataType: 'json',
						type: 'get',
						success: function (result) {
							if (result.success && result.data) {
								if(result.data*1>104857600)
								{
									//大文件，要先等待对应时间再加载
									var waitSeconds= Math.ceil(result.data*1/14898831);
									layer.msg("文件较大，正在加载中，请耐心等待...", {icon: 1, time: (waitSeconds+14)*1000});
									setTimeout(function(){ window.open('/pdf/viewer.html?file=/source/getStandardPdf/' + id,'blank',null,true);},(waitSeconds+14)*1000);
								}
								else
								{
									window.open('/pdf/viewer.html?file=/source/getStandardPdf/' + id,'blank',null,true);
								}
							} else {
								popup.failure(result.msg);
							}
						}
					})
				};
				window.collectWrap=function(container,id,stdName)
				{
					var cPage=container=="search_ul"?currPage:currPageTitleMatch;
					//layer.confirm('要收藏['+stdName+']吗？', {icon: 3, title: '提示'}, function (index) {
						//layer.close(index);
						window.collect1({data:{id:id,isCollected:0}},function(){
							window.search(container,(cPage-1)*pageSize,pageSize,$("#txtKey").val());
						});
					//});
				};
				window.discollectWrap=function(container,id,stdName,container)
				{
					var cPage=container=="search_ul"?currPage:currPageTitleMatch;
					//layer.confirm('要取消收藏['+stdName+']吗？', {icon: 3, title: '提示'}, function (index) {
						//layer.close(index);
						window.discollect1({data:{id:id,isCollected:1}},function(){
							window.search(container,(cPage-1)*pageSize,pageSize,$("#txtKey").val());
						});
					//});
				};
				window.opinion=function(id,stdName)
				{
					parent.layer.open({
						type: 2,
						title: "【"+stdName+"】意见反馈列表",
						shade: 0.1,
						area: ['1200px', '630px'],
						content: '/stdopinion/main?stdId='+id
					});
				};
				window.search=function(container,start,rows,key)
				{
					layer.msg("加载中", {icon: 16, time: 500000});
					var dataField=container=="search_ul"?"content":"stdOrgName";
					var apiName=container=="search_ul"?"solrsearch":"solrtitlesearch";
					$.ajax({
						url: MODULE_PATH+apiName+"?start="+start+"&rows="+rows+"&key="+dataField+"&value="+key,
						data:{},
						type:"get",
						dataType:"json",
						headers : {'Content-Type' : 'application/json;charset=utf-8'}, //接口json格式
						success:function(data){
							layer.closeAll();
							$("#"+container).empty();
							//$("#spAllMatch").text("（"+data.result.total+"）");
							//$("#spTitleMatch").text("（"+data.result.titleMatchCount+"）");
							//$("#summary").text("共搜索到"+data.result.total+"个标准");
							window.fillDataList(container,data,start+1);

						},
						error:function(data){
							layer.msg(JSON.stringify(data), {icon: 1, time: 5000}, function () {
								layer.closeAll();
							});
						}
					});
				};
				window.collect1=function(obj,callback)
				{
					var data=obj.data;
					console.log(obj);
					//取消收藏
					if(obj.data["isCollected"]=="0")
					{
						$.ajax({
							url: "/personalcoll/collect",
							dataType: 'json',
							type: 'post',
							data:JSON.stringify({stdid: obj.data['id']}),
							contentType: 'application/json',
							success: function (result) {
								if (result.success) {
									layer.msg("收藏成功，请在个人收藏夹中查看！", {icon: 1, time: 1000});
									if(callback)
									{
										callback();
									}
								} else {
									layer.msg("收藏失败！", {icon: 5, time: 1000});
								}
							}
						})
					}
					else if(obj.data["isCollected"])
					{
						//取消收藏
						$.ajax({
							url: "/personalcoll/discollect",
							dataType: 'json',
							type: 'post',
							data:JSON.stringify({stdid: obj.data['id']}),
							contentType: 'application/json',
							success: function (result) {
								if (result.success) {
									layer.msg("已取消收藏!", {icon: 1, time: 1000});
									if(callback)
									{
										callback();
									}
								} else {
									layer.msg("取消收藏失败！", {icon: 5, time: 1000});
								}
							}
						})
					}

				};
				window.discollect1=function(obj,callback)
				{
					$.ajax({
						url: "/personalcoll/discollect",
						dataType: 'json',
						type: 'post',
						data:JSON.stringify({stdid: obj.data['id']}),
						contentType: 'application/json',
						success: function (result) {
							if (result.success) {
								layer.msg("已取消收藏！", {icon: 1, time: 1000},function(){
									if(callback)
									{
										callback();
									}
								});
							} else {
								layer.msg("取消失败，请重试！", {icon: 5, time: 1000});
							}
						}
					})
				};
				//solr搜索页面：查看一个标准的更多匹配结果
				window.showMore = function (id,stdNo,stdOrgName) {
					$('#light_'+id).slideToggle('fast','swing',function(){
						console.log('id_'+id+':'+$('#light_'+id).css("display"));
						if($('#light_'+id).css("display")=='block')
						{
							window.writelog("Solr_Query_More_Result",stdNo,stdOrgName,'','',$("#txtKey").val());
						}
					});
				};
				window.writelog=function(_logTypeName, _itemId, _itemName, _columnId, _columnName, _param)
				{
					var data={logTypeName:_logTypeName,itemId:_itemId,itemName:_itemName,columnId:_columnId,columnName:_columnName,param:_param};
					console.log('日志记录');
					console.log(data);
					$.ajax({
						url: "/stdlog/log",
						dataType: 'json',
						type: 'post',
						data:JSON.stringify(data),
						contentType: 'application/json',
						success: function (result) {
							if (result.success) {
								console.log('日志记录成功')
							} else {
								layer.msg("日志记录失败"+result.msg, {icon: 5, time: 1000});
							}
						}
					})
				};
			});
		</script>
	</body>
	<style>
		.match-count
		{
			color:red;
		}
		.d-anchor-down
		{
			display: inline-block;
			background: url("//admin//images//download.png") no-repeat 16px 16px;
			background-size: cover;
			-webkit-background-size: cover;
			-o-background-size: cover;
			background-position: center 0;
			padding:8px;
		}
		.d-anchor-look
		{
			background: url("//admin//images//look.png") no-repeat 16px 16px;
			background-size: cover;
			-webkit-background-size: cover;
			-o-background-size: cover;
			background-position: center 0;
		}
		.d-anchor{
			color: #666;
			font-size: 14px !important;
			display: inline-block;
			margin: 0px;
			text-decoration: none;
		}


		.std-content-more {
			display: block;
		}
		em {
			color:red;
			font-style:normal;
			font-weight: bold;
		}
		.pdf-title{
			font-family: 宋体;
			font-weight: bold;
			font-size:17px;
		}

		.item-title{
			color:#999 !important;
			font-size:13px;
			padding:0 0 0 30px;
			font-style: normal;
			display: block;
			float:left;
			width:80px;
			height: 20px;
			line-height: 20px;
		}
		.std-summary span{
			color:#333;
			font-size:14px;
		}
		.std-summary span:first-child{
			padding-left:0px !important;
		}
		.std-summary{
			font-size:17px;padding:0px 10px;color:#999;
			background-color: #fff;
			font-style: normal;
			height:20px;
		}
		.std-summary:after{
			content: '';
			clear: both;
			display: block;
		}
		.std-primary{
			padding:0 10px;
			background-color: #fff;
		}
		.std-more
		{
			padding:0;
			background-color: #fff;
		}
		.std-title{
			background-color: #fff;
			padding:0px 0 0 10px;
			margin-top:0px;
		}
		.std-split{
			height: 20px;
			background-color:#f9f9f9;
		}
		.std-summary:hover,.std-primary:hover{
			background-color:#f9f9f9;
		}
		.std-no{
			font-size:13px !important;
			font-weight: bold;
			color:#333 !important;
			font-style: normal !important;
			line-height: 20px;
			height: 20px;
			float:left;
			display:block;
			width:500px;
		}
		.match-count{
			display: inline-block;
			float:right;
		}
		.std-ellipsis{
			width:89%;
			float:left;
			display: block;
			overflow: hidden;
		   text-overflow: ellipsis;
		   white-space: nowrap;
			line-height: 20px;
			height: 20px;
		}
		#pager,#pager_title_match
		{
			text-align: center;
		}
		.tip-info
		{
			text-align: center;
			color:blue;
			font-size:12px;
		}
	</style>
</html>
