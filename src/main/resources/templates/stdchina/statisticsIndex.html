<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('网站统计')"/>
</head>
<body class="pear-container">
<div class="layui-card"   style="background-color:#f0f2f5 !important;">
    <div class="layui-card-body">
        <div class="c-list">
            <div>
                <div id="list1" class="static-c layui-card"></div>
            </div>
        </div>
        <div class="c-list">
            <div>
                <div id="list2" class="static-c layui-card"></div>
            </div>
        </div>
    </div>
</div>
</body>

<th:block th:include="include :: footer"/>
<script>
    layui.use(['echarts','table', 'form', 'jquery', 'popup', 'common','upload','layer','element','count'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;
        var count = layui.count;
        let echarts = layui.echarts;
        let MODULE_PATH = "/chinastd/";
        window.fillChinaData=function(data)
        {
            var total=0;
            for(var i=0;i<data.length;i++)
            {
                if(data[i].std_type=="china")
                {
                    total+= 1*(data[i].count);
                }
            }
            var html = [];
            var classArr=['国家军用标准','兵器行业标准','集团公司标准','国家标准','航空行业标准','航天行业标准','电子行业标准','船舶行业标准',
                '计量技术规范','计量检定规程','机械行业标准','化工行业标准','汽车行业标准','铁道行业标准','石油天然气行业标准','电力行业标准',
                '通信行业标准'];
            html.push(`<div class="layui-card-header">
                            <i class="icon pear-icon pear-icon-layers"></i>
                           国内标准统计(共收录${total}条标准）</div>`);
            html.push("<div class='layui-card-body'>");
            html.push('<table class="layui-table" lay-skin="line" cellspacing="0" cellpadding="0" border="0">');
            html.push("<thead>");
            html.push("<tr>");
            // html.push("<th>编号");
            // html.push("</th>");
            html.push("<th>分类名称");
            html.push("</th>");
            html.push("<th  style=\"text-align: center\">标准数量");
            html.push("</th>");
            html.push("<th style='width:80px;'>");
            html.push("</th>");
            // html.push("<th>编号");
            // html.push("</th>");
            html.push("<th>分类名称");
            html.push("</th>");
            html.push("<th  style=\"text-align: center\">标准数量");
            html.push("</th>");
            html.push("</tr>");
            html.push("</thead>");
            html.push("<tbody>");
            for (var i = 0; i < classArr.length; i++) {
                var  className=classArr[i];
                var  counts="0";
                html.push("<tr>");
                for(var idx=0;idx<data.length;idx++)
                {
                    if(data[idx].std_class==className && data[idx].std_type=="china")
                    {
                        counts=data[idx].count;
                        break;
                    }
                }
                // html.push(`<td class="idx-td" style="text-align: center">${i+1}</td>`);
                html.push(`<td><div title="${className}" class="content-no">${className}</div></td>`);
                html.push(`<td class="counts-td" style="text-align: center">${counts}</td>`);
                html.push("<td style='width:80px;'>");
                html.push("</td>");
                i++;
                className="";
                counts="";
                if(classArr.length>i) {
                    counts="0";
                    className=classArr[i];
                    for(var idx=0;idx<data.length;idx++)
                    {
                        if(data[idx].std_class==className && data[idx].std_type=="china")
                        {
                            counts=data[idx].count;
                            break;
                        }
                    }
                }
                html.push(`<td><div title="${className}" class="content-no">${className}</div></td>`);
                html.push(`<td class="counts-td" style="text-align: center">${counts}</td>`);
                html.push("</tr>");
            }
            // var fillEmpty = 12 - data.length;
            // if (fillEmpty > 0) {
            //     for (var i = 0; i < fillEmpty; i++) {
            //         html.push("<tr>");
            //         html.push("<td class='idx-td'><span class='white-sp'>-</span></td>");
            //         html.push("<td><span class='white-sp'>-</span></td>");
            //         html.push("<td><span class='white-sp'>-</span></td>");
            //         html.push("<td class='counts-td'><span class='white-sp'>-</span></td>");
            //         html.push("</tr>");
            //     }
            // }
            html.push("</tbody>");
            html.push("</table>");
            html.push("</div>");
            $("#list1").html(html.join(""));
        };
        window.fillUsaData=function(list)
        {
            var data=[];
            var total=0;
            for(var i=0;i<list.length;i++)
            {
                if(list[i].std_type=="usa")
                {
                    total+=1*(list[i].count);
                    data.push(list[i]);
                }
            }
            console.log("usa :");
            console.log(data);
            var html = [];
            html.push(`<div class="layui-card-header">
                            <i class="icon pear-icon pear-icon-layers"></i>
                           国外标准统计(共收录${total}条标准）</div>`);
            html.push("<div class='layui-card-body'>");
            html.push('<table class="layui-table" lay-skin="line" cellspacing="0" cellpadding="0" border="0">');
            html.push("<thead>");
            html.push("<tr>");
            // html.push("<th>编号");
            // html.push("</th>");
            html.push("<th>分类名称");
            html.push("</th>");
            html.push("<th  style=\"text-align: center\">标准数量");
            html.push("</th>");
            html.push("<th style='width:80px;'>");
            html.push("</th>");
            // html.push("<th>编号");
            // html.push("</th>");
            html.push("<th>分类名称");
            html.push("</th>");
            html.push("<th  style=\"text-align: center\">标准数量");
            html.push("</th>");
            html.push("</tr>");
            html.push("</thead>");
            html.push("<tbody>");
            for (var i = 0; i < data.length; i++) {
                html.push("<tr>");
                // html.push(`<td class="idx-td" style="text-align: center">${i+1}</td>`);
                var  className=data[i].std_class;
                var  counts=data[i].count;
                html.push(`<td><div title="${className}" class="content-no">${className}</div></td>`);
                html.push(`<td class="counts-td" style="text-align: center">${counts}</td>`);
                html.push("<td style='width:80px;'>");
                html.push("</td>");
                i++;
                className="";
                counts="0";
                if(data.length>i) {
                    className = data[i].std_class;
                    counts=data[i].count;
                }
                html.push(`<td><div title="${className}" class="content-no">${className}</div></td>`);
                html.push(`<td class="counts-td" style="text-align: center">${counts}</td>`);
                html.push("</tr>");
            }
            // var fillEmpty = 20 - data.length;
            // if (fillEmpty > 0) {
            //     for (var i = 0; i < fillEmpty; i++) {
            //         html.push("<tr>");
            //         html.push("<td class='idx-td'><span class='white-sp'>-</span></td>");
            //         html.push("<td><span class='white-sp'>-</span></td>");
            //         html.push("<td><span class='white-sp'>-</span></td>");
            //         html.push("<td class='counts-td'><span class='white-sp'>-</span></td>");
            //         html.push("</tr>");
            //     }
            // }
            html.push("</tbody>");
            html.push("</table>");
            html.push("</div>");
            $("#list2").html(html.join(""));
        }

        window.getStatistics=function() {
            $.ajax({
                url: MODULE_PATH + "statisticsList",
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    if (result.code==0) {
                        var data = result.data;
                        console.log(data);
                        window.fillChinaData(data);
                        window.fillUsaData(data);
                      }
                }
            });
        }
        window.getStatistics();
    });
</script>
<style>
    .idx-td
    {
        min-width:28px;
    }
    .counts-td
    {
        min-width:85px;
    }
    .layui-table th, .layui-table td
    {
        font-size:13px;
    }
    .layui-table[lay-skin="line"]
    {
        border-width: 0px !important;
    }
    .white-sp
    {
        color:white;
    }
    #summary > div > div:hover
    {
        background-color: #d7f3e1;
        cursor:pointer;
    }
    #summary .static-icon
    {
        color: #36b368;
        font-size:45px;
        margin-left:20px;
    }
    .top-table
    {
        margin-top:30px;
    }
    /*  .static-c
      {
          box-shadow: 0px 0 55px rgba(255,255,255,.1) inset;
          position: relative;
          box-sizing: border-box;
          border: 1px solid #034c6a;
          border-radius: 15px;
      }
      .static-c th{
          font-size: 14px;
          font-weight: 600;
          color: #61d2f7;
          text-align: center;
      }
      .static-c td{
          color: #fff;
          font-size: 12.8px;
          text-align: center;
      }
      .static-c tbody tr:nth-child(2n+1)
      {
          background-color: #1e2a3c;
      }*/
    .c-list
    {
        width:100%;
        margin-bottom:5px;
        margin-top:10px;
    }
    /*#list > div:nth-child(1)*/
    /*{*/
    /*    margin-right:2%;*/
    /*}*/
    .c-list > div::after
    {
        display: block;
        clear: both;
        content: '';
        visibility: hidden;
        height: 0;
    }
    .c-list table
    {
        width:100%;
    }
    #summary
    {
        width:100%;
        margin:30px auto 5px;
        font-size:17px;
        font-family: 黑体;
        color: #2B4227;
    }
    #summary > div:after
    {
        content:'';
        display: block;
        clear: both;
        visibility: hidden;
        height: 0;
    }
    #summary > div > div
    {
        float:left;
        width:21%;
        margin-right:3%;
        padding:24px 0;
        background-color:#fff;
        border-radius: 5px;
        box-shadow: 0px 0 55px rgba(255,255,255,.1) inset;
        margin-bottom: 15px;
    }
    #summary > div>div>div
    {
        padding-top:10px;
    }
    #summary:last-child
    {
        margin-right:0;
    }
    #summary:after
    {
        content: "";
        display: block;
        clear:both;
        visibility: hidden;
        height: 0;
    }
    #summary>div>div>div:nth-child(1)
    {
        float:left;
        width:30%;
        margin-top:10px;
    }
    #summary>div>div>div:nth-child(2)
    {
        float:left;
        width:70%;
        color:#666;
    }
    #summary>div>div:after
    {
        content: "";
        display: block;
        clear:both;
        visibility: hidden;
        height: 0;
    }
    #summary>div>div:hover
    {
        color:white;
    }
    .search-container
    {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }
    .search-container .item
    {
        display: flex;
        flex-direction: row;
        margin-top:0.625rem;
    }
    .search-container .item .sub-item button
    {
        margin-top:10px;
    }
    .search-container .item label
    {
        min-width: 80px;
        padding: 8px 1px 8px 10px;
        text-align: right;
    }
    .layui-table-tool
    {
        height:70px !important;
        line-height:30px !important;
        padding:10px 0 0 10px !important;
        min-height:0px !important;
    }
    .layui-card-body .layui-form
    {
        margin-top:0px !important;
    }
    .layui-form-item .layui-form-label,.layui-form-item  .layui-input-inline{
        margin-bottom:7px;
    }
    .layui-form-item{
        padding-bottom:0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow:auto;
        white-space: normal;
    }
    #resultInfo{
        width:300px;
        text-align: center;
        color:#999;
        margin:10px auto 0;
    }
    .layui-table thead th {
        font-weight: bold;
    }
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .is-show-0{
        display: none;
    }
    .layui-table-page
    {
        text-align: center !important;
    }

    caption
    {
        padding: 0 0 5px 0;
        width: 700px;
        font: italic 11px "Trebuchet MS" , Verdana, Arial, Helvetica, sans-serif;
        text-align: right;
    }

    th
    {
        font: bold 11px "Trebuchet MS" , Verdana, Arial, Helvetica, sans-serif;
        letter-spacing: 1px;
        text-transform: uppercase;
        text-align: center;
        padding: 6px 6px 6px 12px;
    }
    th.nobg
    {
        border-top: 0;
        border-left: 0;
        background: none;
    }
    td
    {
        padding: 6px 6px 6px 12px;
    }
    .span_link
    {
        cursor:pointer;
        color:Black;
    }
    .tr_Category
    {
    }
    .pageLink
    {
        color:Blue;
        margin-left:2px;
        margin-right:2px;
    }
    .tr_Category_P td
    {
        background-color:#EEEEFF;
    }
    .top-title
    {
        width: 220px;
        height: 35px;
        line-height: 33px;
        background-color: #034c6a;
        border-radius: 18px;
        position: absolute;
        top: -17px;
        left: 50%;
        margin-left: -110px;
        color: #fff;
        font-size: 18px;
        font-weight: 600;
        box-sizing: border-box;
        padding-left: 45px;
    }
    .span-count
    {
        min-height: 24px;
    }
    .content-td
    {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 210px;
    }
    .content-no
    {
        overflow: hidden;
        white-space: nowrap;
       /* text-overflow: ellipsis;*/
        width: 130px;
    }
    .layui-table
    {
        margin-bottom: 25px !important;
    }
    .layui-card-body
    {
        padding-top:0px !important;
    }
    .layui-card-header
    {
        font-weight: bold;
    }
</style>
</html>
