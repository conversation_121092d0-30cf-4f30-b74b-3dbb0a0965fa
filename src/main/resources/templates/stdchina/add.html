<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('信息配置修改')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">标准标识<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="stdIdentification"  lay-verify="required"
                               placeholder="请输入标准标识,如GJB、WJ、Q/CNG等" type="text" />
                       <!-- <select  class="layui-select" name="stdIdentification" placeholder="请选择标准标识">
                            <option selected value="GJB">GJB-国家军用标准</option>
                            <option value="GJB/J">GJB/J-国家军用标准</option>
                            <option value="GJB/Z">GJB/Z-国家军用标准</option>
                            <option value="WJ">WJ-兵器行业标准</option>
                            <option value="WJ/T">WJ/T-兵器行业推荐性标准</option>
                            <option value="WJ/Z">WJ/Z-兵器行业指导性技术文件</option>
                            <option value="Q/CNG">Q/CNG-集团公司标准</option>
                            <option  value="GB">GB-国家标准</option>
                            <option  value="GB/T">GB/T-国家推荐标准</option>
                            <option value="KGJB">KGJB-国家军用标准</option>
                            <option value="KGJB/J">KGJB/J-国家军用标准</option>
                            <option value="KGJB/Z">KGJB/Z-国家军用标准</option>
                        </select>-->
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准类别<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="stdClass"  lay-verify="required"
                               placeholder="请输入标准类别"  type="text" />
<!--                        <select  class="layui-select" name="stdClass" placeholder="请选择标准类别">-->
<!--                            <option selected value="国家军用标准">国家军用标准</option>-->
<!--                            <option value="兵器行业标准">兵器行业标准</option>-->
<!--                            <option value="集团公司标准">集团公司标准</option>-->
<!--                        </select>-->
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准号<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="stdNo"
                               placeholder="请输入标准号"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准名称(原文)<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" lay-verify="required" name="stdOrgName"
                               placeholder="请输入标准名称"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准名称(中文)：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="stdChineseName"
                               placeholder="请输入中文名称"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准名称(英文)：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="stdEnglishName"
                               placeholder="请输入英文名称"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">文本语言<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="stdLangugage" lay-verify="required"
                               placeholder="请输入文本语言,如：中文、英文等" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">密级：</label>
                    <div class="layui-input-block">
                        <select  class="layui-select" name="securityClass" placeholder="请选择密级">
                            <option selected value="非密">非密</option>
<!--                            <option value="敏感">敏感</option>-->
<!--                            <option value="秘密">秘密</option>-->
<!--                            <option value="机密">机密</option>-->
<!--                            <option value="绝密">绝密</option>-->
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">分类号：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="catetoryNo"
                               placeholder="请输入分类号" type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">ICS号：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="stdIcs"
                               placeholder="请输入ICS号"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">发布机构：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="pubDept"
                               placeholder="请输入发布机构"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">发布日期：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" style="width:120px;" class="layui-input"  name="pubDate" id="pubDate"
                               placeholder="请输入发布日期"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">实施日期：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" style="width:120px;" class="layui-input"  name="implementationDate" id="implementationDate"
                               placeholder="请输入实施日期"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">提出单位：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="advanceDept"
                               placeholder="请输入提出单位"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">起草单位：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="draftingUnit"
                               placeholder="请输入起草单位"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">起草人：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="drafter"
                               placeholder="请输入起草人"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准状态：</label>
                    <div class="layui-input-block">
                        <select  class="layui-select" name="stdStatus" placeholder="请选择标准状态">
                            <option selected value="现行">现行</option>
                            <option value="废止">废止</option>
                            <option value="被替代">被替代</option>
                            <option value="限用">限用</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">代替标准号（老标准）：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" name="alternateStdNo"
                               placeholder="请输入代替标准号（老标准）"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">被替代的标准号（新标准）：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input" name="supersededStdNo"
                               placeholder="请输入被替代的标准号（新标准）"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">页数<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" style="width:80px;"  class="layui-input"  name="pageCount" lay-verify="required|isValidPageNo"
                               placeholder="请输入页数"  value="0" type="number" />
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准年代<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" style="width:120px;"  class="layui-input"  name="stdOcr"  id="stdOcr"
                               placeholder="请输入标准年代"  type="number"   lay-verify="required|isFourDigits"/>
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">pdf文件名<span style="color:red;">*</span>：</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  name="pdfFileName" lay-verify="required"
                               placeholder="请输入pdf文件名"  type="text" />
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">主要内容：</label>
                    <div class="layui-input-block">
                        <textarea autocomplete="off"  class="layui-input"  name="primaryCoverage" style="padding:5px;height:100px;"
                                  placeholder="请输入主要内容"  type="text" ></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-update" lay-submit=""
                    type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button class="pear-btn pear-btn-sm" type="reset">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
    <input name="id"  type="hidden" />
</form>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery','laydate'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        var laydate = layui.laydate;

        laydate.render({
               elem: '#implementationDate',
               type:'date',
               theme: 'molv',
               trigger: 'click',
               done: function(value, date) {
               }
           });

        laydate.render({
            elem: '#pubDate',
            type:'date',
            theme: 'molv',
            trigger: 'click',
            done: function(value, date) {
            }
        });

        laydate.render({
            elem: '#stdOcr',
            type:'year',
            theme: 'molv',
            trigger: 'click',
            done: function(value, date) {
            }
        });

        form.on('submit(dict-type-update)', function (data) {
            $.ajax({
                url: '/chinastd/save',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'post',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                            parent.layui.table.reload('role-table');
                            //parent.window.setUpload();
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });
        // 添加自定义验证规则
        form.verify({
            isFourDigits: function(value){
                if(!new RegExp("^[0-9]{4}$").test(value)){
                    return '请输入4位数字';
                }
            },
            isValidPageNo: function(value){
                if(value*1<=0){
                    return '请输入有效的页数';
                }
            }
        });
    })
</script>
<style>
    .layui-input-block { line-height: 36px; }
    .layui-form-label{
        width:120px;
        text-align: right;
        color:#aaa;
    }
    .layui-input, .layui-textarea
    {
        width:90%;
    }
</style>
</body>
</html>
