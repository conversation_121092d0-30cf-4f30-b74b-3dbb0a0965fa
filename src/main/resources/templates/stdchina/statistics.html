<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('网站统计')"/>
</head>
<body class="pear-container">
<div class="layui-card"   style="background-color:#f0f2f5 !important;">
    <div class="layui-card-body">
<!--        <div id="summary">-->
<!--            <div>-->
<!--                <div  style="background-color: #d7f3e1 !important;">-->
<!--                    <div>-->
<!--                        &lt;!&ndash;  <img src="/admin/images/all.svg" width="100" height="100">&ndash;&gt;-->
<!--                        <a target="_blank" href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-chart-bar static-icon"></i></a>-->
<!--                    </div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex">-->
<!--                        <div>收录标准总数</div>-->
<!--                        <div class="span-count"><span id="spanCount1">0</span></div>-->
<!--                        </a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex">-->
<!--                        <i class="icon pear-icon pear-icon-calendar static-icon"></i></a>-->
<!--                    </div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex">-->
<!--                        <div>国家军用标准</div>-->
<!--                        <div class="span-count"><span id="spanCount2">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"> <i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"> <div>兵器行业标准</div>-->
<!--                        <div class="span-count"><span id="spanCount3">0</span></div>-->
<!--                        </a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"> <i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"> <div>集团公司标准</div>-->
<!--                        <div class="span-count"><span id="spanCount4">0</span></div>-->
<!--                        </a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div><a href="/chinastd/sIndex"> <i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>国家标准</div>-->
<!--                            <div class="span-count"><span id="spanCount5">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>航空行业标准</div>-->
<!--                            <div class="span-count"><span id="spanCount6">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>航天行业标准</div>-->
<!--                            <div class="span-count"><span id="spanCount7">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>电子行业标准</div>-->
<!--                            <div class="span-count"><span id="spanCount8">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>船舶行业标准</div>-->
<!--                            <div class="span-count"><span id="spanCount9">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>计量技术规范</div>-->
<!--                            <div class="span-count"><span id="spanCount10">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"> <div>计量检定规程</div>-->
<!--                            <div class="span-count"><span id="spanCount11">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>机械行业标准</div>-->
<!--                            <div class="span-count"><span id="spanCount12">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>化工行业标准</div>-->
<!--                            <div class="span-count"><span id="spanCount13">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>汽车行业标准</div>-->
<!--                            <div class="span-count"><span id="spanCount14">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>铁道行业标准</div>-->
<!--                            <div class="span-count"><span id="spanCount15">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>石油天然气行业标准</div>-->
<!--                            <div class="span-count"><span id="spanCount16">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>电力行业标准</div>-->
<!--                            <div class="span-count"><span id="spanCount17">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>通信行业标准</div>-->
<!--                            <div class="span-count"><span id="spanCount18">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div><a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>北约标准</div>-->
<!--                            <div class="span-count"><span id="spanCount19">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div>-->
<!--                    <div> <a href="/chinastd/sIndex"><i class="icon pear-icon pear-icon-calendar static-icon"></i></a></div>-->
<!--                    <div>-->
<!--                        <a href="/chinastd/sIndex"><div>美国相关标准</div>-->
<!--                            <div class="span-count"><span id="spanCount20">0</span></div></a>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--            &lt;!&ndash;        <div>&ndash;&gt;-->
<!--            &lt;!&ndash;            <div>&ndash;&gt;-->
<!--            &lt;!&ndash;                <div><i class="icon pear-icon pear-icon-chart-bar  static-icon"></i></div>&ndash;&gt;-->
<!--            &lt;!&ndash;                <div>&ndash;&gt;-->
<!--            &lt;!&ndash;                    <div>可下载标准总数</div>&ndash;&gt;-->
<!--            &lt;!&ndash;                    <div><span id="spnDown1"></span></div>&ndash;&gt;-->
<!--            &lt;!&ndash;                </div>&ndash;&gt;-->
<!--            &lt;!&ndash;            </div>&ndash;&gt;-->
<!--            &lt;!&ndash;            <div>&ndash;&gt;-->
<!--            &lt;!&ndash;                <div><i class="icon pear-icon pear-icon-data-view   static-icon"></i></div>&ndash;&gt;-->
<!--            &lt;!&ndash;                <div>&ndash;&gt;-->
<!--            &lt;!&ndash;                    <div>GJB可下载数</div>&ndash;&gt;-->
<!--            &lt;!&ndash;                    <div><span id="spnDown2"></span></div>&ndash;&gt;-->
<!--            &lt;!&ndash;                </div>&ndash;&gt;-->
<!--            &lt;!&ndash;            </div>&ndash;&gt;-->
<!--            &lt;!&ndash;            <div>&ndash;&gt;-->
<!--            &lt;!&ndash;                <div><i class="icon pear-icon  pear-icon-comment  static-icon"></i></div>&ndash;&gt;-->
<!--            &lt;!&ndash;                <div>&ndash;&gt;-->
<!--            &lt;!&ndash;                    <div>WJ可下载数</div>&ndash;&gt;-->
<!--            &lt;!&ndash;                    <div><span id="spnDown3"></span></div>&ndash;&gt;-->
<!--            &lt;!&ndash;                </div>&ndash;&gt;-->
<!--            &lt;!&ndash;            </div>&ndash;&gt;-->
<!--            &lt;!&ndash;            <div>&ndash;&gt;-->
<!--            &lt;!&ndash;                <div><i class="icon pear-icon pear-icon-calendar   static-icon"></i></div>&ndash;&gt;-->
<!--            &lt;!&ndash;                <div>&ndash;&gt;-->
<!--            &lt;!&ndash;                    <div>其他标准可下载数</div>&ndash;&gt;-->
<!--            &lt;!&ndash;                    <div><span id="spnDown4"></span></div>&ndash;&gt;-->
<!--            &lt;!&ndash;                </div>&ndash;&gt;-->
<!--            &lt;!&ndash;            </div>&ndash;&gt;-->
<!--            &lt;!&ndash;        </div>&ndash;&gt;-->
<!--        </div>-->
        <!--        <div>-->
        <!--            <div id="column1" style="float:left;"></div>-->
        <!--            <div id="column2" style="float:left;"></div>-->
        <!--        </div>-->
        <div class="c-list">
            <div>
                <div id="allList" class="static-c layui-card"></div>
            </div>
        </div>
        <div class="c-list">
            <div>
                <div id="chinaList" class="static-c layui-card"></div>
            </div>
        </div>
        <div class="c-list">
            <div>
                <div id="usaList" class="static-c layui-card"></div>
            </div>
        </div>
        <div id="list">
            <div>
                <div id="list1" class="static-c layui-card"></div>
                <div id="list2" class="static-c layui-card static-right"></div>
            </div>
            <div>
                <div id="list3" class="static-c layui-card"></div>
                <div id="list4" class="static-c layui-card static-right"></div>
            </div>
            <div>
                <div id="list5" class="static-c layui-card"></div>
                <div id="list6" class="static-c layui-card static-right"></div>
            </div>
            <div>
                <div id="list7" class="static-c layui-card"></div>
                <div id="list8" class="static-c layui-card static-right"></div>
            </div>
        </div>
    </div>
</div>
</body>

<th:block th:include="include :: footer"/>
<script>
    // layui.config({
    //     base: '/static/component/pear/module/'
    // });
    layui.use(['echarts','table', 'form', 'jquery', 'popup', 'common','upload','layer','element','count','menu','admin'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;
        var count = layui.count;
        let echarts = layui.echarts;
        let menuMgr=layui.menu;
        let admin=layui.admin;
        let menuList=[];
        admin.setConfigType("yml");
        admin.setConfigPath("/pear.config.yml");
        admin.render();
        // var column1 = echarts.init(document.getElementById('column1'),null, {
        //     width: 600,
        //     height: 300
        // });
        // var column2 = echarts.init(document.getElementById('column2'),null, {
        //     width: 600,
        //     height: 300
        // });
        // window.onresize = function() {
        //     column1.resize();
        //     column2.resize();
        // }
        let MODULE_PATH = "/chinastd/";
        let updateTime="";
        window.getMenuList=function()
        {
            $.ajax({
                url: MODULE_PATH + "menuData",
                dataType: 'json',
                type: 'get',
                success: function (result) {
                    if (result.code==0) {
                        var data = result.data;
                        console.log(data);
                        menuList=data;
                    }
                }
            });
        };
        window.getMenuList();
        window.fillChinaData=function(data)
        {
            var total=0;
            for(var i=0;i<data.length;i++)
            {
                if(data[i].stdType=="china")
                {
                    total+= 1*(data[i].counts);
                }
            }
            var allData=0;
            for(var i=0;i<data.length;i++)
            {
                allData+= 1*(data[i].counts);
            }
            var html = [];
            var classArr = [
                {name: '安全生产行业标准', sx: 'AQ'},
                {name: '船舶行业标准', sx: 'CB'},
                {name: '北京市地方标准', sx: 'DB11'},
                {name: '河北省地方标准', sx: 'DB13'},
                {name: '上海市地方标准', sx: 'DB31'},
                {name: '山东省地方标准', sx: 'DB37'},
                {name: '重庆市地方标准', sx: 'DB50'},
                {name: '陕西省地方标准', sx: 'DB61'},
                {name: '电力行业标准', sx: 'DL'},
                {name: '国家标准', sx: 'GB'},
                {name: '国家军用标准', sx: 'GJB'},
                {name: '航空行业标准', sx: 'HB'},
                {name: '化工行业标准', sx: 'HG'},
                {name: '生态环境标准', sx: 'HJ'},
                {name: '机械行业标准', sx: 'JB'},
                {name: '计量技术规范', sx: 'JJF'},
                {name: '计量检定规程', sx: 'JJG'},
                {name: '金融行业标准', sx: 'JR'},
                {name: '矿山安全行业标准', sx: 'KA'},
                {name: '煤炭行业标准', sx: 'MT'},
                {name: '集团公司标准', sx: 'Q/CNG'},
                {name: '汽车行业标准', sx: 'QC'},
                {name: '航天行业标准', sx: 'QJ'},
                {name: '气象行业标准', sx: 'QX'},
                {name: '电子行业标准', sx: 'SJ'},
                {name: '石油天然气行业标准', sx: 'SY'},
                {name: '铁道行业标准', sx: 'TB'},
                {name: '兵器行业标准', sx: 'WJ'},
                {name: '卫生行业标准', sx: 'WS'},
                {name: '通信行业标准', sx: 'YD'},
                {name: '邮政行业标准', sx: 'YZ'}
            ];
            $("#allList").html(`<div class="layui-card-header" style="text-align: center;padding: 10px 0;font-size: 16px;">
                           系统共收录标准${formatNumber(allData)}条</div>`);
            html.push(`<div class="layui-card-header">
                            <i class="icon pear-icon pear-icon-layers"></i>
                           国内标准统计(共收录${formatNumber(total)}条标准）</div>`);
            html.push("<div class='layui-card-body'>");
            html.push('<table class="layui-table" lay-skin="line" cellspacing="0" cellpadding="0" border="0">');
            html.push("<thead>");
            html.push("<tr>");
            // html.push("<th>编号");
            // html.push("</th>");
            html.push("<th style='width:25%;'>标准分类");
            html.push("</th>");
            html.push("<th  style=\"width:20%;text-align: center\">标准数量");
            html.push("</th>");
            html.push("<th style='width:10%;'>");
            html.push("</th>");
            html.push("<th style='width:25%;'>标准分类");
            html.push("</th>");
            html.push("<th  style=\"width:20%;text-align: center\">标准数量");
            html.push("</th>");
            html.push("</tr>");
            html.push("</thead>");
            html.push("<tbody>");
            for (var i = 0; i < classArr.length; i++) {
                var  className=classArr[i].name;
                var sx=classArr[i].sx;
                var  counts="0";
                html.push("<tr>");
                for(var idx=0;idx<data.length;idx++)
                {
                    if(data[idx].stdClass==className && data[idx].stdType=="china")
                    {
                        counts=data[idx].counts;
                        break;
                    }
                }
                // html.push(`<td class="idx-td" style="text-align: center">${i+1}</td>`);
                html.push(`<td><div title="${className}" class="content-no"><a href="javascript:void(0);" onclick="goSearch('china','${sx}')" target="_self">${className}(${sx})</a></div></td>`);
                html.push(`<td class="counts-td" style="text-align: center"><a href="javascript:void(0);" onclick="goSearch('china','${sx}')" target="_self">${formatNumber(counts)}</a></td>`);
                html.push("<td style='width:80px;'>");
                html.push("</td>");
                i++;
                className="";
                sx="";
                counts="";
                if(classArr.length>i) {
                    counts="0";
                    className=classArr[i].name;
                    sx=classArr[i].sx;
                    for(var idx=0;idx<data.length;idx++)
                    {
                        if(data[idx].stdClass==className && data[idx].stdType=="china")
                        {
                            counts=data[idx].counts;
                            break;
                        }
                    }
                    html.push(`<td><div title="${className}" class="content-no"><a href="javascript:void(0);" onclick="goSearch('china','${sx}')" target="_self">${className}(${sx})</a></div></td>`);
                    html.push(`<td class="counts-td" style="text-align: center"><a href="javascript:void(0);" onclick="goSearch('china','${sx}')" target="_self">${formatNumber(counts)}</a></td>`);
                }
                else
                {
                    html.push(`<td></td>`);
                    html.push(`<td class="counts-td" style="text-align: center"></td>`);
                }
                html.push("</tr>");
            }
            html.push("</tbody>");
            html.push("</table>");
            html.push("</div>");
            $("#chinaList").html(html.join(""));
        };
window.fillUsaData=function(list) {
    var data=[];
    var total=0;
    for(var i=0;i<list.length;i++) {
        if(list[i].stdType=="usa") {
            total+=1*(list[i].counts);
            data.push(list[i]);
        }
    }
    var html = [];
    var classArr = [
        {name: '美国铝协会标准', sx: 'AA'},
        {name: '美国国家公路与运输协会标准', sx: 'AASHTO'},
        {name: '美国船舶局标准', sx: 'ABS'},
        {name: '美国航空航天工业联合会标准', sx: 'AIA/NAS',param:'AIA'},
        {name: '美国航空与航天协会标准', sx: 'AIAA'},
        {name: '美国信息与图像管理协会标准', sx: 'AIIM'},
        {name: '美国航空无线电通信公司标准', sx: 'ARINC'},
        {name: '美国土木工程师协会标准', sx: 'ASCE'},
        {name: '美国机械工程师协会标准', sx: 'ASME'},
        {name: '美国质量协会标准', sx: 'ASQ'},
        {name: '美国材料与试验协会标准', sx: 'ASTM'},
        {name: '美国信息技术协会标准', sx: 'ATIS'},
        {name: '美国焊接协会标准', sx: 'AWS'},
        {name: '美国电子工业协会标准', sx: 'EIA'},
        {name: '美国陆军野战手册', sx: 'FM'},
        {name: '美国气体处理协会标准', sx: 'GPA'},
        {name: '美国总务管理局标准', sx: 'GSA'},
        {name: '美国电气与电子工程师协会标准', sx: 'IEEE'},
        {name: '美国仪器、系统与自动化协会标准', sx: 'ISA'},
        {name: '美国军用标准', sx: 'MIL'},
        {name: '美国阀门及配件工业制造商标准化协会标准', sx: 'MSS'},
        {name: '美国全国腐蚀工程师协会标准', sx: 'NACE'},
        {name: '美国宇航局标准', sx: 'NASA'},
        {name: '北约标准', sx: 'NATO'},
        {name: '美国电气制造商协会标准', sx: 'NEMA'},
        {name: '美国国家信息标准协会标准', sx: 'NISO'},
        {name: '美国航空无线电技术委员会标准', sx: 'RTCA'},
        {name: '美国电阻焊接机制造商协会标准', sx: 'RWMA'},
        {name: '美国汽车工程师学会标准', sx: 'SAE'},
        {name: '美国钢结构油漆委员会标准', sx: 'SSPC'},
        {name: '美国通信工业协会标准', sx: 'TIA'},
        {name: '美国陆军技术手册', sx: 'TM'},
        {name: '美国试验操作规则', sx: 'TOP'}
    ];

    html.push(`<div class="layui-card-header">
                    <i class="icon pear-icon pear-icon-layers"></i>
                   国外标准统计(共收录${formatNumber(total)}条标准）</div>`);
    html.push("<div class='layui-card-body'>");
    html.push('<table class="layui-table" lay-skin="line" cellspacing="0" cellpadding="0" border="0">');
    html.push("<thead>");
    html.push("<tr>");
    html.push("<th style='width:25%;'>标准分类");
    html.push("</th>");
    html.push("<th  style=\"width:20%;text-align: center\">标准数量");
    html.push("</th>");
    html.push("<th style='width:10%;'>");
    html.push("</th>");
    html.push("<th  style='width:25%;'>标准分类");
    html.push("</th>");
    html.push("<th  style=\"width:20%;text-align: center\">标准数量");
    html.push("</th>");
    html.push("</tr>");
    html.push("</thead>");
    html.push("<tbody>");

    for (var i = 0; i < classArr.length; i++) {
        html.push("<tr>");
        var className = classArr[i].name;
        var sx = classArr[i].sx;
        var param=classArr[i].param?classArr[i].param:sx;
        var counts = "0";
        for(var idx=0; idx<data.length; idx++) {
            if(data[idx].stdClass == className && data[idx].stdType=="usa") {
                counts = data[idx].counts;
                break;
            }
        }
        html.push(`<td><div title="${className}" class="content-no"><a href="javascript:void(0);" onclick="goSearch('usa','${param}')" target="_self">${className}(${sx})</a></div></td>`);
        html.push(`<td class="counts-td" style="text-align: center"><a href="javascript:void(0);" onclick="goSearch('usa','${param}')" target="_self">${formatNumber(counts)}</a></td>`);
        html.push("<td style='width:80px;'>");
        html.push("</td>");
        i++;
        className = "";
        sx = "";
        param="";
        counts = "";
        if(classArr.length > i) {
            counts = "0";
            className = classArr[i].name;
            sx = classArr[i].sx;
            param=classArr[i].param?classArr[i].param:sx;
            for(var idx=0; idx<data.length; idx++) {
                if(data[idx].stdClass == className && data[idx].stdType=="usa") {
                    counts = data[idx].counts;
                    break;
                }
            }
            html.push(`<td><div title="${className}" class="content-no"><a href="javascript:void(0);" onclick="goSearch('usa','${param}')" target="_self">${className}(${sx})</a></div></td>`);
            html.push(`<td class="counts-td" style="text-align: center"><a href="javascript:void(0);" onclick="goSearch('usa','${param}')" target="_self">${formatNumber(counts)}</a></td>`);
        } else {
            html.push(`<td></td>`);
            html.push(`<td class="counts-td" style="text-align: center"></td>`);
        }
        html.push("</tr>");
    }
    html.push("</tbody>");
    html.push("</table>");
    html.push("</div>");
    $("#usaList").html(html.join(""));
}
        //页面跳转
        window.goSearch=function(cType,abbr){
            var rootName="chinastd/myIndex";
            var cacheKey="chinaClassCachKey";
            if(cType=='china')
            {
                rootName="chinastd/myIndex";
            }
            else{
                rootName="usastd/myIndex";
                cacheKey="usaClassCachKey";
               // return;
            }
                var menu={};
                for(var i=0;i<menuList.length;i++)
                {
                    if(menuList[i].powerUrl==rootName)
                    {
                        menu=menuList[i];
                        break;
                    }
                }
                if(menu.powerId)
                {
                    var exist=parent.getTab(menu.powerId);
                    if(exist>0)
                    {
                        window.parent.layui.element.tabDelete('content',menu.powerId);
                    }
                    window.parent.layui.element.tabAdd('content', {
                        title: '著录项检索',
                        content:'<iframe lay-id="' + menu.powerId + '" src="'+menu.powerUrl+'" frameborder="0" class="admin-iframe" style="width: 100%;height: 100%"></iframe>' //支持传入html
                        ,id: menu.powerId
                    });
                    layui.data(cacheKey, { key: 'params', value: { abbr: abbr} });
                    window.parent.layui.element.tabChange('content', menu.powerId);
                }
            }


        window.getStatistics=function(callback) {
            $.ajax({
                url: MODULE_PATH + "statisticsList",
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    if (result.code==0) {
                        var data = result.data;
                        console.log(data);
                        updateTime=data[0].updateTime;
                        window.fillChinaData(data);
                        window.fillUsaData(data);
                        if(callback)
                        {
                            callback();
                        }
                    }
                }
            });
        }
        window.getTopN=function(container,title,desc,apiAddress,param,topN) {
            $.ajax({
                url: "/stdlog/" + apiAddress+"?page=1&limit="+topN+"&"+param,
                dataType: 'json',
                type: 'get',
                success: function (result) {
                    if (result.code==0) {
                        var data = result.data;
                        var html = [];
                        html.push(`<div class="layui-card-header">
                            <i class="icon pear-icon pear-icon-layers"></i>
                           ${title}`);
                        html.push(`<div style="float:right;margin-right:10px;font-size: 12px;">统计时间截至：${updateTime}</div>`);
                        html.push("</div>");
                        html.push("<div class='layui-card-body'>");
                        html.push('<table class="layui-table" lay-skin="line" cellspacing="0" cellpadding="0" border="0">');
                        html.push("<thead>");
                        html.push("<tr>");
                        html.push("<th>排名");
                        html.push("</th>");
                        html.push("<th>标准号");
                        html.push("</th>");
                        html.push("<th>标准名称");
                        html.push("</th>");
                        html.push(`<th>${desc}`);
                        html.push("</th>");
                        html.push("</tr>");
                        html.push("</thead>");
                        html.push("<tbody>");
                        for (var i = 0; i < data.length; i++) {
                            html.push("<tr>");
                            html.push(`<td class="idx-td" style="text-align: center">${i+1}</td>`);
                            var  name=data[i].itemName;
                            // if(name.length>15)
                            // {
                            //     name=name.substr(0,15)+"...";
                            // }
                            html.push(`<td><div title="${data[i].itemId}" class="content-no">${data[i].itemId}</div></td>`);

                            html.push(`<td><div  title="${data[i].itemName}"  class="content-td">${data[i].itemName}</div></td>`);
                            html.push(`<td class="counts-td" style="text-align: center">${data[i].counts}</td>`);
                            html.push("</tr>");
                        }
                        var fillEmpty = 8 - data.length;
                        if (fillEmpty > 0) {
                            for (var i = 0; i < fillEmpty; i++) {
                                html.push("<tr>");
                                html.push("<td class='idx-td'><span class='white-sp'>-</span></td>");
                                html.push("<td><span class='white-sp'>-</span></td>");
                                html.push("<td><span class='white-sp'>-</span></td>");
                                html.push("<td class='counts-td'><span class='white-sp'>-</span></td>");
                                html.push("</tr>");
                            }
                        }
                        html.push("</tbody>");
                        html.push("</table>");
                        html.push("</div>");
                        $("#"+container).html(html.join(""));
                        console.log(html);
                    } else {

                    }
                }
            });
        }
        window.getStatistics(function(){
            window.getTopN("list1","全文检索统计","全文检索次数","getPageofLogType","logType=Solr_Query_More_Result",8);
            window.getTopN("list2","访问查看统计","访问查看次数","getPageofLogType","logType=Standard_OverView",8);
            window.getTopN("list3","体系引用统计","体系引用量","getPageofReferedBySystem","systemType=1",8);
            window.getTopN("list4","在线预览统计","在线预览次数","getPageofLogType","logType=Standard_Online_Read",8);
            window.getTopN("list5","全文下载统计","全文下载次数","getPageofLogType","logType=Standard_Download",8);
            window.getTopN("list6","意见反馈统计","意见反馈量","getPageofOpinion","",8);
            window.getTopN("list7","用户收藏统计","用户收藏量","getPageofReferedBySystem","systemType=3",8);
            window.getTopN("list8","岗位引用统计","岗位引用量","getPageofReferedBySystem","systemType=2",8);
        });

    });
</script>
<style>
    .idx-td
    {
        min-width:28px;
    }
    .counts-td
    {
        min-width:85px;
    }
    .layui-table th, .layui-table td
    {
        font-size:13px;
    }
    .layui-table[lay-skin="line"]
    {
        border-width: 0px !important;
    }
    .white-sp
    {
        color:white;
    }
    #summary > div > div:hover
    {
        background-color: #d7f3e1;
        cursor:pointer;
    }
    #summary .static-icon
    {
        color: #36b368;
        font-size:45px;
        margin-left:20px;
    }
    .top-table
    {
        margin-top:30px;
    }
    .static-c
    {
        margin-bottom:0 !important;
    }
    /*  .static-c
      {
          box-shadow: 0px 0 55px rgba(255,255,255,.1) inset;
          position: relative;
          box-sizing: border-box;
          border: 1px solid #034c6a;
          border-radius: 15px;
      }
      .static-c th{
          font-size: 14px;
          font-weight: 600;
          color: #61d2f7;
          text-align: center;
      }
      .static-c td{
          color: #fff;
          font-size: 12.8px;
          text-align: center;
      }
      .static-c tbody tr:nth-child(2n+1)
      {
          background-color: #1e2a3c;
      }*/
    #list
    {
        margin-top:10px !important;
    }
    #list > div
    {
        width:100%;
        margin-bottom:5px;
    }
    #list > div>div
    {
        width:49.5%;
        float:left;
    }
    .static-right
    {
        float:right !important;
    }
    #list > div>div:nth-child(1)
    {
      /*  margin-right:2%; */[]
    }
    #list > div::after
    {
        display: block;
        clear: both;
        content: '';
        visibility: hidden;
        height: 0;
    }
    #list table
    {
        width:100%;
    }
    #summary
    {
        width:100%;
        margin:30px auto 5px;
        font-size:17px;
        font-family: 黑体;
        color: #2B4227;
    }
    #summary > div:after
    {
        content:'';
        display: block;
        clear: both;
        visibility: hidden;
        height: 0;
    }
    #summary > div > div
    {
        float:left;
        width:21%;
        margin-right:3%;
        padding:24px 0;
        background-color:#fff;
        border-radius: 5px;
        box-shadow: 0px 0 55px rgba(255,255,255,.1) inset;
        margin-bottom: 15px;
    }
    #summary > div>div>div
    {
        padding-top:10px;
    }
    #summary:last-child
    {
        margin-right:0;
    }
    #summary:after
    {
        content: "";
        display: block;
        clear:both;
        visibility: hidden;
        height: 0;
    }
    #summary>div>div>div:nth-child(1)
    {
        float:left;
        width:30%;
        margin-top:10px;
    }
    #summary>div>div>div:nth-child(2)
    {
        float:left;
        width:70%;
        color:#666;
    }
    #summary>div>div:after
    {
        content: "";
        display: block;
        clear:both;
        visibility: hidden;
        height: 0;
    }
    #summary>div>div:hover
    {
        color:white;
    }
    .search-container
    {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }
    .search-container .item
    {
        display: flex;
        flex-direction: row;
        margin-top:0.625rem;
    }
    .search-container .item .sub-item button
    {
        margin-top:10px;
    }
    .search-container .item label
    {
        min-width: 80px;
        padding: 8px 1px 8px 10px;
        text-align: right;
    }
    .layui-table-tool
    {
        height:70px !important;
        line-height:30px !important;
        padding:10px 0 0 10px !important;
        min-height:0px !important;
    }
    .layui-card-body .layui-form
    {
        margin-top:0px !important;
    }
    .layui-form-item .layui-form-label,.layui-form-item  .layui-input-inline{
        margin-bottom:7px;
    }
    .layui-form-item{
        padding-bottom:0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow:auto;
        white-space: normal;
    }
    #resultInfo{
        width:300px;
        text-align: center;
        color:#999;
        margin:10px auto 0;
    }
    .layui-table thead th {
        font-weight: bold;
    }
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .is-show-0{
        display: none;
    }
    .layui-table-page
    {
        text-align: center !important;
    }

    caption
    {
        padding: 0 0 5px 0;
        width: 700px;
        font: italic 11px "Trebuchet MS" , Verdana, Arial, Helvetica, sans-serif;
        text-align: right;
    }

    th
    {
        font: bold 11px "Trebuchet MS" , Verdana, Arial, Helvetica, sans-serif;
        letter-spacing: 1px;
        text-transform: uppercase;
        text-align: center;
        padding: 6px 6px 6px 12px;
    }
    th.nobg
    {
        border-top: 0;
        border-left: 0;
        background: none;
    }
    td
    {
        padding: 6px 6px 6px 12px;
    }
    .span_link
    {
        cursor:pointer;
        color:Black;
    }
    .tr_Category
    {
    }
    .pageLink
    {
        color:Blue;
        margin-left:2px;
        margin-right:2px;
    }
    .tr_Category_P td
    {
        background-color:#EEEEFF;
    }
    .top-title
    {
        width: 220px;
        height: 35px;
        line-height: 33px;
        background-color: #034c6a;
        border-radius: 18px;
        position: absolute;
        top: -17px;
        left: 50%;
        margin-left: -110px;
        color: #fff;
        font-size: 18px;
        font-weight: 600;
        box-sizing: border-box;
        padding-left: 45px;
    }
    .span-count
    {
        min-height: 24px;
    }
    .content-td
    {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 210px;
    }
    .content-no
    {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 100%;
    }
    .layui-table
    {
        margin-bottom: 25px !important;
    }
    .layui-card-body
    {
        padding-top:0px !important;
    }
    .c-list
    {
        width:100%;
        margin-bottom:5px;
        margin-top:10px;
    }
    /*#list > div:nth-child(1)*/
    /*{*/
    /*    margin-right:2%;*/
    /*}*/
    .c-list > div::after
    {
        display: block;
        clear: both;
        content: '';
        visibility: hidden;
        height: 0;
    }
    .c-list table
    {
        width:100%;
    }
</style>
<script src="/admin/js/common.js" charset="UTF-8"></script>
</html>
