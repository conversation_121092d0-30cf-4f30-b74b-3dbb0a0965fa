<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('信息配置修改')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label">标准标识：</label>
                    <div class="layui-input-block"  th:text="${stdInfo.stdIdentification}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准类别：</label>
                    <div class="layui-input-block"  th:text="${stdInfo.stdClass}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准号：</label>
                    <div class="layui-input-block"  th:text="${stdInfo.stdNo}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准名称(原文)：</label>
                    <div class="layui-input-block"  th:text="${stdInfo.stdOrgName}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准名称(中文)：</label>
                    <div class="layui-input-block"  th:text="${stdInfo.stdChineseName}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准名称(英文)：</label>
                    <div class="layui-input-block"  th:text="${stdInfo.stdEnglishName}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">文本语言：</label>
                    <div class="layui-input-block"  th:text="${stdInfo.stdLangugage}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">密级：</label>
                    <div class="layui-input-block"  th:text="${stdInfo.securityClass}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">分类号：</label>
                    <div class="layui-input-block"  th:text="${stdInfo.catetoryNo}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">ICS号：</label>
                    <div class="layui-input-block"  th:text="${stdInfo.stdIcs}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">发布部门：</label>
                    <div class="layui-input-block" th:text="${stdInfo.pubDept}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">发布日期：</label>
                    <div class="layui-input-block" th:text="${stdInfo.pubDate}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">实施日期：</label>
                    <div class="layui-input-block" th:text="${stdInfo.implementationDate}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">提出单位：</label>
                    <div class="layui-input-block" th:text="${stdInfo.advanceDept}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">起草单位：</label>
                    <div class="layui-input-block" th:text="${stdInfo.draftingUnit}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">起草人：</label>
                    <div class="layui-input-block" th:text="${stdInfo.drafter}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">标准状态：</label>
                    <div class="layui-input-block" th:text="${stdInfo.stdStatus}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">代替标准号（老标准）：</label>
                    <div class="layui-input-block" th:text="${stdInfo.alternateStdNo}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">被替代的标准号（新标准）：</label>
                    <div class="layui-input-block" th:text="${stdInfo.supersededStdNo}">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">页数：</label>
                    <div class="layui-input-block" th:text="${stdInfo.pageCount}">
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">标准年代：</label>
                    <div class="layui-input-block" th:text="${stdInfo.stdOcr}">
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">pdf文件名：</label>
                    <div class="layui-input-block" th:text="${stdInfo.pdfFileName}">
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">主要内容：</label>
                    <div class="layui-input-block" th:text="${stdInfo.primaryCoverage}">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-update" lay-submit=""
                    type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                关闭
            </button>
        </div>
    </div>
</form>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery'], function () {
        let form = layui.form;
        let $ = layui.jquery;

        form.on('submit(dict-type-update)', function (data) {
           /* $.ajax({
                url: '/standard/update',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'put',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                            parent.layui.table.reload("config-table");
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })*/
            parent.layer.close(parent.layer.getFrameIndex(window.name));
            return false;
        });
    })
</script>
<style>
    .layui-form-label{
        width:120px;
        text-align: right;
    }
    .layui-form-label
    {
        color:#aaa;
    }
    .layui-input-block
    {
        line-height: 36px;
        display: inline-block  !important;
        margin-left:0px  !important;
        width:80% !important;
    }
</style>
</body>
</html>
