<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('访问查看统计')"/>
</head>
<body class="pear-container" style="padding:0 20px 20px 20px;">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="search-container">
                    <div class="simple-container">
                        <div class="item" style="width:100%;">
                            <label class="sub-item search-label">标准号/标准名称(多关键词用空格隔开)：</label>
                            <div class="search-cmp">
                                <input class="layui-input sub-item" id="searchKey" name="stdNo" placeholder="" type="text">
                                <button id="btnSimpleSearch" class="search-button">
                                    <i class="layui-icon layui-icon-search"></i>
                                </button>
                            </div>
                            <div class="search-anchor-1"><a href="javascript:void(0);"  id="clearSimpleCon" style="display: none;" class="search-advance">清空</a></div>
                        </div>
                    </div>
                </div>
            <div id="resultInfo"></div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <table id="role-table" lay-filter="role-table"></table>
    </div>
</div>
</body>

<script id="role-toolbar" type="text/html">
</script>

<!--<a class="c-anchor" href="javascript:void(0);" lay-event="detail">详情</a>-->
<script id="role-bar" type="text/html">
    <a class="c-anchor layui-icon layui-icon-form" href="javascript:void(0);" title="详情" lay-event="detail"></a>
    <a class="c-anchor iconfont icon-chakan" href="javascript:void(0);" lay-event="onlineRead" title="在线查看"></a>
    <a class="c-anchor layui-icon layui-icon-{{d.isCollected=='1'?'rate-solid':'rate'}}" href="javascript:void(0);" title="{{d.isCollected=='1'?'取消收藏':'收藏'}}" lay-event="collect"></a>
    <a class="c-anchor layui-icon layui-icon-survey" href="javascript:void(0);" lay-event="opinion" title="意见反馈"></a>
</script>
<!--
<button
        class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit"><i class="layui-icon layui-icon-edit"></i>
</button>
<button
        class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove"><i class="layui-icon layui-icon-delete"></i>
</button>-->
<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common','upload','layer','element'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;
        let MODULE_PATH = "/stdlog/";
        let conditionArr=[1,2];
        let cols = [
            [
                {field: 'number', title: '序号', align: 'center', type: 'numbers',width:'5%'},
                {title: '标准号', field: 'itemId', align: 'left', width: '19%'},
                {title: '标准名称', field: 'itemName', align: 'left',width:'49%'},
                {title: '访问查看次数', field: 'counts', align: 'center', width: '10%'},
                 {title: '操作', toolbar: '#role-bar', align: 'center',width: '11%'}
            ]
        ]
        table.render({
            elem: '#role-table',
            url: MODULE_PATH + 'getPageofLogType',
            where:{
                "logType":"Standard_OverView"
            },
            page: true,
            limit:50,
            limits:[10,15,20,30,50],
            cols: cols,
            skin: 'row,line',
            toolbar: '#role-toolbar',
            cellMinWidth: 'auto',
            defaultToolbar: [],
            done: function (res, curr, count) {
                $("#resultInfo").html('共检索到'+res.count+"条记录");
                var $data = $('div[lay-id="role-table"]').find('.layui-table-body').find('tr').eq(0).find('td');
                var $head = $('div[lay-id="role-table"]').find('.layui-table-header').find('tr').eq(0).find('th');
                for (var i = 0; i < $data.length; i++) {
                    var l1 = $data.eq(i).find('div').width();
                    var l2 = $head.eq(i).find('div').width();
                    if (l1 > l2) {
                        $head.eq(i).find('div').width(l1);
                    } else if(l2>l1) {
                        $data.eq(i).find('div').width(l2);
                    }
                }
            }
        });
        table.on('tool(role-table)', function (obj) {
            if(obj.event==='onlineRead')
            {
                window.getStdInfoByNo(obj.data["itemId"],function(data) {
                    window.onlineRead(data,'Standard_Online_Read');
                });
            }
            else  if(obj.event==='detail')
            {
                window.detail(obj);
            }
            else if(obj.event==='collect')
            {
                window.getStdInfoByNo(obj.data["itemId"],function(data) {
                    window.collect(data,'Post_Standard_Collect','','',function(){
                        window.simpleSearch(false);
                    });
                });
            }
            else if(obj.event==='opinion')
            {
                window.opinion(obj);
            }
        });
        table.on('toolbar(role-table)', function (obj) {
            if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                window.refresh();
            }
        });
        $("#searchKey").keydown(function (e) {//当按下按键时
            if (e.which == 13) {//.which属性判断按下的是哪个键，回车键的键位序号为13
                var clickSearchBtn=true;
               return window.simpleSearch(clickSearchBtn);
            }
        });
        $("#btnSimpleSearch").click(function(e){
            var clickSearchBtn=true;
            return window.simpleSearch(clickSearchBtn);
        });
        $("#clearSimpleCon").click(function(){
            $('#searchKey').val("");
            table.reload('role-table', {where: {logType:"Standard_OverView",simpleSearchKey:""},page: { curr: 1 }});
            $("#clearSimpleCon").hide();
        });
        window.opinion=function(obj)
        {
            window.getStdInfoByNo(obj.data["itemId"],function(data) {
                parent.layer.open({
                    type: 2,
                    title: "【" + data.data["stdOrgName"] + "】意见反馈列表",
                    shade: 0.1,
                    area: ['1200px', '630px'],
                    content: '/stdopinion/main?stdId=' + data.data["id"]
                });
            });
        }
        window.simpleSearch=function(isClickSearchBtn)
        {
            var key = $('#searchKey').val();
            if(isClickSearchBtn) {
                if (!key) {
                    layer.alert("请输入关键字", {
                        title: "提示"
                    });
                    return false;
                }
            }
            //选中的分类
            table.reload('role-table', {where: {logType:"Standard_OverView", simpleSearchKey:key},page: { curr: 1 }});
            $("#clearSimpleCon").show();
            return false;
        }
        window.refresh = function () {
            table.reload('role-table', {where: {logType:"Standard_OverView", simpleSearchKey:""},page: { curr: 1 }});
        }
        //清空文件列表
        $('#clearList').click(function(){
            $('#demoList').html('');
        });

        window.detail = function (obj) {
            window.getStdInfoByNo(obj.data["itemId"],function(data){
                layer.open({
                    type: 2,
                    title: '【' + data.data["stdOrgName"] + '】',
                    shade: 0.1,
                    maxmin: true,
                    area: ['1000px', '500px'],
                    content: '/chinastd/detail?stdID=' + data.data["id"]
                });
                window.writelog('Standard_OverView',data.data["stdNo"],data.data["stdOrgName"],"","","");
            });
        }

    });
</script>
<style>
    .c-tool
    {
        display: flex;
        flex-wrap: nowrap;
        align-items: flex-start;
    }
    .c-tool .class-container{
        flex-grow: 1;
        align-items: flex-start;
    }
    .c-tool  .search-container {
        flex-grow: 3;
    }
    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        flex-grow: 4;
    }


    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
    }

    .simple-container .item .sub-item button {
        margin-top: 10px;
    }

    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }

    .advance-container {
        width: 90%;
        margin: 0px auto;
    }

    .advance-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-bottom: 15px;
    }
    .layui-table-tool
    {
        height:0px !important;
        line-height:30px !important;
        padding:10px 0 0 10px !important;
        min-height:0px !important;
    }
    .layui-form-item .layui-form-label, .layui-form-item .layui-input-inline {
        margin-bottom: 7px;
    }
    .layui-form-item {
        padding-bottom: 0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    #resultInfo {
        width: 100%;
        text-align: center;
        color: #999;
    }
    .layui-table thead th {
        font-weight: bold;
    }
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    .search-label {
        flex-grow: 2;
    }
    .simple-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #ddd;
        border-radius: 5px;
    }
    .simple-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .simple-container .search-cmp button:hover {
        cursor: pointer;
    }
    .simple-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .simple-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .advance-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #efefef;
        border-radius: 5px;
    }
    .advance-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .advance-container .search-cmp button:hover {
        cursor: pointer;
    }
    .advance-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .advance-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 140px;
    }
    .con-type-container {
        width: 100px;
    }
    .search-anchor-1
    {
        flex-grow:5;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }
    .layui-input-block{
        margin-left:115px;
    }
    .layui-form-select .layui-input
    {
        line-height: 36px;
    }
</style>
</html>
