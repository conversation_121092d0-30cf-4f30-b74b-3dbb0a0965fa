<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('标准列表')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="simple-container">
                <div class="item" style="width:100%;">
                    <label class="sub-item search-label">标准号/标准名称(多关键词用空格隔开)：</label>
                    <div class="search-cmp">
                        <input class="layui-input sub-item" id="searchKey" name="stdNo" placeholder="" type="text">
                        <button id="btnSimpleSearch" class="search-button">
                            <i class="layui-icon layui-icon-search"></i>
                        </button>
                    </div>
                    <div class="search-anchor-1"><a href="javascript:void(0);"  id="clearSimpleCon" style="display: none;" class="search-advance">清空</a><a href="javascript:void(0);" id="advanceAnchor" class="search-advance">高级检索&gt;&gt;</a></div>
                </div>
            </div>
            <div class="advance-container" style="display: none;">
                <div class="advance-item" id="advanceItem1" style="width:100%;">
                    <div class="ph-div" style="text-align: right;">高级检索：</div>
                    <div class="search-cmp">
                        <div class="con-field-container">
                            <select id="conField1" class="con-field">
                                <option value="std_no">标准号</option>
                                <option value="std_org_name">标准名称（原文）</option>
                                <option value="std_chinese_name">标准名称（中文）</option>
                                <option value="std_english_name">标准名称（英文）</option>
                                <option value="std_class">标准类别</option>
                                <option value="catetory_no">分类号</option>
                                <option value="std_ics">ICS号</option>
                                <option value="pub_date">发布日期</option>
                                <option value="implementation_date">实施日期</option>
                                <option value="advance_dept">提出单位</option>
                                <option value="drafting_unit">起草单位</option>
                                <option value="drafter">起草人</option>
                                <option value="std_status">标准状态</option>
                            </select>
                        </div>
                        <input class="layui-input sub-item con-val" id="conVal1" placeholder="" type="text">
                        <div class="con-type-container">
                            <select id="conQueryType1" class="con-query-type">
                                <option value="fuzzy">模糊</option>
                                <option value="exact">精确</option>
                            </select>
                        </div>
                    </div>
                    <div class="ph-end-div">
                        <a href="javascript:void(0);" id="simpleAnchor" class="search-advance">普通检索&gt;&gt;</a>
                    </div>
                </div>
                <div class="advance-item" id="advanceItem2" style="width:100%;">
                    <div class="ph-div">
                        <select id="conAndOr2" class="conAndOr">
                            <option value="AND">AND</option>
                            <option value="OR">OR</option>
                            <option value="NOT">NOT</option>
                        </select>
                    </div>
                    <div class="search-cmp">
                        <div class="con-field-container">
                            <select id="conField2">
                                <option value="std_no">标准号</option>
                                <option value="std_org_name">标准名称（原文）</option>
                                <option value="std_chinese_name">标准名称（中文）</option>
                                <option value="std_english_name">标准名称（英文）</option>
                                <option value="std_class">标准类别</option>
                                <option value="catetory_no">分类号</option>
                                <option value="std_ics">ICS号</option>
                                <option value="pub_date">发布日期</option>
                                <option value="implementation_date">实施日期</option>
                                <option value="advance_dept">提出单位</option>
                                <option value="drafting_unit">起草单位</option>
                                <option value="drafter">起草人</option>
                                <option value="std_status">标准状态</option>
                            </select>
                        </div>
                        <input class="layui-input sub-item" id="conVal2" placeholder="" type="text">
                        <div class="con-type-container">
                            <select id="conQueryType2">
                                <option value="fuzzy">模糊</option>
                                <option value="exact">精确</option>
                            </select>
                        </div>
                    </div>
                    <div class="ph-end-div">
                        <a href="javascript:void(0);" onclick="delCondition(2)" class="conDel" id="conDel2" style="display: none;">-</a>
                        <a href="javascript:void(0);" onclick="addCondition()" class="conAdd" id="conAdd2">+</a>
                    </div>
                </div>
            </div>
            <div class="item" style="width:100%;justify-content: center;align-items: center;margin-bottom: 0.625rem;flex-wrap: wrap;">
                <input type="checkbox" name="chkClass" id="chkAll" value="0" title="全部" lay-skin="primary" lay-filter="i-all" />
                <input type="checkbox" name="chkClass" id="chkAA" value="AA" title="AA" lay-skin="primary" lay-filter="i-aa" />
                <input type="checkbox" name="chkClass" id="chkAASHTO" value="AASHTO" title="AASHTO" lay-skin="primary" lay-filter="i-aashto" />
                <input type="checkbox" name="chkClass" id="chkABS" value="ABS" title="ABS" lay-skin="primary" lay-filter="i-abs" />
                <input type="checkbox" name="chkClass" id="chkAIA" value="AIA" title="AIA/NAS" lay-skin="primary" lay-filter="i-aia" />
                <input type="checkbox" name="chkClass" id="chkAIAA" value="AIAA" title="AIAA" lay-skin="primary" lay-filter="i-aiaa" />
                <input type="checkbox" name="chkClass" id="chkAIIM" value="AIIM" title="AIIM" lay-skin="primary" lay-filter="i-aiim" />
                <input type="checkbox" name="chkClass" id="chkARINC" value="ARINC" title="ARINC" lay-skin="primary" lay-filter="i-arinc" />
                <input type="checkbox" name="chkClass" id="chkASCE" value="ASCE" title="ASCE" lay-skin="primary" lay-filter="i-asce" />
                <input type="checkbox" name="chkClass" id="chkASME" value="ASME" title="ASME" lay-skin="primary" lay-filter="i-asme" />
                <input type="checkbox" name="chkClass" id="chkASQ" value="ASQ" title="ASQ" lay-skin="primary" lay-filter="i-asq" />
                <input type="checkbox" name="chkClass" id="chkASTM" value="ASTM" title="ASTM" lay-skin="primary" lay-filter="i-astm" />
                <input type="checkbox" name="chkClass" id="chkATIS" value="ATIS" title="ATIS" lay-skin="primary" lay-filter="i-atis" />
                <input type="checkbox" name="chkClass" id="chkAWS" value="AWS" title="AWS" lay-skin="primary" lay-filter="i-aws" />
                <input type="checkbox" name="chkClass" id="chkEIA" value="EIA" title="EIA" lay-skin="primary" lay-filter="i-eia" />
                <input type="checkbox" name="chkClass" id="chkFM" value="FM" title="FM" lay-skin="primary" lay-filter="i-fm" />
                <input type="checkbox" name="chkClass" id="chkGPA" value="GPA" title="GPA" lay-skin="primary" lay-filter="i-gpa" />
                <input type="checkbox" name="chkClass" id="chkGSA" value="GSA" title="GSA" lay-skin="primary" lay-filter="i-gsa" />
                <input type="checkbox" name="chkClass" id="chkIEEE" value="IEEE" title="IEEE" lay-skin="primary" lay-filter="i-ieee" />
                <input type="checkbox" name="chkClass" id="chkISA" value="ISA" title="ISA" lay-skin="primary" lay-filter="i-isa" />
                <input type="checkbox" checked="checked" name="chkClass" id="chkMIL" value="MIL" title="MIL" lay-skin="primary" lay-filter="i-mil" />
                <input type="checkbox" name="chkClass" id="chkMSS" value="MSS" title="MSS" lay-skin="primary" lay-filter="i-mss" />
                <input type="checkbox" name="chkClass" id="chkNACE" value="NACE" title="NACE" lay-skin="primary" lay-filter="i-nace" />
                <input type="checkbox" name="chkClass" id="chkNASA" value="NASA" title="NASA" lay-skin="primary" lay-filter="i-nasa" />
                <input type="checkbox" checked="checked" name="chkClass" id="chkNATO" value="NATO" title="NATO" lay-skin="primary" lay-filter="i-nato" />
                <input type="checkbox" name="chkClass" id="chkNEMA" value="NEMA" title="NEMA" lay-skin="primary" lay-filter="i-nema" />
                <input type="checkbox" name="chkClass" id="chkNISO" value="NISO" title="NISO" lay-skin="primary" lay-filter="i-niso" />
                <input type="checkbox" name="chkClass" id="chkRTCA" value="RTCA" title="RTCA" lay-skin="primary" lay-filter="i-rtca" />
                <input type="checkbox" name="chkClass" id="chkRWMA" value="RWMA" title="RWMA" lay-skin="primary" lay-filter="i-rwma" />
                <input type="checkbox" name="chkClass" id="chkSAE" value="SAE" title="SAE" lay-skin="primary" lay-filter="i-sae" />
                <input type="checkbox" name="chkClass" id="chkSSPC" value="SSPC" title="SSPC" lay-skin="primary" lay-filter="i-sspc" />
                <input type="checkbox" name="chkClass" id="chkTIA" value="TIA" title="TIA" lay-skin="primary" lay-filter="i-tia" />
                <input type="checkbox" name="chkClass" id="chkTM" value="TM" title="TM" lay-skin="primary" lay-filter="i-tm" />
                <input type="checkbox" name="chkClass" id="chkTOP" value="TOP"  title="TOP" lay-skin="primary" lay-filter="i-top" />
            </div>
            <div class="item" id="advTool" style="text-align: center;display: none;">
                <button class="pear-btn pear-btn-md sub-item" type="reset" style="margin-left:30px;margin-top:0px;width:80px;padding-left:5px;">
                    <i class="layui-icon layui-icon-refresh"></i>
                    重 置
                </button>
                <button class="pear-btn pear-btn-md pear-btn-primary sub-item" lay-filter="role-query" lay-submit
                        style="margin-left:40px;margin-top:0px;width:80px;padding-left:5px;">
                    <i class="layui-icon layui-icon-search"></i>
                    检 索
                </button>
            </div>
            <div id="resultInfo"></div>
        </form>
    </div>
</div>
<!--<div class="item">
               <label class="sub-item">标准号：</label>
               <input class="layui-input sub-item" name="stdNo" placeholder="" type="text">
           </div>
               <div class="item">
               <label class="sub-item">标准名称：</label>
                   <input class="layui-input sub-item" name="stdOrgName" placeholder="" type="text">
           </div>
               <div class="item">
           <label class="sub-item">主要内容：</label>
                   <input class="layui-input sub-item" name="primaryCoverage" placeholder="" type="text">
           </div>
               <div class="item">
               <label class="sub-item">起草单位：</label>
                   <input class="layui-input sub-item" name="draftingUnit" placeholder="" type="text" >
           </div>
               <div class="item">
           <label class="sub-item">起草人：</label>
                   <input class="layui-input sub-item" name="drafter" placeholder="" type="text">
           </div>
               <div class="item">
                   <button class="pear-btn pear-btn-md pear-btn-primary sub-item" lay-filter="role-query" lay-submit style="margin-left:40px;margin-top:0px;">
                       <i class="layui-icon layui-icon-search"></i>
                       查询
                   </button>
                   <button class="pear-btn pear-btn-md sub-item" type="reset" style="margin-left:30px;margin-top:0px;">
                       <i class="layui-icon layui-icon-refresh"></i>
                       重置
                   </button>
           </div>-->
<div class="layui-card">
    <div class="layui-card-body">
        <div id="pagination1"></div>
        <table id="role-table" lay-filter="role-table"></table>
        <div id="pagination2"></div>
    </div>
</div>
</body>

<script id="role-toolbar" type="text/html">
    <button  class="pear-btn pear-btn-primary pear-btn-md" style="margin-left:15px;" lay-event="export">
        <i class="layui-icon layui-icon-export"></i>
        导出
    </button>
</script>
<!--
<button
        class="pear-btn pear-btn-primary pear-btn-md" lay-event="add">
    <i class="layui-icon layui-icon-add-1"></i>
    新增
</button>
<button  class="pear-btn pear-btn-md"
         lay-event="batchRemove">
    <i class="layui-icon layui-icon-delete"></i>
    删除
</button>-->
<script id="role-bar" type="text/html">
    <a class="c-anchor layui-icon layui-icon-form" href="javascript:void(0);" title="详情" lay-event="edit"></a>
    <a class="c-anchor iconfont icon-chakan" href="javascript:void(0);" title="在线查看" lay-event="onlineRead"></a>
    <a href="javascript:void(0);" lay-event="download" class="c-anchor iconfont icon-xiazaidaoru is-show-{{d.pdfIsExists}}" title="下载"></a>
    <a id="collect-anchor-{{d.id}}"  class="c-anchor layui-icon layui-icon-{{d.isCollected=='1'?'rate-solid':'rate'}}" href="javascript:void(0);" title="{{d.isCollected=='1'?'取消收藏':'收藏'}}" lay-event="collect"></a>
    <a class="c-anchor layui-icon layui-icon-survey" href="javascript:void(0);" lay-event="opinion" title="意见反馈"></a>
</script>
<div id="colChoose" style="display: none;">
    <div style="padding:10px">单次导出最多<span id="spnMaxDownCount">1000</span>条。</div>
    <table>
        <thead>
        <tr>
            <th>序号</th>
            <th>供选择的字段</th>
            <th><input type="checkbox" id="chkDownColAll"  onchange="changeDownCol()" /></th>
        </tr>
        </thead>
        <tbody id="tbColConfig">

        </tbody>
    </table>
</div>

<th:block th:include="include :: footer"/>

<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common', 'upload', 'layer', 'element','laypage'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;
        let laypage= layui.laypage;
        let MODULE_PATH = "/usastd/";
        let totalRecord=0;
        let conditionArr=[1,2];
        //是否有检索条件
        let hasQueryCondtion=false;
        // 格式化搜索关键字，放到一个json中用来在结果中高亮显示关键字
        let searchKeyMap=[];
        var currPage1=1;
        var currPage2=1;
        var pageSize=50;
        //允许导出的最大数量
        var maxDownloadCount=1000;
        var colDownloadChoose=[];
        var colDownload=["标准号","标准名称(原文)","标准名称(中文)","标准名称(英文)","标准类别","分类号","ICS 号","发布部门","发布日期","实施日期","提出单位","起草单位","起草人","标准状态","代替标准号(老标准)","被替代标准号(新标准)","页数","标准年代","主要内容"];
        //读取从其他页面传递的参数
        window.initClassName=function()
        {
            var identity="MIL,NATO";
            console.log("initClassName------------------------------------------------");
            var params = layui.data('usaClassCachKey').params;
            if(params && params.abbr) {
                identity=params.abbr.toUpperCase().replace("/","");
            }
            if(identity) {
                iArray=identity.split(",");
                let checkboxes = ['chkAll', 'chkAA', 'chkAASHTO', 'chkABS', 'chkAIA', 'chkAIAA', 'chkAIIM',
                    'chkARINC', 'chkASCE', 'chkASME', 'chkASQ', 'chkASTM', 'chkATIS', 'chkAWS', 'chkEIA',
                    'chkFM', 'chkGPA', 'chkGSA', 'chkIEEE', 'chkISA', 'chkMIL', 'chkMSS', 'chkNASA',
                    'chkNATO', 'chkNEMA', 'chkNISO', 'chkRTCA', 'chkRWMA', 'chkSAE', 'chkSSPC','chkNACE',
                    'chkTIA', 'chkTM', 'chkTOP'];
                checkboxes.forEach(id => {
                     $(`input[id=${id}]`).prop("checked", false);
                });
                iArray.forEach(item=>{
                    checkboxes.forEach(id => {
                        if ('chk' + item == id) {
                            $(`input[id=${id}]`).prop("checked", true);
                        }
                    });
                });
                form.render();
                //window.doSimpleSearch();
            }
            return identity;
        };
        initClassName();

        window.changeDownCol=function()
        {
            var checked=true;
            if($("#chkDownColAll").prop("checked"))
            {
                checked=true;
            }
            else
            {
                checked=false;
            }
            for(var i=3;i<19;i++)
            {
                $("#chkDownloadCol_"+i).prop("checked",checked);
            }
        }

        window.getMaxCount=function()
        {
            $.ajax({
                url: "/source/std_m_d_c",
                dataType: 'json',
                type: 'get',
                success: function (result) {
                    if (result.success) {
                        maxDownloadCount=result.data;
                        $("#spnMaxDownCount").text(maxDownloadCount);
                    }
                }
            });
        }
        getMaxCount();
        window.getColDownloadConfig=function()
        {
            $.ajax({
                url: "/source/g_user_d_col",
                dataType: 'json',
                type: 'get',
                success: function (result) {
                    if (result.success) {
                        var data=result.data.split(",");
                        colDownloadChoose=data;
                    }
                }
            });
        }
        getColDownloadConfig();
       // 全选/取消全选处理
       form.on('checkbox(i-all)',function(data){
        let checkboxes = ['chkAll', 'chkAA', 'chkAASHTO', 'chkABS', 'chkAIA', 'chkAIAA', 'chkAIIM',
                    'chkARINC', 'chkASCE', 'chkASME', 'chkASQ', 'chkASTM', 'chkATIS', 'chkAWS', 'chkEIA',
                    'chkFM', 'chkGPA', 'chkGSA', 'chkIEEE', 'chkISA', 'chkMIL', 'chkMSS', 'chkNASA',
                    'chkNATO', 'chkNEMA', 'chkNISO', 'chkRTCA', 'chkRWMA', 'chkSAE', 'chkSSPC','chkNACE',
                    'chkTIA', 'chkTM', 'chkTOP'];
            checkboxes.forEach(id => {
                 if((!data.elem.checked) && (id=='chkMIL' || id=='chkNATO'))
                {
                    $(`input[id=${id}]`).prop("checked", !data.elem.checked);
                }
                else{
                    $(`input[id=${id}]`).prop("checked", data.elem.checked);
                }
            });
            form.render();
        });

        // 监听各个标准类型复选框的变化
        let standardTypes = ['aa', 'aashto', 'abs', 'aia', 'aiaa', 'aiim', 'arinc', 'asce', 'asme',
                           'asq', 'astm', 'atis', 'aws', 'eia', 'fm', 'gpa', 'gsa', 'ieee', 'isa',
                           'mil', 'mss', 'nasa', 'nato', 'nema', 'niso', 'rtca', 'rwma', 'sae','nace',
                           'sspc', 'tia', 'tm', 'top'];

        standardTypes.forEach(type => {
            form.on(`checkbox(i-${type})`,function(data){
                if(!data.elem.checked) {
                    $("input[id=chkAll]").prop("checked", false);
                } else {
                    if(checkAllSelected()) {
                        $("input[id=chkAll]").prop("checked", true);
                    }
                }
                form.render();
            });
        });

        // 检查是否所有标准类型都被选中
        window.checkAllSelected = function() {
            let allSelected = true;
            let checkboxes = ['chkAll', 'chkAA', 'chkAASHTO', 'chkABS', 'chkAIA', 'chkAIAA', 'chkAIIM',
                    'chkARINC', 'chkASCE', 'chkASME', 'chkASQ', 'chkASTM', 'chkATIS', 'chkAWS', 'chkEIA',
                    'chkFM', 'chkGPA', 'chkGSA', 'chkIEEE', 'chkISA', 'chkMIL', 'chkMSS', 'chkNASA',
                    'chkNATO', 'chkNEMA', 'chkNISO', 'chkRTCA', 'chkRWMA', 'chkSAE', 'chkSSPC','chkNACE',
                    'chkTIA', 'chkTM', 'chkTOP'];

            checkboxes.forEach(id => {
                if(!$(`input[id=${id}]`).prop("checked")) {
                    allSelected = false;
                }
            });
            return allSelected;
        }

        //搜索用函数
        window.createKeywords=function()
        {
            //console.log("$(\".simple-container\").css(\"display\"):"+$(".simple-container").css("display"));
            //console.log("$(\".advance-container\").css(\"display\"):"+$(".advance-container").css("display"));
            var data=[];
            if($(".simple-container").css("display")=="flex" || $(".simple-container").css("display")=="block")
            {
                var words=$("#searchKey").val().trim().split(/[\s+]/);
                console.log("words:"+words);
                for(var i=0;i<words.length;i++)
                {
                    data.push({fieldname:'stdno',keyword:words[i]});
                    data.push({fieldname:'stdenglishname',keyword:words[i]});
                    data.push({fieldname:'stdchinesename',keyword:words[i]});
                }
            }
            else if($(".advance-container").css("display")=="flex" || $(".advance-container").css("display")=="block")
            {
                for(var i=0;i<conditionArr.length;i++)
                {
                    var keyword=$("#conVal"+conditionArr[i]).val().trim();
                    if(keyword) {
                        var obj = {};
                        obj.fieldname = $("#conField" + conditionArr[i]).val().replaceAll("_", "");
                        obj.keyword = keyword;
                        data.push(obj);
                    }
                }
            }
            console.log(data);
            searchKeyMap=data;
        };
        window.findWordIndex=function(str, searchWord) {
            if(!str || !searchWord)
            {
                return -1;
            }
            // 将两个字符串都转换为小写
            const lowerStr = str.toLowerCase();
            const lowerSearchWord = searchWord.toLowerCase();

            // 使用includes判断是否包含，但不直接返回索引
            const isIncluded = lowerStr.includes(lowerSearchWord);

            // 如果包含，使用indexOf获取索引
            if (isIncluded) {
                return lowerStr.indexOf(lowerSearchWord);
            } else {
                return -1; // 或其他表示未找到的逻辑
            }
        };
        // layui.table 使用搜索关键字高亮显示指定列
        window.highlight=function(fieldName,rowData)
        {
            var result=rowData[fieldName]!=null?rowData[fieldName].toString():"";
            var len=searchKeyMap.length;
            for(var i=0;i<len;i++)
            {
                if(fieldName.toLowerCase()==searchKeyMap[i].fieldname)
                {
                    var  rest=result,result='',word=searchKeyMap[i].keyword;
                    while(rest.length>0)
                    {
                        var idx=findWordIndex(rest,word);
                        if(idx>-1)
                        {
                            result+=(idx>0?rest.substr(0,idx):'') +"<em>"+rest.substr(idx,word.length)+"</em>";
                            rest=rest.substring(idx+word.length);
                        }
                        else
                        {
                            result+=rest;
                            rest='';
                        }
                    }
                    // result=result.replaceAll(searchKeyMap[i].keyword,"<em>"+searchKeyMap[i].keyword+"</em>");
                }
            }
            return result;
        };
        let cols = [
            [
                {field:'number', title: '序号' , align:'center',type:'numbers',width:'4%'},
                {title: '标准类别', field: 'stdClass', align: 'center',width:'8%'},
                {title: '标准号', align: 'left', width: '11%',templet:function(rowData){
                        return window.highlight("stdNo",rowData);
                    }},
                {title: '标准名称(原文)',  align: 'left', width: '18%',templet:function(rowData){
                        return window.highlight("stdEnglishName",rowData);
                    }},
                {title: '标准名称(中文)', align: 'left',width:'16%',templet:function(rowData){
                        return window.highlight("stdChineseName",rowData);
                    }},
                {title: '实施日期', field: 'implementationDate', align: 'left',width:'11%'},
                {title: '标准状态', field: 'stdStatus', align: 'center',width:'6%'},
                {title: '页数', field: 'pageCount', align: 'center', width: '4%'},
                {title: '操作', toolbar: '#role-bar', align: 'center',width:'18%'}
                // {field: 'number', title: '序号', align: 'center', type: 'numbers', width: '5%'},
                // {title: '标准<br/>类别', field: 'stdClass', align: 'left', width: '5%'},
                // {title: '标准号', align: 'left', width: '11%',templet:function(rowData){
                //         return window.highlight("stdNo",rowData);
                //     }},
                // {title: '标准名称',  align: 'left', width: '20%',templet:function(rowData){
                //         return window.highlight("stdOrgName",rowData);
                //     }},
                // {title: '实施日期', field: 'implementationDate', align: 'left', width: '8%'},
                // /*{title: '标准英文名称', field: 'stdEnglishName', align: 'left'},
                // {title: '密级', field: 'securityClass', align: 'center'},
                // {title: '发布部门', field: 'pubDept', align: 'left',width:200},
                // {title: '发布日期', field: 'pubDate', align: 'center'},
                // {title: '提出单位', field: 'advanceDept', align: 'left',width:200},*/
                // /*  {title: '起草单位', field: 'draftingUnit', align: 'left',width:'21%'},
                //   {title: '起草人', field: 'drafter', align: 'left',width:'15%'},*/
                // {title: '主要内容', field: 'primaryCoverage', align: 'left', width: '21%'},
                // {title: '标准<br/>状态', field: 'stdStatus', align: 'left', width: '5%'},
                // {title: '代替标准号<br/>(老标准)', field: 'alternateStdNo', align: 'left', width: '6%'},
                // {title: '被替代的标准号<br/>(新标准)', field: 'supersededStdNo', align: 'left', width: '6%'},
                // /*  {title: '页数', field: 'pageCount', align: 'center'},
                //   {title: '代替标准号', field: 'replaceStdNo', align: 'left'},
                //   {title: 'pdf文件名称', field: 'pdfFileName', align: 'left'},*/
                // {title: '操作', toolbar: '#role-bar', align: 'center',width: '13%'}
            ]
        ]
        window.tableRender=function(pageSize) {
            //console.log("window.tableRender------------------------------------------");
            var abbr="0";
            var params = layui.data('usaClassCachKey').params;
            if(params && params.abbr) {
                abbr=params.abbr.toUpperCase();
                // layui.data('usaClassCachKey', {
                //     clear: true // 使用 clear 选项来清除所有数据
                // });
                layui.data('usaClassCachKey', { key: 'params', value: { abbr: 'MIL,NATO'} });
            }
            table.render({
                elem: '#role-table',
                url: MODULE_PATH + 'data',
                page: false,
                limit: pageSize,
                cols: cols,
                skin: 'row,line',
                toolbar: '#role-toolbar',
                cellMinWidth: 'auto',
                where: {
                    queryIdenti: abbr
                },
                defaultToolbar: [{
                    title: '刷新',
                    layEvent: 'refresh',
                    icon: 'layui-icon-refresh',
                }, 'filter', 'print', 'exports'],
                done: function (res, curr, count) {
                    if (hasQueryCondtion) {
                        $("#resultInfo").html('命中' + res.count + "条记录，" + res.extData + "条可下载");
                    } else {
                        $("#resultInfo").html('共' + res.count + "条记录，" + res.extData + "条可下载");
                    }
                    var $data = $('div[lay-id="role-table"]').find('.layui-table-body').find('tr').eq(0).find('td');
                    var $head = $('div[lay-id="role-table"]').find('.layui-table-header').find('tr').eq(0).find('th');
                    for (var i = 0; i < $data.length; i++) {
                        var l1 = $data.eq(i).find('div').width();
                        var l2 = $head.eq(i).find('div').width();
                        if (l1 > l2) {
                            $head.eq(i).find('div').width(l1);
                        } else if (l2 > l1) {
                            $data.eq(i).find('div').width(l2);
                        }
                    }
                    initPage(curr, res.count);
                }
            });
        }
        tableRender(pageSize);

        window.doPage=function()
        {
            //简单搜索
            if($(".simple-container").css("display")=="flex" || $(".simple-container").css("display")=="block")
            {
                var key = $('#searchKey').val();
                if(!key)
                {
                    hasQueryCondtion=false;
                }
                else
                {
                    hasQueryCondtion=true;
                }
                var classes=getClassCond();
                table.reload('role-table', {where: {simpleSearchKey:key,queryIdenti:classes},page: { curr: currPage1,limit:pageSize }});
                //$("#clearSimpleCon").show();
                window.createKeywords();
            }
            else if($(".advance-container").css("display")=="flex" || $(".advance-container").css("display")=="block") {
                var fields=[];
                for(var i=0;i<conditionArr.length;i++)
                {
                    var obj={};
                    obj.logic="AND";
                    if(conditionArr[i]>1)
                    {
                        obj.logic= $("#conAndOr"+conditionArr[i]).val();
                    }
                    obj.field=$("#conField"+conditionArr[i]).val();
                    obj.val=$("#conVal"+conditionArr[i]).val();
                    obj.type=$("#conQueryType"+conditionArr[i]).val();
                    fields.push(obj);
                }
                if(fields.length<=0)
                {
                    hasQueryCondtion=false;
                }
                else
                {
                    hasQueryCondtion=true;
                }
                table.reload('role-table', {where: {advSearchKey:JSON.stringify(fields),queryIdenti:getClassCond()},page: { curr: currPage1 ,limit:pageSize }});
                window.createKeywords();
            }
        }
        window.onPage1Change=function(page,isFirst) {
            currPage1 = page.curr;
            currPage2=currPage1;
            var p = page.curr;//改变当前页码
            var l = page.limit;
            pageSize=l;
            if (!isFirst) {
                doPage();
                laypage.render({
                    elem: 'pagination2',
                    count: totalRecord,
                    limit: l,
                    curr: currPage1,
                    limits:[30,50,100],
                    layout: ['prev', 'page', 'next', 'skip', 'count','limit'],
                    jump: onPage2Change
                });
            }
        }
        window.onPage2Change=function(page,isFirst) {
            currPage1 = page.curr;
            currPage2=currPage1;
            var p = page.curr;//改变当前页码
            var l = page.limit;
            pageSize=l;
            if (!isFirst) {
                doPage();
                laypage.render({
                    elem: 'pagination1',
                    count: totalRecord,
                    limit: l,
                    curr: currPage1,
                    limits:[30,50,100],
                    layout: ['prev', 'page', 'next', 'skip', 'count','limit'],
                    jump: onPage1Change
                });
            }
        }
        window.initPage=function(currPage,total) {
            totalRecord=total;
            currPage1=currPage;
            currPage2=currPage;
            console.log("init Page1");
            laypage.render({
                elem: 'pagination1',
                count: totalRecord,
                limit: pageSize,
                curr:currPage1,
                limits:[30,50,100],
                layout: ['prev', 'page', 'next', 'skip', 'count','limit'],
                jump: onPage1Change
            });
            console.log("init Page2");
            laypage.render({
                elem: 'pagination2',
                count: totalRecord,
                limit: pageSize,
                limits:[30,50,100],
                curr:currPage1,
                layout: ['prev', 'page', 'next', 'skip', 'count','limit'],
                jump:onPage1Change
            });
            console.log("init Page3");
        }
        table.on('tool(role-table)', function (obj) {
            if (obj.event === 'remove') {
                window.remove(obj);
            } else if (obj.event === 'edit') {
                window.edit(obj);
            } else if (obj.event === 'download') {
                window.download(obj);
            }
            else if(obj.event==='onlineRead')
            {
                window.onlineRead(obj);
            }
            else if(obj.event==='collect')
            {
                window.collect(obj,'Post_Standard_Collect','','',function(){
                    if(obj.data["isCollected"]==0)
                    {
                        obj.update({
                            isCollected: 1
                        });
                        obj.data["isCollected"]=1;
                        $("#collect-anchor-"+obj.data["id"]).attr("class","c-anchor layui-icon layui-icon-rate-solid");
                        $("#collect-anchor-"+obj.data["id"]).prop("title","取消收藏");
                    }
                    else if(obj.data["isCollected"]==1)
                    {
                        obj.update({
                            isCollected: 0
                        });
                        $("#collect-anchor-"+obj.data["id"]).attr("class","c-anchor layui-icon layui-icon-rate");
                        $("#collect-anchor-"+obj.data["id"]).prop("title","收藏");
                    }
                })
            }
            else if(obj.event==='opinion')
            {
                window.opinion(obj);
            }
            /*else if (obj.event === 'power') {
                window.power(obj);
            } else if (obj.event === 'dept') {
                window.dept(obj);
            } */
        });
        table.on('toolbar(role-table)', function (obj) {
            console.log("colDownloadChoose:"+colDownloadChoose);
            if (obj.event === 'export') {
                $("#tbColConfig").empty();
                for(var i=0;i<colDownload.length;i++)
                {
                    var checked=colDownloadChoose[i]==1?"checked='checked'":"";
                    $("#tbColConfig").append(`<tr><td>${i+1}</td><td>${colDownload[i]}</td><td><input type="checkbox" name="chkDownloadCol" id="chkDownloadCol_${i+1}" value="${colDownload[i]}"  ${checked}/></td></tr>`);
                }
                layer.open({
                    title:"请选择要导出的列",
                    type:1,
                    area:["400px","500px"],
                    content:$("#colChoose"),
                    btn:['确定','取消'],
                    yes:function(index,layero)
                    {
                        var hasChooseCount=0;
                        for(var i=0;i<18;i++)
                        {
                            colDownloadChoose[i]=$("#chkDownloadCol_"+(i+1)).prop("checked")?1:0;
                            hasChooseCount+=colDownloadChoose[i];
                        }
                        console.log("colDownloadChoose:"+colDownloadChoose);
                        if(hasChooseCount==0)
                        {
                            layer.msg('请选择要导出的列！',{
                                icon:1,
                                time: 10000
                            });
                            return false;
                        }
                        layer.closeAll();
                        layer.msg('正在导出数据...',{
                            icon:16,
                            time: 10000
                        });
                        var exportCols=colDownloadChoose.join(",");
                        var classes=getClassCond();
                        //简单搜索
                        if($(".simple-container").css("display")=="flex" || $(".simple-container").css("display")=="block")
                        {
                            var key = $('#searchKey').val();
                            if(!key)
                            {
                                hasQueryCondtion=false;
                            }
                            else
                            {
                                hasQueryCondtion=true;
                            }

                            window.open("/source/downloadStdByCond?stdType=usa&simpleSearchKey" +
                                "="+key+"&queryIdenti="+classes+"&chooseCol="+exportCols);
                        }
                        else if($(".advance-container").css("display")=="flex" || $(".advance-container").css("display")=="block") {
                            var fields=[];
                            for(var i=0;i<conditionArr.length;i++)
                            {
                                var obj={};
                                obj.logic="AND";
                                if(conditionArr[i]>1)
                                {
                                    obj.logic= $("#conAndOr"+conditionArr[i]).val();
                                }
                                obj.field=$("#conField"+conditionArr[i]).val();
                                obj.val=$("#conVal"+conditionArr[i]).val();
                                obj.type=$("#conQueryType"+conditionArr[i]).val();
                                fields.push(obj);
                            }
                            if(fields.length<=0)
                            {
                                hasQueryCondtion=false;
                            }
                            else
                            {
                                hasQueryCondtion=true;
                            }
                            window.open("/source/downloadStdByCond?stdType=usa&advSearchKey="+encodeURIComponent(JSON.stringify(fields))+"&queryIdenti="+classes+"&chooseCol="+exportCols);
                        }
                        return false;
                    },
                    btn2:function(index,layero){//按钮二回到
                        return true;
                    },
                    cancel:function(){//右上角关闭毁回调
                        //return false;
                    }
                })

            }
            else if (obj.event === 'add') {
                window.add();
            } else if (obj.event === 'refresh') {
                window.refresh();
            } else if (obj.event === 'batchRemove') {
                window.batchRemove(obj);
            } else if (obj.event === 'updateIndex') {
                window.updateIndex(obj);
            } else if (obj.event === 'updateAll') {
                window.updateAll(obj);
            } else if (obj.event === 'delete') {
                window.deleteRecordAndIndex(obj);
            } else if (obj.event === 'importExcel') {
                //window.importExcel(obj);
            }
        });

        form.on('submit(role-query)', function (data) {
            console.log("----submit(role-query)-----------------------------------------");
            console.log(data);
            var fields=[];
            for(var i=0;i<conditionArr.length;i++)
            {
                var obj={};
                obj.logic="AND";
                if(conditionArr[i]>1)
                {
                    obj.logic= $("#conAndOr"+conditionArr[i]).val();
                }
                obj.field=$("#conField"+conditionArr[i]).val();
                obj.val=$("#conVal"+conditionArr[i]).val();
                obj.type=$("#conQueryType"+conditionArr[i]).val();
                fields.push(obj);
            }
            if(fields.length<=0)
            {
                hasQueryCondtion=false;
            }
            else
            {
                hasQueryCondtion=true;
            }
            table.reload('role-table', {where: {advSearchKey:JSON.stringify(fields),queryIdenti:getClassCond()},page: { curr: 1 }});
            window.createKeywords();
            window.writelog('Standard_Query','','','','',JSON.stringify(fields));
            return false;
        });
        window.opinion=function(obj)
        {
            parent.layer.open({
                type: 2,
                title: "【"+obj.data["stdEnglishName"]+"】意见反馈列表",
                shade: 0.1,
                area: ['1200px', '630px'],
                content: '/stdopinion/main?stdId='+obj.data["id"]
            });
        }
        $("#advanceAnchor").click(function () {
            $(".simple-container").toggle();
            $(".advance-container").toggle();
            $("#advTool").toggle();
        });
        $("#simpleAnchor").click(function () {
            $(".simple-container").toggle();
            $(".advance-container").toggle();
            $("#advTool").toggle();

        });

        window.getClassCond=function(){
            var chkList=[];
            $('input[type=checkbox][name=chkClass]:checked').each(function(){
                chkList.push($(this).val());
            });
            return chkList.join(",");
        }
        $("#btnSimpleSearch").click(function(e){
            if (e.which == 1) {//.which属性判断按下的是哪个键，回车键的键位序号为13
                //简单搜索
                console.log("----simple search--------------------------");
                if($(".simple-container").css("display")=="flex" || $(".simple-container").css("display")=="block")
                {
                    var key = $('#searchKey').val();
                    if(!key)
                    {
                        hasQueryCondtion=false;
                    }
                    else
                    {
                        hasQueryCondtion=true;
                    }
                    var classes=getClassCond();
                    table.reload('role-table', {where: {simpleSearchKey:key,queryIdenti:classes},page: { curr: currPage1,limit:pageSize }});
                    //$("#clearSimpleCon").show();
                    window.createKeywords();
                }
                else if($(".advance-container").css("display")=="flex" || $(".advance-container").css("display")=="block") {
                    console.log("----advance search--------------------------");
                    var fields=[];
                    for(var i=0;i<conditionArr.length;i++)
                    {
                        var obj={};
                        obj.logic="AND";
                        if(conditionArr[i]>1)
                        {
                            obj.logic= $("#conAndOr"+conditionArr[i]).val();
                        }
                        obj.field=$("#conField"+conditionArr[i]).val();
                        obj.val=$("#conVal"+conditionArr[i]).val();
                        obj.type=$("#conQueryType"+conditionArr[i]).val();
                        fields.push(obj);
                    }
                    if(fields.length<=0)
                    {
                        hasQueryCondtion=false;
                    }
                    else
                    {
                        hasQueryCondtion=true;
                    }
                    table.reload('role-table', {where: {advSearchKey:JSON.stringify(fields),queryIdenti:getClassCond()},page: { curr: currPage1 ,limit:pageSize }});
                    window.createKeywords();
                }

            }
            return false;
        });
        $("#clearSimpleCon").click(function(){
            $('#searchKey').val("");
            table.reload('role-table', {where: {simpleSearchKey:"",queryIdenti:"0"},page: { curr: 1 }});
            $("#clearSimpleCon").hide();
            window.createKeywords();
        });
        window.addCondition=function(){
            var id=0;
            for(var i=0;i<conditionArr.length;i++)
            {
                if(conditionArr[i]>id)
                {
                    id=conditionArr[i];
                }
            }
            if(id>0)
            {
                id+=1;
                conditionArr[conditionArr.length]=id;
            }
            var html=`<div class="advance-item" id="advanceItem${id}" style="width:100%;">
                    <div class="ph-div">
                        <select id="conAndOr${id}" class="conAndOr">
                            <option value="AND">AND</option>
                            <option value="OR">OR</option>
                            <option value="NOT">NOT</option>
                        </select>
                    </div>
                    <div class="search-cmp">
                        <div class="con-field-container">
                            <select id="conField${id}">
                                <option value="std_no">标准号</option>
                                <option value="std_org_name">标准名称（原文）</option>
                                <option value="std_chinese_name">标准名称（中文）</option>
                                <option value="std_english_name">标准名称（英文）</option>
                                <option value="std_class">标准类别</option>
                                <option value="catetory_no">分类号</option>
                                <option value="std_ics">ICS号</option>
                                <option value="pub_date">发布日期</option>
                                <option value="implementation_date">实施日期</option>
                                <option value="advance_dept">提出单位</option>
                                <option value="drafting_unit">起草单位</option>
                                <option value="drafter">起草人</option>
                                <option value="std_status">标准状态</option>
                            </select>
                        </div>
                        <input class="layui-input sub-item" id="conVal${id}" placeholder="" type="text">
                        <div class="con-type-container">
                            <select id="conQueryType${id}">
                                <option value="fuzzy">模糊</option>
                                <option value="exact">精确</option>
                            </select>
                        </div>
                    </div>
                    <div class="ph-end-div">
                        <a href="javascript:void(0);" class="conDel" onclick="delCondition(${id})" id="conDel${id}">-</a>
                        <a href="javascript:void(0);" onclick="addCondition()" class="conAdd" id="conAdd${id}">+</a>
                    </div>
                </div>`;
            $(".advance-container").append(html);
            form.render("select");
            $("#conAdd"+(id-1)).toggle();
            for(var i=1;i<conditionArr.length-1;i++)
            {
                $("#conDel"+conditionArr[i]).show();
            }
        };

        window.delCondition=function(id){
            $("#advanceItem"+id).remove();
            for(var i=0;i<conditionArr.length;i++)
            {
                if(conditionArr[i]==id)
                {
                    if(i==conditionArr.length-1)
                    {
                        $("#conAdd"+conditionArr[i-1]).toggle();
                    }
                    conditionArr.splice(i, 1);
                    break;
                }
            }
            console.log('conditionArr.length:'+conditionArr.length);
            //需要保留至少两行
            if(conditionArr.length==2)
            {
                $("#conDel"+conditionArr[1]).hide();
            }
        };

        window.add = function () {
            layer.open({
                type: 2,
                title: '新增',
                shade: 0.1,
                area: ['500px', '500px'],
                content: MODULE_PATH + 'add'
            });
        }

        window.edit = function (obj) {
            layer.open({
                type: 2,
                title: '【' + obj.data['stdEnglishName'] + '】',
                shade: 0.1,
                maxmin: true,
                area: ['1000px', '500px'],
                content: MODULE_PATH + 'detail?stdID=' + obj.data['id']
            });
            window.writelog('Standard_OverView',obj.data["stdNo"],obj.data["stdEnglishName"],"","","");
        }

        //刷新全部索引
        window.updateAll = function (obj) {
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "updateallsolrindex",
                dataType: 'json',
                type: 'post',
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("更新成功!", {icon: 1, time: 2000});
                    } else {
                        layer.msg("更新失败:" + result.message, {icon: 2, time: 2000});
                    }
                }
            })
        }

        //刷新指定索引
        window.updateIndex = function (obj) {
            let ids = common.checkField(obj, 'id');
            if (common.isEmpty(ids)) {
                popup.warning("请选择要更新的记录！");
                return false;
            }
            let loading = layer.load();
            $.ajax({
                url: MODULE_PATH + "updatesolrindex",
                dataType: 'json',
                type: 'post',
                data: 'ids=' + ids,
                success: function (result) {
                    layer.close(loading);
                    if (result.success) {
                        layer.msg("更新成功!", {icon: 1, time: 2000});
                    } else {
                        layer.msg("更新失败:" + result.message, {icon: 2, time: 2000});
                    }
                }
            })
        }

        //多文件上传
        var uploadListIns = upload.render({
            elem: '#uploadBtn'     // 选择文件按钮
            , elemList: $('#demoList') //列表元素对象
            , url: MODULE_PATH + 'file-upload' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
            , accept: 'file'  //指定允许上传时校验的文件类型，可选值有：images（图片）、file（所有文件）、video（视频）、audio（音频）
            , multiple: false  //是否允许多文件上传。设置 true即可开启。不支持ie8/9
            , number: 5   //设置同时可上传的文件数量，一般配合 multiple 参数出现; 0 不限制
            , auto: true  //是否选完文件后自动上传。如果设定 false，那么需要设置 bindAction 参数来指向一个其它按钮提交上传
            , bindAction: '#testListAction'  //指向一个按钮触发上传，一般配合 auto: false 来使用
            , choose: function (obj) {   //选择文件后的回调函数。返回一个object参数
            }
            , done: function (res, index, upload) { //成功的回调
                var that = this;
                if (res.success) { //上传成功
                    popup.success("导入完成！", function () {
                        table.reload('role-table');
                    });
                    //delete this.files[index]; //删除文件队列已经上传成功的文件
                    return;
                } else {
                    popup.failure(res.message);
                }
            }
            , allDone: function (obj) { //多文件上传完毕后的状态回调
                console.log(obj)
            }
            , error: function (index, upload) { //错误回调
                popup.failure("导入失败！");
            }
            , progress: function (n, elem, e, index) { //注意：index 参数为 layui 2.6.6 新增
                element.progress('progress-demo-' + index, n + '%'); //执行进度条。n 即为返回的进度百分比
            }
        });

        window.remove = function (obj) {
            layer.confirm('确定要删除该数据吗', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "remove/" + obj.data['id'],
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                                obj.del();
                            });
                        } else {
                            layer.msg(result.msg, {icon: 2, time: 1000});
                        }
                    }
                })
            });
        }

        window.batchRemove = function (obj) {
            let ids = common.checkField(obj, 'roleId');
            if (common.isEmpty(ids)) {
                popup.warning("未选中数据");
                return false;
            }
            layer.confirm('确定要删除选中角色', {icon: 3, title: '提示'}, function (index) {
                layer.close(index);
                let loading = layer.load();
                $.ajax({
                    url: MODULE_PATH + "batchRemove/" + ids,
                    dataType: 'json',
                    type: 'delete',
                    success: function (result) {
                        layer.close(loading);
                        if (result.success) {
                            popup.success(result.msg, function () {
                                table.reload('role-table');
                            });
                        } else {
                            popup.failure(result.msg);
                        }
                    }
                })
            });
        }

        window.refresh = function () {
            table.reload('role-table');
        }
        //清空文件列表
        $('#clearList').click(function () {
            $('#demoList').html('');
        });
    });
</script>
<style>
    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: flex-start;
        flex-direction: column;
    }

    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
        margin-bottom: 0.625rem;
    }

    .simple-container .item .sub-item button {
        margin-top: 10px;
    }

    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }

    .advance-container {
        width: 75%;
        margin: 0px auto;
        padding: 0px 0px 0px 10px;
    }

    .advance-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-bottom: 15px;
    }
    .layui-table-tool {
        height: 50px !important;
        line-height: 1px !important;
        padding: 0px !important;
        min-height: 0px !important;
    }
    .layui-form-item .layui-form-label, .layui-form-item .layui-input-inline {
        margin-bottom: 7px;
    }
    .layui-form-item {
        padding-bottom: 0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    #resultInfo {
        width: 100%;
        text-align: center;
        color: #999;
        margin-top: 10px;
    }
    .layui-table thead th {
        font-weight: bold;
    }
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    .search-label {
        flex-grow: 2;
    }
    .simple-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #ddd;
        border-radius: 5px;
    }
    .simple-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .simple-container .search-cmp button:hover {
        cursor: pointer;
    }
    .simple-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .simple-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .advance-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #efefef;
        border-radius: 5px;
    }
    .advance-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .advance-container .search-cmp button:hover {
        cursor: pointer;
    }
    .advance-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .advance-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 240px;
    }
    .con-type-container {
        width: 110px;
    }
    .search-anchor-1
    {
        flex-grow:5;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }
    em{
        color:red;
        font-style: normal;
    }
    #pagination1,#pagination2{
        text-align: center;
    }
    .layui-laypage {
        margin-bottom: 0px !important;
    }
    .layui-table-view {
        margin-top:0px !important;
    }
    .layui-table-column{
        display: none;
    }

    #colChoose table {
        width: 100%;
        border: 1px solid black;
        margin: 0 auto;
        border-collapse: collapse;
    }

    #colChoose table thead th {
        height: 50px;
        border: 1px solid black;
        text-align: center;
        vertical-align: center;
        font-weight: bold;
    }

    #colChoose table td {
        height: 40px;
        border: 1px solid black;
        text-align: center;
        vertical-align: center;
    }

    #colChoose table tbody tr:nth-child(even) {
        background-color:#efefef;
    }
</style>
</html>
