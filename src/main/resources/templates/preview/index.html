<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('我的预览记录')"/>
    <style>
        .date-tab {
            position: relative;
            padding: 5px 15px;
            text-align: center;
        }
        .date-tab .badge {
            position: absolute;
            top: 0;
            right: 5px;
            height: 16px;
            line-height: 16px;
            padding: 0 5px;
            border-radius: 8px;
            background-color: #FF5722;
            color: #fff;
            font-size: 12px;
        }
        .date-tab.layui-this .badge {
            background-color: #fff;
            color: #FF5722;
        }
        .search-container {
            margin-bottom: 10px;
        }
        .preview-count-summary {
            margin-top: 10px;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <!-- 日期选项卡 -->
        <div class="layui-tab layui-tab-brief" lay-filter="dateTab">
            <ul class="layui-tab-title">
                <li th:each="date,iterStat : ${dateList}" th:class="${iterStat.index == 0 ? 'layui-this' : ''}" th:attr="data-date=${date.value}" class="date-tab">
                    <span th:text="${date.display}">05月26日</span>
                    <span class="badge" th:id="'badge-' + ${date.value}">0</span>
                </li>
            </ul>
            <div class="layui-tab-content">
                <!-- 搜索框 -->
                <div class="search-container">
                    <form action="" class="layui-form">
                        <div class="simple-container">
                            <div class="item" style="width:100%;">
                                <label class="sub-item search-label">标准号/标准名称(多关键词用空格隔开)：</label>
                                <div class="search-cmp">
                                    <input class="layui-input sub-item" id="searchKey" name="stdNo" placeholder="" type="text">
                                    <button id="btnSimpleSearch" class="search-button">
                                        <i class="layui-icon layui-icon-search"></i>
                                    </button>
                                </div>
                                <div class="search-anchor-1"><a href="javascript:void(0);" id="clearSimpleCon" style="display: none;" class="search-advance">清空</a></div>
                            </div>
                        </div>
                        <div id="resultInfo"></div>
                    </form>
                </div>
                <!-- 每个日期的数据表格 -->
                <div>
                    <div class="preview-count-summary"  id="summary">加载中...</div>
                    <div id="pagination1" style="text-align: center; margin-top: 10px;"></div>
                    <table id="table" lay-filter="table"></table>
                    <div id="pagination2" style="text-align: center; margin-top: 10px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>

<script id="role-toolbar" type="text/html">
</script>
<script id="role-bar" type="text/html">
    <a class="c-anchor layui-icon layui-icon-form" href="javascript:void(0);" title="详情" lay-event="edit"></a>
    <a class="c-anchor iconfont icon-chakan" href="javascript:void(0);" title="在线查看" lay-event="onlineRead"></a>
    <a href="javascript:void(0);" lay-event="download" class="c-anchor iconfont icon-xiazaidaoru is-show-{{d.pdfIsExists}}" title="下载"></a>
    <a id="collect-anchor-{{d.id}}" class="c-anchor layui-icon layui-icon-{{d.isCollected=='1'?'rate-solid':'rate'}}" href="javascript:void(0);" title="{{d.isCollected=='1'?'取消收藏':'收藏'}}" lay-event="collect"></a>
    <a class="c-anchor layui-icon layui-icon-survey" href="javascript:void(0);" lay-event="opinion" title="意见反馈"></a>
</script>
<th:block th:include="include :: footer"/>
<script th:src="@{/admin/js/common.js}"></script>
<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common', 'upload', 'layer', 'element','laypage'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;
        let laypage = layui.laypage;
        let MODULE_PATH = "/stdpreview/";

        // 当前选中的日期
        let currentDate = $(".layui-tab-title li.layui-this").data("date");

        // 格式化搜索关键字，放到一个json中用来在结果中高亮显示关键字
        let searchKeyMap = [];

        // 每个日期的分页信息
        let pageInfo = {};

        // 表格列定义
        let cols = [
            [
                {field: 'number', title: '序号', align: 'center', type: 'numbers', width: '5%'},
                {title: '标准号', align: 'left', width: '15%', templet: function(rowData) {
                    return window.highlight("stdNo", rowData);
                }},
                {title: '标准名称(原文)', align: 'left', width: '22%', templet: function(rowData) {
                    return window.highlight("stdOrgName", rowData);
                }},
                {title: '标准名称(中文)', align: 'left', width: '21%', templet: function(rowData) {
                    return window.highlight("stdChineseName", rowData);
                }},
                {title: 'PDF文件名', align: 'left', width: '15%', templet: function(rowData) {
                    return window.highlight("pdfFileName", rowData);
                }},
                {title: '预览时间', field: 'createTime', align: 'center', width: '10%'},
                {title: '操作', toolbar: '#role-bar', align: 'center', width: '8%'}
            ]
        ];
        // 搜索用函数
        window.createKeywords = function() {
            var data = [];
            var words = $("#searchKey").val().trim().split(/[\s+]/);
            console.log("Search keywords:", words);
            for (var i = 0; i < words.length; i++) {
                if (words[i]) {
                    data.push({fieldname: 'stdno', keyword: words[i]});
                    data.push({fieldname: 'stdorgname', keyword: words[i]});
                    data.push({fieldname: 'stdchinesename', keyword: words[i]});
                    data.push({fieldname: 'pdfFileName', keyword: words[i]});
                }
            }
            console.log("Search keyword map:", data);
            searchKeyMap = data;
        };

        // 查找关键词在字符串中的位置
        window.findWordIndex = function(str, searchWord) {
            if (!str || !searchWord) {
                return -1;
            }
            // 将两个字符串都转换为小写
            const lowerStr = str.toLowerCase();
            const lowerSearchWord = searchWord.toLowerCase();

            // 使用includes判断是否包含，但不直接返回索引
            const isIncluded = lowerStr.includes(lowerSearchWord);

            // 如果包含，使用indexOf获取索引
            if (isIncluded) {
                return lowerStr.indexOf(lowerSearchWord);
            } else {
                return -1; // 或其他表示未找到的逻辑
            }
        };

        // 高亮显示搜索关键词
        window.highlight = function(fieldName, rowData) {
            var result = rowData[fieldName] != null ? rowData[fieldName].toString() : "";
            var len = searchKeyMap.length;
            for (var i = 0; i < len; i++) {
                if (fieldName.toLowerCase() == searchKeyMap[i].fieldname) {
                    var rest = result, result = '', word = searchKeyMap[i].keyword;
                    while (rest.length > 0) {
                        var idx = findWordIndex(rest, word);
                        if (idx > -1) {
                            result += (idx > 0 ? rest.substr(0, idx) : '') + "<em>" + rest.substr(idx, word.length) + "</em>";
                            rest = rest.substring(idx + word.length);
                        } else {
                            result += rest;
                            rest = '';
                        }
                    }
                }
            }
            return result;
        };

        // 加载预览统计数据
        function loadPreviewStats() {
            $.ajax({
                url: MODULE_PATH + "stats",
                type: "get",
                dataType: "json",
                success: function(res) {
                    console.log("Preview stats:", res);
                    if (res.success && res.data) {
                        // 更新每个日期的预览数量
                        res.data.forEach(function(item) {
                            $("#badge-" + item.date).text('('+item.count+')');
                            $("#summary").text("共有 " + item.count + " 条预览记录");

                            // 初始化分页信息
                            pageInfo[item.date] = {
                                currentPage: 1,
                                pageSize: 10,
                                total: item.count
                            };
                        });

                        // 加载第一个日期的数据
                        loadTableData(currentDate);
                    } else {
                        console.error("Failed to load preview stats:", res.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error loading preview stats:", error);
                }
            });
        }

        // 渲染指定日期的数据表格
        function renderTable(date) {
            var tableId = "table";
            var paginationId = "pagination";
            var info = pageInfo[date] || { currentPage: 1, pageSize: 10, total: 0 };
            $('#' + tableId).html('');
            // 渲染表格
            table.render({
                elem: '#' + tableId,
                url: MODULE_PATH + 'data/' + date,
                page: { curr:info.currentPage},
                limit: info.pageSize,
                cols: cols,
                skin: 'row,line',
                toolbar: '#role-toolbar',
                cellMinWidth: 'auto',
                where: {
                    simpleSearchKey: $("#searchKey").val()
                },
                defaultToolbar: [{
                    title: '刷新',
                    layEvent: 'refresh',
                    icon: 'layui-icon-refresh',
                }, 'filter', 'print', 'exports'],
                done: function(res, curr, count) {
                    console.log("Table render done for " + date + ":", res);
                    try {
                        // 更新总数
                        if (res && res.count !== undefined) {
                            var searchKey = $("#searchKey").val();
                            if (searchKey) {
                                $("#summary").html('命中 ' + res.count + " 条预览记录");
                            } else {
                                $("#summary").html('共有 ' + res.count + " 条预览记录");
                            }
                            // 更新分页信息
                            pageInfo[date].total = res.count;
                            // 渲染分页
                            renderPagination(date);
                        } else {
                            $("#summary").html('暂无预览记录');
                            pageInfo[date].total = 0;
                            renderPagination(date);
                        }
                    } catch (e) {
                        console.error("Error in table done callback:", e);
                        $("#summary").html('加载预览记录时出错');
                    }
                }
            });
        }

        // 渲染分页
        function renderPagination(date) {
            var paginationId = "pagination";
            var info = pageInfo[date];

            if (!info) return;

            laypage.render({
                elem: paginationId+"1",
                count: info.total,
                limit: info.pageSize,
                curr: info.currentPage,
                limits: [10, 20, 50, 100],
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                jump: function(obj, first) {
                    if (!first) {
                        // 页码或每页条数变更
                        pageInfo[date].currentPage = obj.curr;
                        pageInfo[date].pageSize = obj.limit;
                        loadTableData(date);
                    }
                }
            });
            laypage.render({
                elem: paginationId+"2",
                count: info.total,
                limit: info.pageSize,
                curr: info.currentPage,
                limits: [10, 20, 50, 100],
                layout: ['count', 'prev', 'page', 'next', 'limit', 'skip'],
                jump: function(obj, first) {
                    if (!first) {
                        // 页码或每页条数变更
                        pageInfo[date].currentPage = obj.curr;
                        pageInfo[date].pageSize = obj.limit;
                        loadTableData(date);
                    }
                }
            });
        }

        // 加载表格数据
        function loadTableData(date) {
            // 创建搜索关键字
            window.createKeywords();
            // 渲染表格
            renderTable(date);
        }

        // 搜索按钮点击事件
        $("#btnSimpleSearch").click(function(e) {
            e.preventDefault();
            loadTableData(currentDate);
            return false;
        });

        // 回车键搜索
        $("#searchKey").keypress(function(e) {
            if (e.which == 13) {
                e.preventDefault();
                loadTableData(currentDate);
                return false;
            }
        });

        // 清空搜索条件
        $("#clearSimpleCon").click(function() {
            $('#searchKey').val("");
            $("#clearSimpleCon").hide();
            loadTableData(currentDate);
        });

        // 监听搜索框输入，显示/隐藏清空按钮
        $("#searchKey").on("input", function() {
            if ($(this).val()) {
                $("#clearSimpleCon").show();
            } else {
                $("#clearSimpleCon").hide();
            }
        });

        // 表格工具栏事件
        table.on('toolbar(role-table)', function(obj) {
            if (obj.event === 'refresh') {
                loadTableData(currentDate);
            }
        });

        // 表格行工具事件
        table.on('tool', function(obj) {
            if (obj.event === 'edit') {
                window.edit(obj);
            } else if (obj.event === 'download') {
                window.download({'id':obj.data['stdInfoId'],'stdNo':'','stdOrgName':''});
            }
            else if(obj.event==='onlineRead')
            {
                try {
                    console.log("onlineRead event triggered with data:", obj.data);
                    var stdObj = {};
                    $.extend(true, stdObj, obj);

                    // 确保 stdInfoId 存在
                    if (stdObj.data["stdInfoId"]) {
                        stdObj.data["id"] = stdObj.data["stdInfoId"];
                        console.log("Previewing document with ID: " + stdObj.data["id"]);

                        // 使用 preview 函数
                        window.preview(stdObj);
                    } else {
                        console.error("stdInfoId is missing in the data");
                        layer.alert('文档ID不存在', {
                            title: '提示',
                            icon: 1,
                            btn: ['确定']
                        });
                    }
                } catch (e) {
                    console.error("Error in preview function: " + e.message);
                    layer.alert('预览时发生错误', {
                        title: '提示',
                        icon: 1,
                        btn: ['确定']
                    });
                }
            }
            else if(obj.event==='collect')
            {
                window.collect(obj,'Post_Standard_Collect','','',function(){
                    console.log("obj.data[\"isCollected\"]:"+obj.data["isCollected"]);
                    if(obj.data["isCollected"]==0)
                    {
                        obj.update({
                            isCollected: 1
                        });
                        obj.data["isCollected"]=1;
                        $("#collect-anchor-"+obj.data["id"]).attr("class","c-anchor layui-icon layui-icon-rate-solid");
                        $("#collect-anchor-"+obj.data["id"]).prop("title","取消收藏");
                    }
                    else if(obj.data["isCollected"]==1)
                    {
                        obj.update({
                            isCollected: 0
                        });
                        $("#collect-anchor-"+obj.data["id"]).attr("class","c-anchor layui-icon layui-icon-rate");
                        $("#collect-anchor-"+obj.data["id"]).prop("title","收藏");
                    }
                });
            }
            else if(obj.event==='opinion')
            {
                window.opinion(obj);
            }
        });

        window.edit = function (obj) {
            layer.open({
                type: 2,
                title: '【' + obj.data['stdOrgName'] + '】',
                shade: 0.1,
                maxmin: true,
                area: ['1000px', '500px'],
                content: (obj.stdType==='usa'?'/usastd':obj.stdType==='china'?'/chinastd/':'/stdforeigin/')+'detail?stdID=' + obj.data['stdInfoId']
            });
            window.writelog('Standard_OverView',obj.data["stdNo"],obj.data["stdOrgName"],"","","");
        }

        // 监听tab切换事件
        element.on('tab(dateTab)', function(data) {
            // 获取选中的日期
            currentDate = $(this).attr("data-date");
            console.log("Tab changed to date: " + currentDate);
            console.log("pageInfo[currentDate]:");
            console.log(pageInfo[currentDate]);
            // 加载当前日期的数据
            // 如果还没有加载过该日期的数据，则加载
            console.log("loadTableData(currentDate)");
            loadTableData(currentDate);
        });

        // 初始化
        $(function() {
            // 加载预览统计数据
            loadPreviewStats();

            // 初始化搜索框状态
            if ($("#searchKey").val()) {
                $("#clearSimpleCon").show();
            } else {
                $("#clearSimpleCon").hide();
            }
        });
    });
</script>
<style>
    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: flex-start;
        flex-direction: column;
    }

    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
        margin-bottom: 0.625rem;
    }

    .simple-container .item .sub-item button {
        margin-top: 10px;
    }

    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }

    .advance-container {
        width: 75%;
        margin: 0px auto;
        padding: 0px 0px 0px 10px;
    }

    .advance-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-bottom: 15px;
    }
    .layui-table-tool {
        height: 50px !important;
        line-height: 1px !important;
        padding: 0px !important;
        min-height: 0px !important;
    }
    .layui-form-item .layui-form-label, .layui-form-item .layui-input-inline {
        margin-bottom: 7px;
    }
    .layui-form-item {
        padding-bottom: 0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    #resultInfo {
        width: 100%;
        text-align: center;
        color: #999;
        margin-top: 10px;
    }
    .layui-table thead th {
        font-weight: bold;
    }
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    .search-label {
        flex-grow: 2;
    }
    .simple-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #ddd;
        border-radius: 5px;
    }
    .simple-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .simple-container .search-cmp button:hover {
        cursor: pointer;
    }
    .simple-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .simple-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .advance-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #efefef;
        border-radius: 5px;
    }
    .advance-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .advance-container .search-cmp button:hover {
        cursor: pointer;
    }
    .advance-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .advance-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 140px;
    }
    .con-type-container {
        width: 100px;
    }
    .search-anchor-1
    {
        flex-grow:5;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }
    em{
        color:red;
        font-style: normal;
    }
    #pagination1,#pagination2{
        text-align: center;
    }
    .layui-laypage {
        margin-bottom: 0px !important;
    }
    .layui-table-view {
        margin-top:0px !important;
    }
    .layui-table-column{
        display: none;
    }
</style>
</html>
