<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('预览记录')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="simple-container">
                <div class="item" style="width:100%;">
                    <label class="sub-item search-label">标准号/标准名称(多关键词用空格隔开)：</label>
                    <div class="search-cmp">
                        <input class="layui-input sub-item" id="searchKey" name="stdNo" placeholder="" type="text">
                        <button id="btnSimpleSearch" class="search-button">
                            <i class="layui-icon layui-icon-search"></i>
                        </button>
                    </div>
                    <div class="search-anchor-1"><a href="javascript:void(0);" id="clearSimpleCon" style="display: none;" class="search-advance">清空</a></div>
                </div>
            </div>
            <div id="resultInfo"></div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <div id="pagination1"></div>
        <table id="role-table" lay-filter="role-table"></table>
        <div id="pagination2"></div>
    </div>
</div>
</body>

<script id="role-toolbar" type="text/html">
</script>
<script id="role-bar" type="text/html">
    <a class="c-anchor iconfont icon-chakan" href="javascript:void(0);" title="在线查看" lay-event="onlineRead"></a>
</script>
<th:block th:include="include :: footer"/>
<script th:src="@{/admin/js/common.js}"></script>
<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common', 'upload', 'layer', 'element','laypage'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;
        let laypage= layui.laypage;
        let MODULE_PATH = "/stdpreview/";
        let totalRecord=0;
        let conditionArr=[1,2];
        //是否有检索条件
        let hasQueryCondtion=false;
        // 格式化搜索关键字，放到一个json中用来在结果中高亮显示关键字
        let searchKeyMap=[];
        var currPage1=1;
        var currPage2=1;
        var pageSize=50;
        //搜索用函数
        window.createKeywords=function()
        {
            var data=[];
            if($(".simple-container").css("display")=="flex" || $(".simple-container").css("display")=="block")
            {
                var words=$("#searchKey").val().trim().split(/[\s+]/);
                console.log("words:"+words);
                for(var i=0;i<words.length;i++)
                {
                    data.push({fieldname:'stdno',keyword:words[i]});
                    data.push({fieldname:'stdorgname',keyword:words[i]});
                    data.push({fieldname:'stdchinesename',keyword:words[i]});
                    data.push({fieldname:'pdfFileName',keyword:words[i]});
                }
            }
            else if($(".advance-container").css("display")=="flex" || $(".advance-container").css("display")=="block")
            {
                for(var i=0;i<conditionArr.length;i++)
                {
                    var keyword=$("#conVal"+conditionArr[i]).val().trim();
                    if(keyword) {
                        var obj = {};
                        obj.fieldname = $("#conField" + conditionArr[i]).val().replaceAll("_", "");
                        obj.keyword = keyword;
                        data.push(obj);
                    }
                }
            }
            console.log(data);
            searchKeyMap=data;
        };
        window.findWordIndex=function(str, searchWord) {
            if(!str || !searchWord)
            {
                return -1;
            }
            // 将两个字符串都转换为小写
            const lowerStr = str.toLowerCase();
            const lowerSearchWord = searchWord.toLowerCase();

            // 使用includes判断是否包含，但不直接返回索引
            const isIncluded = lowerStr.includes(lowerSearchWord);

            // 如果包含，使用indexOf获取索引
            if (isIncluded) {
                return lowerStr.indexOf(lowerSearchWord);
            } else {
                return -1; // 或其他表示未找到的逻辑
            }
        };
        // layui.table 使用搜索关键字高亮显示指定列
        window.highlight=function(fieldName,rowData)
        {
            var result=rowData[fieldName]!=null?rowData[fieldName].toString():"";
            var len=searchKeyMap.length;
            for(var i=0;i<len;i++)
            {
                if(fieldName.toLowerCase()==searchKeyMap[i].fieldname)
                {
                    var  rest=result,result='',word=searchKeyMap[i].keyword;
                    while(rest.length>0)
                    {
                        var idx=findWordIndex(rest,word);
                        if(idx>-1)
                        {
                            result+=(idx>0?rest.substr(0,idx):'') +"<em>"+rest.substr(idx,word.length)+"</em>";
                            rest=rest.substring(idx+word.length);
                        }
                        else
                        {
                            result+=rest;
                            rest='';
                        }
                    }
                }
            }
            return result;
        };
        let cols = [
            [
                {field: 'number', title: '序号', align: 'center', type: 'numbers', width: '5%'},
                {title: '标准号', align: 'left', width: '15%',templet:function(rowData){
                        return window.highlight("stdNo",rowData);
                    }},
                {title: '标准名称(原文)',  align: 'left', width: '22%',templet:function(rowData){
                        return window.highlight("stdOrgName",rowData);
                    }},
                {title: '标准名称(中文)',  align: 'left', width: '21%',templet:function(rowData){
                        return window.highlight("stdChineseName",rowData);
                    }},
                {title: 'PDF文件名',  align: 'left', width: '15%',templet:function(rowData){
                        return window.highlight("pdfFileName",rowData);
                    }},
                {title: '预览时间', field: 'createTime', align: 'center', width: '10%'},
                {title: '操作', toolbar: '#role-bar', align: 'center',width: '8%'}
            ]
        ]
        window.tableRender=function(pageSize) {
            table.render({
                elem: '#role-table',
                url: MODULE_PATH + 'data',
                page: false,
                limit: pageSize,
                cols: cols,
                skin: 'row,line',
                toolbar: '#role-toolbar',
                cellMinWidth: 'auto',
                where: {
                    queryIdenti: "0"
                },
                defaultToolbar: [{
                    title: '刷新',
                    layEvent: 'refresh',
                    icon: 'layui-icon-refresh',
                }, 'filter', 'print', 'exports'],
                done: function (res, curr, count) {
                    console.log("Table render done", res);
                    try {
                        if (res && res.count !== undefined) {
                            if (hasQueryCondtion) {
                                $("#resultInfo").html('命中' + res.count + "条预览记录");
                            } else {
                                $("#resultInfo").html('共' + res.count + "条预览记录");
                            }

                            var $table = $('div[lay-id="role-table"]');
                            if ($table.length > 0) {
                                var $data = $table.find('.layui-table-body').find('tr').eq(0).find('td');
                                var $head = $table.find('.layui-table-header').find('tr').eq(0).find('th');

                                if ($data && $data.length > 0 && $head && $head.length > 0) {
                                    for (var i = 0; i < $data.length; i++) {
                                        var l1 = $data.eq(i).find('div').width();
                                        var l2 = $head.eq(i).find('div').width();
                                        if (l1 > l2) {
                                            $head.eq(i).find('div').width(l1);
                                        } else if (l2 > l1) {
                                            $data.eq(i).find('div').width(l2);
                                        }
                                    }
                                }
                            }

                            initPage(curr || 1, res.count || 0);
                        } else {
                            $("#resultInfo").html('暂无预览记录');
                            initPage(1, 0);
                        }
                    } catch (e) {
                        console.error("Error in table done callback:", e);
                        $("#resultInfo").html('加载预览记录时出错');
                        initPage(1, 0);
                    }
                }
            });
        }
        tableRender(pageSize);

        window.doPage=function()
        {
            var key = $('#searchKey').val();
            if(!key)
            {
                hasQueryCondtion=false;
            }
            else
            {
                hasQueryCondtion=true;
            }
            table.reload('role-table', {where: {simpleSearchKey:key},page: { curr: currPage1,limit:pageSize }});
            window.createKeywords();
        }
        window.onPage1Change=function(page,isFirst) {
            currPage1 = page.curr;
            currPage2=currPage1;
            var p = page.curr;//改变当前页码
            var l = page.limit;
            pageSize=l;
            if (!isFirst) {
                doPage();
                laypage.render({
                    elem: 'pagination2',
                    count: totalRecord,
                    limit: l,
                    curr: currPage1,
                    limits:[30,50,100],
                    layout: ['prev', 'page', 'next', 'skip', 'count','limit'],
                    jump: onPage2Change
                });
            }
        }
        window.onPage2Change=function(page,isFirst) {
            currPage1 = page.curr;
            currPage2=currPage1;
            var p = page.curr;//改变当前页码
            var l = page.limit;
            pageSize=l;
            if (!isFirst) {
                doPage();
                laypage.render({
                    elem: 'pagination1',
                    count: totalRecord,
                    limit: l,
                    curr: currPage1,
                    limits:[30,50,100],
                    layout: ['prev', 'page', 'next', 'skip', 'count','limit'],
                    jump: onPage1Change
                });
            }
        }
        window.initPage=function(currPage,total) {
            try {
                totalRecord = total || 0;
                currPage1 = currPage || 1;
                currPage2 = currPage || 1;
                console.log("Initializing pagination with page:", currPage1, "total:", totalRecord);

                if (document.getElementById('pagination1')) {
                    laypage.render({
                        elem: 'pagination1',
                        count: totalRecord,
                        limit: pageSize,
                        curr: currPage1,
                        limits: [30, 50, 100],
                        layout: ['prev', 'page', 'next', 'skip', 'count', 'limit'],
                        jump: onPage1Change
                    });
                }

                if (document.getElementById('pagination2')) {
                    laypage.render({
                        elem: 'pagination2',
                        count: totalRecord,
                        limit: pageSize,
                        limits: [30, 50, 100],
                        curr: currPage1,
                        layout: ['prev', 'page', 'next', 'skip', 'count', 'limit'],
                        jump: onPage1Change
                    });
                }
            } catch (e) {
                console.error("Error initializing pagination:", e);
            }
        }
        table.on('tool(role-table)', function (obj) {
            if (obj.event === 'onlineRead') {
                try {
                    console.log("onlineRead event triggered with data:", obj.data);
                    var stdObj = {};
                    $.extend(true, stdObj, obj);

                    // 确保 stdInfoId 存在
                    if (stdObj.data["stdInfoId"]) {
                        stdObj.data["id"] = stdObj.data["stdInfoId"];
                        console.log("Previewing document with ID: " + stdObj.data["id"]);

                        // 使用 preview 函数
                        window.preview(stdObj);
                    } else {
                        console.error("stdInfoId is missing in the data");
                        layer.alert('文档ID不存在', {
                            title: '提示',
                            icon: 1,
                            btn: ['确定']
                        });
                    }
                } catch (e) {
                    console.error("Error in preview function: " + e.message);
                    layer.alert('预览时发生错误', {
                        title: '提示',
                        icon: 1,
                        btn: ['确定']
                    });
                }
            }
        });
        table.on('toolbar(role-table)', function (obj) {
           if (obj.event === 'refresh') {
                window.refresh();
            }
        });

        $("#btnSimpleSearch").click(function(e){
                if (e.which == 1) {//.which属性判断按下的是哪个键，回车键的键位序号为13
                    //简单搜索
                    console.log("----simple search--------------------------");
                    var key = $('#searchKey').val();
                    if(!key)
                    {
                        hasQueryCondtion=false;
                    }
                    else
                    {
                        hasQueryCondtion=true;
                    }
                    table.reload('role-table', {where: {simpleSearchKey:key},page: { curr: currPage1,limit:pageSize }});
                    window.createKeywords();
                }
            return false;
        });
        $("#clearSimpleCon").click(function(){
            $('#searchKey').val("");
            table.reload('role-table', {where: {simpleSearchKey:""},page: { curr: 1 }});
            $("#clearSimpleCon").hide();
            window.createKeywords();
        });
        window.refresh = function () {
            table.reload('role-table');
        }
    });
</script>
<style>
    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: flex-start;
        flex-direction: column;
    }

    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
        margin-bottom: 0.625rem;
    }

    .simple-container .item .sub-item button {
        margin-top: 10px;
    }

    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }

    .advance-container {
        width: 75%;
        margin: 0px auto;
        padding: 0px 0px 0px 10px;
    }

    .advance-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-bottom: 15px;
    }
    .layui-table-tool {
        height: 50px !important;
        line-height: 1px !important;
        padding: 0px !important;
        min-height: 0px !important;
    }
    .layui-form-item .layui-form-label, .layui-form-item .layui-input-inline {
        margin-bottom: 7px;
    }
    .layui-form-item {
        padding-bottom: 0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    #resultInfo {
        width: 100%;
        text-align: center;
        color: #999;
        margin-top: 10px;
    }
    .layui-table thead th {
        font-weight: bold;
    }
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    .search-label {
        flex-grow: 2;
    }
    .simple-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #ddd;
        border-radius: 5px;
    }
    .simple-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .simple-container .search-cmp button:hover {
        cursor: pointer;
    }
    .simple-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .simple-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .advance-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #efefef;
        border-radius: 5px;
    }
    .advance-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .advance-container .search-cmp button:hover {
        cursor: pointer;
    }
    .advance-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .advance-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 140px;
    }
    .con-type-container {
        width: 100px;
    }
    .search-anchor-1
    {
        flex-grow:5;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }
    em{
        color:red;
        font-style: normal;
    }
    #pagination1,#pagination2{
        text-align: center;
    }
    .layui-laypage {
        margin-bottom: 0px !important;
    }
    .layui-table-view {
        margin-top:0px !important;
    }
    .layui-table-column{
        display: none;
    }
</style>
</html>
