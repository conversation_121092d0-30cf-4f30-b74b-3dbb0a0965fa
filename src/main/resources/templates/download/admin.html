<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('下载记录管理')"/>
    <style>
        .admin-container {
            display: flex;
            height: calc(100vh - 120px);
            gap: 10px;
        }
        .user-panel {
            width: 350px;
            min-width: 350px;
            background: #fff;
            border-radius: 2px;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        }
        .download-panel {
            flex: 1;
            background: #fff;
            border-radius: 2px;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        }
        .panel-header {
            padding: 15px 20px;
            border-bottom: 1px solid #f0f0f0;
            font-weight: bold;
            color: #333;
        }
        .panel-body {
            padding: 15px 20px;
            height: calc(100% - 60px);
            overflow: auto;
        }
        .user-item {
            padding: 12px 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .user-item:hover {
            background-color: #f8f8f8;
        }
        .user-item.active {
            background-color: #e6f7ff;
            border-left: 3px solid #1890ff;
        }
        .user-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .user-info {
            font-size: 12px;
            color: #666;
            line-height: 1.5;
        }
        .download-count {
            color: #1890ff;
            font-weight: bold;
        }
        .no-user-selected {
            text-align: center;
            color: #999;
            padding: 50px 20px;
            font-size: 14px;
        }
        .search-container {
            margin-bottom: 15px;
        }
        .layui-table-view {
            margin-top: 0 !important;
        }
    </style>
</head>
<body class="pear-container">
<div class="admin-container">
    <!-- 左侧用户列表 -->
    <div class="user-panel">
        <div class="panel-header">
            用户列表
        </div>
        <div class="panel-body">
            <div id="userList">
                <div class="no-user-selected">加载中...</div>
            </div>
            <div id="userPagination" style="text-align: center; margin-top: 15px;"></div>
        </div>
    </div>

    <!-- 右侧下载记录 -->
    <div class="download-panel">
        <div class="panel-header">
            <span id="downloadPanelTitle">下载记录</span>
        </div>
        <div class="panel-body">
            <div id="downloadContent">
                <div class="no-user-selected">请选择左侧用户查看其下载记录</div>
            </div>
        </div>
    </div>
</div>
</body>

<script id="role-toolbar" type="text/html">
</script>
<script id="role-bar" type="text/html">
    <a class="c-anchor layui-icon layui-icon-form" href="javascript:void(0);" title="详情" lay-event="edit"></a>
    <a class="c-anchor iconfont icon-chakan" href="javascript:void(0);" title="在线查看" lay-event="onlineRead"></a>
    <a href="javascript:void(0);" lay-event="download" class="c-anchor iconfont icon-xiazaidaoru is-show-1" title="下载"></a>
    <a id="collect-anchor-{{d.id}}" class="c-anchor layui-icon layui-icon-{{d.isCollected=='1'?'rate-solid':'rate'}}" href="javascript:void(0);" title="{{d.isCollected=='1'?'取消收藏':'收藏'}}" lay-event="collect"></a>
    <a class="c-anchor layui-icon layui-icon-survey" href="javascript:void(0);" lay-event="opinion" title="意见反馈"></a>
</script>
<th:block th:include="include :: footer"/>
<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common', 'upload', 'layer', 'element','laypage'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;
        let laypage = layui.laypage;
        let MODULE_PATH = "/stddownload/";

        // 全局变量
        let currentUserId = null;
        let currentUserName = '';
        let userPageInfo = { current: 1, size: 10, total: 0 };
        let downloadPageInfo = { current: 1, size: 10, total: 0 };
        let searchKeyMap = [];
        let hasQueryCondition = false;
        // 搜索关键字处理函数
        window.createKeywords = function(searchKey) {
            var data = [];
            if (searchKey && searchKey.trim()) {
                var words = searchKey.trim().split(/[\s+]/);
                console.log("Search words:", words);
                for (var i = 0; i < words.length; i++) {
                    if (words[i]) {
                        data.push({fieldname: 'stdno', keyword: words[i]});
                        data.push({fieldname: 'stdorgname', keyword: words[i]});
                        data.push({fieldname: 'stdchinesename', keyword: words[i]});
                        data.push({fieldname: 'pdfFileName', keyword: words[i]});
                    }
                }
            }
            console.log("Search keyword map:", data);
            searchKeyMap = data;
        };
        // 查找关键字在字符串中的位置
        window.findWordIndex = function(str, searchWord) {
            if (!str || !searchWord) {
                return -1;
            }
            const lowerStr = str.toLowerCase();
            const lowerSearchWord = searchWord.toLowerCase();
            const isIncluded = lowerStr.includes(lowerSearchWord);
            if (isIncluded) {
                return lowerStr.indexOf(lowerSearchWord);
            } else {
                return -1;
            }
        };

        // 高亮显示搜索关键字
        window.highlight = function(fieldName, rowData) {
            var result = rowData[fieldName] != null ? rowData[fieldName].toString() : "";
            var len = searchKeyMap.length;
            for (var i = 0; i < len; i++) {
                if (fieldName.toLowerCase() == searchKeyMap[i].fieldname) {
                    var rest = result, result = '', word = searchKeyMap[i].keyword;
                    while (rest.length > 0) {
                        var idx = findWordIndex(rest, word);
                        if (idx > -1) {
                            result += (idx > 0 ? rest.substr(0, idx) : '') + "<em>" + rest.substr(idx, word.length) + "</em>";
                            rest = rest.substring(idx + word.length);
                        } else {
                            result += rest;
                            rest = '';
                        }
                    }
                }
            }
            return result;
        };

        // 加载用户列表
        function loadUserList(page = 1) {
            console.log("Loading user list, page:", page);
            $.ajax({
                url: MODULE_PATH + 'adminUsers',
                type: 'GET',
                data: {
                    page: page,
                    limit: userPageInfo.size
                },
                success: function(res) {
                    console.log("User list response:", res);
                    if (res.code === 0 && res.data) {
                        renderUserList(res.data);
                        userPageInfo.total = res.count || 0;
                        userPageInfo.current = page;
                        renderUserPagination();
                    } else {
                        $('#userList').html('<div class="no-user-selected">暂无用户数据</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error loading user list:", error);
                    $('#userList').html('<div class="no-user-selected">加载用户列表失败</div>');
                }
            });
        }
        // 渲染用户列表
        function renderUserList(users) {
            let html = '';
            if (users && users.length > 0) {
                users.forEach(function(user) {
                    html += '<div class="user-item" data-user-id="' + user.userId + '" data-username="' + (user.realName || user.username) + '">';
                    html += '<div class="user-name">' + (user.realName || user.username) + '</div>';
                    html += '<div class="user-info">';
                    html += '用户名: ' + user.username + '<br>';
                    html += '下载次数: <span class="download-count">' + user.downloadCount + '</span><br>';
                    html += '最后下载: ' + (user.lastDownloadTime || '无');
                    html += '</div>';
                    html += '</div>';
                });
            } else {
                html = '<div class="no-user-selected">暂无用户数据</div>';
            }
            $('#userList').html(html);

            // 绑定用户点击事件
            $('.user-item').click(function() {
                $('.user-item').removeClass('active');
                $(this).addClass('active');
                currentUserId = $(this).data('user-id');
                currentUserName = $(this).data('username');
                $('#downloadPanelTitle').text(currentUserName + ' 的下载记录');
                loadDownloadRecords(1);
            });
        }

        // 渲染用户分页
        function renderUserPagination() {
            if (userPageInfo.total > 0) {
                laypage.render({
                    elem: 'userPagination',
                    count: userPageInfo.total,
                    limit: userPageInfo.size,
                    curr: userPageInfo.current,
                    limits: [10, 20, 50],
                    layout: ['count', 'prev', 'page', 'next', 'limit'],
                    jump: function(obj, first) {
                        if (!first) {
                            userPageInfo.size = obj.limit;
                            loadUserList(obj.curr);
                        }
                    }
                });
            } else {
                $('#userPagination').empty();
            }
        }

        // 加载下载记录
        function loadDownloadRecords(page = 1, searchKey = '') {
            if (!currentUserId) {
                $('#downloadContent').html('<div class="no-user-selected">请选择左侧用户查看其下载记录</div>');
                return;
            }

            console.log("Loading download records for user:", currentUserId, "page:", page, "searchKey:", searchKey);

            // 创建搜索关键字映射
            window.createKeywords(searchKey);
            hasQueryCondition = !!searchKey;

            // 渲染下载记录表格
            renderDownloadTable(page, searchKey);
        }

        // 渲染下载记录表格
        function renderDownloadTable(page = 1, searchKey = '') {
            let tableHtml = `
                <div class="search-container">
                    <form class="layui-form">
                        <div class="layui-form-item">
                            <div class="layui-input-group">
                                <input type="text" id="downloadSearchKey" placeholder="搜索标准号/标准名称" class="layui-input" value="${searchKey}">
                                <div class="layui-input-split layui-input-suffix">
                                    <button type="button" id="downloadSearchBtn" class="layui-btn layui-btn-primary">
                                        <i class="layui-icon layui-icon-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                    <div id="downloadResultInfo" style="text-align: center; color: #666; margin: 10px 0;">加载中...</div>
                </div>
                <table id="downloadTable" lay-filter="downloadTable"></table>
                <div id="downloadPagination" style="text-align: center; margin-top: 15px;"></div>
            `;

            $('#downloadContent').html(tableHtml);

            // 定义表格列
            let cols = [
                [
                    {field: 'number', title: '序号', align: 'center', type: 'numbers', width: '8%'},
                    {title: '标准号', align: 'left', width: '15%', templet: function(rowData) {
                        return window.highlight("stdNo", rowData);
                    }},
                    {title: '标准名称(原文)', align: 'left', width: '25%', templet: function(rowData) {
                        return window.highlight("stdOrgName", rowData);
                    }},
                    {title: '标准名称(中文)', align: 'left', width: '25%', templet: function(rowData) {
                        return window.highlight("stdChineseName", rowData);
                    }},
                    {title: '下载时间', field: 'createTime', align: 'center', width: '12%'},
                    {title: '收藏状态', align: 'center', width: '8%', templet: function(rowData) {
                        if (rowData.isCollected === '1') {
                            return '<span class="layui-badge layui-bg-green">已收藏</span>';
                        } else {
                            return '<span class="layui-badge layui-bg-gray">未收藏</span>';
                        }
                    }},
                    {title: '操作', toolbar: '#role-bar', align: 'center', width: '7%'}
                ]
            ];

            // 渲染表格
            table.render({
                elem: '#downloadTable',
                url: MODULE_PATH + 'adminData',
                page: false,
                limit: downloadPageInfo.size,
                cols: cols,
                skin: 'row,line',
                cellMinWidth: 'auto',
                where: {
                    userId: currentUserId,
                    simpleSearchKey: searchKey,
                    page: page,
                    limit: downloadPageInfo.size
                },
                done: function(res, curr, count) {
                    console.log("Download table render done:", res);
                    downloadPageInfo.total = res.count || 0;
                    downloadPageInfo.current = page;

                    // 更新结果信息
                    let resultText = hasQueryCondition ?
                        '命中 ' + downloadPageInfo.total + ' 条下载记录' :
                        '共 ' + downloadPageInfo.total + ' 条下载记录';
                    $('#downloadResultInfo').text(resultText);

                    // 渲染分页
                    renderDownloadPagination(searchKey);
                }
            });

            // 绑定搜索事件
            $('#downloadSearchBtn').click(function() {
                let newSearchKey = $('#downloadSearchKey').val();
                loadDownloadRecords(1, newSearchKey);
            });

            $('#downloadSearchKey').keypress(function(e) {
                if (e.which == 13) {
                    let newSearchKey = $('#downloadSearchKey').val();
                    loadDownloadRecords(1, newSearchKey);
                }
            });
        }

        // 渲染下载记录分页
        function renderDownloadPagination(searchKey = '') {
            if (downloadPageInfo.total > 0) {
                laypage.render({
                    elem: 'downloadPagination',
                    count: downloadPageInfo.total,
                    limit: downloadPageInfo.size,
                    curr: downloadPageInfo.current,
                    limits: [10, 20, 50],
                    layout: ['count', 'prev', 'page', 'next', 'limit'],
                    jump: function(obj, first) {
                        if (!first) {
                            downloadPageInfo.size = obj.limit;
                            loadDownloadRecords(obj.curr, searchKey);
                        }
                    }
                });
            } else {
                $('#downloadPagination').empty();
            }
        }
        // 表格行工具事件处理
        table.on('tool(downloadTable)', function(obj) {
            if (obj.event === 'edit') {
                window.detail(obj);
            } else if (obj.event === 'download') {
                var stdObj = {'data': {'id': obj.data['stdInfoId'], 'stdNo': obj.data['stdNo'], 'stdOrgName': obj.data['stdOrgName'], 'isCollected': obj.data['isCollected']}};
                window.download(stdObj);
            } else if (obj.event === 'onlineRead') {
                try {
                    console.log("onlineRead event triggered with data:", obj.data);
                    var stdObj = {};
                    $.extend(true, stdObj, obj);

                    if (stdObj.data["stdInfoId"]) {
                        stdObj.data["id"] = stdObj.data["stdInfoId"];
                        console.log("Previewing document with ID: " + stdObj.data["id"]);
                        window.preview(stdObj);
                    } else {
                        console.error("stdInfoId is missing in the data");
                        layer.alert('文档ID不存在', {
                            title: '提示',
                            icon: 1,
                            btn: ['确定']
                        });
                    }
                } catch (e) {
                    console.error("Error in preview function: " + e.message);
                    layer.alert('预览时发生错误', {
                        title: '提示',
                        icon: 1,
                        btn: ['确定']
                    });
                }
            } else if (obj.event === 'collect') {
                var stdObj = {'data': {'id': obj.data['stdInfoId'], 'stdNo': obj.data['stdNo'], 'stdOrgName': obj.data['stdOrgName'], 'isCollected': obj.data['isCollected']}};
                window.collect(stdObj, 'Post_Standard_Collect', '', '', function() {
                    // 重新加载当前用户的下载记录
                    loadDownloadRecords(downloadPageInfo.current, $('#downloadSearchKey').val());
                });
            } else if (obj.event === 'opinion') {
                window.opinion(obj);
            }
        });
        // 详情查看函数
        window.detail = function(obj) {
            layer.open({
                type: 2,
                title: '【' + obj.data['stdOrgName'] + '】',
                shade: 0.1,
                maxmin: true,
                area: ['1000px', '500px'],
                content: (obj.data["stdType"] === 'usa' ? '/usastd/' : obj.data["stdType"] === 'china' ? '/chinastd/' : '/stdforeigin/') + 'detail?stdID=' + obj.data['stdInfoId']
            });
            window.writelog('Standard_OverView', obj.data["stdNo"], obj.data["stdOrgName"], "", "", "");
        };

        // 意见反馈函数
        window.opinion = function(obj) {
            parent.layer.open({
                type: 2,
                title: "【" + obj.data["stdOrgName"] + "】意见反馈列表",
                shade: 0.1,
                area: ['1200px', '630px'],
                content: '/stdopinion/main?stdId=' + obj.data["stdInfoId"]
            });
        };

        // 初始化页面
        $(function() {
            console.log("Initializing admin download page...");
            // 加载用户列表
            loadUserList(1);
        });
        // 页面刷新函数
        window.refresh = function() {
            if (currentUserId) {
                loadDownloadRecords(downloadPageInfo.current, $('#downloadSearchKey').val() || '');
            }
        };
    });
</script>
<style>
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    .layui-table thead th {
        font-weight: bold;
    }
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    em {
        color: red;
        font-style: normal;
    }
    .layui-laypage {
        margin-bottom: 0px !important;
    }
    .layui-table-view {
        margin-top: 0px !important;
    }
</style>
</html>
