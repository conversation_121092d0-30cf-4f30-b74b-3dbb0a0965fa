<!DOCTYPE html>
<html lang="en" xmlns:sec="http://www.thymeleaf.org/extras/spring-security" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('情报室业务')"/>
</head>
<body class="pear-container">
<div class="layui-card">
    <div class="layui-card-body">
        <form action="" class="layui-form">
            <div class="layui-tab" lay-filter="query_switch">
                <ul class="layui-tab-title">
                    <li class="layui-this" lay-id="basic">情报室业务</li>
<!--                    <li lay-id="adv">高级检索</li>-->
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show">
                        <div class="big-container">
                            <div class="left-container">
                                <div>
                                    <ul id="conditionUl1">
<!--                                        <li>检索字段：</li>-->
                                        <li>
                                            <input type="checkbox" id="chkStdNo" checked="checked" name="chkQueryField" value="std_no" /><label class="chk-label" for="chkStdNo">标准号</label>
                                        </li>
                                        <li>
                                            <input type="checkbox" id="chkStdName"  name="chkQueryField"  value="std_org_name" /><label  class="chk-label" for="chkStdName">标准名称</label>
                                        </li>
                                        <li>
                                            <input type="checkbox" id="chkPdfFileName" name="chkQueryField"  value="pdf_file_name"  /><label  class="chk-label" for="chkPdfFileName">pdfname</label>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="middle-container">
<!--                                <label>条件录入：</label>-->
                                <ul id="keywordsUl">
                                </ul>
                                <div>
                                    <input  type="text" value="" placeholder="请输入关键词" id="txtKeyWord" onkeydown="handleKeyDown()" />
                                    <button class="pear-btn pear-btn-primary pear-btn-sm" id="addToList"
                                            type="button">
                                        <i class="layui-icon layui-icon-add-1"></i>
                                        增加
                                    </button>
                                    <div id="uploadElem" style="display: inline-block;"></div>
                                    <button class="pear-btn pear-btn-danger pear-btn-sm" style="background-color:#ff0000 !important;" id="btnClearList"
                                            type="button">
                                        <i class="layui-icon layui-icon-delete"></i>
                                        清空
                                    </button>
                                </div>
                            </div>
                            <div class="right-container">
                                <ul id="conditionUl">
                                    <li style="display: none">
                                        <input type="radio" name="chkLogic"  value="1" id="rdoAnd" /><label class="chk-label" for="rdoAnd">And</label>
                                        <input type="radio" name="chkLogic" checked="checked" value="2"  id="rdoOr"/><label class="chk-label" for="rdoOr">Or</label>
                                    </li>
                                    <li>
                                        <label>左侧：</label>
                                        <input type="checkbox" id="chkLeftLike" checked="checked" /><label class="chk-label  star" for="chkLeftLike">*</label>
                                        <input type="checkbox" id="chkLeftLike_space" /><label  class="chk-label  star" for="chkLeftLike_space">*<span style="font-size:14px !important;">空格</span></label>
                                    </li>
                                    <li>
                                        <label>右侧：</label>
                                        <input type="checkbox" id="chkRightLike" checked="checked" /><label  class="chk-label star" for="chkRightLike">*</label>
                                        <label style="padding-right: 15px;">（</label>
                                        <input type="checkbox" id="chkRightLike_strike"  /><label  class="chk-label  star" for="chkRightLike_strike"><span class="char_up">ˉ</span>*</label>
                                        <input type="checkbox" id="chkRightLike_char"  /><label  class="chk-label  star" for="chkRightLike_char"><span style="font-size:14px !important;">ABC</span>*</label>
                                        <input type="checkbox" id="chkRightLike_dot"  /><label  class="chk-label  star" for="chkRightLike_dot">.*</label>
                                        <input type="checkbox" id="chkRightLike_bias"  /><label  class="chk-label  star" for="chkRightLike_bias">/*</label>
                                        <label>）</label>
                                    </li>
                                    <li>
                                        括号内的检索方式最多支持50行关键词
                                    </li>
                                    <li style="margin-top:10px">
                                        <button class="pear-btn pear-btn-primary pear-btn-sm action-btn" id="btnQuery"
                                                type="button">
                                            <i class="layui-icon layui-icon-search"></i>
                                            查 找
                                        </button>
                                        <button class="pear-btn pear-btn-primary pear-btn-sm action-btn" id="btnExport"
                                                type="button" style="margin-left:15px;">
                                            <i class="layui-icon layui-icon-export"></i>
                                            导 出
                                        </button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div  class="item" style="display:flex;margin-left:300px;width:100%;align-items: center;margin-bottom: 0.625rem;">
                            <input type="checkbox" name="chkClass" id="chkAll" value="0" title="全部" lay-skin="primary" lay-filter="i-all" />
                            <input type="checkbox" checked="checked" name="chkClass" id="chkGJB" value="1"  title="GJB" lay-skin="primary" lay-filter="i-gjb" />
                            <input type="checkbox" checked="checked" name="chkClass" id="chkWJ" value="2"  title="WJ" lay-skin="primary"  lay-filter="i-wj" />
                            <input type="checkbox" checked="checked" name="chkClass" id="chkQCNG" value="3"  title="Q/CNG" lay-skin="primary"  lay-filter="i-qcng" />
                            <input type="checkbox" name="chkClass" id="chkGB" value="4"  title="GB" lay-skin="primary"  lay-filter="i-gb" />
                            <input type="checkbox" name="chkClass" id="chkOther" value="5"  title="其他标准" lay-skin="primary"  lay-filter="i-other" />
                        </div>
                    </div>
                    <div class="layui-tab-item">
                        <div class="advance-container">
                            <div class="advance-item" id="advanceItem1" style="width:100%;">
                                <div class="ph-div" style="text-align: right;">高级检索：</div>
                                <div class="search-cmp">
                                    <div class="con-field-container">
                                        <select id="conField1" class="con-field">
                                            <option value="std_no">标准号</option>
                                            <option value="std_org_name">标准名称（原文）</option>
                                            <option value="std_chinese_name">标准名称（中文）</option>
                                            <option value="std_english_name">标准名称（英文）</option>
                                            <option value="std_class">标准类别</option>
                                            <option value="catetory_no">分类号</option>
                                            <option value="std_ics">ICS号</option>
                                            <option value="pub_date">发布日期</option>
                                            <option value="implementation_date">实施日期</option>
                                            <option value="advance_dept">提出单位</option>
                                            <option value="drafting_unit">起草单位</option>
                                            <option value="drafter">起草人</option>
                                            <option value="std_status">标准状态</option>
                                        </select>
                                    </div>
                                    <input class="layui-input sub-item con-val" id="conVal1" placeholder="" type="text">
                                    <div class="con-type-container">
                                        <select id="conQueryType1" class="con-query-type">
                                            <option value="fuzzy">模糊</option>
                                            <option value="exact">精确</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="ph-end-div">
                                </div>
                            </div>
                            <div class="advance-item" id="advanceItem2" style="width:100%;">
                                <div class="ph-div">
                                    <select id="conAndOr2" class="conAndOr">
                                        <option value="AND">AND</option>
                                        <option value="OR">OR</option>
                                        <option value="NOT">NOT</option>
                                    </select>
                                </div>
                                <div class="search-cmp">
                                    <div class="con-field-container">
                                        <select id="conField2">
                                            <option value="std_no">标准号</option>
                                            <option value="std_org_name">标准名称（原文）</option>
                                            <option value="std_chinese_name">标准名称（中文）</option>
                                            <option value="std_english_name">标准名称（英文）</option>
                                            <option value="std_class">标准类别</option>
                                            <option value="catetory_no">分类号</option>
                                            <option value="std_ics">ICS号</option>
                                            <option value="pub_date">发布日期</option>
                                            <option value="implementation_date">实施日期</option>
                                            <option value="advance_dept">提出单位</option>
                                            <option value="drafting_unit">起草单位</option>
                                            <option value="drafter">起草人</option>
                                            <option value="std_status">标准状态</option>
                                        </select>
                                    </div>
                                    <input class="layui-input sub-item" id="conVal2" placeholder="" type="text">
                                    <div class="con-type-container">
                                        <select id="conQueryType2">
                                            <option value="fuzzy">模糊</option>
                                            <option value="exact">精确</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="ph-end-div">
                                    <a href="javascript:void(0);" onclick="delCondition(2)" class="conDel" id="conDel2" style="display: none;">-</a>
                                    <a href="javascript:void(0);" onclick="addCondition()" class="conAdd" id="conAdd2">+</a>
                                </div>
                            </div>
                        </div>
                        <div class="item" id="advTool" style="text-align: center;">
                            <button class="pear-btn pear-btn-md sub-item" type="reset" style="margin-left:30px;margin-top:0px;width:80px;padding-left:5px;">
                                <i class="layui-icon layui-icon-refresh"></i>
                                重 置
                            </button>
                            <button class="pear-btn pear-btn-md pear-btn-primary sub-item"
                                id="btnQueryAdv"    style="margin-left:40px;margin-top:0px;width:80px;padding-left:5px;" type="button">
                                <i class="layui-icon layui-icon-search"></i>
                                检 索
                            </button>
                            <button class="pear-btn pear-btn-md pear-btn-primary sub-item" id="btnExport1"
                                    type="button">
                                <i class="layui-icon layui-icon-export"></i>
                                导出
                            </button>
                        </div>
                    </div>
                    <div id="resultInfo"></div>
                    <div style="width:100%;text-align: left;color: black;font-size: 14px;padding-top:5px;">
                        <span><span>注：</span>系统一次最多可以导出<span id="spnDownMaxCount">2000</span>条数据，如果超过该限制，请分批执行导出操作。</span>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<div class="layui-card">
    <div class="layui-card-body">
        <div id="pagination1"></div>
        <table id="role-table" lay-filter="role-table"></table>
        <div id="pagination2"></div>
    </div>
</div>
</body>
<script id="role-toolbar" type="text/html">
    <button id="btnExportCurr"  class="pear-btn pear-btn-primary pear-btn-md custom-blue" style="margin-left:15px;" lay-event="btnExportCurrPage">
        <i class="layui-icon layui-icon-export"></i>
        当前页选中项导出
    </button>
</script>

<th:block th:include="include :: footer"/>

<script>
    layui.use(['table', 'form', 'jquery', 'popup', 'common', 'upload', 'layer', 'element','laypage'], function () {
        let table = layui.table;
        let form = layui.form;
        let $ = layui.jquery;
        let popup = layui.popup;
        let common = layui.common;
        let upload = layui.upload;
        let layer = layui.layer;
        let element = layui.element;
        let q_type='basic';//basic adv
        let laypage= layui.laypage;
        let conditionArr=[1,2];
        let searchKeyMap=[];
        var currPage1=1;
        var currPage2=1;
        var pageSize=50;
        let MODULE_PATH = "/inteligbusi/";
        window.getMaxCount=function()
        {
            $.ajax({
                url: "/source/ib_m_d_c",
                dataType: 'json',
                type: 'get',
                success: function (result) {
                    if (result.success) {
                        $("#spnDownMaxCount").text(result.data);
                    }
                }
            });
        }
        getMaxCount();
        element.on('tab(query_switch)', function(){
             q_type=this.getAttribute('lay-id');
             console.log(q_type+":"+q_type);
        });
        table.on('toolbar(role-table)', function (obj) {
            if (obj.event === 'btnExportCurrPage') {
                let ids = common.checkField(obj, 'id');
                if (common.isEmpty(ids)) {
                    popup.warning("请在数据列表左侧勾选要导出的记录！");
                    return false;
                }
                layer.msg('正在导出数据...',{
                    icon:16,
                    time: 30000
                });
                $.ajax({
                    url: '/inteligbusi/exportcurrpage',
                    data: JSON.stringify({ids:ids}),
                    dataType: 'json',
                    contentType: 'application/json',
                    type: 'post',
                    success: function (result) {
                        if (result.success) {
                            layer.closeAll();
                            layer.open({
                                type:1,
                                content:"<div style='padding: 15px 25px 30px 25px;line-height: 25px;font-size: 12px;background-color: #efefef;'>已导出，请在服务器的【"+result.data+"】目录下查看导出数据。</div>",
                                offset:'50px'
                            })
                        } else {
                            layer.closeAll();
                            layer.open({
                                type:1,
                                content:"<div style='padding: 15px 25px 30px 25px;line-height: 25px;font-size: 12px;background-color: #efefef;'>导出失败"+(result.msg?"，具体原因："+result.msg:"。")+"</div>",
                                area: "300px",
                                offset:'50px'
                            })
                        }
                    }
                })
                return false;
            }
        });
        window.handleKeyDown=function() {
            var event=window.event;
           // console.log(event);
            // You can perform actions based on the key pressed
           // console.log('Key pressed:', event.key);

            // For example, prevent the default behavior of the Enter key
            if (event.key === 'Enter') {
                var kw=$("#txtKeyWord").val();
                if(kw) {
                    $("#keywordsUl").append('<li value="' + kw + '"><span class="word">' + kw + '</span><span class="close" onclick="window.deleteItem(this)">删除</span></li>');
                    $("#txtKeyWord").val("");
                }
            }
        };
        //var input = document.getElementById("txtKeyWord");
        // element.on('keydown(key-down-word)',function(e){
        //     console.log(e);
        //     var value = e.elem.value;
        //     if(value === 13){
        //
        //     }
        // });
        // 格式化搜索关键字，放到一个json中用来在结果中高亮显示关键字
        //搜索用函数
        window.createKeywords=function()
        {
            //console.log("$(\".simple-container\").css(\"display\"):"+$(".simple-container").css("display"));
            //console.log("$(\".advance-container\").css(\"display\"):"+$(".advance-container").css("display"));
            var data=[];
            if($(".simple-container").css("display")=="flex" || $(".simple-container").css("display")=="block")
            {
                var words=$("#searchKey").val().trim().split(/[\s+]/);
                console.log("words:"+words);
                for(var i=0;i<words.length;i++)
                {
                    data.push({fieldname:'stdno',keyword:words[i]});
                    data.push({fieldname:'stdorgname',keyword:words[i]});
                }
            }
            else if($(".advance-container").css("display")=="flex" || $(".advance-container").css("display")=="block")
            {
                for(var i=0;i<conditionArr.length;i++)
                {
                    var keyword=$("#conVal"+conditionArr[i]).val().trim();
                    if(keyword) {
                        var obj = {};
                        obj.fieldname = $("#conField" + conditionArr[i]).val().replaceAll("_", "");
                        obj.keyword = keyword;
                        data.push(obj);
                    }
                }
            }
            console.log(data);
            searchKeyMap=data;
        };
        $("#btnClearList").on("click",function(){
            $("#keywordsUl").empty();
        });
        let uploadSessionId="";
        $("#addToList").on("click",function(){
            var kw=$("#txtKeyWord").val();
            if(kw) {
                $("#keywordsUl").append('<li value="' + kw + '"><span class="word">' + kw + '</span><span class="close" onclick="window.deleteItem(this)">删除</span></li>');
                $("#txtKeyWord").val("");
            }
        });
        $("#btnExport,#btnExport1").on("click",function(){
            var data={};
            console.log("q_type:"+q_type);
            if(q_type=="basic") {
                var keywords = "";
                $("#keywordsUl > li > .word").each(function (index, ele) {
                    var itemText = $(ele).text();
                    keywords += "," + itemText;
                });
                if (!keywords) {
                    layer.msg("请先设置导出条件！", {icon: 2, time: 2000});
                }
                var data = {
                    q_type:q_type,
                    keywords: keywords,
                    condLogic: $("input[name='chkLogic']:checked").val(),
                    leftlike: $("#chkLeftLike").prop("checked"),
                    leftSpace:$("#chkLeftLike_space").prop("checked"),
                    rightlike: $("#chkRightLike").prop("checked"),
                    rightStrike:$("#chkRightLike_strike").prop("checked"),
                    rightChar:$("#chkRightLike_char").prop("checked"),
                    rightDot:$("#chkRightLike_dot").prop("checked"),
                    rightBias:$("#chkRightLike_bias").prop("checked"),
                    q_field:getQueryField(),
                    q_class:getClassCond()
                };
            }
            else if(q_type=="adv")
            {
                var fields=[];
                for(var i=0;i<conditionArr.length;i++)
                {
                    var obj={};
                    obj.logic="AND";
                    if(conditionArr[i]>1)
                    {
                        obj.logic= $("#conAndOr"+conditionArr[i]).val();
                    }
                    obj.field=$("#conField"+conditionArr[i]).val();
                    obj.val=$("#conVal"+conditionArr[i]).val();
                    obj.type=$("#conQueryType"+conditionArr[i]).val();
                    fields.push(obj);
                }
                var data = {
                    q_type:q_type,
                    keywords: JSON.stringify(fields),
                };
            }
            layer.msg('正在导出数据...',{
                icon:16,
                time: 6000000
            });
            $.ajax({
                url: '/inteligbusi/export',
                data: JSON.stringify(data),
                dataType: 'json',
                contentType: 'application/json',
                type: 'post',
                success: function (result) {
                    if (result.success) {
                        layer.closeAll();
                        layer.open({
                            type:1,
                            content:"<div style='padding: 15px 25px 30px 25px;line-height: 25px;font-size: 12px;background-color: #efefef;'>已导出，请在服务器的【"+result.data+"】目录下查看导出数据。</div>"
                        })
                    } else {
                        layer.closeAll();
                        layer.open({
                            type:1,
                            content:"<div style='padding: 15px 25px 30px 25px;line-height: 25px;font-size: 12px;background-color: #efefef;'>导出失败"+(result.msg?"，具体原因："+result.msg:"。")+"</div>",
                            area: "300px"
                        })
                    }
                }
            })
            return false;
        });
        $("#btnQuery,#btnQueryAdv").on("click",function(){
            console.log("q_type:"+q_type);
            if(q_type=="basic") {
                var keywords = "";
                $("#keywordsUl > li > .word").each(function (index, ele) {
                    var itemText = $(ele).text();
                    keywords += "," + itemText;
                });
                var data = {
                    q_type:q_type,
                    keywords: keywords,
                    condLogic: $("input[name='chkLogic']:checked").val(),
                    leftlike: $("#chkLeftLike").prop("checked"),
                    leftSpace:$("#chkLeftLike_space").prop("checked"),
                    rightlike: $("#chkRightLike").prop("checked"),
                    rightStrike:$("#chkRightLike_strike").prop("checked"),
                    rightChar:$("#chkRightLike_char").prop("checked"),
                    rightDot:$("#chkRightLike_dot").prop("checked"),
                    rightBias:$("#chkRightLike_bias").prop("checked"),
                    q_field:getQueryField()
                };
                table.reload('role-table', {where: {simpleSearchKey: JSON.stringify(data),queryIdenti:getClassCond()}, page: {curr: 1}});
               // table.reload('role-table', {post:{url:"/inteligbusi/data",data:{simpleSearchKey: JSON.stringify(data),queryIdenti:getClassCond()},page: {curr: 1}}});
            }
            else if(q_type=="adv")
            {
                var fields=[];
                for(var i=0;i<conditionArr.length;i++)
                {
                    var obj={};
                    obj.logic="AND";
                    if(conditionArr[i]>1)
                    {
                        obj.logic= $("#conAndOr"+conditionArr[i]).val();
                    }
                    obj.field=$("#conField"+conditionArr[i]).val();
                    obj.val=$("#conVal"+conditionArr[i]).val();
                    obj.type=$("#conQueryType"+conditionArr[i]).val();
                    fields.push(obj);
                }
                    var data = {
                        q_type:q_type,
                        keywords: JSON.stringify(fields),
                    };
                    table.reload('role-table', {where: {advSearchKey: JSON.stringify(data)}, page: {curr: 1}});
                }
            window.setUpload();
                // $.ajax({
                //     url: '/inteligbusi/query',
                //     data: JSON.stringify(data),
                //     dataType: 'json',
                //     contentType: 'application/json',
                //     type: 'post',
                //     success: function (result) {
                //         if (result.success) {
                //             layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                //
                //             });
                //         } else {
                //             layer.msg(result.msg, {icon: 2, time: 1000});
                //         }
                //     }
                // })
                // return false;

        });
        // layui.table 使用搜索关键字高亮显示指定列
        window.highlight=function(fieldName,rowData)
        {
            var result=rowData[fieldName].toString();
            var len=searchKeyMap.length;
            for(var i=0;i<len;i++)
            {
                if(fieldName.toLowerCase()==searchKeyMap[i].fieldname)
                {
                    if(result.indexOf(searchKeyMap[i].keyword)!=-1)
                    {
                        result=result.replaceAll(searchKeyMap[i].keyword,"<em>"+searchKeyMap[i].keyword+"</em>");
                    }
                }
            }
            return result;
        };
        let cols = [
            [
                {type: 'checkbox', width: '3%'},
                {field: 'number', title: '序号', align: 'center', type: 'numbers', width: '4%'},
                {title: '标准<br/>类别', field: 'stdClass', align: 'left', width: '8%'},
                {title: '标准号', align: 'left', width: '12%',templet:function(rowData){
                        return window.highlight("stdNo",rowData);
                    }},
                {title: '标准名称',  align: 'left', width: '20%',templet:function(rowData){
                        return window.highlight("stdOrgName",rowData);
                    }},
                {title: '实施日期', field: 'implementationDate', align: 'left', width: '8%'},
                {title: '主要内容', field: 'primaryCoverage', align: 'left', width: '21%'},
                {title: '标准<br/>状态', field: 'stdStatus', align: 'left', width: '5%'},
                {title: '代替标准号<br/>(老标准)', field: 'alternateStdNo', align: 'left', width: '9%'},
                {title: '被替代的标准号<br/>(新标准)', field: 'supersededStdNo', align: 'left', width: '10%'},
            ]
        ]
        table.render({
            elem: '#role-table',
            url: '/inteligbusi/data',
            method:'post',
            page: false,
            limit: pageSize,
            limits: [30, 50,100],
            where:{
                queryIdenti:"0"
            },
            cols: cols,
            skin: 'row,line',
            toolbar: '#role-toolbar',
            cellMinWidth: 'auto',
            defaultToolbar: [{
                title: '刷新',
                layEvent: 'refresh',
                icon: 'layui-icon-refresh',
            }, 'filter', 'print', 'exports'],
            done: function (res, curr, count) {
                $("#resultInfo").html('共检索到' + res.count + "条记录，" + res.extData + "条可导出");
                var $data = $('div[lay-id="role-table"]').find('.layui-table-body').find('tr').eq(0).find('td');
                var $head = $('div[lay-id="role-table"]').find('.layui-table-header').find('tr').eq(0).find('th');
                for (var i = 0; i < $data.length; i++) {
                    var l1 = $data.eq(i).find('div').width();
                    var l2 = $head.eq(i).find('div').width();
                    if (l1 > l2) {
                        $head.eq(i).find('div').width(l1);
                    } else if (l2 > l1) {
                        $data.eq(i).find('div').width(l2);
                    }
                }
                layer.closeAll();
                initPage(curr, res.count);
            }
        });
        window.doPage=function()
        {
            //简单搜索
            var keywords = "";
            $("#keywordsUl > li > .word").each(function (index, ele) {
                var itemText = $(ele).text();
                keywords += "," + itemText;
            });
            var data = {
                q_type:q_type,
                keywords: keywords,
                condLogic: $("input[name='chkLogic']:checked").val(),
                leftlike: $("#chkLeftLike").prop("checked"),
                leftSpace:$("#chkLeftLike_space").prop("checked"),
                rightlike: $("#chkRightLike").prop("checked"),
                rightStrike:$("#chkRightLike_strike").prop("checked"),
                rightChar:$("#chkRightLike_char").prop("checked"),
                rightDot:$("#chkRightLike_dot").prop("checked"),
                rightBias:$("#chkRightLike_bias").prop("checked"),
                q_field:getQueryField()
            };
            table.reload('role-table', {where: {simpleSearchKey: JSON.stringify(data),queryIdenti:getClassCond()}, page: { curr: currPage1,limit:pageSize }});
        }
        window.onPage1Change=function(page,isFirst) {
            currPage1 = page.curr;
            currPage2=currPage1;
            var p = page.curr;//改变当前页码
            var l = page.limit;
            pageSize=l;
            if (!isFirst) {
                doPage();
                laypage.render({
                    elem: 'pagination2',
                    count: totalRecord,
                    limit: l,
                    curr: currPage1,
                    limits:[30,50,100],
                    layout: ['prev', 'page', 'next', 'skip', 'count','limit'],
                    jump: onPage2Change
                });
            }
        }
        window.onPage2Change=function(page,isFirst) {
            currPage1 = page.curr;
            currPage2=currPage1;
            var p = page.curr;//改变当前页码
            var l = page.limit;
            pageSize=l;
            if (!isFirst) {
                doPage();
                laypage.render({
                    elem: 'pagination1',
                    count: totalRecord,
                    limit: l,
                    curr: currPage1,
                    limits:[30,50,100],
                    layout: ['prev', 'page', 'next', 'skip', 'count','limit'],
                    jump: onPage1Change
                });
            }
        }
        window.initPage=function(currPage,total) {
            totalRecord=total;
            currPage1=currPage;
            currPage2=currPage;
            console.log("init Page1");
            laypage.render({
                elem: 'pagination1',
                count: totalRecord,
                limit: pageSize,
                curr:currPage1,
                limits:[30,50,100],
                layout: ['prev', 'page', 'next', 'skip', 'count','limit'],
                jump: onPage1Change
            });
            console.log("init Page2");
            laypage.render({
                elem: 'pagination2',
                count: totalRecord,
                limit: pageSize,
                limits:[30,50,100],
                curr:currPage1,
                layout: ['prev', 'page', 'next', 'skip', 'count','limit'],
                jump:onPage1Change
            });
            console.log("init Page3");
        }
        window.setUpload=function(){
            $('#uploadElem').html(`<div id="uploadBox"><button type="button" class="pear-btn pear-btn-primary pear-btn-md" style="background-color:#888 !important;" id="uploadBtn"><i class="layui-icon">&#xe67c;</i>上传</button><span id="selected"></span></div>`);
            uploadSessionId=Date.parse(new Date());
            upload.render({
                elem: '#uploadBtn'     // 选择文件按钮
                ,elemList: $('#demoList') //列表元素对象
                ,data:{customSessionId:uploadSessionId}
                ,url:  '/inteligbusi/uploadcondition' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
                ,accept: 'file'  //指定允许上传时校验的文件类型，可选值有：images（图片）、file（所有文件）、video（视频）、audio（音频）
                ,multiple: false  //是否允许多文件上传。设置 true即可开启。不支持ie8/9
                ,number: 1   //设置同时可上传的文件数量，一般配合 multiple 参数出现; 0 不限制
                ,auto: true  //是否选完文件后自动上传。如果设定 false，那么需要设置 bindAction 参数来指向一个其它按钮提交上传
                ,bindAction: '#testListAction'  //指向一个按钮触发上传，一般配合 auto: false 来使用
                ,choose: function(obj){   //选择文件后的回调函数。返回一个object参数
                },
                before: function(obj){ //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
                    layer.open({
                        type: 1,
                        title: '上传中...',
                        shade: 0.1,
                        area: ['500px', '100px'],
                        content: "正在上传过滤条件...",
                        end : function() {
                        }
                    });
                }
                ,done: function(res, index, upload){ //成功的回调
                    var that = this;
                    if(res.success){ //上传成功
                        layer.closeAll();
                        if(res.result)
                        {
                            var data=res.result.toString().split(",");
                            for(var i=0;i<data.length;i++) {
                                if (data[i]) {
                                    $("#keywordsUl").append('<li value="' + data[i] + '"><span class="word">' + data[i] + '</span><span class="close" onclick="window.deleteItem(this)">删除</span></li>');
                                }
                            }
                        }
                        layer.msg("导入完成！", {icon: 1, time: 500}, function () {
                            window.setUpload();
                        });
                        return;
                    }else {
                        layer.msg("导入失败！"+res.message, {icon: 2, time: 10000}, function () {
                            window.setUpload();
                        });
                    }
                }
                ,allDone: function(obj){ //多文件上传完毕后的状态回调
                    console.log(obj)
                    window.setUpload();
                }
                ,error: function(index, upload){ //错误回调
                    popup.failure("导入失败！");
                    window.setUpload();
                }
                ,progress: function(n, elem, e, index){ //注意：index 参数为 layui 2.6.6 新增
                    /*var percent = n + '%' //获取进度百分比
                    element.progress('progressBar', percent); //可配合 layui 进度条元素使用
                    console.log(elem); //得到当前触发的元素 DOM 对象。可通过该元素定义的属性值匹配到对应的进度条。
                    console.log(res); //得到 progress 响应信息
                    console.log(index); //得到当前上传文件的索引，多文件上传时的进度条控制，如：
                    element.progress('progressBar-'+ index, n + '%'); //进度条*/
                }
            });
        }
        window.setUpload();

        window.deleteItem=function(obj)
        {
            $(obj).parent().remove();
        }

        form.on('submit(role-query)', function (data) {
            console.log(data);
            var fields=[];
            for(var i=0;i<conditionArr.length;i++)
            {
                var obj={};
                obj.logic="AND";
                if(conditionArr[i]>1)
                {
                    obj.logic= $("#conAndOr"+conditionArr[i]).val();
                }
                obj.field=$("#conField"+conditionArr[i]).val();
                obj.val=$("#conVal"+conditionArr[i]).val();
                obj.type=$("#conQueryType"+conditionArr[i]).val();
                fields.push(obj);
            }
            table.reload('role-table', {where: {advSearchKey:JSON.stringify(fields),queryIdenti:getClassCond()},page: { curr: 1 }});
            window.createKeywords();
            //window.writelog('Standard_Query','','','','',JSON.stringify(fields));
            return false;
        });

        window.addCondition=function(){
            var id=0;
            for(var i=0;i<conditionArr.length;i++)
            {
                if(conditionArr[i]>id)
                {
                    id=conditionArr[i];
                }
            }
            if(id>0)
            {
                id+=1;
                conditionArr[conditionArr.length]=id;
            }
            var html=`<div class="advance-item" id="advanceItem${id}" style="width:100%;">
                    <div class="ph-div">
                        <select id="conAndOr${id}" class="conAndOr">
                            <option value="AND">AND</option>
                            <option value="OR">OR</option>
                            <option value="NOT">NOT</option>
                        </select>
                    </div>
                    <div class="search-cmp">
                        <div class="con-field-container">
                            <select id="conField${id}">
                                <option value="std_no">标准号</option>
                                 <option value="std_org_name">标准名称（原文）</option>
                                <option value="std_chinese_name">标准名称（中文）</option>
                                <option value="std_english_name">标准名称（英文）</option>
                                <option value="std_class">标准类别</option>
                                <option value="catetory_no">分类号</option>
                                <option value="std_ics">ICS号</option>
                                <option value="pub_date">发布日期</option>
                                <option value="implementation_date">实施日期</option>
                                <option value="advance_dept">提出单位</option>
                                <option value="drafting_unit">起草单位</option>
                                <option value="drafter">起草人</option>
                                <option value="std_status">标准状态</option>
                            </select>
                        </div>
                        <input class="layui-input sub-item" id="conVal${id}" placeholder="" type="text">
                        <div class="con-type-container">
                            <select id="conQueryType${id}">
                                <option value="fuzzy">模糊</option>
                                <option value="exact">精确</option>
                            </select>
                        </div>
                    </div>
                    <div class="ph-end-div">
                        <a href="javascript:void(0);" class="conDel" onclick="delCondition(${id})" id="conDel${id}">-</a>
                        <a href="javascript:void(0);" onclick="addCondition()" class="conAdd" id="conAdd${id}">+</a>
                    </div>
                </div>`;
            $(".advance-container").append(html);
            form.render("select");
            $("#conAdd"+(id-1)).toggle();
            for(var i=1;i<conditionArr.length-1;i++)
            {
                $("#conDel"+conditionArr[i]).show();
            }
        };

        window.delCondition=function(id){
            $("#advanceItem"+id).remove();
            for(var i=0;i<conditionArr.length;i++)
            {
                if(conditionArr[i]==id)
                {
                    if(i==conditionArr.length-1)
                    {
                        $("#conAdd"+conditionArr[i-1]).toggle();
                    }
                    conditionArr.splice(i, 1);
                    break;
                }
            }
            console.log('conditionArr.length:'+conditionArr.length);
            //需要保留至少两行
            if(conditionArr.length==2)
            {
                $("#conDel"+conditionArr[1]).hide();
            }
        };
        window.getClassCond=function(){
            var chkList=[];
            $('input[type=checkbox][name=chkClass]:checked').each(function(){
                chkList.push($(this).val());
            });
            return chkList.join(",");
        }

        window.getQueryField=function(){
            var chkList=[];
            $('input[type=checkbox][name=chkQueryField]:checked').each(function(){
                chkList.push($(this).val());
            });
            return chkList.join(",");
        }

        form.on('checkbox(i-all)',function(data){
            //console.log('------------:'+data.elem.checked);
            if(data.elem.checked==true){
                $("input[id=chkGJB]").prop("checked", true);
                $("input[id=chkGJB]").prop("checked", true);
                $("input[id=chkWJ]").prop("checked", true);
                $("input[id=chkQCNG]").prop("checked", true);
                $("input[id=chkGB]").prop("checked", true);
                $("input[id=chkOther]").prop("checked", true);
            }
            else
            {
                $("input[id=chkGJB]").prop("checked", false);
                $("input[id=chkGJB]").prop("checked", false);
                $("input[id=chkWJ]").prop("checked", false);
                $("input[id=chkQCNG]").prop("checked", false);
                $("input[id=chkGB]").prop("checked", false);
                $("input[id=chkOther]").prop("checked", false);
            }
            form.render();
        });

        form.on('checkbox(i-gjb)',function(data){
            if(data.elem.checked!=true)
            {
                $("input[id=chkAll]").prop("checked", false);
            }
            else
            {
                if(checkIdentiStatus())
                {
                    $("input[id=chkAll]").prop("checked", true);
                }
            }
            form.render();
        });
        form.on('checkbox(i-wj)',function(data){
            if(data.elem.checked!=true)
            {
                $("input[id=chkAll]").prop("checked", false);
            }
            else
            {
                if(checkIdentiStatus())
                {
                    $("input[id=chkAll]").prop("checked", true);
                }
            }
            form.render();
        });
        form.on('checkbox(i-qcng)',function(data){
            if(data.elem.checked!=true)
            {
                $("input[id=chkAll]").prop("checked", false);
            }
            else
            {
                if(checkIdentiStatus())
                {
                    $("input[id=chkAll]").prop("checked", true);
                }
            }
            form.render();
        });
        form.on('checkbox(i-gb)',function(data){
            if(data.elem.checked!=true)
            {
                $("input[id=chkAll]").prop("checked", false);
            }
            else
            {
                if(checkIdentiStatus())
                {
                    $("input[id=chkAll]").prop("checked", true);
                }
            }
            form.render();
        });
        form.on('checkbox(i-other)',function(data){
            if(data.elem.checked!=true)
            {
                $("input[id=chkAll]").prop("checked", false);
            }
            else
            {
                if(checkIdentiStatus())
                {
                    $("input[id=chkAll]").prop("checked", true);
                }
            }
            form.render();
        });

        window.checkIdentiStatus=function()
        {
            console.log('GJB:'+$("input[id=chkGJB]").prop("checked"));
            if($("input[id=chkGJB]").prop("checked") && $("input[id=chkWJ]").prop("checked") &&
                $("input[id=chkQCNG]").prop("checked") &&
                $("input[id=chkGB]").prop("checked") &&
                $("input[id=chkOther]").prop("checked"))
            {
                return true;
            }
            return false;
        }
        form.render();
    });
</script>
<style>
    .big-container
    {
        width:1000px;
        margin:0 0 0 200px;
        background-color:#fff;
        min-height: 200px;
    }
    .left-container
    {
        width:15%;
        float:left;
    }
    .middle-container
    {
        width:30%;
        float:left;
    }
    .right-container
    {
        width:54%;
        float:left;
    }
    #btnClearList,#addToList,#uploadBtn
    {
        padding: 0 8px !important;
    }
    .right-container:after
    {
        content:'';
        display: block;
        height:0;
        visibility:hidden;
        clear:both;
    }
    #keywordsUl{
        min-height: 150px;
        max-height: 150px;
        overflow: scroll;
        background-color: #efefef;
        border:solid 1px #ccc;
        display: block;
        margin-bottom:2px;
    }
    #keywordsUl li {
        padding:3px 5px;
    }
    #keywordsUl li:hover{
        background: #fefefe;
    }
    #keywordsUl li .close{
        display: none;
    }
    #keywordsUl li:hover .close{
        display: block;
    }
    #addToList {

    }
    #txtKeyWord{
        width:34%;
        padding:6px;
    }
    .layui-form-checkbox
    {
        margin-right: 1px !important;
    }
    #conditionUl {
        margin-left:40px;
    }
    #conditionUl li {
        line-height: 40px;
        height:47px;
    }
    .action-btn {
        width:100px;
    }
    .chk-label
    {
        margin-left:2px;
        margin-right: 15px;
    }
    .close {
        display: block;
        float:right;
        float: right;
        cursor: pointer;
    }
    .close:hover{
        color:#ff0000;
    }
    /*--检索--*/
    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }

    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
    }

    .simple-container .item .sub-item button {
        margin-top: 10px;
    }

    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }

    .advance-container {
        width: 70%;
        margin: 0px auto;
    }

    .advance-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-bottom: 15px;
    }
    .search-label {
        flex-grow: 2;
    }
    .advance-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #efefef;
        border-radius: 5px;
    }
    .advance-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .advance-container .search-cmp button:hover {
        cursor: pointer;
    }
    .advance-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .advance-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 240px;
    }
    .con-type-container {
        width: 110px;
    }
    .search-anchor-1
    {
        flex-grow:5;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }
    em{
        color:red;
        font-style: normal;
    }
    #conditionUl1
    {
        padding-top:5px;
    }
    #conditionUl1 li {
        line-height: 40px;
        height: 47px;
    }
    #btnExport,#btnExportCurr{
        background-color:#2D8CF0 !important;
    }
    .layui-table-page
    {
        text-align: center;
    }
    .star
    {
        display: inline-block;
        height: 10px;
        vertical-align: middle;
        line-height: 15px;
        font-size:16px;
    }







    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        flex-direction: column;
    }

    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
        margin-bottom: 0.625rem;
    }

    .simple-container .item .sub-item button {
        margin-top: 10px;
    }

    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }

    .advance-container {
        width: 70%;
        margin: 0px auto;
    }

    .advance-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-bottom: 15px;
    }
    .layui-table-tool {
        height: 50px !important;
        line-height: 1px !important;
        padding: 0px !important;
        min-height: 0px !important;
    }
    .layui-form-item .layui-form-label, .layui-form-item .layui-input-inline {
        margin-bottom: 7px;
    }
    .layui-form-item {
        padding-bottom: 0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    #resultInfo {
        text-align: left;
        margin-top: 10px;
        margin-left:370px;
    }
    .layui-table thead th {
        font-weight: bold;
    }
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    .search-label {
        flex-grow: 2;
    }
    .simple-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #ddd;
        border-radius: 5px;
    }
    .simple-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .simple-container .search-cmp button:hover {
        cursor: pointer;
    }
    .simple-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .simple-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .advance-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #efefef;
        border-radius: 5px;
    }
    .advance-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .advance-container .search-cmp button:hover {
        cursor: pointer;
    }
    .advance-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .advance-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 240px;
    }
    .con-type-container {
        width: 110px;
    }
    .search-anchor-1
    {
        flex-grow:5;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }
    em{
        color:red;
        font-style: normal;
    }
    .layui-table-page {
        text-align: center;
    }
    #pagination1,#pagination2{
        text-align: center;

    }
    .layui-laypage {
        margin-bottom: 0px !important;
    }
    .layui-table-view {
        margin-top:0px !important;
    }
    .char_up
    {
        display: inline-block;
        margin-top:-6px;
    }
    #pagination1,#pagination2{
        text-align: center;
    }
    .layui-table-column{
        display: none;
    }
</style>
</html>
