<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增标准意见')"/>
</head>
<body>
<form action="" class="layui-form">
    <div class="mainBox">
        <div class="main-container">
            <div class="main-container">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;">*</span>标准号</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" readonly class="layui-input"  name="stdNo" placeholder="请输入标准号"
                               type="text">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;">*</span>标准名称</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" readonly class="layui-input"  name="stdOrgName" placeholder="请输入标准名称"
                               type="text">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;">*</span>章节号</label>
                    <div class="layui-input-block">
                        <input autocomplete="off" class="layui-input"  lay-verify="required"  name="chapterNo" placeholder="请输入章节号"
                               type="text">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span style="color: red;">*</span>修改意见或建议</label>
                    <div class="layui-input-block">
                        <textarea rows="5" class="layui-textarea" lay-verify="required" name="suggestion" ></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="bottom">
        <div class="button-container">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-filter="dict-type-save" lay-submit=""
                    type="submit">
                <i class="layui-icon layui-icon-ok"></i>
                提交
            </button>
            <button class="pear-btn pear-btn-sm" type="reset">
                <i class="layui-icon layui-icon-refresh"></i>
                重置
            </button>
        </div>
    </div>
    <input type="hidden" id="stdId" name="stdId" th:value="${stdInfo.id}" />
</form>
<input type="hidden" id="hdStdOrgName" name="hdStdOrgName" th:value="${stdInfo.stdOrgName}" />
<input type="hidden" id="hdStdNo" name="hdStdNo"  th:value="${stdInfo.stdNo}" />
<th:block th:include="include :: footer"/>
<script>
    layui.use(['form', 'jquery'], function () {
        let form = layui.form;
        let $ = layui.jquery;
        $("input[name='stdNo']").val($("#hdStdNo").val());
        $("input[name='stdOrgName']").val($("#hdStdOrgName").val());
        form.on('submit(dict-type-save)', function (data) {
            console.log(data.field);
            $.ajax({
                url: '/stdopinion/save',
                data: JSON.stringify(data.field),
                dataType: 'json',
                contentType: 'application/json',
                type: 'post',
                success: function (result) {
                    if (result.success) {
                        layer.msg(result.msg, {icon: 1, time: 1000}, function () {
                            parent.layer.close(parent.layer.getFrameIndex(window.name));//关闭当前页
                            //parent.layui.table.reload("role-table");
                            //var thisFrame = parent.window.document.getElementById("LAY_layuiStampDuty1").getElementsByTagName("iframe")[0].id;//获取父级弹框id值
                            //var dcmt = parent.$('#' + thisFrame)[0].contentWindow.reload();//直接调用方法
                            //parent.reload();
                        });
                    } else {
                        layer.msg(result.msg, {icon: 2, time: 1000});
                    }
                }
            })
            return false;
        });
    })
</script>
</body>
<style>
    .simple-container {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
    }

    .simple-container .item {
        display: flex;
        flex-direction: row;
        margin-top: 0.625rem;
    }

    .simple-container .item .sub-item button {
        margin-top: 10px;
    }

    .simple-container .item label {
        min-width: 80px;
        padding: 8px 0px 8px 10px;
        text-align: right;
    }

    .advance-container {
        width: 70%;
        margin: 0px auto;
    }

    .advance-item {
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        margin-bottom: 15px;
    }
    .layui-table-tool
    {
        height:70px !important;
        line-height:30px !important;
        padding:10px 0 0 10px !important;
        min-height:0px !important;
    }
    .layui-form-item .layui-form-label, .layui-form-item .layui-input-inline {
        margin-bottom: 7px;
    }
    .layui-form-item {
        padding-bottom: 0px !important;
    }
    .layui-table-cell {
        height: auto;
        line-height: 28px;
        overflow: auto;
        white-space: normal;
    }
    #resultInfo {
        width: 100%;
        text-align: center;
        color: #999;
        margin-top: 10px;
    }
    .layui-table thead th {
        font-weight: bold;
    }
    .layui-table thead th .layui-table-cell {
        text-align: center !important;
    }
    .layui-table-page {
        text-align: center !important;
    }
    .is-show-0 {
        display: none;
    }
    .search-label {
        flex-grow: 2;
    }
    .simple-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #ddd;
        border-radius: 5px;
    }
    .simple-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .simple-container .search-cmp button:hover {
        cursor: pointer;
    }
    .simple-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .simple-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .advance-container .search-cmp {
        flex-grow: 6;
        display: flex;
        border: solid 1px #efefef;
        border-radius: 5px;
    }
    .advance-container .search-cmp button {
        background: none;
        border: none;
        flex-grow: 2;
        width: 40px;
    }
    .advance-container .search-cmp button:hover {
        cursor: pointer;
    }
    .advance-container .search-cmp input {
        background: none;
        border: none;
        flex-grow: 10;
    }
    .advance-container .search-cmp input:focus {
        border: none !important;
        box-shadow: none !important;
    }
    .search-advance {
        line-height: 38px;
        height: 38px;
        flex-grow: 6;
        padding-left: 10px
    }
    .conAndOr {
        width: 50px;
    }
    .conAdd, .conDel {
        display: inline-block;
        padding: 5px 10px;
        font-size: 18px;
        font-weight: bold;
        font-family: 宋体;
        color: #888;
    }
    .con-field {
        width: 100px;
    }
    .con-query-type {
        width: 70px;
    }
    .ph-div {
        width: 80px;
        padding-right: 15px;
    }
    .ph-end-div {
        width: 100px;
    }
    .con-field-container {
        width: 240px;
    }
    .con-type-container {
        width: 110px;
    }
    .search-anchor-1
    {
        flex-grow:5;
    }
    #clearSimpleCon {
        width:44px;
    }
    #advanceAnchor {
        width:88px;
    }
    #simpleAnchor:link,#simpleAnchor:visited,#advanceAnchor:link,#advanceAnchor:visited{
        font-size:13px;
        color:#36b368 !important;
    }
    #simpleAnchor:hover,#advanceAnchor:hover{
        font-size:13px;
        color: #3abb6e !important;
    }
</style>
</html>
