# Tomcat 配置
server:
  # 端 口
  port: 8089
  # Servlet
  servlet:
    # Session
    session:
      # 超 时 时 间
      timeout: 120m
# Spring 配置
spring:
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  # Session
  session:
    # 超 时 时 间
    timeout: 120m
  # 路 径 匹 配 机 制
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  # 运 行 环 境
  profiles:
    # 运 行 环 境
    active: dev
  # 工 作 流 程
  activiti:
    # 是 否 检 查 流 程 定 义 文 件
    check-process-definitions: false

# 内 置 组 件
pear:
  # Document 配 置 信 息
  swagger:
    # 文 档 标 题
    title: 集团公司标准资源服务平台
    # 是 否 开 启
    enable: true
    # 描 述 信 息
    describe: 对军事化标准进行管理及检索的工具
    # 扫 包 路 径
    scan-package: com.pearadmin
    # 协 议
    licence: MIT
    # 协 议 地 址
    licence-url: https://gitee.com/pear-admin/Pear-Admin-Boot/blob/master/LICENSE
    # 组 织 信 息
    terms-of-service-url: https://www.yinghes.com
    # 版 本 信 息
    version: Release 2.0.0
    # 扩 展 信 息
    contact:
      # 作 者
      name: chongyibo
      # 连 接
      url: www.yinghes.com
      # 邮 箱
      email: <EMAIL>
  # 权 限 配 置
  security:
    # 开 启 超 级 管 理 员 账 号 -- 不需要验证权限
    super-auth-open: true
    # 超 级 管 理 员 账 号 -- 不需要验证权限
    super-admin: admin
    # 允 许 同 一 账 号 多 端 登 录 个 数
    maximum: 1
    # 记 住 密 码
    remember-key: PEAR_REMEMBER
    # 不 需 要 权 限 拦 截 的 接 口 配 置
    open-api:
      - /login/**  # 开放登录接口
      - /system/captcha/** # 开放验证码接口
      - /assets/** # 开放静态资源
      - /admin/** # 开放静态资源
      - /pdf/** # 开放静态资源
      - /component/** # 开放静态资源
      - /favicon.ico # 开放FAVICON
  # 上 传 配 置
  upload:
    # windows 上传路径
    windows-path: D:\home\uploads\
    # linux 上传路径
    linux-path: /home/<USER>/
    # 是否使用ftp服务器
    ftp-use: false
    #  ftp服务器地址
    hostname:
    # ftp服务器端口
    port: 0000
    # ftp服务器用户名
    username:
    # ftp服务器密码
    password:

# 分 页 配 置
pagehelper:
  # 方 言
  helper-dialect: mysql
  params: count=countSql
  reasonable: true
  support-methods-arguments: true

# 日志配置
logging:
  config: classpath:logback-spring.xml
jasypt:
  encryptor:
    password: tps$zwNlcbYx
stdsys:
  limithours: 2160
  check-security: 0
  # 预览记录保留天数
  preview-retention-days: 7

# Mybatis 配 置
mybatis-plus:
  # 配 置 扫 描
  mapper-locations: classpath*:com/pearadmin/modules/**/xml/**.xml
  # 别 名 扫 描
  #type-aliases-package: com.pearadmin
  ## 日志打印
  configuration:
    ## 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
upload:
  total: 0
  dealed: 0
  err:
std:
  encryptexe: D:\\work\\Projects\\FileAes\\FileEncrypt\\bin\\Release\\FileEncrypt.exe

