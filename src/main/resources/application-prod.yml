spring:
  redis:
    # 主 机 地 址
    host: 127.0.0.1
    # 端 口
    port: 6379
    # 认 证
    password:
    # 选 择 数 据 库
    database: 0
    # 连 接 池 配 置
    lettuce:
      pool:
        # 连接池中的最大空闲连接
        max-active: 20
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 4
        # 连接池中的最小空闲连接
        min-idle: 0
    # 超时时间
    timeout: 1000
  # 数据源配置
  datasource:
    # 连接池配置
    druid:
      filters: stat,wall
      maxActive: 20
      initialSize: 1
      maxWait: 60000
      minIdle: 10
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      maxOpenPreparedStatements: 20
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: /druid/*,*js,*gif,*jpg,*bmp,*png,*css,*ico
        session-stat-enable: true
        session-stat-max-count: 10
        principal-session-name: session_name
        principal-cookie-name: cookie_name
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        aop-patterns: com.pearadmin.*
    # 数据源
    dynamic:
      primary: master
      strict: false
      p6spy: false
      datasource:
        # 主库
        master:
          url: *******************************************************************************************************************************************************************************
          username: root
          password: sa123456
        # 从库
        second:
          url: *******************************************************************************************************************************************************************************
          username: root
          password: sa123456
  # Quartz 配置
  quartz:
    properties:
      tablePrefix: schedule_
pdf:
  source:
    path: D:\indexPdfs
    #path: D:\work\兵器标准索引\testPDF\Sources
  index:
    path: D:\luceneIndex
    #path: D:\work\兵器标准索引\testPDF\Index1

# Mybatis 配 置
mybatis-plus:
  # 配 置 扫 描
  mapper-locations: classpath*:com/pearadmin/modules/**/xml/**.xml
  # 别 名 扫 描
  #type-aliases-package: com.pearadmin
  ## 日志打印
  configuration:
    ## 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
