spring:
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
  redis:
    # 主 机 地 址
    host: 127.0.0.1
    # 端 口
    port: 6379
    # 认 证
    password:
    # 选 择 数 据 库
    database: 0
    # 连 接 池 配 置
    lettuce:
      pool:
        # 连接池中的最大空闲连接
        max-active: 20
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1
        # 连接池中的最大空闲连接
        max-idle: 4
        # 连接池中的最小空闲连接
        min-idle: 0
    # 超时时间
    timeout: 1000
  # 数据源配置
  datasource:
    hikari:
      data-source-properties:
        maxAllowedPacket: 20000000000
    # 连接池配置
    druid:
      filters: stat,wall
      maxActive: 20
      initialSize: 1
      maxWait: 60000
      minIdle: 10
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 'x'
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      maxOpenPreparedStatements: 20
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: /druid/*,*js,*gif,*jpg,*bmp,*png,*css,*ico
        session-stat-enable: true
        session-stat-max-count: 10
        principal-session-name: session_name
        principal-cookie-name: cookie_name
        profile-enable: true
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        aop-patterns: com.pearadmin.*
    # 数据源
    dynamic:
      primary: master
      strict: false
      p6spy: false
      datasource:
        #主库
        master:
          url: *****************************************************************************************************************************************************************************
          username: root
          password: sa123456
        # 从库
        second:
          url: *****************************************************************************************************************************************************************************
          username: root
          password: sa123456
  # Quartz 配置
  quartz:
    properties:
      tablePrefix: schedule_cx
  jpa:
    properties:
      hibernate:
        jdbc:
          batch_versioned_data: true
        id:
          new_generator_mappings: true
        format_sql: true
pdf:
  source:
    #china-path: D:\allPDF\chinaPdfs
    china-path: D:\allPDF\chinaPdfs
    usa-path:  E:\allPDF\usaPdfs
    foreign-path:  D:\allPDF\foreignPdfs
    #path: D:\work\兵器标准索引\testPDF\Sources
  encryptSource:
    #path: C:\Program Files (x86)\StdSys\StdInstall_2\EncryptSources
    path: E:\EncryptSources
std:
  #encryptexe: D:\\work\\Projects\\FileAes\\FileEncrypt\\bin\\Release\\FileEncrypt.exe
  encryptexe: D:\\ProgramFiles\\StdSys\\FileEncrypt.exe
