var UDPDF_VERSION = "2.4.2"; //当前最新的控件版本号
var licenseStr = "C57BBCA06805627D8E22C1B3E719D238B3E0115F89BAB9FD69159888344C18EA7C00FA2777080C8403545309C388D0AE962E830A83D16B95A613AB4666AF5B0E08362CA80ED64C603DA71E19F89B18F742C54F9AC9551AE084319A83D4AD5A304F3318494A52A1B2DBEDDD40874F01624F3318494A52A1B2DBEDDD40874F0162F58489DA69DF5D4224786E49DCD7F991179DF0FE031A8AC1FA1409547647DEE64F3318494A52A1B2DBEDDD40874F01624F3318494A52A1B2DBEDDD40874F01624F3318494A52A1B2DBEDDD40874F0162"; //注册码
licenseStr="";
var ctlStr = "";
ctlStr += "<object id=\"udPdfCtl\" width=\"100%\" height=\"100%\" classid=\"CLSID:8C3D3705-1100-4D52-9D04-7E9A435CE220\">";
ctlStr += "<param name=\"License\" value=\"" + licenseStr + "\" />";
ctlStr += "<embed id=\"udPdfPlugin\" width=\"100%\" height=\"100%\" type=\"application/udpdf-plugin\"";
ctlStr += "License=\"" + licenseStr + "\"";
ctlStr += "onReady=\"f_onReady\" onDownload=\"f_onDownload\" onPageChanged=\"f_onPageChanged\" onZoom=\"f_onZoom\" onError=\"f_onError\">";
ctlStr += "</embed></object>";

//初始化并获取PDF控件对象
function f_initPdf() {
    var udPdfCtl = null;
    var supportActiveX = (window.ActiveXObject !== undefined);
    if (supportActiveX) {//if IE
        var axobj = null;
        var setupFile = "static/UdPdf/axUdPdf32.msi"; //PDF控件安装文件
        if (window.navigator.platform == "Win64"
		|| window.navigator.cpuClass == "x64")//64位浏览器安装文件
            setupFile = "static/UdPdf/axUdPdf64.msi";

        try {
            axobj = new ActiveXObject('UdPdf.Viewer');
        } catch (e) { }
        if (axobj) {
            if (f_checkNewVersion(axobj.GetVersion())) {
                document.getElementById("udPdfDiv").innerHTML = "<a href='" +"/source/getPdfPlugIn?fileName="+ setupFile
		         + "' class='inst' target='_blank'>PDF控件有新版本v"
		         + UDPDF_VERSION + "，点这里升级安装...</a>";
            }
            else {
                udPdfCtl = document.getElementById("udPdfCtl");
                return udPdfCtl;
            }
        }
        else {
            document.getElementById("udPdfDiv").innerHTML = "<a href='" +"/source/getPdfPlugIn?fileName="+ setupFile
		     + "' class='inst' target='_blank'>您尚未安装PDF控件，点这里进行安装...</a>";
        }
    }
    else {
		//NOT IE
        var setupFile = "static/UdPdf/npUdPdf.msi";
        if (navigator.plugins && navigator.plugins.length > 0) {
            console.log('navigator.plugins:');
            console.log(navigator.plugins);
            for(var key in navigator.plugins)
            {
                console.log("key:"+key);
                console.log(navigator.plugins[key]);
            }
            var plugin = navigator.plugins["UdPdf Plugin"];
            if (plugin) {
                udPdfCtl = document.getElementById("udPdfPlugin");
                if (f_checkNewVersion(udPdfCtl.GetVersion())) {
                    document.getElementById("udPdfDiv").innerHTML = "<a href='" +"/source/getPdfPlugIn?fileName="+ setupFile
		        	 + "' target='_blank' class='inst'>标准全文浏览器有新版本v"
		        	 + UDPDF_VERSION + "，点这里升级安装...</a>";
                }
                else {
                    return udPdfCtl;
                }
            }
            else {
                document.getElementById("udPdfDiv").innerHTML = "<a href='" +"/source/getPdfPlugIn?fileName="+ setupFile
		          + "' target='_blank' class='inst'>您尚未安装PDF插件，点这里进行安装...</a>";
            }
        }
        else {
            document.getElementById("udPdfDiv").innerHTML = "<span class='inst'>当前浏览器不支持插件<span>";
        }
    }
    return null;
}

//版本比较，检查是否安装了新版本
function f_checkNewVersion(instVer) {
    var newVer = UDPDF_VERSION.split(".");
    var curVer = instVer.split(".");
    if (parseInt(newVer[0]) > parseInt(curVer[0]))
        return true;
    if (parseInt(newVer[0]) == parseInt(curVer[0]) && parseInt(newVer[1]) > parseInt(curVer[1]))
        return true;
    if (parseInt(newVer[0]) == parseInt(curVer[0]) && parseInt(newVer[1]) == parseInt(curVer[1])
  	&& parseInt(newVer[2]) > parseInt(curVer[2]))
        return true;
    return false;
}

//记录事件日志
function f_log(str) {
    document.getElementById("info").innerHTML = str;
}

//控件事件处理
function f_onReady() {
    if (ctl.HasBookmark) {//如果有书签目录
        document.getElementById("chkBookmark").checked = true;
        document.getElementById("chkBookmark").disabled = false;
    }
    else {
        document.getElementById("chkBookmark").disabled = true;
    }
    document.getElementById("pageInfo").value = ctl.CurrentPage + "/" + ctl.TotalPage

    f_log("OnReady事件，文档加载完成")
}
function f_onDownload(totalSize, loadingSize) {
    f_log("OnDownload事件，文件大小：" + totalSize + ",已加载大小:" + loadingSize)
}
function f_onPageChanged(currentPage, totalPage) {
    document.getElementById("pageInfo").value = currentPage + "/" + totalPage;
}
function f_onZoom(zoom) {
    f_log("OnZoom事件，缩放比例：" + zoom + "%");
}
function f_onError(errorCode) {
    f_log("OnError事件，异常代号：" + errorCode)
}

//输出控件内容
document.writeln("<div id=\"udPdfDiv\" style=\"height:100%\">" + ctlStr + "</div>");
document.writeln("<script type=\"text/javascript\" event=\"OnDownload(totalSize,loadingSize)\" for=\"udPdfCtl\">");
document.writeln("f_onDownload(totalSize,loadingSize)");
document.writeln("</script>");
document.writeln("<script type=\"text/javascript\" event=\"OnReady()\" for=\"udPdfCtl\">");
document.writeln("f_onReady()");
document.writeln("</script>");
document.writeln("<script type=\"text/javascript\" event=\"OnPageChanged(currentPage,totalPage)\" for=\"udPdfCtl\">");
document.writeln("f_onPageChanged(currentPage,totalPage)");
document.writeln("</script>");
document.writeln("<script type=\"text/javascript\" event=\"OnZoom(zoom)\" for=\"udPdfCtl\">");
document.writeln("f_onZoom(zoom)");
document.writeln("</script>");
document.writeln("<script type=\"text/javascript\" event=\"OnError(errorCode)\" for=\"udPdfCtl\">");
document.writeln("f_onError(errorCode)");
document.writeln("</script>");
