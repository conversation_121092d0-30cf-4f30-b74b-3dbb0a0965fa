var UDPDF_VERSION = "2.7.4"; //当前最新的控件版本号
var UDPDF_LICENSE = ""; //注册码

var UDPDF_SETUP = "UdPdf/axUdPdf32.msi"; //安装文件
var ctlStr = "";//控件对象字符
var supportActiveX = (window.ActiveXObject !== undefined); //是否支持ActiveX,IE
if (supportActiveX) {
    ctlStr += "<object id=\"udPdfCtl\" width=\"100%\" height=\"600\" classid=\"CLSID:8C3D3705-1100-4D52-9D04-7E9A435CE220\">";
    ctlStr += "<param name=\"License\" value=\"" + UDPDF_LICENSE + "\" />";
    ctlStr += "</object>";

    if (window.navigator.platform == "Win64" || window.navigator.cpuClass == "x64")//64位浏览器安装文件
        UDPDF_SETUP = "UdPdf/axUdPdf64.msi";
} else {
    ctlStr += "<embed id=\"udPdfPlugin\" width=\"100%\" height=\"600\" type=\"application/udpdf-plugin\" ";
    ctlStr += "License=\"" + UDPDF_LICENSE + "\">";
    ctlStr += "</embed>";

}

//初始化并获取PDF控件对象
function f_initPdf() {
    var udPdfCtl = null;
    if (supportActiveX) {//if IE
        var axobj = document.getElementById("udPdfCtl");
        if (axobj.IsReady != undefined) {
            if (f_checkNewVersion(axobj.GetVersion())) {
                document.getElementById("udPdfDiv").innerHTML = "<a href='" + UDPDF_SETUP
		         + "' class='inst' target='_blank'>PDF控件有新版本v"
		         + UDPDF_VERSION + "，点这里升级安装。安装时需关闭浏览器窗口。</a>";
            }
            else {
                udPdfCtl = document.getElementById("udPdfCtl");
                //以下IE事件注册
                f_addEvent(udPdfCtl, "OnReady", f_onReady);
                f_addEvent(udPdfCtl, "OnDownload", f_onDownload);
                f_addEvent(udPdfCtl, "OnPageChanged", f_onPageChanged);
                f_addEvent(udPdfCtl, "OnZoom", f_onZoom);
                f_addEvent(udPdfCtl, "OnError", f_onError);
                f_addEvent(udPdfCtl, "OnPrint", f_onPrint);
                f_addEvent(udPdfCtl, "OnFind", f_onFind);
                f_addEvent(udPdfCtl, "OnClosed", f_onClosed);
                return udPdfCtl;
            }
        }
        else {
            document.getElementById("udPdfDiv").innerHTML = "<a href='" + UDPDF_SETUP
		     + "' class='inst' target='_blank'>您尚未安装PDF控件，点这里进行安装。安装后请重新打开浏览器。</a>";
        }
    }
    else {	 //NOT IE
        if (navigator.plugins && navigator.plugins.length > 0) {
            var plugin = navigator.plugins["UdPdf Plugin"];
            if (plugin) {
                udPdfCtl = document.getElementById("udPdfPlugin");
                if (f_checkNewVersion(udPdfCtl.GetVersion())) {
                    document.getElementById("udPdfDiv").innerHTML = "<a href='" + UDPDF_SETUP
		        	 + "' target='_blank' class='inst'>PDF控件有新版本v"
		        	 + UDPDF_VERSION + "，点这里升级安装。安装时需关闭浏览器窗口。</a>";
                }
                else {
                    //事件处理
                    udPdfCtl.OnReady = "f_onReady";
                    udPdfCtl.OnDownload = "f_onDownload";
                    udPdfCtl.OnPageChanged = "f_onPageChanged";
                    udPdfCtl.OnZoom = "f_onZoom";
                    udPdfCtl.OnError = "f_onError";
                    udPdfCtl.OnPrint = "f_onPrint";
                    udPdfCtl.OnFind = "f_onFind";
                    udPdfCtl.OnClosed = "f_onClosed";
                    return udPdfCtl;
                }
            }
            else {
                document.getElementById("udPdfDiv").innerHTML = "<a href='" + UDPDF_SETUP
		          + "' target='_blank' class='inst'>您尚未安装PDF插件，点这里进行安装。安装后请重新打开浏览器。</a>";
            }
        }
        else {
            document.getElementById("udPdfDiv").innerHTML = "<span class='inst'>当前浏览器不支持插件<span>";
        }
    }
    return null;
}

//版本比较，检查是否安装了新版本
function f_checkNewVersion(instVer) {
    var newVer = UDPDF_VERSION.split(".");
    var curVer = instVer.split(".");
    if (parseInt(newVer[0]) > parseInt(curVer[0]))
        return true;
    if (parseInt(newVer[0]) == parseInt(curVer[0]) && parseInt(newVer[1]) > parseInt(curVer[1]))
        return true;
    if (parseInt(newVer[0]) == parseInt(curVer[0]) && parseInt(newVer[1]) == parseInt(curVer[1])
  	&& parseInt(newVer[2]) > parseInt(curVer[2]))
        return true;
    return false;
}

//IE事件注册
function f_addEvent(element, type, handler) {
    if (element.attachEvent) {
        element.attachEvent(type, handler);
    } else {
        f_attachIE11Event(element, type, handler);
    }
}
//单独处理IE11的事件
function f_attachIE11Event(obj, eventId, _functionCallback) {
    var nameFromToStringRegex = /^function\s?([^\s(]*)/;
    var paramsFromToStringRegex = /\(\)|\(.+\)/;
    var params = _functionCallback.toString().match(paramsFromToStringRegex)[0];
    var functionName = _functionCallback.name || _functionCallback.toString().match(nameFromToStringRegex)[1];
    var handler = document.createElement("script");
    handler.setAttribute("for", obj.id);
    handler.event = eventId + params;
    handler.appendChild(document.createTextNode(functionName + params + ";"));
    document.body.appendChild(handler);
};


//记录事件日志
function f_log(str) {
    document.getElementById("info").innerHTML = str;
}

//控件事件处理
function f_onReady() {
    if (ctl.HasBookmark) {//如果有书签目录
        document.getElementById("chkBookmark").checked = true;
        document.getElementById("chkBookmark").disabled = false;
    }
    else {
        document.getElementById("chkBookmark").disabled = true;
    }
    document.getElementById("pageInfo").value = ctl.CurrentPage + "/" + ctl.TotalPage

    f_log("OnReady事件，文档加载完成")
}
function f_onDownload(totalSize, loadingSize) {
    f_log("OnDownload事件，文件大小：" + totalSize + ",已加载大小:" + loadingSize)
}
function f_onPageChanged(currentPage, totalPage) {
    document.getElementById("pageInfo").value = currentPage + "/" + totalPage;
}
function f_onZoom(zoom) {
    f_log("OnZoom事件，缩放比例：" + zoom + "%");
}
function f_onError(errorCode) {
    f_log("OnError事件，异常代号：" + errorCode)
}
function f_onPrint() {
    f_log("OnPrint事件，文档已打印。")
}
function f_onFind(page) {
    if(page != -1)
        f_log("OnFind事件，在第" + page + "页找到文本。")
    else
        f_log("OnFind事件，没找到匹配的文本。")
} 
function f_onClosed() {
    f_log("OnClosed事件，文档已关闭。")
}

//输出控件内容
document.writeln("<div id=\"udPdfDiv\">" + ctlStr + "</div>");