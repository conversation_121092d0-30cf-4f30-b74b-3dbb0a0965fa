/**

 @Name��layui.tree ��
 @Author��star1029
 @License��MIT
 */

layui.define('form', function(exports){
  "use strict";

  var $ = layui.$
  ,form = layui.form
  ,layer = layui.layer

  //ģ����
  ,MOD_NAME = 'tree'

  //�ⲿ�ӿ�
  ,tree = {
    config: {}
    ,index: layui[MOD_NAME] ? (layui[MOD_NAME].index + 10000) : 0

    //����ȫ����
    ,set: function(options){
      var that = this;
      that.config = $.extend({}, that.config, options);
      return that;
    }

    //�¼�����
    ,on: function(events, callback){
      return layui.onevent.call(this, MOD_NAME, events, callback);
    }
  }

  //������ǰʵ��
  ,thisModule = function(){
    var that = this
    ,options = that.config
    ,id = options.id || that.index;

    thisModule.that[id] = that; //��¼��ǰʵ������
    thisModule.config[id] = options; //��¼��ǰʵ��������

    return {
      config: options
      //����ʵ��
      ,reload: function(options){
        that.reload.call(that, options);
      }
      ,getChecked: function(){
        return that.getChecked.call(that);
      }
      ,setChecked: function(id){//����ֵ
        return that.setChecked.call(that, id);
      }
    }
  }

  //��ȡ��ǰʵ��������
  ,getThisModuleConfig = function(id){
    var config = thisModule.config[id];
    if(!config) hint.error('The ID option was not found in the '+ MOD_NAME +' instance');
    return config || null;
  }

  //�ַ�����
  ,SHOW = 'layui-show', HIDE = 'layui-hide', NONE = 'layui-none', DISABLED = 'layui-disabled'

  ,ELEM_VIEW = 'layui-tree', ELEM_SET = 'layui-tree-set', ICON_CLICK = 'layui-tree-iconClick'
  ,ICON_ADD = 'layui-icon-addition', ICON_SUB = 'layui-icon-subtraction', ELEM_ENTRY = 'layui-tree-entry', ELEM_MAIN = 'layui-tree-main', ELEM_TEXT = 'layui-tree-txt', ELEM_PACK = 'layui-tree-pack', ELEM_SPREAD = 'layui-tree-spread'
  ,ELEM_LINE_SHORT = 'layui-tree-setLineShort', ELEM_SHOW = 'layui-tree-showLine', ELEM_EXTEND = 'layui-tree-lineExtend'

  //������
  ,Class = function(options){
    var that = this;
    that.index = ++tree.index;
    that.config = $.extend({}, that.config, tree.config, options);
    that.render();
  };

  //Ĭ������
  Class.prototype.config = {
    data: []  //����

    ,showCheckbox: false  //�Ƿ���ʾ��ѡ��
    ,showLine: true  //�Ƿ���������
    ,accordion: false  //�Ƿ����ַ���ģʽ
    ,onlyIconControl: false  //�Ƿ������ڵ����ͼ�����չ������
    ,isJump: false  //�Ƿ��������ڵ�ʱ�����´�����ת
    ,edit: false  //�Ƿ����ڵ�Ĳ���ͼ��
    ,customOperate: false
    ,limitNodeAddLevel: 0 // ���õ�X���ڵ㲻������Ӳ���
    ,limitNodeDelLevel: 0 // ���õ�X���ڵ㲻����ɾ������
    ,text: {
      defaultNodeName: 'δ����' //�ڵ�Ĭ������
      ,none: '������'  //����Ϊ��ʱ���ı���ʾ
    }
  };

  //����ʵ��
  Class.prototype.reload = function(options){
    var that = this;

    layui.each(options, function(key, item){
      if(item.constructor === Array) delete that.config[key];
    });

    that.config = $.extend(true, {}, that.config, options);
    that.render();
  };

  //������Ⱦ
  Class.prototype.render = function(){
    var that = this
    ,options = that.config;

    that.checkids = [];

    var temp = $('<div class="layui-tree'+ (options.showCheckbox ? " layui-form" : "") + (options.showLine ? " layui-tree-line" : "") +'" lay-filter="LAY-tree-'+ that.index +'"></div>');
    that.tree(temp);

    var othis = options.elem = $(options.elem);
    if(!othis[0]) return;

    //����
    that.key = options.id || that.index;

    //��������ṹ
    that.elem = temp;
    that.elemNone = $('<div class="layui-tree-emptyText">'+ options.text.none +'</div>');
    othis.html(that.elem);

    if(that.elem.find('.layui-tree-set').length == 0){
      return that.elem.append(that.elemNone);
    };

    //��ѡ����Ⱦ
    if(options.showCheckbox){
      that.renderForm('checkbox');
    };

    that.elem.find('.layui-tree-set').each(function(){
      var othis = $(this);
      //�����
      if(!othis.parent('.layui-tree-pack')[0]){
        othis.addClass('layui-tree-setHide');
      };

      //û����һ���ڵ� ��һ�㸸����������
      if(!othis.next()[0] && othis.parents('.layui-tree-pack').eq(1).hasClass('layui-tree-lineExtend')){
        othis.addClass(ELEM_LINE_SHORT);
      };

      //û����һ���ڵ� ������һ��
      if(!othis.next()[0] && !othis.parents('.layui-tree-set').eq(0).next()[0]){
        othis.addClass(ELEM_LINE_SHORT);
      };
    });

    that.events();
  };

  //��Ⱦ��
  Class.prototype.renderForm = function(type){
    form.render(type, 'LAY-tree-'+ this.index);
  };

  //�ڵ����
  Class.prototype.tree = function(elem, children){
    var that = this
    ,options = that.config
    ,data = children || options.data;

    //��������
    layui.each(data, function(index, item){
      var hasChild = item.children && item.children.length > 0
      ,packDiv = $('<div class="layui-tree-pack" '+ (item.spread ? 'style="display: block;"' : '') +'"></div>')
      ,entryDiv = $(['<div data-id="'+ item.id +'" class="layui-tree-set'+ (item.spread ? " layui-tree-spread" : "") + (item.checked ? " layui-tree-checkedFirst" : "") +'">'
        ,'<div class="layui-tree-entry">'
          ,'<div class="layui-tree-main">'
            //��ͷ
            ,function(){
              if(options.showLine){
                if(hasChild){
                  return '<span class="layui-tree-iconClick layui-tree-icon"><i class="layui-icon '+ (item.spread ? "layui-icon-subtraction" : "layui-icon-addition") +'"></i></span>';
                }else{
                  return '<span class="layui-tree-iconClick"><i class="layui-icon layui-icon-file"></i></span>';
                };
              }else{
                return '<span class="layui-tree-iconClick"><i class="layui-tree-iconArrow '+ (hasChild ? "": HIDE) +'"></i></span>';
              };
            }()

            //��ѡ��
            ,function(){
              return options.showCheckbox ? '<input type="checkbox" name="'+ (item.field || ('layuiTreeCheck_'+ item.id)) +'" same="layuiTreeCheck" lay-skin="primary" '+ (item.disabled ? "disabled" : "") +' value="'+ item.id +'">' : '';
            }()

            //�ڵ�
            ,function(){
              if(options.isJump && item.href){
                return '<a href="'+ item.href +'" target="_blank" class="'+ ELEM_TEXT +'">'+ (item.title || item.label || options.text.defaultNodeName) +'</a>';
              }else{
                return '<span class="'+ ELEM_TEXT + (item.disabled ? ' '+ DISABLED : '') +'">'+ (item.title || item.label || options.text.defaultNodeName) +'</span>';
              }
            }()
      ,'</div>'

      //�ڵ����ͼ��
      ,function(){
        if(!options.edit) return '';

        var editIcon = {
          add: '<i class="layui-icon layui-icon-add-1"  data-type="add"></i>'
          ,update: '<i class="layui-icon layui-icon-edit" data-type="update"></i>'
          ,del: '<i class="layui-icon layui-icon-delete" data-type="del"></i>'
        };
        if(item.classPath=="-1") {
            editIcon = {
              add: '<i class="layui-icon layui-icon-add-1" data-type="add"></i>',
            }
          }
          else if(options.limitNodeAddLevel>0  && item.classPath.length > options.limitNodeAddLevel) {
            editIcon = {
              update: '<i class="layui-icon layui-icon-edit" data-type="update"></i>',
              del: '<i class="layui-icon layui-icon-delete" data-type="del"></i>'
            }
          }
          else if (item.children.length > 0) { //有子类不能删�?
            editIcon = {
              add: '<i class="layui-icon layui-icon-add-1" data-type="add"></i>',
              update: '<i class="layui-icon layui-icon-edit" data-type="update"></i>'
            }
          }
	  
        var arr = ['<div class="layui-btn-group layui-tree-btnGroup">'];

        if(options.edit === true){
          options.edit = ['update', 'del']
        }

        if(typeof options.edit === 'object'){
          layui.each(options.edit, function(i, val){
            arr.push(editIcon[val] || '')
          });
          return arr.join('') + '</div>';
        }
      }()
      ,'</div></div>'].join(''));

      //������ӽڵ㣬��ݹ����������
      if(hasChild){
        entryDiv.append(packDiv);
        that.tree(packDiv, item.children);
      };

      elem.append(entryDiv);

      //����ǰ�ýڵ㣬ǰ�ýڵ��������
      if(entryDiv.prev('.'+ELEM_SET)[0]){
        entryDiv.prev().children('.layui-tree-pack').addClass('layui-tree-showLine');
      };

      //�����ӽڵ㣬�򸸽ڵ��������
      if(!hasChild){
        entryDiv.parent('.layui-tree-pack').addClass('layui-tree-lineExtend');
      };

      //չ���ڵ����
      that.spread(entryDiv, item);

      //ѡ���
      if(options.showCheckbox){
        item.checked && that.checkids.push(item.id);
        that.checkClick(entryDiv, item);
      }

      //�����ڵ�
      options.edit && that.operate(entryDiv, item);

    });
  };

  //չ���ڵ�
  Class.prototype.spread = function(elem, item){
    var that = this
    ,options = that.config
    ,entry = elem.children('.'+ELEM_ENTRY)
    ,elemMain = entry.children('.'+ ELEM_MAIN)
    ,elemIcon = entry.find('.'+ ICON_CLICK)
    ,elemText = entry.find('.'+ ELEM_TEXT)
    ,touchOpen = options.onlyIconControl ? elemIcon : elemMain //�ж�չ��ͨ���ڵ㻹�Ǽ�ͷͼ��
    ,state = '';

    //չ������
    touchOpen.on('click', function(e){
      var packCont = elem.children('.'+ELEM_PACK)
      ,iconClick = touchOpen.children('.layui-icon')[0] ? touchOpen.children('.layui-icon') : touchOpen.find('.layui-tree-icon').children('.layui-icon');

      //��û���ӽڵ�
      if(!packCont[0]){
        state = 'normal';
      }else{
        if(elem.hasClass(ELEM_SPREAD)){
          elem.removeClass(ELEM_SPREAD);
          packCont.slideUp(200);
          iconClick.removeClass(ICON_SUB).addClass(ICON_ADD);
        }else{
          elem.addClass(ELEM_SPREAD);
          packCont.slideDown(200);
          iconClick.addClass(ICON_SUB).removeClass(ICON_ADD);

          //�Ƿ��ַ���
          if(options.accordion){
            var sibls = elem.siblings('.'+ELEM_SET);
            sibls.removeClass(ELEM_SPREAD);
            sibls.children('.'+ELEM_PACK).slideUp(200);
            sibls.find('.layui-tree-icon').children('.layui-icon').removeClass(ICON_SUB).addClass(ICON_ADD);
          };
        };
      };
    });

    //����ص�
    elemText.on('click', function(){
      var othis = $(this);

      //�ж��Ƿ����״̬
      if(othis.hasClass(DISABLED)) return;

      //�ж�չ������״̬
      if(elem.hasClass(ELEM_SPREAD)){
        state = options.onlyIconControl ? 'open' : 'close';
      } else {
        state = options.onlyIconControl ? 'close' : 'open';
      }

      //��������Ļص�
      options.click && options.click({
        elem: elem
        ,state: state
        ,data: item
      });
    });
  };

  //���㸴ѡ��ѡ��״̬
  Class.prototype.setCheckbox = function(elem, item, elemCheckbox){
    var that = this
    ,options = that.config
    ,checked = elemCheckbox.prop('checked');

    if(elemCheckbox.prop('disabled')) return;

    //ͬ���ӽڵ�ѡ��״̬
    if(typeof item.children === 'object' || elem.find('.'+ELEM_PACK)[0]){
      var childs = elem.find('.'+ ELEM_PACK).find('input[same="layuiTreeCheck"]');
      childs.each(function(){
        if(this.disabled) return; //���ɵ��������
        this.checked = checked;
      });
    };

    //ͬ�����ڵ�ѡ��״̬
    var setParentsChecked = function(thisNodeElem){
      //���޸��ڵ㣬����ֹ�ݹ�
      if(!thisNodeElem.parents('.'+ ELEM_SET)[0]) return;

      var state
      ,parentPack = thisNodeElem.parent('.'+ ELEM_PACK)
      ,parentNodeElem = parentPack.parent()
      ,parentCheckbox =  parentPack.prev().find('input[same="layuiTreeCheck"]');

      //����ӽڵ�������һ��ѡ�У��򸸽ڵ�Ϊѡ��״̬
      if(checked){
        parentCheckbox.prop('checked', checked);
      } else { //�����ǰ�ڵ�ȡ��ѡ�У�����ݼ��㡰�ֵܺ�����ڵ�ѡ��״̬����ͬ�����ڵ�ѡ��״̬
        parentPack.find('input[same="layuiTreeCheck"]').each(function(){
          if(this.checked){
            state = true;
          }
        });

        //����ֵ�����ڵ�ȫ��δѡ�У��򸸽ڵ�ҲӦΪ��ѡ��״̬
        state || parentCheckbox.prop('checked', false);
      }

      //�򸸽ڵ�ݹ�
      setParentsChecked(parentNodeElem);
    };

    setParentsChecked(elem);

    that.renderForm('checkbox');
  };

  //��ѡ��ѡ��
  Class.prototype.checkClick = function(elem, item){
    var that = this
    ,options = that.config
    ,entry = elem.children('.'+ ELEM_ENTRY)
    ,elemMain = entry.children('.'+ ELEM_MAIN);



    //�����ѡ��
    elemMain.on('click', 'input[same="layuiTreeCheck"]+', function(e){
      layui.stope(e); //��ֹ����ڵ��¼�

      var elemCheckbox = $(this).prev()
      ,checked = elemCheckbox.prop('checked');

      if(elemCheckbox.prop('disabled')) return;

      that.setCheckbox(elem, item, elemCheckbox);

      //��ѡ���������Ļص�
      options.oncheck && options.oncheck({
        elem: elem
        ,checked: checked
        ,data: item
      });
    });
  };

  //�ڵ����
  Class.prototype.operate = function(elem, item){
    var that = this
    ,options = that.config
    ,entry = elem.children('.'+ ELEM_ENTRY)
    ,elemMain = entry.children('.'+ ELEM_MAIN);

    entry.children('.layui-tree-btnGroup').on('click', '.layui-icon', function(e){
      layui.stope(e);  //��ֹ�ڵ����

      var type = $(this).data("type")
      ,packCont = elem.children('.'+ELEM_PACK)
      ,returnObj = {
        data: item
        ,type: type
        ,elem:elem
      };
      if(options.customOperate){
    	  console.log("�޸�Դ�롣����");

			//ע�������g ��Ӧ�������g = {data: a, type: f, elem: e}�ı��� �п�����Ĳ���g���޸ĳ��Լ��ı�������
    	  options.operate && options.operate(returnObj);
      }else{
          //����
          if(type == 'add'){
            //���ڵ㱾�����ӽڵ�
            if(!packCont[0]){
              //�����������ߣ�����ͼ����ʽ
              if(options.showLine){
                elemMain.find('.'+ICON_CLICK).addClass('layui-tree-icon');
                elemMain.find('.'+ICON_CLICK).children('.layui-icon').addClass(ICON_ADD).removeClass('layui-icon-file');
              //��δ���������ߣ���ʾ��ͷ
              }else{
                elemMain.find('.layui-tree-iconArrow').removeClass(HIDE);
              };
              //�ڵ�����ӽڵ�����
              elem.append('<div class="layui-tree-pack"></div>');
            };

            //�����ڵ�
            var key = options.operate && options.operate(returnObj)
            ,obj = {};
            obj.title = options.text.defaultNodeName;
            obj.id = key;
            that.tree(elem.children('.'+ELEM_PACK), [obj]);

            //�����������棬��ΪҪ��Ԫ�ؽ��в���
            if(options.showLine){
              //�ڵ㱾�����ӽڵ�
              if(!packCont[0]){
                //�����ֵܽڵ㣬�ж��ֵܽڵ��Ƿ����ӽڵ�
                var siblings = elem.siblings('.'+ELEM_SET), num = 1
                ,parentPack = elem.parent('.'+ELEM_PACK);
                layui.each(siblings, function(index, i){
                  if(!$(i).children('.'+ELEM_PACK)[0]){
                    num = 0;
                  };
                });

                //���ֵܽڵ㶼���ӽڵ�
                if(num == 1){
                  //�ֵܽڵ����������
                  siblings.children('.'+ELEM_PACK).addClass(ELEM_SHOW);
                  siblings.children('.'+ELEM_PACK).children('.'+ELEM_SET).removeClass(ELEM_LINE_SHORT);
                  elem.children('.'+ELEM_PACK).addClass(ELEM_SHOW);
                  //�����Ƴ�������
                  parentPack.removeClass(ELEM_EXTEND);
                  //ͬ��ڵ����һ�������ߵ�״̬
                  parentPack.children('.'+ELEM_SET).last().children('.'+ELEM_PACK).children('.'+ELEM_SET).last().addClass(ELEM_LINE_SHORT);
                }else{
                  elem.children('.'+ELEM_PACK).children('.'+ELEM_SET).addClass(ELEM_LINE_SHORT);
                };
              }else{
                //���������
                if(!packCont.hasClass(ELEM_EXTEND)){
                  packCont.addClass(ELEM_EXTEND);
                };
                //�ӽڵ����������
                elem.find('.'+ELEM_PACK).each(function(){
                  $(this).children('.'+ELEM_SET).last().addClass(ELEM_LINE_SHORT);
                });
                //���ǰһ���ڵ���������
                if(packCont.children('.'+ELEM_SET).last().prev().hasClass(ELEM_LINE_SHORT)){
                  packCont.children('.'+ELEM_SET).last().prev().removeClass(ELEM_LINE_SHORT);
                }else{
                  //��֮ǰ��û�У�˵����������״̬
                  packCont.children('.'+ELEM_SET).last().removeClass(ELEM_LINE_SHORT);
                };
                //��������㣬Ҫʼ�ձ���������״̬
                if(!elem.parent('.'+ELEM_PACK)[0] && elem.next()[0]){
                  packCont.children('.'+ELEM_SET).last().removeClass(ELEM_LINE_SHORT);
                };
              };
            };
            if(!options.showCheckbox) return;
            //��������ѡ��ͬ�������ڵ�״̬
            if(elemMain.find('input[same="layuiTreeCheck"]')[0].checked){
              var packLast = elem.children('.'+ELEM_PACK).children('.'+ELEM_SET).last();
              packLast.find('input[same="layuiTreeCheck"]')[0].checked = true;
            };
            that.renderForm('checkbox');

          //�޸�
          }else if(type == 'update'){
            var text = elemMain.children('.'+ ELEM_TEXT).html();
            elemMain.children('.'+ ELEM_TEXT).html('');
            //�������򣬸����������Ϸ�
            elemMain.append('<input type="text" class="layui-tree-editInput">');
            //��ȡ����
            elemMain.children('.layui-tree-editInput').val(text).focus();
            //Ƕ�������Ƴ������
            var getVal = function(input){
              var textNew = input.val().trim();
              textNew = textNew ? textNew : options.text.defaultNodeName;
              input.remove();
              elemMain.children('.'+ ELEM_TEXT).html(textNew);

              //ͬ������
              returnObj.data.title = textNew;

              //�ڵ��޸ĵĻص�
              options.operate && options.operate(returnObj);
            };
            //ʧȥ����
            elemMain.children('.layui-tree-editInput').blur(function(){
              getVal($(this));
            });
            //�س�
            elemMain.children('.layui-tree-editInput').on('keydown', function(e){
              if(e.keyCode === 13){
                e.preventDefault();
                getVal($(this));
              };
            });

          //ɾ��
          } else {
            layer.confirm('ȷ��ɾ���ýڵ� "<span style="color: #999;">'+ (item.title || '') +'</span>" ��', function(index){
              options.operate && options.operate(returnObj); //�ڵ�ɾ���Ļص�
              returnObj.status = 'remove'; //��ע�ڵ�ɾ��

              layer.close(index);

              //��ɾ�����һ������ʾ��������ʾ
              if(!elem.prev('.'+ELEM_SET)[0] && !elem.next('.'+ELEM_SET)[0] && !elem.parent('.'+ELEM_PACK)[0]){
                elem.remove();
                that.elem.append(that.elemNone);
                return;
              };
              //�����ֵܽڵ�
              if(elem.siblings('.'+ELEM_SET).children('.'+ELEM_ENTRY)[0]){
                //��������ѡ��
                if(options.showCheckbox){
                  //��������ѡ�򣬽����²�����
                  var elemDel = function(elem){
                    //���޸���㣬��ִ��
                    if(!elem.parents('.'+ELEM_SET)[0]) return;
                    var siblingTree = elem.siblings('.'+ELEM_SET).children('.'+ELEM_ENTRY)
                    ,parentTree = elem.parent('.'+ELEM_PACK).prev()
                    ,checkState = parentTree.find('input[same="layuiTreeCheck"]')[0]
                    ,state = 1, num = 0;
                    //�����ڵ�δ��ѡ
                    if(checkState.checked == false){
                      //�����ֵܽڵ�
                      siblingTree.each(function(i, item1){
                        var input = $(item1).find('input[same="layuiTreeCheck"]')[0]
                        if(input.checked == false && !input.disabled){
                          state = 0;
                        };
                        //�ж��Ƿ�ȫΪ���ɹ�ѡ��
                        if(!input.disabled){
                          num = 1;
                        };
                      });
                      //���пɹ�ѡѡ������ѹ�ѡ
                      if(state == 1 && num == 1){
                        //��ѡ���ڵ�
                        checkState.checked = true;
                        that.renderForm('checkbox');
                        //���ϱ������Ƚڵ�
                        elemDel(parentTree.parent('.'+ELEM_SET));
                      };
                    };
                  };
                  elemDel(elem);
                };
                //������������
                if(options.showLine){
                  //�����ֵܽڵ㣬�ж��ֵܽڵ��Ƿ����ӽڵ�
                  var siblings = elem.siblings('.'+ELEM_SET), num = 1
                  ,parentPack = elem.parent('.'+ELEM_PACK);
                  layui.each(siblings, function(index, i){
                    if(!$(i).children('.'+ELEM_PACK)[0]){
                      num = 0;
                    };
                  });
                  //���ֵܽڵ㶼���ӽڵ�
                  if(num == 1){
                    //���ڵ㱾�����ӽڵ�
                    if(!packCont[0]){
                      //����ȥ�������ߣ���Ϊ��ʱ�ӽڵ���û�пսڵ�
                      parentPack.removeClass(ELEM_EXTEND);
                      siblings.children('.'+ELEM_PACK).addClass(ELEM_SHOW);
                      siblings.children('.'+ELEM_PACK).children('.'+ELEM_SET).removeClass(ELEM_LINE_SHORT);
                    };
                    //��Ϊ���һ���ڵ�
                    if(!elem.next()[0]){
                      elem.prev().children('.'+ELEM_PACK).children('.'+ELEM_SET).last().addClass(ELEM_LINE_SHORT);
                    }else{
                      parentPack.children('.'+ELEM_SET).last().children('.'+ELEM_PACK).children('.'+ELEM_SET).last().addClass(ELEM_LINE_SHORT);
                    };
                    //��Ϊ��������һ���ڵ㣬ȥ��ǰһ������������
                    if(!elem.next()[0] && !elem.parents('.'+ELEM_SET)[1] && !elem.parents('.'+ELEM_SET).eq(0).next()[0]){
                      elem.prev('.'+ELEM_SET).addClass(ELEM_LINE_SHORT);
                    };
                  }else{
                    //��Ϊ���һ���ڵ�����������
                    if(!elem.next()[0] && elem.hasClass(ELEM_LINE_SHORT)){
                      elem.prev().addClass(ELEM_LINE_SHORT);
                    };
                  };
                };

              }else{
                //�����ֵܽڵ�
                var prevDiv = elem.parent('.'+ELEM_PACK).prev();
                //��������������
                if(options.showLine){
                  prevDiv.find('.'+ICON_CLICK).removeClass('layui-tree-icon');
                  prevDiv.find('.'+ICON_CLICK).children('.layui-icon').removeClass(ICON_SUB).addClass('layui-icon-file');
                  //���ڵ����ڲ����������
                  var pare = prevDiv.parents('.'+ELEM_PACK).eq(0);
                  pare.addClass(ELEM_EXTEND);

                  //�ֵܽڵ�����ӽڵ����������
                  pare.children('.'+ELEM_SET).each(function(){
                    $(this).children('.'+ELEM_PACK).children('.'+ELEM_SET).last().addClass(ELEM_LINE_SHORT);
                  });
                }else{
                //���ڵ����ؼ�ͷ
                  prevDiv.find('.layui-tree-iconArrow').addClass(HIDE);
                };
                //�Ƴ�չ������
                elem.parents('.'+ELEM_SET).eq(0).removeClass(ELEM_SPREAD);
                //�Ƴ��ڵ�����
                elem.parent('.'+ELEM_PACK).remove();
              };

              elem.remove();
            });

          };
      }


      ///////////////////////////////////////////////////
    });
  };

  //�����¼�
  Class.prototype.events = function(){
    var that = this
    ,options = that.config
    ,checkWarp = that.elem.find('.layui-tree-checkedFirst');

    //��ʼѡ��
    that.setChecked(that.checkids);

    //����
    that.elem.find('.layui-tree-search').on('keyup', function(){
      var input = $(this)
      ,val = input.val()
      ,pack = input.nextAll()
      ,arr = [];

      //�������е�ֵ
      pack.find('.'+ ELEM_TEXT).each(function(){
        var entry = $(this).parents('.'+ELEM_ENTRY);
        //��ֵƥ�䣬��һ����������ʶ
        if($(this).html().indexOf(val) != -1){
          arr.push($(this).parent());

          var select = function(div){
            div.addClass('layui-tree-searchShow');
            //���ϸ��ڵ���Ⱦ
            if(div.parent('.'+ELEM_PACK)[0]){
              select(div.parent('.'+ELEM_PACK).parent('.'+ELEM_SET));
            };
          };
          select(entry.parent('.'+ELEM_SET));
        };
      });

      //���ݱ�־�޳�
      pack.find('.'+ELEM_ENTRY).each(function(){
        var parent = $(this).parent('.'+ELEM_SET);
        if(!parent.hasClass('layui-tree-searchShow')){
          parent.addClass(HIDE);
        };
      });
      if(pack.find('.layui-tree-searchShow').length == 0){
        that.elem.append(that.elemNone);
      };

      //�ڵ���˵Ļص�
      options.onsearch && options.onsearch({
        elem: arr
      });
    });

    //��ԭ������ʼ״̬
    that.elem.find('.layui-tree-search').on('keydown', function(){
      $(this).nextAll().find('.'+ELEM_ENTRY).each(function(){
        var parent = $(this).parent('.'+ELEM_SET);
        parent.removeClass('layui-tree-searchShow '+ HIDE);
      });
      if($('.layui-tree-emptyText')[0]) $('.layui-tree-emptyText').remove();
    });
  };

  //�õ�ѡ�нڵ�
  Class.prototype.getChecked = function(){
    var that = this
    ,options = that.config
    ,checkId = []
    ,checkData = [];

    //�����ڵ��ҵ�ѡ������
    that.elem.find('.layui-form-checked').each(function(){
      checkId.push($(this).prev()[0].value);
    });

    //�����ڵ�
    var eachNodes = function(data, checkNode){
      layui.each(data, function(index, item){
        layui.each(checkId, function(index2, item2){
          if(item.id == item2){
            var cloneItem = $.extend({}, item);
            delete cloneItem.children;

            checkNode.push(cloneItem);

            if(item.children){
              cloneItem.children = [];
              eachNodes(item.children, cloneItem.children);
            }
            return true
          }
        });
      });
    };

    eachNodes($.extend({}, options.data), checkData);

    return checkData;
  };

  //����ѡ�нڵ�
  Class.prototype.setChecked = function(checkedId){
    var that = this
    ,options = that.config;

    //��ʼѡ��
    that.elem.find('.'+ELEM_SET).each(function(i, item){
      var thisId = $(this).data('id')
      ,input = $(item).children('.'+ELEM_ENTRY).find('input[same="layuiTreeCheck"]')
      ,reInput = input.next();

      //����������
      if(typeof checkedId === 'number'){
        if(thisId == checkedId){
          if(!input[0].checked){
            reInput.click();
          };
          return false;
        };
      }
      //����������
      else if(typeof checkedId === 'object'){
        layui.each(checkedId, function(index, value){
          if(value == thisId && !input[0].checked){
            reInput.click();
            return true;
          }
        });
      };
    });
  };

  //��¼����ʵ��
  thisModule.that = {}; //��¼����ʵ������
  thisModule.config = {}; //��¼����ʵ��������

  //����ʵ��
  tree.reload = function(id, options){
    var that = thisModule.that[id];
    that.reload(options);

    return thisModule.call(that);
  };

  //���ѡ�еĽڵ�����
  tree.getChecked = function(id){
    var that = thisModule.that[id];
    return that.getChecked();
  };

  //����ѡ�нڵ�
  tree.setChecked = function(id, checkedId){
    var that = thisModule.that[id];
    return that.setChecked(checkedId);
  };

  //�������
  tree.render = function(options){
    var inst = new Class(options);
    return thisModule.call(inst);
  };

  exports(MOD_NAME, tree);
})
