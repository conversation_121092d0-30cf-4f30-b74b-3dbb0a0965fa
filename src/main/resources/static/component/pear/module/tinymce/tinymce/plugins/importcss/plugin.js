/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.6.2 (2020-12-08)
 */
(function () {
    'use strict';

    var global = tinymce.util.Tools.resolve('tinymce.PluginManager');

    var global$1 = tinymce.util.Tools.resolve('tinymce.dom.DOMUtils');

    var global$2 = tinymce.util.Tools.resolve('tinymce.EditorManager');

    var global$3 = tinymce.util.Tools.resolve('tinymce.Env');

    var global$4 = tinymce.util.Tools.resolve('tinymce.util.Tools');

    var shouldMergeClasses = function (editor) {
        return editor.getParam('importcss_merge_classes');
    };
    var shouldImportExclusive = function (editor) {
        return editor.getParam('importcss_exclusive');
    };
    var getSelectorConverter = function (editor) {
        return editor.getParam('importcss_selector_converter');
    };
    var getSelectorFilter = function (editor) {
        return editor.getParam('importcss_selector_filter');
    };
    var getCssGroups = function (editor) {
        return editor.getParam('importcss_groups');
    };
    var shouldAppend = function (editor) {
        return editor.getParam('importcss_append');
    };
    var getFileFilter = function (editor) {
        return editor.getParam('importcss_file_filter');
    };
    var getSkin = function (editor) {
        var skin = editor.getParam('skin');
        return skin !== false ? skin || 'oxide' : false;
    };
    var getSkinUrl = function (editor) {
        return editor.getParam('skin_url');
    };

    var typeOf = function (x) {
        var t = typeof x;
        if (x === null) {
            return 'null';
        } else if (t === 'object' && (Array.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'Array')) {
            return 'array';
        } else if (t === 'object' && (String.prototype.isPrototypeOf(x) || x.constructor && x.constructor.name === 'String')) {
            return 'string';
        } else {
            return t;
        }
    };
    var isType = function (type) {
        return function (value) {
            return typeOf(value) === type;
        };
    };
    var isArray = isType('array');

    var nativePush = Array.prototype.push;
    var map = function (xs, f) {
        var len = xs.length;
        var r = new Array(len);
        for (var i = 0; i < len; i++) {
            var x = xs[i];
            r[i] = f(x, i);
        }
        return r;
    };
    var flatten = function (xs) {
        var r = [];
        for (var i = 0, len = xs.length; i < len; ++i) {
            if (!isArray(xs[i])) {
                throw new Error('Arr.flatten item ' + i + ' was not an array, input: ' + xs);
            }
            nativePush.apply(r, xs[i]);
        }
        return r;
    };
    var bind = function (xs, f) {
        return flatten(map(xs, f));
    };

    var generate = function () {
        var ungroupedOrder = [];
        var groupOrder = [];
        var groups = {};
        var addItemToGroup = function (groupTitle, itemInfo) {
            if (groups[groupTitle]) {
                groups[groupTitle].push(itemInfo);
            } else {
                groupOrder.push(groupTitle);
                groups[groupTitle] = [itemInfo];
            }
        };
        var addItem = function (itemInfo) {
            ungroupedOrder.push(itemInfo);
        };
        var toFormats = function () {
            var groupItems = bind(groupOrder, function (g) {
                var items = groups[g];
                return items.length === 0 ? [] : [{
                    title: g,
                    items: items
                }];
            });
            return groupItems.concat(ungroupedOrder);
        };
        return {
            addItemToGroup: addItemToGroup,
            addItem: addItem,
            toFormats: toFormats
        };
    };

    var removeCacheSuffix = function (url) {
        var cacheSuffix = global$3.cacheSuffix;
        if (typeof url === 'string') {
            url = url.replace('?' + cacheSuffix, '').replace('&' + cacheSuffix, '');
        }
        return url;
    };
    var isSkinContentCss = function (editor, href) {
        var skin = getSkin(editor);
        if (skin) {
            var skinUrlBase = getSkinUrl(editor);
            var skinUrl = skinUrlBase ? editor.documentBaseURI.toAbsolute(skinUrlBase) : global$2.baseURL + '/skins/ui/' + skin;
            var contentSkinUrlPart = global$2.baseURL + '/skins/content/';
            return href === skinUrl + '/content' + (editor.inline ? '.inline' : '') + '.min.css' || href.indexOf(contentSkinUrlPart) !== -1;
        }
        return false;
    };
    var compileFilter = function (filter) {
        if (typeof filter === 'string') {
            return function (value) {
                return value.indexOf(filter) !== -1;
            };
        } else if (filter instanceof RegExp) {
            return function (value) {
                return filter.test(value);
            };
        }
        return filter;
    };
    var isCssImportRule = function (rule) {
        return rule.styleSheet;
    };
    var isCssPageRule = function (rule) {
        return rule.selectorText;
    };
    var getSelectors = function (editor, doc, fileFilter) {
        var selectors = [], contentCSSUrls = {};

        function append(styleSheet, imported) {
            var href = styleSheet.href, rules;
            href = removeCacheSuffix(href);
            if (!href || !fileFilter(href, imported) || isSkinContentCss(editor, href)) {
                return;
            }
            global$4.each(styleSheet.imports, function (styleSheet) {
                append(styleSheet, true);
            });
            try {
                rules = styleSheet.cssRules || styleSheet.rules;
            } catch (e) {
            }
            global$4.each(rules, function (cssRule) {
                if (isCssImportRule(cssRule)) {
                    append(cssRule.styleSheet, true);
                } else if (isCssPageRule(cssRule)) {
                    global$4.each(cssRule.selectorText.split(','), function (selector) {
                        selectors.push(global$4.trim(selector));
                    });
                }
            });
        }

        global$4.each(editor.contentCSS, function (url) {
            contentCSSUrls[url] = true;
        });
        if (!fileFilter) {
            fileFilter = function (href, imported) {
                return imported || contentCSSUrls[href];
            };
        }
        try {
            global$4.each(doc.styleSheets, function (styleSheet) {
                append(styleSheet);
            });
        } catch (e) {
        }
        return selectors;
    };
    var defaultConvertSelectorToFormat = function (editor, selectorText) {
        var format;
        var selector = /^(?:([a-z0-9\-_]+))?(\.[a-z0-9_\-\.]+)$/i.exec(selectorText);
        if (!selector) {
            return;
        }
        var elementName = selector[1];
        var classes = selector[2].substr(1).split('.').join(' ');
        var inlineSelectorElements = global$4.makeMap('a,img');
        if (selector[1]) {
            format = {title: selectorText};
            if (editor.schema.getTextBlockElements()[elementName]) {
                format.block = elementName;
            } else if (editor.schema.getBlockElements()[elementName] || inlineSelectorElements[elementName.toLowerCase()]) {
                format.selector = elementName;
            } else {
                format.inline = elementName;
            }
        } else if (selector[2]) {
            format = {
                inline: 'span',
                title: selectorText.substr(1),
                classes: classes
            };
        }
        if (shouldMergeClasses(editor) !== false) {
            format.classes = classes;
        } else {
            format.attributes = {class: classes};
        }
        return format;
    };
    var getGroupsBySelector = function (groups, selector) {
        return global$4.grep(groups, function (group) {
            return !group.filter || group.filter(selector);
        });
    };
    var compileUserDefinedGroups = function (groups) {
        return global$4.map(groups, function (group) {
            return global$4.extend({}, group, {
                original: group,
                selectors: {},
                filter: compileFilter(group.filter),
                item: {
                    text: group.title,
                    menu: []
                }
            });
        });
    };
    var isExclusiveMode = function (editor, group) {
        return group === null || shouldImportExclusive(editor) !== false;
    };
    var isUniqueSelector = function (editor, selector, group, globallyUniqueSelectors) {
        return !(isExclusiveMode(editor, group) ? selector in globallyUniqueSelectors : selector in group.selectors);
    };
    var markUniqueSelector = function (editor, selector, group, globallyUniqueSelectors) {
        if (isExclusiveMode(editor, group)) {
            globallyUniqueSelectors[selector] = true;
        } else {
            group.selectors[selector] = true;
        }
    };
    var convertSelectorToFormat = function (editor, plugin, selector, group) {
        var selectorConverter;
        if (group && group.selector_converter) {
            selectorConverter = group.selector_converter;
        } else if (getSelectorConverter(editor)) {
            selectorConverter = getSelectorConverter(editor);
        } else {
            selectorConverter = function () {
                return defaultConvertSelectorToFormat(editor, selector);
            };
        }
        return selectorConverter.call(plugin, selector, group);
    };
    var setup = function (editor) {
        editor.on('init', function (_e) {
            var model = generate();
            var globallyUniqueSelectors = {};
            var selectorFilter = compileFilter(getSelectorFilter(editor));
            var groups = compileUserDefinedGroups(getCssGroups(editor));
            var processSelector = function (selector, group) {
                if (isUniqueSelector(editor, selector, group, globallyUniqueSelectors)) {
                    markUniqueSelector(editor, selector, group, globallyUniqueSelectors);
                    var format = convertSelectorToFormat(editor, editor.plugins.importcss, selector, group);
                    if (format) {
                        var formatName = format.name || global$1.DOM.uniqueId();
                        editor.formatter.register(formatName, format);
                        return global$4.extend({}, {
                            title: format.title,
                            format: formatName
                        });
                    }
                }
                return null;
            };
            global$4.each(getSelectors(editor, editor.getDoc(), compileFilter(getFileFilter(editor))), function (selector) {
                if (selector.indexOf('.mce-') === -1) {
                    if (!selectorFilter || selectorFilter(selector)) {
                        var selectorGroups = getGroupsBySelector(groups, selector);
                        if (selectorGroups.length > 0) {
                            global$4.each(selectorGroups, function (group) {
                                var menuItem = processSelector(selector, group);
                                if (menuItem) {
                                    model.addItemToGroup(group.title, menuItem);
                                }
                            });
                        } else {
                            var menuItem = processSelector(selector, null);
                            if (menuItem) {
                                model.addItem(menuItem);
                            }
                        }
                    }
                }
            });
            var items = model.toFormats();
            editor.fire('addStyleModifications', {
                items: items,
                replace: !shouldAppend(editor)
            });
        });
    };

    var get = function (editor) {
        var convertSelectorToFormat = function (selectorText) {
            return defaultConvertSelectorToFormat(editor, selectorText);
        };
        return {convertSelectorToFormat: convertSelectorToFormat};
    };

    function Plugin() {
        global.add('importcss', function (editor) {
            setup(editor);
            return get(editor);
        });
    }

    Plugin();

}());
