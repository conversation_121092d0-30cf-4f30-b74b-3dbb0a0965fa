.layui-layer-msg {
    border-color: transparent !important;
    box-shadow: 2px 0 6px rgb(0 21 41 / 0.05) !important;
}

/* 扩展动画开始 */
.pear-drawer.layui-layer {
    border-radius: 0 !important;
    overflow: auto;
}

.pear-drawer.layui-layer.position-absolute {
    position: absolute !important;
}
.pear-drawer-anim,
.pear-drawer-anim.layui-anim {
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-timing-function: cubic-bezier(0.7, 0.3, 0.1, 1);
    animation-timing-function: cubic-bezier(0.7, 0.3, 0.1, 1);
}

/* right to left */
@keyframes layer-rl {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        -ms-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        opacity: 1;

    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

@-webkit-keyframes layer-rl {
    from {
        -webkit-transform: translate3d(100%, 0, 0);
        -ms-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
        opacity: 1;

    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

.layer-anim-rl {
    -webkit-animation-name: layer-rl;
    animation-name: layer-rl;
}

/* right to left close */
@keyframes layer-rl-close {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        -webkit-transform: translate3d(100%, 0, 0);
        -ms-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }
}

@-webkit-keyframes layer-rl-close {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);

    }

    to {
        -webkit-transform: translate3d(100%, 0, 0);
        -ms-transform: translate3d(100%, 0, 0);
        transform: translate3d(100%, 0, 0);
    }
}

.layer-anim-rl-close,
.layer-anim-rl.layer-anim-close {
    -webkit-animation-name: layer-rl-close;
    animation-name: layer-rl-close;
}

/* left to right */
@-webkit-keyframes layer-lr {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }

    to {
        -webkit-transform: translate3d(-100%, 0, 0);
        -ms-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
        opacity: 1
    }
}

@keyframes layer-lr {
    from {
        -webkit-transform: translate3d(-100%, 0, 0);
        -ms-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
        opacity: 1
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

.layer-anim-lr {
    -webkit-animation-name: layer-lr;
    animation-name: layer-lr
}

/* left to right close */
@-webkit-keyframes layer-lr-close {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        -webkit-transform: translate3d(-100%, 0, 0);
        -ms-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }
}

@keyframes layer-lr-close {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        -webkit-transform: translate3d(-100%, 0, 0);
        -ms-transform: translate3d(-100%, 0, 0);
        transform: translate3d(-100%, 0, 0);
    }
}

.layer-anim-lr-close,
.layer-anim-lr.layer-anim-close {
    -webkit-animation-name: layer-lr-close;
    animation-name: layer-lr-close
}

/* top to bottom */
@-webkit-keyframes layer-tb {
    from {
        -webkit-transform: translate3d(0, -100%, 0);
        -ms-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
        opacity: 1;
        animation-timing-function: cubic-bezier(0.7, 0.3, 0.1, 1);
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1;
        animation-timing-function: cubic-bezier(0.7, 0.3, 0.1, 1);
    }
}

@keyframes layer-tb {
    from {
        -webkit-transform: translate3d(0, -100%, 0);
        -ms-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
        opacity: 1;
        animation-timing-function: cubic-bezier(0.7, 0.3, 0.1, 1);
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1;
        animation-timing-function: cubic-bezier(0.7, 0.3, 0.1, 1);
    }
}

.layer-anim-tb {
    -webkit-animation-name: layer-tb;
    animation-name: layer-tb
}

/* top to bottom close */
@-webkit-keyframes layer-tb-close {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        -webkit-transform: translate3d(0, -100%, 0);
        -ms-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

@keyframes layer-tb-close {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        -webkit-transform: translate3d(0, -100%, 0);
        -ms-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
    }
}

.layer-anim-tb-close,
.layer-anim-tb.layer-anim-close {
    -webkit-animation-name: layer-tb-close;
    animation-name: layer-tb-close
}

/* bottom to top */
@-webkit-keyframes layer-bt {
    from {
        -webkit-transform: translate3d(0, 100%, 0);
        -ms-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
        opacity: 1
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

@keyframes layer-bt {
    from {
        -webkit-transform: translate3d(0, 100%, 0);
        -ms-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
        opacity: 1
    }

    to {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
        opacity: 1
    }
}

.layer-anim-bt {
    -webkit-animation-name: layer-bt;
    animation-name: layer-bt
}

/* bottom to top close */
@-webkit-keyframes layer-bt-close {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);
    }

    to {
        -webkit-transform: translate3d(0, 100%, 0);
        -ms-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);
    }
}

@keyframes layer-bt-close {
    from {
        -webkit-transform: translate3d(0, 0, 0);
        -ms-transform: translate3d(0, 0, 0);
        transform: translate3d(0, 0, 0);

    }

    to {
        -webkit-transform: translate3d(0, 100%, 0);
        -ms-transform: translate3d(0, 100%, 0);
        transform: translate3d(0, 100%, 0);

    }
}

.layer-anim-bt-close,
.layer-anim-bt.layer-anim-close {
    -webkit-animation-name: layer-bt-close;
    animation-name: layer-bt-close
}

/* 扩展动画结束 */