# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Pagjine precedente
previous_label=IndaÃ»r
next.title=Prossime pagjine
next_label=Indevant

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Pagjine
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=di {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} di {{pagesCount}})

zoom_out.title=ImpiÃ§ulÃ¬s
zoom_out_label=ImpiÃ§ulÃ¬s
zoom_in.title=IngrandÃ¬s
zoom_in_label=IngrandÃ¬s
zoom.title=Ingrandiment
presentation_mode.title=Passe ae modalitÃ¢t presentazion
presentation_mode_label=ModalitÃ¢t presentazion
open_file.title=VierÃ§ un file
open_file_label=VierÃ§
print.title=Stampe
print_label=Stampe
download.title=Discjame
download_label=Discjame
save.title=Salve
save_label=Salve
bookmark.title=Viodude atuÃ¢l (copie o vierÃ§ intun gnÃ»f barcon)
bookmark_label=Viodude atuÃ¢l

bookmark1.title=Pagjine corinte (mostre URL de pagjine atuÃ¢l)
bookmark1_label=Pagjine corinte

# Secondary toolbar and context menu
tools.title=Struments
tools_label=Struments
first_page.title=Va ae prime pagjine
first_page_label=Va ae prime pagjine
last_page.title=Va ae ultime pagjine
last_page_label=Va ae ultime pagjine
page_rotate_cw.title=Zire in sens orari
page_rotate_cw_label=Zire in sens orari
page_rotate_ccw.title=Zire in sens antiorari
page_rotate_ccw_label=Zire in sens antiorari

cursor_text_select_tool.title=Ative il strument di selezion dal test
cursor_text_select_tool_label=Strument di selezion dal test
cursor_hand_tool.title=Ative il strument manute
cursor_hand_tool_label=Strument manute

scroll_page.title=Dopre il scoriment des pagjinis
scroll_page_label=Scoriment pagjinis
scroll_vertical.title=Dopre scoriment verticÃ¢l
scroll_vertical_label=Scoriment verticÃ¢l
scroll_horizontal.title=Dopre scoriment orizontÃ¢l
scroll_horizontal_label=Scoriment orizontÃ¢l
scroll_wrapped.title=Dopre scoriment par blocs
scroll_wrapped_label=Scoriment par blocs

spread_none.title=No sta meti dongje pagjinis in cubie
spread_none_label=No cubiis di pagjinis
spread_odd.title=Met dongje cubiis di pagjinis scomenÃ§ant des pagjinis dispar
spread_odd_label=Cubiis di pagjinis, dispar a Ã§ampe
spread_even.title=Met dongje cubiis di pagjinis scomenÃ§ant des pagjinis pÃ¢r
spread_even_label=Cubiis di pagjinis, pÃ¢r a Ã§ampe

# Document properties dialog box
document_properties.title=ProprietÃ¢ts dal documentâ¦
document_properties_label=ProprietÃ¢ts dal documentâ¦
document_properties_file_name=Non dal file:
document_properties_file_size=Dimension dal file:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=Titul:
document_properties_author=AutÃ´r:
document_properties_subject=Ogjet:
document_properties_keywords=Peraulis clÃ¢f:
document_properties_creation_date=Date di creazion:
document_properties_modification_date=Date di modifiche:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=CreatÃ´r
document_properties_producer=GjeneradÃ´r PDF:
document_properties_version=Version PDF:
document_properties_page_count=Numar di pagjinis:
document_properties_page_size=Dimension de pagjine:
document_properties_page_size_unit_inches=oncis
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=verticÃ¢l
document_properties_page_size_orientation_landscape=orizontÃ¢l
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letare
document_properties_page_size_name_legal=LegÃ¢l
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Visualizazion web svelte:
document_properties_linearized_yes=SÃ¬
document_properties_linearized_no=No
document_properties_close=Siere

print_progress_message=DaÃ»r a prontÃ¢ il document pe stampeâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Anule

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Ative/Disative sbare laterÃ¢l
toggle_sidebar_notification2.title=Ative/Disative sbare laterÃ¢l (il document al conten struture/zontis/strÃ¢ts)
toggle_sidebar_label=Ative/Disative sbare laterÃ¢l
document_outline.title=Mostre la struture dal document (dopli clic par slargjÃ¢/strenzi ducj i elements)
document_outline_label=Struture dal document
attachments.title=Mostre lis zontis
attachments_label=Zontis
layers.title=Mostre i strÃ¢ts (dopli clic par ristabilÃ® ducj i strÃ¢ts al stÃ¢t predefinÃ®t)
layers_label=StrÃ¢ts
thumbs.title=Mostre miniaturis
thumbs_label=Miniaturis
current_outline_item.title=Cjate l'element de struture atuÃ¢l
current_outline_item_label=Element de struture atuÃ¢l
findbar.title=Cjate tal document
findbar_label=Cjate

additional_layers=StrÃ¢ts adizionÃ¢i
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Pagjine {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Pagjine {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Miniature de pagjine {{page}}

# Find panel button title and messages
find_input.title=Cjate
find_input.placeholder=Cjate tal documentâ¦
find_previous.title=Cjate il cÃ¢s precedent dal test
find_previous_label=Precedent
find_next.title=Cjate il cÃ¢s sucessÃ®f dal test
find_next_label=SucessÃ®f
find_highlight=Evidenzie dut
find_match_case_label=FÃ¢s distinzion tra maiusculis e minusculis
find_match_diacritics_label=Corispondence diacritiche
find_entire_word_label=Peraulis interiis
find_reached_top=Si Ã¨ rivÃ¢ts al inizi dal document e si Ã  continuÃ¢t de fin
find_reached_bottom=Si Ã¨ rivÃ¢t ae fin dal document e si Ã  continuÃ¢t dal inizi
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} di {{total}} corispondence
find_match_count[two]={{current}} di {{total}} corispondencis
find_match_count[few]={{current}} di {{total}} corispondencis
find_match_count[many]={{current}} di {{total}} corispondencis
find_match_count[other]={{current}} di {{total}} corispondencis
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Plui di {{limit}} corispondencis
find_match_count_limit[one]=Plui di {{limit}} corispondence
find_match_count_limit[two]=Plui di {{limit}} corispondencis
find_match_count_limit[few]=Plui di {{limit}} corispondencis
find_match_count_limit[many]=Plui di {{limit}} corispondencis
find_match_count_limit[other]=Plui di {{limit}} corispondencis
find_not_found=Test no cjatÃ¢t

# Error panel labels
error_more_info=Altris informazions
error_less_info=Mancul informazions
error_close=Siere
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (compilazion: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=MessaÃ§: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stack: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=File: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Rie: {{line}}

# Predefined zoom values
page_scale_width=Largjece de pagjine
page_scale_fit=Pagjine interie
page_scale_auto=Ingrandiment automatic
page_scale_actual=Dimension reÃ¢l
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading=DaÃ»r a cjamÃ¢â¦
loading_error=Al Ã¨ vignÃ»t fÃ»r un erÃ´r intant che si cjariave il PDF.
invalid_file_error=File PDF no valit o ruvinÃ¢t.
missing_file_error=Al mancje il file PDF.
unexpected_response_error=Rispueste dal servidÃ´r inspietade.

rendering_error=Al Ã¨ vignÃ»t fÃ»r un erÃ´r tal realizÃ¢ la visualizazion de pagjine.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Anotazion {{type}}]
password_label=InserÃ¬s la password par vierzi chest file PDF.
password_invalid=Password no valide. Par plasÃª torne prove.
password_ok=Va ben
password_cancel=Anule

printing_not_supported=Atenzion: la stampe no je supuartade ad implen di chest navigadÃ´r.
printing_not_ready=Atenzion: il PDF nol Ã¨ stÃ¢t cjamÃ¢t dal dut pe stampe.
web_fonts_disabled=I caratars dal Web a son disativÃ¢ts: Impussibil doprÃ¢ i caratars PDF incorporÃ¢ts.

# Editor
editor_free_text2.title=Test
editor_free_text2_label=Test
editor_ink2.title=Dissen
editor_ink2_label=Dissen

free_text2_default_content=Scomence a scriviâ¦

# Editor Parameters
editor_free_text_color=ColÃ´r
editor_free_text_size=Dimension
editor_ink_color=ColÃ´r
editor_ink_thickness=SpessÃ´r
editor_ink_opacity=OpacitÃ¢t

# Editor aria
editor_free_text2_aria_label=EditÃ´r di test
editor_ink2_aria_label=EditÃ´r dissens
editor_ink_canvas_aria_label=Imagjin creade dal utent
