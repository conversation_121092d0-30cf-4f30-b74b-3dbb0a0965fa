# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Ævv<PERSON>lki sÉhifÉ
previous_label=Ævv<PERSON><PERSON><PERSON> tap
next.title=NÃ¶vb<PERSON><PERSON> sÉhifÉ
next_label=Ä°rÉli

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=SÉhifÉ
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=/ {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} / {{pagesCount}})

zoom_out.title=UzaqlaÅ
zoom_out_label=UzaqlaÅ
zoom_in.title=YaxÄ±nlaÅ
zoom_in_label=YaxÄ±nlaÅ
zoom.title=YaxÄ±nlaÅdÄ±rma
presentation_mode.title=TÉqdimat RejiminÉ KeÃ§
presentation_mode_label=TÉqdimat Rejimi
open_file.title=Fayl AÃ§
open_file_label=AÃ§
print.title=YazdÄ±r
print_label=YazdÄ±r
download.title=Endir
download_label=Endir
bookmark.title=HazÄ±rkÄ± gÃ¶rÃ¼nÃ¼Å (kÃ¶Ã§Ã¼r vÉ ya yeni pÉncÉrÉdÉ aÃ§)
bookmark_label=HazÄ±rkÄ± gÃ¶rÃ¼nÃ¼Å

# Secondary toolbar and context menu
tools.title=AlÉtlÉr
tools_label=AlÉtlÉr
first_page.title=Ä°lk SÉhifÉyÉ get
first_page_label=Ä°lk SÉhifÉyÉ get
last_page.title=Son SÉhifÉyÉ get
last_page_label=Son SÉhifÉyÉ get
page_rotate_cw.title=Saat Ä°stiqamÉtindÉ FÄ±rlat
page_rotate_cw_label=Saat Ä°stiqamÉtindÉ FÄ±rlat
page_rotate_ccw.title=Saat Ä°stiqamÉtinin ÆksinÉ FÄ±rlat
page_rotate_ccw_label=Saat Ä°stiqamÉtinin ÆksinÉ FÄ±rlat

cursor_text_select_tool.title=YazÄ± seÃ§mÉ alÉtini aktivlÉÅdir
cursor_text_select_tool_label=YazÄ± seÃ§mÉ alÉti
cursor_hand_tool.title=Æl alÉtini aktivlÉÅdir
cursor_hand_tool_label=Æl alÉti

scroll_vertical.title=Åaquli sÃ¼rÃ¼ÅdÃ¼rmÉ iÅlÉt
scroll_vertical_label=Åaquli sÃ¼rÃ¼ÅdÃ¼rmÉ
scroll_horizontal.title=ÃfÃ¼qi sÃ¼rÃ¼ÅdÃ¼rmÉ iÅlÉt
scroll_horizontal_label=ÃfÃ¼qi sÃ¼rÃ¼ÅdÃ¼rmÉ
scroll_wrapped.title=BÃ¼kÃ¼lÃ¼ sÃ¼rÃ¼ÅdÃ¼rmÉ iÅlÉt
scroll_wrapped_label=BÃ¼kÃ¼lÃ¼ sÃ¼rÃ¼ÅdÃ¼rmÉ

spread_none.title=Yan-yana birlÉÅdirilmiÅ sÉhifÉlÉri iÅlÉtmÉ
spread_none_label=BirlÉÅdirmÉ
spread_odd.title=Yan-yana birlÉÅdirilmiÅ sÉhifÉlÉri tÉk nÃ¶mrÉli sÉhifÉlÉrdÉn baÅlat
spread_odd_label=TÉk nÃ¶mrÉli
spread_even.title=Yan-yana birlÉÅdirilmiÅ sÉhifÉlÉri cÃ¼t nÃ¶mrÉli sÉhifÉlÉrdÉn baÅlat
spread_even_label=CÃ¼t nÃ¶mrÉli

# Document properties dialog box
document_properties.title=SÉnÉd xÃ¼susiyyÉtlÉriâ¦
document_properties_label=SÉnÉd xÃ¼susiyyÉtlÉriâ¦
document_properties_file_name=Fayl adÄ±:
document_properties_file_size=Fayl Ã¶lÃ§Ã¼sÃ¼:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bayt)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bayt)
document_properties_title=BaÅlÄ±k:
document_properties_author=MÃ¼Éllif:
document_properties_subject=MÃ¶vzu:
document_properties_keywords=AÃ§ar sÃ¶zlÉr:
document_properties_creation_date=YaradÄ±lÄ±Å Tarixi :
document_properties_modification_date=DÉyiÅdirilmÉ Tarixi :
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Yaradan:
document_properties_producer=PDF yaradÄ±cÄ±sÄ±:
document_properties_version=PDF versiyasÄ±:
document_properties_page_count=SÉhifÉ sayÄ±:
document_properties_page_size=SÉhifÉ ÃlÃ§Ã¼sÃ¼:
document_properties_page_size_unit_inches=inÃ§
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=portret
document_properties_page_size_orientation_landscape=albom
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=MÉktub
document_properties_page_size_name_legal=HÃ¼quqi
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Fast Web View:
document_properties_linearized_yes=BÉli
document_properties_linearized_no=Xeyr
document_properties_close=Qapat

print_progress_message=SÉnÉd Ã§ap Ã¼Ã§Ã¼n hazÄ±rlanÄ±râ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=LÉÄv et

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Yan Paneli AÃ§/BaÄla
toggle_sidebar_notification2.title=Yan paneli Ã§evir (sÉnÉddÉ icmal/baÄlamalar/laylar mÃ¶vcuddur)
toggle_sidebar_label=Yan Paneli AÃ§/BaÄla
document_outline.title=SÉnÉdin eskizini gÃ¶stÉr (bÃ¼tÃ¼n bÉndlÉri aÃ§maq/yÄ±Ämaq Ã¼Ã§Ã¼n iki dÉfÉ kliklÉyin)
document_outline_label=SÉnÉd strukturu
attachments.title=BaÄlamalarÄ± gÃ¶stÉr
attachments_label=BaÄlamalar
layers.title=LaylarÄ± gÃ¶stÉr (bÃ¼tÃ¼n laylarÄ± ilkin halÄ±na sÄ±fÄ±rlamaq Ã¼Ã§Ã¼n iki dÉfÉ kliklÉyin)
layers_label=Laylar
thumbs.title=KiÃ§ik ÅÉkillÉri gÃ¶stÉr
thumbs_label=KiÃ§ik ÅÉkillÉr
findbar.title=SÉnÉddÉ Tap
findbar_label=Tap

additional_layers=ÆlavÉ laylar
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=SÉhifÉ{{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas={{page}} sÉhifÉsinin kiÃ§ik vÉziyyÉti

# Find panel button title and messages
find_input.title=Tap
find_input.placeholder=SÉnÉddÉ tapâ¦
find_previous.title=Bir Ã¶ncÉki uyÄun gÉlÉn sÃ¶zÃ¼ tapÄ±r
find_previous_label=Geri
find_next.title=Bir sonrakÄ± uyÄun gÉlÉn sÃ¶zÃ¼ tapÄ±r
find_next_label=Ä°rÉli
find_highlight=Ä°ÅarÉlÉ
find_match_case_label=BÃ¶yÃ¼k/kiÃ§ik hÉrfÉ hÉssaslÄ±q
find_entire_word_label=Tam sÃ¶zlÉr
find_reached_top=SÉnÉdin yuxarÄ±sÄ±na Ã§atdÄ±, aÅaÄÄ±dan davam edir
find_reached_bottom=SÉnÉdin sonuna Ã§atdÄ±, yuxarÄ±dan davam edir
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} / {{total}} uyÄunluq
find_match_count[two]={{current}} / {{total}} uyÄunluq
find_match_count[few]={{current}} / {{total}} uyÄunluq
find_match_count[many]={{current}} / {{total}} uyÄunluq
find_match_count[other]={{current}} / {{total}} uyÄunluq
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]={{limit}}-dan Ã§ox uyÄunluq
find_match_count_limit[one]={{limit}}-dÉn Ã§ox uyÄunluq
find_match_count_limit[two]={{limit}}-dÉn Ã§ox uyÄunluq
find_match_count_limit[few]={{limit}} uyÄunluqdan daha Ã§ox
find_match_count_limit[many]={{limit}} uyÄunluqdan daha Ã§ox
find_match_count_limit[other]={{limit}} uyÄunluqdan daha Ã§ox
find_not_found=UyÄunlaÅma tapÄ±lmadÄ±

# Error panel labels
error_more_info=Daha Ã§ox mÉlumati
error_less_info=Daha az mÉlumat
error_close=Qapat
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (yÄ±Äma: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Ä°smarÄ±c: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stek: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Fayl: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=SÉtir: {{line}}
rendering_error=SÉhifÉ gÃ¶stÉrilÉrkÉn sÉhv yarandÄ±.

# Predefined zoom values
page_scale_width=SÉhifÉ geniÅliyi
page_scale_fit=SÉhifÉni sÄ±ÄdÄ±r
page_scale_auto=Avtomatik yaxÄ±nlaÅdÄ±r
page_scale_actual=HazÄ±rkÄ± HÉcm
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

loading_error=PDF yÃ¼klenÉrkÉn bir sÉhv yarandÄ±.
invalid_file_error=SÉhv vÉ ya zÉdÉlÉnmiÅ olmuÅ PDF fayl.
missing_file_error=PDF fayl yoxdur.
unexpected_response_error=GÃ¶zlÉnilmÉz server cavabÄ±.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} AnnotasiyasÄ±]
password_label=Bu PDF faylÄ± aÃ§maq Ã¼Ã§Ã¼n parolu daxil edin.
password_invalid=Parol sÉhvdir. Bir daha yoxlayÄ±n.
password_ok=Tamam
password_cancel=LÉÄv et

printing_not_supported=XÉbÉrdarlÄ±q: Ãap bu sÉyyah tÉrÉfindÉn tam olaraq dÉstÉklÉnmir.
printing_not_ready=XÉbÉrdarlÄ±q: PDF Ã§ap Ã¼Ã§Ã¼n tam yÃ¼klÉnmÉyib.
web_fonts_disabled=Web ÅriftlÉr sÃ¶ndÃ¼rÃ¼lÃ¼b: yerlÉÅdirilmiÅ PDF ÅriftlÉrini istifadÉ etmÉk mÃ¼mkÃ¼n deyil.

