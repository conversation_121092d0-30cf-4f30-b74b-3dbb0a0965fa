# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Ù¾ÚÚ¾ÙØ§ ÙØ±ÙÛ
previous_label=Ù¾ÚÚ¾ÙØ§
next.title=Ø§Ú³ÙØ§ ÙØ±ÙÛ
next_label=Ø§Ú³ÙØ§

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=ÙØ±ÙÛ
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}} Ø¯Ø§
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} Ø¯Ø§ {{pagesCount}})

zoom_out.title=Ø²ÙÙ Ø¢Ø¤Ù¹
zoom_out_label=Ø²ÙÙ Ø¢Ø¤Ù¹
zoom_in.title=Ø²ÙÙ Ø§ÙÙ
zoom_in_label=Ø²ÙÙ Ø§ÙÙ
zoom.title=Ø²ÙÙ
presentation_mode.title=Ù¾Ø±ÛØ²ÙÙ¹ÛØ´Ù ÙÙÚ ØªÛ Ø³ÙØ¦Ú Ú©Ø±Ù
presentation_mode_label=Ù¾Ø±ÛØ²ÙÙ¹ÛØ´Ù ÙÙÚ
open_file.title=ÙØ§Ø¦Ù Ú©Ú¾ÙÙÙ
open_file_label=Ú©Ú¾ÙÙÙ
print.title=ÚÚ¾Ø§Ù¾Ù
print_label=ÚÚ¾Ø§Ù¾Ù
download.title=ÚØ§Ø¤Ù ÙÙÚ 
download_label=ÚØ§Ø¤Ù ÙÙÚ
save.title=ÛØªÚ¾ÛÚ©ÚØ§ Ú©Ø±Ù
save_label=ÛØªÚ¾ÛÚ©ÚØ§ Ú©Ø±Ù
bookmark.title=Ø­Ø§ÙÛÛ ÙØ¸Ø§Ø±Û (ÙÙÛÚº ÙÙÚÙ ÙÙÚ ÙÙÙ Ú©Ø±Ù ÛØ§ Ú©Ú¾ÙÙÙ)
bookmark_label=Ø­Ø§ÙÛÛ ÙØ¸Ø§Ø±Û

bookmark1.title=ÙÙØ¬ÙØ¯Û ÙØ±ÙÛ (ÙÙØ¬ÙØ¯Û ÙØ±ÙÛ Ú©ÙÙÚº ÛÙØ¢Ø±Ø§ÛÙ ÝÛÚ©Ú¾Ù)
bookmark1_label=ÙÙØ¬ÙØ¯Û ÙØ±ÙÛ

# Secondary toolbar and context menu
tools.title=Ø§ÙØ²Ø§Ø±
tools_label=Ø§ÙØ²Ø§Ø±
first_page.title=Ù¾ÛÙÛ ÙØ±ÙÛ ØªÛ ÙÙÚÙ
first_page_label=Ù¾ÛÙÛ ÙØ±ÙÛ ØªÛ ÙÙÚÙ
last_page.title=ÚÚ¾ÛÚ©ÚÛ ÙØ±ÙÛ ØªÛ ÙÙÚÙ
last_page_label=ÚÚ¾ÛÚ©ÚÛ ÙØ±ÙÛ ØªÛ ÙÙÚÙ
page_rotate_cw.title=Ú¯Ú¾ÚÛ ÙØ§ÙÚ¯ÙÚº Ú¯Ú¾ÙØ§Ø¤
page_rotate_cw_label=Ú¯Ú¾ÚÛ ÙØ§ÙÚ¯ÙÚº Ú¯Ú¾ÙØ§Ø¤
page_rotate_ccw.title=Ú¯Ú¾ÚÛ ØªÛ Ø§ÙÙ¾Ù¹Ú¾ Ú¯Ú¾ÙØ§Ø¤
page_rotate_ccw_label=Ú¯Ú¾ÚÛ ØªÛ Ø§ÙÙ¾Ù¹Ú¾ Ú¯Ú¾ÙØ§Ø¤

cursor_text_select_tool.title=ÙØªÙ ÙÙØªØ®Ø¨ Ú©Ý¨ ÙØ§ÙØ§ Ø¢ÙÛ ÙØ¹Ø§Ù Ø¨Ý¨Ø§Ø¤
cursor_text_select_tool_label=ÙØªÙ ÙÙØªØ®Ø¨ Ú©Ø±Ý¨ ÙØ§ÙØ§ Ø¢ÙÛ
cursor_hand_tool.title=ÛÛÙÚ Ù¹ÙÙ ÙØ¹Ø§Ù Ø¨Ý¨Ø§Ø¤
cursor_hand_tool_label=ÛÛÙÚ Ù¹ÙÙ

scroll_page.title=Ù¾ÛØ¬ Ø³Ú©Ø±ÙÙÙÚ¯ Ø§Ø³ØªØ¹ÙØ§Ù Ú©Ø±Ù
scroll_page_label=Ù¾ÛØ¬ Ø³Ú©Ø±ÙÙÙÚ¯
scroll_vertical.title=Ø¹ÙÙØ¯Û Ø³Ú©Ø±ÙÙÙÚ¯ Ø§Ø³ØªØ¹ÙØ§Ù Ú©Ø±Ù
scroll_vertical_label=Ø¹ÙÙØ¯Û Ø³Ú©Ø±ÙÙÙÚ¯
scroll_horizontal.title=Ø§ÙÙÛ Ø³Ú©Ø±ÙÙÙÚ¯ Ø§Ø³ØªØ¹ÙØ§Ù Ú©Ø±Ù
scroll_horizontal_label=Ø§ÙÙÛ Ø³Ú©Ø±ÙÙÙÚ¯
scroll_wrapped.title=ÙÛÚÚ¾Û ÛÙØ¦Û Ø³Ú©Ø±ÙÙÙÚ¯ Ø§Ø³ØªØ¹ÙØ§Ù Ú©Ø±Ù
scroll_wrapped_label=ÙÛÚÚ¾Û ÛÙØ¦Û Ø³Ú©Ø±ÙÙÙÚ¯

spread_none.title=Ù¾ÛØ¬ Ø³Ù¾Ø±ÛÚØ² ÙÙÚ Ø´Ø§ÙÙ ÙÛ ØªÚ¾ÛÙÙÛ
spread_none_label=Ú©ÙØ¦Û Ù¾ÙÙÚ¾ Ú©Ø§Ø¦ÙÛ
spread_odd.title=Ø·Ø§Ù ÙÙØ¨Ø± ÙØ§ÙÛ ÙØ±ÙÛØ§Úº Ø¯Û ÙØ§Ù Ø´Ø±ÙØ¹ ØªÚ¾ÛÙÝ¨ ÙØ§ÙÛ Ù¾ÛØ¬ Ø³Ù¾Ø±ÛÚØ² ÙÙÚ Ø´Ø§ÙÙ ØªÚ¾ÛÙÙÛ
spread_odd_label=ØªØ§Ú© Ù¾Ú¾ÛÙØ§Ø¤
spread_even.title=Ø¬ÙØª ÙÙØ± ÙØ§ÙÛ ÙØ±ÙÛØ§Úº ÙØ§Ù Ø´Ø±ÙØ¹ ØªÚ¾ÛÙÝ¨ ÙØ§ÙÛ Ù¾ÛØ¬ Ø³Ù¾Ø±ÛÚØ² ÙÙ Ø´Ø§ÙÙ ØªÚ¾ÛÙÙÛ
spread_even_label=Ø¬ÙØª Ù¾Ú¾ÛÙØ§Ø¤

# Document properties dialog box
document_properties.title=Ø¯Ø³ØªØ§ÙÛØ² Ø®ÙØ§Øµâ¦
document_properties_label=Ø¯Ø³ØªØ§ÙÛØ² Ø®ÙØ§Øµ â¦
document_properties_file_name=ÙØ§Ø¦Ù Ø¯Ø§ ÙØ§Úº:
document_properties_file_size=ÙØ§Ø¦Ù Ø¯Ø§ Ø³Ø§Ø¦Ø²:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} Ú©Û Ø¨Û ({{size_b}} Ø¨Ø§Ø¦Ù¹Ø³)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} Ø§ÛÙ Ø¨Û ({{size_b}} Ø¨Ø§Ø¦Ù¹Ø³)
document_properties_title=Ø¹ÙÙØ§Ù:
document_properties_author=ØªØ®ÙÛÙ Ú©Ø§Ø±:
document_properties_subject=ÙÙØ¶ÙØ¹:
document_properties_keywords=Ú©ÙÛØ¯Û Ø§ÙÙØ§Ø¸:
document_properties_creation_date=ØªØ®ÙÛÙ Ø¯Û ØªØ§Ø±ÛØ®:
document_properties_modification_date=ØªØ±ÙÛÙ Ø¯Û ØªØ§Ø±ÛØ®:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=ØªØ®ÙÛÙ Ú©Ø§Ø±:
document_properties_producer=PDF Ù¾ÛØ¯Ø§ Ú©Ø§Ø±:
document_properties_version=PDF ÙØ±ÚÙ:
document_properties_page_count=ÙØ±ÙÛ Ø´ÙØ§Ø±Û:
document_properties_page_size=ÙØ±ÙÛ Ø¯Û Ø³Ø§Ø¦Ø²:
document_properties_page_size_unit_inches=ÙÙÚ
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=Ø¹ÙÙØ¯Û Ø§ÙØ¯Ø§Ø²
document_properties_page_size_orientation_landscape=Ø§ÙÙÙ Ø§ÙØ¯Ø§Ø²
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=ÙÛÙ¹Ø±
document_properties_page_size_name_legal=ÙÙÙÙÛ
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=ØªÚ©Ú¾Ø§ ÙÛØ¨ ÙØ¸Ø§Ø±Û:
document_properties_linearized_yes=Ø¬ÛØ§
document_properties_linearized_no=Ú©Ù
document_properties_close=Ø¨ÙØ¯ Ú©Ø±Ù

print_progress_message=ÚÚ¾Ø§Ù¾Ý¨ Ú©ÛØªÛ Ø¯Ø³ØªØ§ÙÛØ² ØªÛØ§Ø± ØªÚ¾ÛÙØ¯Û Ù¾Ø¦Û ÛÙ â¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ÙÙØ³ÙØ® Ú©Ø±Ù

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Ø³Ø§Ø¦ÛÚ Ø¨Ø§Ø± Ù¹ÙÚ¯Ù Ú©Ø±Ù
toggle_sidebar_notification2.title=Ø³Ø§Ø¦ÛÚ Ø¨Ø§Ø± Ù¹ÙÚ¯Ù Ú©Ø±Ù (Ø¯Ø³ØªØ§ÙÛØ² ÙÙÚ Ø¢Ø¤Ù¹ ÙØ§Ø¦Ù/ ÙÙØ³ÙÚ©Ø§Øª/ Ù¾Ø±ØªØ§Úº Ø´Ø§ÙÙ ÛÙ)
toggle_sidebar_label=Ø³Ø§Ø¦ÛÚ Ø¨Ø§Ø± Ù¹ÙÚ¯Ù Ú©Ø±Ù
document_outline.title=Ø¯Ø³ØªØ§ÙÛØ² Ø¯Ø§ Ø®Ø§Ú©Û ÝÚ©Ú¾Ø§Ø¤ (ØªÙØ§Ù Ø¢Ø¦Ù¹ÙØ² Ú©ÙÚº Ù¾Ú¾ÛÙØ§ÙÝ¨/Ø³ÙÚ¯ÙÚÝ¨ Ú©ÛØªÛ ÚØ¨Ù Ú©ÙÚ© Ú©Ø±Ù)
document_outline_label=Ø¯Ø³ØªØ§ÙÛØ² Ø¢Ø¤Ù¹ ÙØ§Ø¦Ù
attachments.title=ÙØªÚ¾ÛØ§Úº ÝÚ©Ú¾Ø§Ø¤
attachments_label=ÙÙØ³ÙÚ©Ø§Øª
layers.title=Ù¾Ø±ØªØ§Úº ÝÚ©Ú¾Ø§Ø¤ (ØªÙØ§Ù Ù¾Ø±ØªØ§Úº Ú©ÙÚº ÚÛÙØ§ÙÙ¹ Ø­Ø§ÙØª ÙÙÚ Ø¯ÙØ¨Ø§Ø±Û ØªØ±ØªÛØ¨ ÝÛÙÝ¨ Ú©ÛØªÛ ÚØ¨Ù Ú©ÙÚ© Ú©Ø±Ù)
layers_label=Ù¾Ø±ØªØ§Úº
thumbs.title=ØªÚ¾ÙØ¨ÙÛÙ ÝÚ©Ú¾Ø§Ø¤
thumbs_label=ØªÚ¾ÙØ¨ÙÛÙØ²
current_outline_item.title=ÙÙØ¬ÙØ¯Û Ø¢Ø¤Ù¹ ÙØ§Ø¦Ù Ø¢Ø¦Ù¹Ù ÙØ¨Ú¾Ù
current_outline_item_label=ÙÙØ¬ÙØ¯Û Ø¢Ø¤Ù¹ ÙØ§Ø¦Ù Ø¢Ø¦Ù¹Ù
findbar.title=Ø¯Ø³ØªØ§ÙÛØ² ÙÙÚ ÙØ¨Ú¾Ù
findbar_label=ÙØ¨Ú¾Ù

additional_layers=Ø§Ø¶Ø§ÙÛ Ù¾Ø±ØªØ§Úº
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=ÙØ±ÙÛ {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=ÙØ±ÙÛ {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=ÙØ±ÙÛ Ø¯Ø§ ØªÚ¾ÙØ¨ÙÛÙ {{page}}

# Find panel button title and messages
find_input.title=ÙØ¨Ú¾Ù
find_input.placeholder=Ø¯Ø³ØªØ§ÙÛØ² ÙÙÚ ÙØ¨Ú¾Ù â¦
find_previous.title=ÙÙØ±Û Ø¯Ø§ Ù¾ÚÚ¾ÙØ§ ÙØ§ÙØ¹Û ÙØ¨Ú¾Ù
find_previous_label=Ù¾ÚÚ¾ÙØ§
find_next.title=ÙÙØ±Û Ø¯Ø§ Ø§Ú³ÙØ§ ÙØ§ÙØ¹Û ÙØ¨Ú¾Ù
find_next_label=Ø§Ú³ÙØ§
find_highlight=ØªÙØ§Ù ÙØ´Ø§Ø¨Ø± Ú©Ø±Ù
find_match_case_label=Ø­Ø±ÙÙ ÙØ´Ø§Ø¨Û Ú©Ø±Ù
find_match_diacritics_label=ÚØ§Ø¦ÛÚ©Ø±Ù¹Ú©Ø³ ÙØ´Ø§Ø¨Û Ú©Ø±Ù
find_entire_word_label=ØªÙØ§Ù Ø§ÙÙØ§Ø¸
find_reached_top=ÙØ±ÙÛ Ø¯Û Ø´Ø±ÙØ¹ ØªÛ Ù¾ÙØ¬ Ú³ÛØ§Ø ØªÙÙÚº Ø¬Ø§Ø±Û Ú©ÛØªØ§ Ú³ÛØ§
find_reached_bottom=ÙØ±ÙÛ Ø¯Û Ù¾Ø§ÙØ¯ ØªÛ Ù¾ÙÚ Ú³ÛØ§Ø Ø§ÙØªÙÚº Ø´Ø±ÙØ¹ Ú©ÛØªØ§ Ú³ÛØ§
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ Ø¬ÙØ¹ (Ú©Ù) ]}
find_match_count[one]={{current}} Ø¯Ø§ {{total}} ÙÙØ§Ø²ÙÛ Ú©Ø±Ù
find_match_count[two]={{current}} Ø¯Ø§ {{total}} ÙÙØ§Ø²ÙÛ
find_match_count[few]={{current}} Ø¯Ø§ {{total}} ÙÙØ§Ø²ÙÛ
find_match_count[many]={{current}} Ø¯Ø§ {{total}} ÙÙØ§Ø²ÙÛ
find_match_count[other]={{current}} Ø¯Ø§ {{total}} ÙÙØ§Ø²ÙÛ
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ Ø¬ÙØ¹ (Ø­Ø¯) ]}
find_match_count_limit[zero]={{limit}} Ú©ÙÙÚº Ø²ÛØ§Ø¯Û ÙÙØ§Ø«ÙØªØ§ÚºÛ
find_match_count_limit[one]={{limit}}  ÙÙØ§Ø«Ù Ú©ÙÙÚº ÙØ¯Ú¾
find_match_count_limit[two]={{limit}} Ú©ÙÙÚº Ø²ÛØ§Ø¯Û ÙÙØ§Ø«ÙØªØ§ÚºÛ
find_match_count_limit[few]={{limit}}  ÙÙØ§Ø«ÙØ§Úº Ú©ÙÙÚº ÙØ¯Ú¾
find_match_count_limit[many]={{limit}}  ÙÙØ§Ø«ÙØ§Úº Ú©ÙÙÚº ÙØ¯Ú¾
find_match_count_limit[other]={{limit}}  ÙÙØ§Ø«ÙØ§Úº Ú©ÙÙÚº ÙØ¯Ú¾
find_not_found=ÙÙØ±Û ÙØ¦ÛÚº ÙÙÛØ§

# Error panel labels
error_more_info=ÙØ¯Ú¾ÛÚ© ÙØ¹ÙÙÙØ§Øª
error_less_info=Ú¯Ú¾Ù¹ ÙØ¹ÙÙÙØ§Øª
error_close=Ø¨ÙØ¯ Ú©Ø±Ù
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (Ø¨Ý¨Ø§Ø¤: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Ø³ÙÛÛØ§: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Ø³Ù¹ÛÚ©: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ÙØ§Ø¦Ù: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=ÙØ§Ø¦Ù: {{line}}

# Predefined zoom values
page_scale_width=ÙØ±ÙÛ Ø¯Û ÚÙÚØ§Ø¦Û
page_scale_fit=ÙØ±ÙÛ ÙÙ¹ÙÚ¯
page_scale_auto=Ø¢Ù¾ÙÚº Ø¢Ù¾ Ø²ÙÙ
page_scale_actual=Ø§ØµÙ ÙÛÚØ§
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading=ÙÙÚ ØªÚ¾ÛÙØ¯Ø§ Ù¾Ø¦ÛÛÛÛ
loading_error=PDF ÙÙÚ Ú©Ø±ÛÙØ¯Û ÙÛÙÚ¾Û ÙÙØµ Ø¢ Ú³ÛØ§Û
invalid_file_error=ØºÙØ· ÛØ§ Ø®Ø±Ø§Ø¨ Ø´Ø¯Û PDF ÙØ§Ø¦ÙÛ
missing_file_error=PDF ÙØ§Ø¦Ù ØºØ§Ø¦Ø¨ ÛÛÛ
unexpected_response_error=Ø³Ø±ÙØ± Ø¯Ø§ ØºÛØ± ÙØªÙÙØ¹ Ø¬ÙØ§Ø¨Û

rendering_error=ÙØ±ÙÛ Ø±ÛÙÚØ± Ú©Ø±ÛÙØ¯Û ÙÛÙÚ¾Û ÛÚ© Ø®Ø±Ø§Ø¨Û Ù¾ÛØ´ Ø¢Ú³Ø¦ÛÛ

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} ØªØ´Ø±ÛØ­]
password_label=Ø§ÛÛ PDF ÙØ§Ø¦Ù Ú©Ú¾ÙÙÝ¨ Ú©ÛØªÛ Ù¾Ø§Ø³ ÙØ±Ú Ø¯Ø±Ø¬ Ú©Ø±ÙÛ
password_invalid=ØºÙØ· Ù¾Ø§Ø³ ÙØ±Ú: Ø¨Ø±Ø§Û ÙÛØ±Ø¨Ø§ÙÛ ÙÙØ¯Ø§ Ú©ÙØ´Ø´ Ú©Ø±ÙÛ
password_ok=Ù¹Ú¾ÛÚ© ÛÛ
password_cancel=ÙÙØ³ÙØ® Ú©Ø±Ù

printing_not_supported=ÚØªØ§ÙÝ¨Û: ÚÚ¾Ù¾Ø§Ø¦Û Ø§ÛÚº Ø¨Ø±Ø§Ø¤Ø²Ø± ØªÛ Ù¾ÙØ±Û Ø·Ø±Ø§Úº ÙØ¹Ø§ÙÙØª Ø´Ø¯Û Ú©Ø§Ø¦ÙÛÛ
printing_not_ready=ÚØªØ§ÙÝ¨Û: PDF ÚÚ¾Ù¾Ø§Ø¦Û Ú©ÛØªÛ Ù¾ÙØ±Û Ø·Ø±Ø§Úº ÙÙÚ ÙØ¦ÛÚº ØªÚ¾Ø¦ÛÛ
web_fonts_disabled=ÙÛØ¨ ÙÙÙÙ¹Ø³ ØºÛØ± ÙØ¹Ø§Ù ÛÙ: Ø§ÛÙØ¨ÛÚÚ PDF  ÙÙÙÙ¹Ø³ Ø§Ø³ØªØ¹ÙØ§Ù Ú©Ø±Ý¨ Ú©ÙÙÚº ÙØ§ØµØ± ÛÙ

# Editor
editor_free_text2.title=ÙØªÙ
editor_free_text2_label=ÙØªÙ
editor_ink2.title=ÚÚ¾Ú©Ù
editor_ink2_label=ÚÚ¾Ú©Ù

free_text2_default_content=Ù¹Ø§Ø¦Ù¾ÙÚ¯ Ø´Ø±ÙØ¹ Ú©Ø±Ù â¦

# Editor Parameters
editor_free_text_color=Ø±ÙÚ¯
editor_free_text_size=Ø³Ø§Ø¦Ø²
editor_ink_color=Ø±ÙÚ¯
editor_ink_thickness=Ù¹Ú¾ÙÙÚ¾
editor_ink_opacity=Ø¯Ú¾ÙØ¯ÙØ§Ù¾Ù

# Editor aria
editor_free_text2_aria_label=Ù¹ÛÚ©Ø³Ù¹ Ø§ÛÚÛÙ¹Ø±
editor_ink2_aria_label=ÚØ±Ø§ Ø§ÛÚÛÙ¹Ø±
editor_ink_canvas_aria_label=ØµØ§Ø±Ù Ø¯Û Ø¨Ý¨Ø§Ø¦Û ÛÙØ¦Û ØªØµÙÛØ±
