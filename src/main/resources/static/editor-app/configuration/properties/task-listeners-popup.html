<div class="modal" ng-controller="KisBpmTaskListenersPopupCtrl">
    <div class="modal-dialog modal-wide">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-hidden="true" class="close" data-dismiss="modal" ng-click="close()" type="button">&times;
                </button>
                <h2>{{'PROPERTY.PROPERTY.EDIT.TITLE' | translate:property}}</h2>
            </div>
            <div class="modal-body">

                <div class="row row-no-gutter">
                    <div class="col-xs-6">
                        <div class="kis-listener-grid" ng-grid="gridOptions" ng-if="translationsRetrieved"></div>
                        <div class="pull-right">
                            <div class="btn-group">
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{ACTION.MOVE.UP | translate}}" ng-click="moveListenerUp()" rel="tooltip"
                                   title=""><i class="glyphicon glyphicon-arrow-up"></i></a>
                                <a class="btn btn-icon btn-lg" data-original-title=""
                                   data-placement="bottom" data-title="{{ACTION.MOVE.DOWN | translate}}"
                                   ng-click="moveListenerDown()" rel="tooltip" title=""><i
                                        class="glyphicon glyphicon-arrow-down"></i></a>
                            </div>
                            <div class="btn-group">
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{ACTION.ADD | translate}}" ng-click="addNewListener()" rel="tooltip"
                                   title=""><i class="glyphicon glyphicon-plus"></i></a>
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{ACTION.REMOVE | translate}}" ng-click="removeListener()" rel="tooltip"
                                   title=""><i class="glyphicon glyphicon-minus"></i></a>
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-6">
                        <div ng-show="selectedListeners.length > 0">

                            <div class="form-group">
                                <label for="eventField">{{'PROPERTY.TASKLISTENERS.EVENT' | translate}}</label>
                                <select class="form-control" id="eventField" ng-model="selectedListeners[0].event">
                                    <option>create</option>
                                    <option>assignment</option>
                                    <option>complete</option>
                                    <option>delete</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="classField">{{'PROPERTY.TASKLISTENERS.CLASS' | translate}}</label>
                                <input class="form-control" id="classField" ng-change="listenerDetailsChanged()"
                                       ng-model="selectedListeners[0].className" placeholder="{{'PROPERTY.TASKLISTENERS.CLASS.PLACEHOLDER' | translate}}"
                                       type="text"/>
                            </div>
                            <div class="form-group">
                                <label for="expressionField">{{'PROPERTY.TASKLISTENERS.EXPRESSION' | translate}}</label>
                                <input class="form-control" id="expressionField" ng-change="listenerDetailsChanged()"
                                       ng-model="selectedListeners[0].expression" placeholder="{{'PROPERTY.TASKLISTENERS.EXPRESSION.PLACEHOLDER' | translate}}"
                                       type="text"/>
                            </div>
                            <div class="form-group">
                                <label for="delegateExpressionField">{{'PROPERTY.TASKLISTENERS.DELEGATEEXPRESSION' | translate}}</label>
                                <input class="form-control" id="delegateExpressionField" ng-change="listenerDetailsChanged()"
                                       ng-model="selectedListeners[0].delegateExpression"
                                       placeholder="{{'PROPERTY.TASKLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER' | translate}}"
                                       type="text"/>
                            </div>
                        </div>
                        <div class="muted no-property-selected" ng-show="selectedListeners.length == 0" translate>
                            PROPERTY.TASKLISTENERS.UNSELECTED
                        </div>
                    </div>
                </div>

                <div class="row row-no-gutter">
                    <div class="col-xs-6">
                        <div class="kis-field-grid" ng-grid="gridFieldOptions" ng-if="translationsRetrieved"></div>
                        <div class="pull-right">
                            <div class="btn-group">
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{ACTION.MOVE.UP | translate}}" ng-click="moveFieldUp()" rel="tooltip" title=""><i
                                        class="glyphicon glyphicon-arrow-up"></i></a>
                                <a class="btn btn-icon btn-lg" data-original-title=""
                                   data-placement="bottom" data-title="{{ACTION.MOVE.DOWN | translate}}"
                                   ng-click="moveFieldDown()" rel="tooltip" title=""><i
                                        class="glyphicon glyphicon-arrow-down"></i></a>
                            </div>
                            <div class="btn-group">
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{ACTION.ADD | translate}}" ng-click="addNewField()" rel="tooltip" title=""><i
                                        class="glyphicon glyphicon-plus"></i></a>
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{ACTION.REMOVE | translate}}" ng-click="removeField()" rel="tooltip" title=""><i
                                        class="glyphicon glyphicon-minus"></i></a>
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-6">
                        <div ng-show="selectedFields.length > 0">

                            <div class="form-group">
                                <label for="nameField">{{'PROPERTY.TASKLISTENERS.FIELDS.NAME' | translate}}</label>
                                <input class="form-control" id="nameField" ng-model="selectedFields[0].name" placeholder="{{'PROPERTY.TASKLISTENERS.FIELDS.NAME.PLACEHOLDER' | translate}}"
                                       type="text"/>
                            </div>
                            <div class="form-group">
                                <label for="stringValueField">{{'PROPERTY.TASKLISTENERS.FIELDS.STRINGVALUE' | translate}}</label>
                                <input class="form-control" id="stringValueField" ng-change="fieldDetailsChanged()"
                                       ng-model="selectedFields[0].stringValue" placeholder="{{'PROPERTY.TASKLISTENERS.FIELDS.STRINGVALUE.PLACEHOLDER' | translate}}"
                                       type="text"/>
                            </div>
                            <div class="form-group">
                                <label for="expressionField">{{'PROPERTY.TASKLISTENERS.FIELDS.EXPRESSION' | translate}}</label>
                                <input class="form-control" id="expressionField" ng-change="fieldDetailsChanged()"
                                       ng-model="selectedFields[0].expression" placeholder="{{'PROPERTY.TASKLISTENERS.FIELDS.EXPRESSION.PLACEHOLDER' | translate}}"
                                       type="text"/>
                            </div>
                            <div class="form-group">
                                <label for="stringField">{{'PROPERTY.TASKLISTENERS.FIELDS.STRING' | translate}}</label>
                                <textarea class="form-control" id="stringField" ng-change="fieldDetailsChanged()"
                                          ng-model="selectedFields[0].string"
                                          placeholder="{{'PROPERTY.TASKLISTENERS.FIELDS.STRING.PLACEHOLDER' | translate}}"></textarea>
                            </div>

                        </div>
                        <div class="muted no-property-selected" ng-show="selectedFields.length == 0" translate>
                            PROPERTY.TASKLISTENERS.FIELDS.EMPTY
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" ng-click="cancel()" translate>ACTION.CANCEL</button>
                <button class="btn btn-primary" ng-click="save()" translate>ACTION.SAVE</button>
            </div>
        </div>
    </div>
</div>
