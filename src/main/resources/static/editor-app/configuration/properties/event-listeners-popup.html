<div class="modal" ng-controller="KisBpmEventListenersPopupCtrl">
    <div class="modal-dialog modal-wide">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-hidden="true" class="close" data-dismiss="modal" ng-click="close()" type="button">&times;
                </button>
                <h2>{{'PROPERTY.PROPERTY.EDIT.TITLE' | translate:property}}</h2>
            </div>
            <div class="modal-body">

                <div class="row row-no-gutter">
                    <div class="col-xs-10">
                        <div class="kis-listener-grid" ng-grid="gridOptions" ng-if="translationsRetrieved"></div>
                        <div class="pull-right">
                            <div class="btn-group">
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{ACTION.MOVE.UP | translate}}" ng-click="moveListenerUp()" rel="tooltip"
                                   title=""><i class="glyphicon glyphicon-arrow-up"></i></a>
                                <a class="btn btn-icon btn-lg" data-original-title=""
                                   data-placement="bottom" data-title="{{ACTION.MOVE.DOWN | translate}}"
                                   ng-click="moveListenerDown()" rel="tooltip" title=""><i
                                        class="glyphicon glyphicon-arrow-down"></i></a>
                            </div>
                            <div class="btn-group">
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{ACTION.ADD | translate}}" ng-click="addNewListener()" rel="tooltip"
                                   title=""><i class="glyphicon glyphicon-plus"></i></a>
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{ACTION.REMOVE | translate}}" ng-click="removeListener()" rel="tooltip"
                                   title=""><i class="glyphicon glyphicon-minus"></i></a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row row-no-gutter">
                    <div class="col-xs-6" ng-if="translationsRetrieved" ng-show="selectedListeners.length > 0">
                        <div class="form-group">
                            <label for="userField">{{'PROPERTY.EVENTLISTENERS.EVENTS' | translate}}</label>
                            <div ng-repeat="eventDefinition in selectedListeners[0].events">
                                <select class="form-control" id="eventField" ng-change="listenerDetailsChanged()"
                                        ng-model="eventDefinition.event">
                                    <option title="{{'EVENT_TYPE.ACTIVITY.COMPENSATE.TOOLTIP' | translate}}">
                                        ACTIVITY_COMPENSATE
                                    </option>
                                    <option title="{{'EVENT_TYPE.ACTIVITY.COMPLETED.TOOLTIP' | translate}}">
                                        ACTIVITY_COMPLETED
                                    </option>
                                    <option title="bla">ACTIVITY_ERROR_RECEIVED</option>
                                    <option>ACTIVITY_MESSAGE_RECEIVED</option>
                                    <option>ACTIVITY_SIGNALED</option>
                                    <option>ACTIVITY_STARTED</option>
                                    <option>ENGINE_CLOSED</option>
                                    <option>ENGINE_CREATED</option>
                                    <option>ENTITY_ACTIVATED</option>
                                    <option>ENTITY_CREATED</option>
                                    <option>ENTITY_DELETED</option>
                                    <option>ENTITY_INITIALIZED</option>
                                    <option>ENTITY_SUSPENDED</option>
                                    <option>ENTITY_UPDATED</option>
                                    <option>JOB_EXECUTION_FAILURE</option>
                                    <option>JOB_EXECUTION_SUCCESS</option>
                                    <option>JOB_RETRIES_DECREMENTED</option>
                                    <option title="{{'EVENT_TYPE.MEMBERSHIP.CREATED.TOOLTIP' | translate}}">
                                        MEMBERSHIP_CREATED
                                    </option>
                                    <option title="{{'EVENT_TYPE.MEMBERSHIP.DELETED.TOOLTIP' | translate}}">
                                        MEMBERSHIP_DELETED
                                    </option>
                                    <option title="{{'EVENT_TYPE.MEMBERSHIPS.DELETED.TOOLTIP' | translate}}">
                                        MEMBERSHIPS_DELETED
                                    </option>
                                    <option title="{{'EVENT_TYPE.TASK.ASSIGNED.TOOLTIP' | translate}}">TASK_ASSIGNED
                                    </option>
                                    <option title="{{'EVENT_TYPE.TASK.COMPLETED.TOOLTIP' | translate}}">TASK_COMPLETED
                                    </option>
                                    <option>TIMER_FIRED</option>
                                    <option title="{{'EVENT_TYPE.UNCAUGHT.BPMNERROR.TOOLTIP' | translate}}">
                                        UNCAUGHT_BPMN_ERROR
                                    </option>
                                    <option title="{{'EVENT_TYPE.VARIABLE.CREATED.TOOLTIP' | translate}}">
                                        VARIABLE_CREATED
                                    </option>
                                    <option title="{{'EVENT_TYPE.VARIABLE.DELETED.TOOLTIP' | translate}}">
                                        VARIABLE_DELETED
                                    </option>
                                    <option title="{{'EVENT_TYPE.VARIABLE.UPDATED.TOOLTIP' | translate}}">
                                        VARIABLE_UPDATED
                                    </option>
                                </select>
                                <i class="glyphicon glyphicon-minus clickable-property" ng-click="removeEventValue($index)"
                                   ng-if="$index > 0"></i>
                                <i class="glyphicon glyphicon-plus clickable-property"
                                   ng-click="addEventValue($index)"></i>
                            </div>
                            <div class="form-group">
                                <label for="classField">{{'PROPERTY.EVENTLISTENERS.RETHROW' | translate}}</label>
                                <input class="form-control" id="rethrowField" ng-change="listenerDetailsChanged()"
                                       ng-model="selectedListeners[0].rethrowEvent"
                                       type="checkbox"/>
                            </div>
                        </div>
                    </div>
                    <div class="col-xs-6"
                         ng-show="selectedListeners.length > 0 && selectedListeners[0].events[0].event">
                        <div class="form-group" ng-if="!selectedListeners[0].rethrowEvent">
                            <label for="classField">{{'PROPERTY.EVENTLISTENERS.CLASS' | translate}}</label>
                            <input class="form-control" id="classField" ng-change="listenerDetailsChanged()"
                                   ng-model="selectedListeners[0].className" placeholder="{{'PROPERTY.EVENTLISTENERS.CLASS.PLACEHOLDER' | translate}}"
                                   type="text"/>
                        </div>
                        <div class="form-group" ng-if="!selectedListeners[0].rethrowEvent">
                            <label for="delegateExpressionField">{{'PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION' | translate}}</label>
                            <input class="form-control" id="delegateExpressionField" ng-change="listenerDetailsChanged()"
                                   ng-model="selectedListeners[0].delegateExpression"
                                   placeholder="{{'PROPERTY.EVENTLISTENERS.DELEGATEEXPRESSION.PLACEHOLDER' | translate}}"
                                   type="text"/>
                        </div>
                        <div class="form-group" ng-if="!selectedListeners[0].rethrowEvent">
                            <label for="entityTypeField">{{'PROPERTY.EVENTLISTENERS.ENTITYTYPE' | translate}}</label>
                            <input class="form-control" id="entityTypeField" ng-change="listenerDetailsChanged()"
                                   ng-model="selectedListeners[0].entityType" placeholder="{{'PROPERTY.EVENTLISTENERS.ENTITYTYPE.PLACEHOLDER' | translate}}"
                                   type="text"/>
                        </div>
                        <div class="form-group" ng-if="selectedListeners[0].rethrowEvent">
                            <label for="delegateExpressionField">{{'PROPERTY.EVENTLISTENERS.RETHROWTYPE' | translate}}</label>
                            <select class="form-control" id="rethrowTypeField"
                                    ng-change="rethrowTypeChanged()" ng-model="selectedListeners[0].rethrowType">
                                <option>error</option>
                                <option>message</option>
                                <option>signal</option>
                                <option>globalSignal</option>
                            </select>
                        </div>
                        <div class="form-group" ng-if="selectedListeners[0].rethrowType === 'error'">
                            <label for="errorCodeField">{{'PROPERTY.EVENTLISTENERS.ERRORCODE' | translate}}</label>
                            <input class="form-control" id="errorCodeField" ng-change="listenerDetailsChanged()"
                                   ng-model="selectedListeners[0].errorcode" placeholder="{{'PROPERTY.EVENTLISTENERS.ERRORCODE.PLACEHOLDER' | translate}}"
                                   type="text"/>
                        </div>
                        <div class="form-group" ng-if="selectedListeners[0].rethrowType === 'message'">
                            <label for="messageNameField">{{'PROPERTY.EVENTLISTENERS.MESSAGENAME' | translate}}</label>
                            <input class="form-control" id="messageNameField" ng-change="listenerDetailsChanged()"
                                   ng-model="selectedListeners[0].messagename" placeholder="{{'PROPERTY.EVENTLISTENERS.MESSAGENAME.PLACEHOLDER' | translate}}"
                                   type="text"/>
                        </div>
                        <div class="form-group"
                             ng-if="selectedListeners[0].rethrowType === 'signal' || selectedListeners[0].rethrowType === 'globalSignal'">
                            <label for="messageNameField">{{'PROPERTY.EVENTLISTENERS.SIGNALNAME' | translate}}</label>
                            <input class="form-control" id="signalNameField" ng-change="listenerDetailsChanged()"
                                   ng-model="selectedListeners[0].signalname" placeholder="{{'PROPERTY.EVENTLISTENERS.SIGNALNAME.PLACEHOLDER' | translate}}"
                                   type="text"/>
                        </div>
                    </div>
                    <div class="col-xs-6 muted no-property-selected" ng-show="selectedListeners.length == 0" translate>
                        PROPERTY.EVENTLISTENERS.UNSELECTED
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" ng-click="cancel()" translate>ACTION.CANCEL</button>
                <button class="btn btn-primary" ng-click="save()" translate>ACTION.SAVE</button>
            </div>
        </div>
    </div>
</div>