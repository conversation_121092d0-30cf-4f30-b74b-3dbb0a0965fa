<div class="modal" ng-controller="KisBpmFieldsPopupCtrl">
    <div class="modal-dialog modal-wide">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-hidden="true" class="close" data-dismiss="modal" ng-click="close()" type="button">&times;
                </button>
                <h3>{{'PROPERTY.PROPERTY.EDIT.TITLE' | translate:property}}</h3>
            </div>
            <div class="modal-body">

                <div class="row row-no-gutter">
                    <div class="col-xs-6">
                        <div class="kis-listener-grid" ng-grid="gridOptions" ng-if="translationsRetrieved"></div>
                        <div class="pull-right">
                            <div class="btn-group">
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{'ACTION.MOVE.UP' | translate}}" href="#"
                                   ng-click="moveFieldUp()" rel="tooltip" title=""><i
                                        class="glyphicon glyphicon-arrow-up"></i></a>
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{'ACTION.MOVE.DOWN' | translate}}" href="#"
                                   ng-click="moveFieldDown()" rel="tooltip" title=""><i
                                        class="glyphicon glyphicon-arrow-down"></i></a>
                            </div>
                            <div class="btn-group">
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{'ACTION.ADD' | translate}}" href="#"
                                   ng-click="addNewField()" rel="tooltip" title=""><i
                                        class="glyphicon glyphicon-plus"></i></a>
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{'ACTION.REMOVE' | translate}}" href="#"
                                   ng-click="removeField()" rel="tooltip" title=""><i
                                        class="glyphicon glyphicon-minus"></i></a>
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-6">
                        <div ng-show="selectedFields.length > 0">

                            <div class="form-group">
                                <label for="fieldName">{{'PROPERTY.FIELDS.NAME' | translate}}</label>
                                <input class="form-control" id="fieldName" ng-model="selectedFields[0].name" placeholder="{{'PROPERTY.FIELDS.NAME.PLACEHOLDER' | translate}}"
                                       type="text"/>
                            </div>

                            <div class="form-group">
                                <label for="fieldStringValue">{{'PROPERTY.FIELDS.STRINGVALUE' | translate}}</label>
                                <input class="form-control" id="fieldStringValue" ng-change="fieldDetailsChanged()"
                                       ng-model="selectedFields[0].stringValue" placeholder="{{'PROPERTY.FIELDS.STRINGVALUE.PLACEHOLDER' | translate}}"
                                       type="text"/>
                            </div>

                            <div class="form-group">
                                <label for="fieldExpression">{{'PROPERTY.FIELDS.EXPRESSION' | translate}}</label>
                                <input class="form-control" id="fieldExpression" ng-change="fieldDetailsChanged()"
                                       ng-model="selectedFields[0].expression" placeholder="{{'PROPERTY.FIELDS.EXPRESSION.PLACEHOLDER' | translate}}"
                                       type="text"/>
                            </div>

                            <div class="form-group">
                                <label for="fieldString">{{'PROPERTY.FIELDS.STRING' | translate}}</label>
                                <textarea class="form-control" id="fieldString" ng-change="fieldDetailsChanged()"
                                          ng-model="selectedFields[0].string" placeholder="{{'PROPERTY.FIELDS.STRING.PLACEHOLDER' | translate}}"
                                          type="text"></textarea>
                            </div>

                        </div>
                        <div class="muted no-property-selected" ng-show="selectedFields.length == 0" translate>
                            PROPERTY.FIELDS.EMPTY
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" ng-click="cancel()" translate>ACTION.CANCEL</button>
                <button class="btn btn-primary" ng-click="save()" translate>ACTION.SAVE</button>
            </div>
        </div>
    </div>
</div>
