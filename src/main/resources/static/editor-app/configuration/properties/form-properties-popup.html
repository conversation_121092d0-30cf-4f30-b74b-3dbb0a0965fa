<div class="modal" ng-controller="KisBpmFormPropertiesPopupCtrl">
    <div class="modal-dialog modal-wide">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-hidden="true" class="close" data-dismiss="modal" ng-click="close()" type="button">&times;
                </button>
                <h2>{{'PROPERTY.PROPERTY.EDIT.TITLE' | translate:property}}</h2>
            </div>
            <div class="modal-body">

                <div class="row row-no-gutter">
                    <div class="col-xs-6">
                        <div class="default-grid" ng-grid="gridOptions" ng-if="translationsRetrieved"></div>
                        <div class="pull-right">
                            <div class="btn-group">
                                <a class="btn btn-icon btn-lg" data-original-title=""
                                   data-placement="bottom" data-title="{{'ACTION.MOVE.UP' | translate}}"
                                   ng-click="movePropertyUp()" rel="tooltip" title=""><i
                                        class="glyphicon glyphicon-arrow-up"></i></a>
                                <a class="btn btn-icon btn-lg" data-original-title=""
                                   data-placement="bottom" data-title="{{'ACTION.MOVE.DOWN' | translate}}"
                                   ng-click="movePropertyDown()" rel="tooltip" title=""><i
                                        class="glyphicon glyphicon-arrow-down"></i></a>
                            </div>
                            <div class="btn-group">
                                <a class="btn btn-icon btn-lg" data-original-title="" data-placement="bottom"
                                   data-title="{{'ACTION.ADD' | translate}}" ng-click="addNewProperty()" rel="tooltip"
                                   title=""><i class="glyphicon glyphicon-plus"></i></a>
                                <a class="btn btn-icon btn-lg" data-original-title=""
                                   data-placement="bottom" data-title="{{'ACTION.REMOVE' | translate}}"
                                   ng-click="removeProperty()" rel="tooltip" title=""><i
                                        class="glyphicon glyphicon-minus"></i></a>
                            </div>
                        </div>
                    </div>

                    <div class="col-xs-6">
                        <div ng-show="selectedProperties.length > 0">

                            <div class="form-group">
                                <label for="idField">{{'PROPERTY.FORMPROPERTIES.ID' | translate}}</label>
                                <input class="form-control" id="idField" ng-model="selectedProperties[0].id" placeholder="{{'PROPERTY.FORMPROPERTIES.ID.PLACEHOLDER' | translate }}"
                                       type="text"/>
                            </div>
                            <div class="form-group">
                                <label for="nameField">{{'PROPERTY.FORMPROPERTIES.NAME' | translate}}</label>
                                <input class="form-control" id="nameField" ng-model="selectedProperties[0].name"
                                       placeholder="{{'PROPERTY.FORMPROPERTIES.NAME.PLACEHOLDER' | translate }}"
                                       type="text"/>
                            </div>
                            <div class="form-group">
                                <label for="typeField">{{'PROPERTY.FORMPROPERTIES.TYPE' | translate}}</label>
                                <select class="form-control" id="typeField" ng-change="propertyTypeChanged()"
                                        ng-model="selectedProperties[0].type">
                                    <option>string</option>
                                    <option>long</option>
                                    <option>boolean</option>
                                    <option>date</option>
                                    <option>enum</option>
                                </select>
                            </div>
                            <div class="form-group" ng-show="selectedProperties[0].datePattern">
                                <label for="datePatternField">{{'PROPERTY.FORMPROPERTIES.DATEPATTERN' | translate}}</label>
                                <input class="form-control" id="datePatternField" ng-model="selectedProperties[0].datePattern"
                                       placeholder="{{'PROPERTY.FORMPROPERTIES.DATEPATTERN.PLACEHOLDER' | translate }}"
                                       type="text"/>
                            </div>
                            <div ng-if="selectedProperties[0].type == 'enum'" style="padding-bottom:10px">
                                <div class="row row-no-gutter">
                                    <div class="col-xs-6">
                                        <div class="kis-listener-grid" ng-grid="enumGridOptions"
                                             ng-if="translationsRetrieved"></div>
                                        <div class="pull-right">
                                            <div class="btn-group">
                                                <a class="btn btn-icon btn-lg" data-original-title=""
                                                   data-placement="bottom" data-title="{{ACTION.MOVE.UP | translate}}"
                                                   ng-click="moveEnumValueUp()" rel="tooltip" title=""><i
                                                        class="glyphicon glyphicon-arrow-up"></i></a>
                                                <a class="btn btn-icon btn-lg" data-original-title=""
                                                   data-placement="bottom" data-title="{{ACTION.MOVE.DOWN | translate}}"
                                                   ng-click="moveEnumValueDown()" rel="tooltip" title=""><i
                                                        class="glyphicon glyphicon-arrow-down"></i></a>
                                            </div>
                                            <div class="btn-group">
                                                <a class="btn btn-icon btn-lg" data-original-title=""
                                                   data-placement="bottom" data-title="{{ACTION.ADD | translate}}"
                                                   ng-click="addNewEnumValue()" rel="tooltip" title=""><i
                                                        class="glyphicon glyphicon-plus"></i></a>
                                                <a class="btn btn-icon btn-lg" data-original-title=""
                                                   data-placement="bottom" data-title="{{ACTION.REMOVE | translate}}"
                                                   ng-click="removeEnumValue()" rel="tooltip" title=""><i
                                                        class="glyphicon glyphicon-minus"></i></a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-xs-6">
                                        <div ng-show="selectedEnumValues.length > 0">

                                            <div class="form-group">
                                                <label for="classField">{{'PROPERTY.FORMPROPERTIES.VALUES.ID' | translate}}</label>
                                                <input class="form-control" id="classField" ng-model="selectedEnumValues[0].id"
                                                       placeholder="{{'PROPERTY.FORMPROPERTIES.VALUES.ID.PLACEHOLDER' | translate}}"
                                                       type="text"/>
                                            </div>
                                            <div class="form-group">
                                                <label for="classField">{{'PROPERTY.FORMPROPERTIES.VALUES.NAME' | translate}}</label>
                                                <input class="form-control" id="classField" ng-model="selectedEnumValues[0].name"
                                                       placeholder="{{'PROPERTY.FORMPROPERTIES.VALUES.NAME.PLACEHOLDER' | translate}}"
                                                       type="text"/>
                                            </div>
                                        </div>
                                        <div class="muted no-property-selected" ng-show="selectedEnumValues.length == 0"
                                             translate>PROPERTY.FORMPROPERTIES.ENUMVALUES.EMPTY
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="expressionField">{{'PROPERTY.FORMPROPERTIES.EXPRESSION' | translate}}</label>
                                <input class="form-control" id="expressionField" ng-model="selectedProperties[0].expression"
                                       placeholder="{{'PROPERTY.FORMPROPERTIES.EXPRESSION.PLACEHOLDER' | translate }}"
                                       type="text"/>
                            </div>
                            <div class="form-group">
                                <label for="variableField">{{'PROPERTY.FORMPROPERTIES.VARIABLE' | translate}}</label>
                                <input class="form-control" id="variableField" ng-model="selectedProperties[0].variable"
                                       placeholder="{{'PROPERTY.FORMPROPERTIES.VARIABLE.PLACEHOLDER' | translate }}"
                                       type="text"/>
                            </div>
                            <div class="form-inline">
                                <div class="form-group col-xs-2">
                                    <label for="requiredField">{{'PROPERTY.FORMPROPERTIES.REQUIRED' | translate}}</label>
                                    <input class="form-control" id="requiredField" ng-model="selectedProperties[0].required"
                                           type="checkbox"/>
                                </div>
                                <div class="form-group col-xs-2">
                                    <label for="readableField">{{'PROPERTY.FORMPROPERTIES.READABLE' | translate}}</label>
                                    <input class="form-control" id="readableField" ng-model="selectedProperties[0].readable"
                                           type="checkbox"/>
                                </div>
                                <div class="form-group col-xs-2">
                                    <label for="writableField">{{'PROPERTY.FORMPROPERTIES.WRITABLE' | translate}}</label>
                                    <input class="form-control" id="writableField" ng-model="selectedProperties[0].writable"
                                           type="checkbox"/>
                                </div>
                            </div>
                        </div>
                        <div class="muted no-property-selected" ng-show="selectedProperties.length == 0" translate>
                            PROPERTY.FORMPROPERTIES.EMPTY
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" ng-click="cancel()" translate>ACTION.CANCEL</button>
                <button class="btn btn-primary" ng-click="save()" translate>ACTION.SAVE</button>
            </div>
        </div>
    </div>
</div>
