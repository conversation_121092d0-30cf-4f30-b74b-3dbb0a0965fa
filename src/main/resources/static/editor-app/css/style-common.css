/**
 Colors:

  - Header: #333333
  - Subheader: #e8edf1
  - Subheader border: #a4acb9
  - Highlight buttons/text: #36a7c4
  - Text color: #1a1a1a
  - Filter color: #373e48
  - Dark highlight: #606b7d
*/

.form-control {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    padding: 4px 8px;
}

body {
    font-family: 'Lato', sans-serif;
}

.label {
    font-size: 18px;
    font-weight: normal;
    margin-bottom: 0;
    color: #1a1a1a;
}

.subtle {
    color: #999999;
    font-size: 13px;
}


.simple-list {
    list-style: none inside;
    padding: 0;
    margin: 5px 0;
}

.simple-list.pack {
    max-height: 250px;
    overflow: auto;
}

.simple-list li {
    padding: 6px;
    position: relative;
}

.simple-list li > .icon {
    padding-right: 5px;
}

.simple-list li:hover {
    background-color: #f8f8f9;
}

.simple-list li.nothing-to-see:hover {
    background-color: transparent;
}

.simple-list li.active {
    background-color: #eeeeee;
}

.simple-list li > .actions {
    visibility: hidden;
    position: absolute;
    top: 3px;
    right: 5px;
    font-size: 20px;
    background-color: #f8f8f9;
    padding: 0 0 0 4px;
}

.simple-list li > .actions a {
    padding: 4px 4px 0 4px;
}

.simple-list li > .actions a:hover {
    background-color: #ffffff;
}

.simple-list li:hover > .actions {
    visibility: visible;
}

.simple-list.grid li {
    border-bottom: 1px solid #eeeeee;
}

.simple-list.grid li:first-child {
    border-top: 1px solid #eeeeee;
}

.simple-list li .subtle {
    color: #999999;
    font-size: 13px;
}

.simple-list.selectable li {
    cursor: pointer;
}

.simple-list .loading {
    position: absolute;
    left: 50%;
    margin-left: -15px;
    line-height: 30px;
    top: 8px;
    z-index: 1030;
}


.lt-ie9 .container {
    display: none !important;
    visibility: hidden !important;
}

.unsupported-browser {
    margin: 60px 20px 20px 20px;
}

a {
    cursor: pointer;
}

a:hover {
    text-decoration: none;
}

label {
    font-weight: normal;
    color: #636363;
    font-size: 14px;
}

[ng\:cloak], [ng-cloak], .ng-cloak {
    display: none;
}

.nothing-to-see {
    padding: 5px 0 20px 0;
    cursor: default;
}

.nothing-to-see span {
    font-size: 14px;
    color: #aaaaaa;
}

.fixed-container {
    max-width: 1400px;
    min-width: 1000px;
    margin: 0 auto;
}

.well {
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
}

/** Buttons **/
button.btn, a.btn {
    background-color: #36a7c4;
    color: #ffffff;
    border-color: #ffffff;
    font-size: 15px;
}

button.btn-subtle, a.btn-subtle {
    background-color: #fafafb;
    color: #1a1a1a;
    font-size: 15px;
}

.btn-xs {
    padding: 1px 8px;
}

button.btn.btn-danger {
    background-color: #d35f5f;
}

.btn.btn-danger:hover, .btn.btn-danger.active, .btn.btn-danger:focus {
    background-color: #c83737;
}

.btn:hover, .btn.active, .btn:focus {
    background-color: #2a8198;
    border-color: #ffffff;
    color: #ffffff;
}

.btn.disabled, .btn[disabled], .btn[disabled]:active, .btn[disabled]:hover {
    background-color: #668b94;
    border-color: #668b94;
    color: #ffffff;
}

.btn-subtle:hover, .btn-subtle.active, .btn-subtle:focus {
    background-color: #f6f6f7;
    border-color: #ffffff;
    color: #000000;
}

.btn-subtle[disabled] {
    background-color: #f6f6f7;
    color: #555555;
}

.modal-header .btn, .header .btn {
    border-color: #e8edf1;
}

.content {
    padding: 0 10px;
    overflow: auto;
}

.content.split {
    background: transparent url('../../images/line-1px.png') repeat-y 60% 0;
}

.content .split-left {
    float: left;
    width: 60%;
    padding: 0 10px 0 5px;
}

.content .split-right {
    float: right;
    width: 40%;
    padding: 0 0 0 15px;
}


.form-group .pull-right {
    margin: 10px 0 0 5px;
}

.form-group.box {
    padding-bottom: 10px;
    margin-bottom: 5px;
    border-bottom: 1px dotted #eeeeee;
}

.form-group .marker {
    font-size: 15px;
    color: #666666;
}

/** Dropdowns and dropdown triggers */

.dropdown-menu {
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    box-shadow: none;
}

.dropdown-menu:focus {
    outline: none;
}

.dropdown-menu > li > a:hover, .dropdown-menu > ul > li > a:hover {
    background: #36a7c4;
    color: #ffffff;
}


.dropdown-menu > li.active > a, .dropdown-menu > li.active > a:hover {
    background: #e8edf1;
    color: #1a1a1a;
}

.dropdown-menu > ul > li > a {
    display: block;
    text-decoration: none;
    color: #1a1a1a;
    padding: 5px;
    cursor: pointer;
}

.dropdown-menu > ul {
    padding: 10px;
}

.dropdown-menu.large-width {
    min-width: 300px;
}

a.dropdown-toggle {
    color: #1a1a1a;
    text-decoration: none;
}

.open a.dropdown-toggle, a.dropdown-toggle:hover {
    color: #36a7c4;
}

.btn-group.open .dropdown-toggle {
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

/** Subtle dropdown (eg. sort) */

.dropdown-subtle {
    margin-right: 5px;
    color: #606b7d;
}

.dropdown-subtle .btn {
    background: transparent;
    line-height: 36px;
    color: #606b7d;
    padding: 0;
    font-size: 14px;
    border: none;
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.dropdown-subtle .btn-group.open .dropdown-toggle {
    box-shadow: none;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
}

.dropdown-subtle .btn:hover, .dropdown-subtle .btn:focus {
    background: transparent;
    color: #333333;
}

.dropdown-subtle a {
    cursor: pointer;
}

/** Popovers */
.popover {
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    border-radius: 0;
    max-width: 400px;
    min-width: 300px;
    padding: 0px;
}

.popover.bottom-left, .popover.bottom-right {
    margin-top: 10px;
}

.popover > .arrow, .popover > .arrow {
    margin-left: -11px;
    border-top-width: 0;
    border-bottom-color: #999;
    border-bottom-color: rgba(0, 0, 0, .25);
    top: -11px;

}

.popover.bottom-left > .arrow {
    left: 40px;
}

.popover.bottom-right > .arrow {
    right: 40px;
}

.popover.bottom-left > .arrow:after, .popover.bottom-right > .arrow:after, .popover.bottom > .arrow:after {
    content: " ";
    top: 1px;
    margin-left: -10px;
    border-top-width: 0;
    border-bottom-color: #e8edf1;
}

.popover-wrapper {
    padding: 10px;
}

.popover-header {
    position: relative;
    background-color: #e8edf1;
    min-height: 30px;
    font-size: 18px;
    color: #a4acb9;
    padding: 10px 0;
}

.popover-footer {
    overflow: hidden;
    clear: both;
    padding: 5px 10px 10px 10px;
}

.popover-header .actions {
    position: absolute;
    top: 6px;
    right: 5px;
    font-size: 12px;
}

.popover-header .actions a {
    display: inline-block;
    padding: 8px 5px;
}

.popover-header span {
    padding: 0 10px;
}

.popover-wrapper .form-group {
    margin-bottom: 10px;
}

.popover.wide {
    max-width: 1000px;
    min-width: 1000px;
}

.popover.wide .popover-wrapper {
    max-height: 400px;
    overflow: auto;
}

.popover.medium {
    max-width: 600px;
    min-width: 250px;
}

.popover .section {
    border-top: 1px solid #eeeeee;
}

.center {
    text-align: center;
}

.popover .center .btn, .popover .center .btn-group > .btn:hover, .popover .center .btn-group > .btn:focus {
    border-color: #ffffff;
}

/* Navigation */

.navbar {
    background-color: #333333;
    border: none;
    min-height: 40px;
}


.navbar .btn-group .btn-default {
    border: none;
    color: #ffffff;
    background-color: transparent;
    padding-top: 0px;
    padding-bottom: 0px;
    line-height: 40px;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    font-size: 13px;
}

.navbar .btn-group .btn-default:hover, .navbar .btn-group .btn-default:focus {
    background-color: #121212;
}

.navbar .btn-group .btn-default {
    border: none;
    color: #ffffff;
    background-color: transparent;
}

.navbar .btn-group .btn-icon {
    font-size: 22px;
}


.navbar-header .navbar-brand {
    padding-top: 0px;
    line-height: 40px;
    height: 40px;
    background: url(../images/logo.png) no-repeat 10px center;
    width: 180px;
}


.navbar-nav {
    height: 40px;
}

.navbar-nav > li > a {
    line-height: 20px;
    padding: 10px;
    font-size: 17px;
    padding: 10px 35px 10px 35px;
    color: #ffffff;
}

.navbar-nav > li.active:after {
    top: 100%;
    left: 50%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(0, 0, 0, 0);
    border-top-color: #000000;
    border-width: 6px;
    margin-left: -6px;
}

.navbar-nav > li.active {
    background-color: #000000;
    position: relative;
}


.navbar-nav > li.active > a {
    color: #36a7c4;
}

/* Sub header */
.subheader {
    background-color: #e8edf1;
    min-height: 60px;
    border-bottom: 1px solid #a4acb9;
}

.subheader > div > .btn-group {
    margin: 12px 15px 0px 0px;
}

.subheader h2 {
    font-family: 'Lato', sans-serif;
    color: #1a1a1a;
    font-size: 20px;
    font-weight: normal;
    padding: 19px 0px 5px 10px;
    margin-top: 0px;
}

.subheader .version {
    font-weight: bold;
    color: #36a7c4;
    font-size: 110%;
    padding-left: 5px;
    line-height: 1;
    padding-right: 5px;
    border-right: 1px solid #a4acb9;
    margin-right: 5px;
}

.subheader .btn {
    border-color: #e8edf1;
}

.subheader a.btn:hover, .subheader a.btn:focus {
    border-color: #e8edf1;
    color: #ffffff;
}

.subheader .dropdown-menu .detail {
    vertical-align: middle;
    color: #1a1a1a;
}

.subheader p {
    font-size: 14px;
    color: #1a1a1a;
    word-wrap: break-word;
}

.subheader p.hint a {
    cursor: pointer;
    color: #1a1a1a;
}

.subheader .details.subheader .details {
    margin-bottom: 5px;
    margin-left: -1px;
    border-right: 1px solid #a4acb9;
    border-left: 1px solid #a4acb9;
    padding: 0px 15px 5px 15px;
}

.subheader .details:first-child {
    border-left: none;
}

.subheader .details:last-child {
    border-right: none;
}

.subheader .details > span, span.detail {
    font-size: 13px;
    display: block;
    padding-bottom: 5px;
}

.subheader .details p {
    font-size: 13px;
}

.subheader .related {
    float: right;
    margin: 0 -10px 10px 10px;
}

.subheader .details span i, span.detail i {
    font-size: 90%;
    padding-right: 8px;
}

.subheader > div > .pull-right {
    margin-top: 12px;
    margin-right: 5px;
}

.subheader a.action {
    color: #1a1a1a;
    margin-right: 10px;
    line-height: 36px;
    text-decoration: underline;
    font-size: 14px;
}

.subheader a.action i {
    text-decoration: none;
    font-style: normal;
}

.subheader a:hover {
    color: #606b7d;
}

.subheader .highlight {
    color: #ffeeaa;
}


/** Custom icons **/

.icon {
    position: relative;
    top: 1px;
    display: inline-block;
    font-family: 'cherokeeregular';
    font-style: normal;
    font-weight: 400;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-user-add:before {
    content: "\e008";
}

.icon-comment-add:before {
    content: "\e111";
}

.icon-diagram:before {
    content: "\e011";
}

.icon-caret-down:before {
    content: "\e114";
}

.icon-caret-left:before {
    content: "\e115";
}

.icon-caret-right:before {
    content: "\e116";
}

.icon-remove:before {
    content: "\e117";
}

.icon-pencil:before {
    content: "\270f";
}

.icon-caret-up:before {
    content: "\e118";
}

.icon-user:before {
    content: "\e119";
}

.icon-choice:before {
    content: "\e120";
}

.icon-move:before {
    content: "\e121";
}

.icon-mail:before {
    content: "\e122";
}

.icon-clock:before {
    content: "\e123";
}

.icon-download:before {
    content: "\e124";
}

.icon-word:before {
    content: "\e125";
}

.icon-excel:before {
    content: "\e126";
}

.icon-powerpoint:before {
    content: "\e127";
}

.icon-pdf:before {
    content: "\e128";
}

.icon-content:before {
    content: "\e129";
}

.icon-folder:before {
    content: "\e130";
}

.icon-image:before {
    content: "\e131";
}

.icon-bpmn-stencil:before {
    content: "\e132";
}

.icon-kickstart-stencil:before {
    content: "\e133";
}

.icon-form-stencil:before {
    content: "\e134";
}

.simple-list .icon-image, .related-content .icon-image {
    color: #484b84;
}

.simple-list .icon-pdf, .related-content .icon-pdf {
    color: #ac2020;
}

.simple-list .icon-powerpoint, .related-content .icon-powerpoint {
    color: #dc5b31;
}

.simple-list .icon-excel, .related-content .icon-excel {
    color: #13743d;
}

.simple-list .icon-word, .related-content .icon-word {
    color: #2974b8;
}

.simple-list .icon-content, .related-content .icon-content {
    color: #666666;
}

.loading {
    margin: 0px 15px;
    text-align: center;
    line-height: 34px;
}

.loading > div {
    width: 10px;
    height: 10px;
    background-color: #9fd7e5;
    margin: 1px;

    border-radius: 100%;
    display: inline-block;
    -webkit-animation: bouncedelay 1.4s infinite ease-in-out;
    animation: bouncedelay 1.4s infinite ease-in-out;
    /* Prevent first frame from flickering when animation starts */
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

.loading .l1 {
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.loading .l2 {
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

.loading-box {
    text-align: center;
    margin: 50px auto 10px auto;
    padding: 20px 50px;
    max-width: 400px;
}

.loading-box span {
    font-size: 16px;
    color: #333333;
}


@-webkit-keyframes bouncedelay {
    0%, 80%, 100% {
        -webkit-transform: scale(0.0)
    }
    40% {
        -webkit-transform: scale(1.0)
    }
}

@keyframes bouncedelay {
    0%, 80%, 100% {
        transform: scale(0.0);
        -webkit-transform: scale(0.0);
    }
    40% {
        transform: scale(1.0);
        -webkit-transform: scale(1.0);
    }
}

/** Alerts */
.alert-wrapper {

}

.alert-wrapper {
    position: fixed;
    top: 40px;
    left: 0;
    right: 0;
    z-index: 1010;
}

.alert-wrapper.no-header {
    top: 0px;
}

.alert {
    text-align: center;
    width: 100%;
    min-height: 20px;
    background-color: #eef4d7;
    background-color: rgba(238, 244, 215, .7);
    padding: 8px 10px;
    cursor: pointer;
    border: none;
    border-bottom: 1px solid #bcd35f;

    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;

    -webkit-transition: all .5s ease;
    -moz-transition: all .5s ease;
    -o-transition: all .5s ease;
    transition: all .5s ease;
}

.alert.ng-hide-remove {
    opacity: 1;
    display: block !important;
}


.alert.ng-hide {
    opacity: 0;
}

.alert .badge {
    background-color: #bcd35f;
    color: #ffffff;
    font-size: 12px;
    margin-top: 2px;
    margin-left: 10px;
}


.alert .glyphicon {
    padding-right: 8px;
    color: #bcd35f;
}

.alert span {
    color: #445016;
    font-size: 15px;
}

.alert.error {
    background-color: #e9af9f;
    border-color: #e4593d;
    background-color: rgba(228, 89, 61, .7);
}

.alert.error .glyphicon {
    color: #e4593d;
}

.alert.error span {
    color: #471313;
}

.alert.error .badge {
    background-color: #e4593d;
    color: #ffffff;
}

.wrapper {
    padding: 55px 15px 15px 15px;
    max-width: 1400px;
    min-width: 1024px;
    margin: 0 auto;
}

.wrapper.full {
    padding: 40px 0px 0px 0px;
    overflow: hidden;
    max-width: 100%;
    min-width: 100%;
}

.wrapper.no-header {
    padding-top: 10px;
}

/** Main list **/
.main-list {
    position: relative;
    float: left;
    width: 400px;
    border: 1px solid #cccccc;
    background-color: #ffffff;
    margin-right: 20px;

    -webkit-box-shadow: 2px 2px 2px 0px rgba(220, 220, 220, 0.50);
    -moz-box-shadow: 2px 2px 2px 0px rgba(220, 220, 220, 0.50);
    box-shadow: 2px 2px 2px 0px rgba(220, 220, 220, 0.50);
}

.main-list .sort {
    position: absolute;
    top: 12px;
    right: 5px;
}

.list-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    background-color: rgba(255, 255, 255, .9);
    border-bottom: 1px solid #cccccc;

    -webkit-box-shadow: 0px 1px 1px 0px rgba(220, 220, 220, 0.65);
    -moz-box-shadow: 0px 1px 1px 0px rgba(220, 220, 220, 0.65);
    box-shadow: 0px 1px 1px 0px rgba(220, 220, 220, 0.65);
    z-index: 2;

}

.list-header .loading {
    position: absolute;
    left: 50%;
    margin-left: -15px;
    line-height: 30px;
}

.list-header .summary {
    cursor: pointer;
    padding: 10px 10px 10px 10px;
    min-height: 30px;
}

.list-header .summary > span {
    color: #373e48;
}

.list-header .summary .divider {
    content: '&bull';
    font-size: 70%;
    line-height: 1;
    font-style: normal;
    padding: 0 5px;
}

.list-header .form-group {
    margin-bottom: 10px;
    position: relative;
}

.selection {
    position: relative;
    margin: 0;
    padding: 6px 8px;

    border: 1px solid #cccccc;
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    background-color: #ffffff;

    font-size: 14px;

    cursor: pointer;
}

.selection .glyphicon, .selection .icon {
    padding-right: 5px;
}

.selection .actions {
    float: right;
}

.selection .actions.no-upload {
    float: left;
    margin-right: 10px;
}

.selection.narrow {
    padding: 0;
}

.selection .pull-right {
    margin: 4px 4px 4px 0;
}

.selection.narrow .simple-list {
    margin-bottom: 0;
    padding-bottom: 0;
}

.selection.narrow .simple-list li {
    border-top: 1px dotted #eeeeee;
}

.selection.narrow .simple-list li:first-child {
    border-top: none;
}


.selection.narrow .no-results {
    padding: 6px 0 0 5px;
}

.selection.narrow .details {
    margin: 5px;
    border: none;
}

.selection.narrow .label {
    font-size: 13px;
    padding: 0 10px 0 0;
    margin: 0;
    color: #666666;
}

.selection > .icon-caret-down {
    visibility: hidden;
    position: absolute;
    top: 8px;
    right: 5px;
}

.selection .empty {
    color: #666666;
}

.selection:hover > .icon-caret-down, button.selection:active > .icon-caret-down, button.selection:focus > .icon-caret-down {
    visibility: visible;
}

.selection[disabled]:hover > .icon-caret-down, button.selection[disabled]:active > .icon-caret-down, button[disabled].selection:focus > .icon-caret-down {
    visibility: hidden;
}

.selection[disabled] {
    background-color: #f6f6f7;
    color: #999999;
}

.selection + .dropdown-menu {
    width: 100%;
}

button.selection:active, button.selection:focus {
    outline: none;
    border-color: #acacac;
}

.selection.toggle {
    overflow: hidden;
    clear: both;
    padding: 0;
}

.selection.toggle .toggle-2 {
    width: 50%;
    float: left;
}


.selection.toggle .toggle-3 {
    width: 33.333%;
    float: left;
}

.selection.toggle .toggle-4 {
    width: 25%;
    float: left;
}

.selection.toggle .btn {
    border: none;
    border-right: 1px solid #bbbbbb;
    width: 100%;
    background-color: #eeeeee;
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
    border-radius: 0px;
    color: #666666;
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);

}

.selection.toggle .btn:active, .selection.toggle .btn:focus {
    outline: none;
    color: #1a1a1a;
    background-color: #f8f8f8;
}

.selection.toggle > .active .btn {
    background-color: #ffffff;
    color: #1a1a1a;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.selection.toggle > div:last-child .btn {
    border: none;
}

.subtle-select {
    padding: 6px 8px;
    background-color: transparent;
    color: #1a1a1a;
    text-decoration: none;
}

.subtle-select .icon {
    visibility: hidden;
}

.subtle-select:hover .icon {
    visibility: visible;
}

.subtle-select:hover, .header .detail a.subtle-select:hover {
    background-color: #ffffff;
    text-decoration: none;
    color: #1a1a1a;
}


.list-header .summary label, .list-header .summary .filter-action {
    font-size: 11px;
    font-weight: normal;
    text-transform: uppercase;
    margin-bottom: 0;
    color: #1a1a1a;
}

.list-wrapper {
    overflow: auto;
}


.main-list {
    height: 100%;
    overflow: hidden;
}

.main-list .nothing-to-see {
    text-align: center;
    padding: 50px 20px;
}

.main-list .nothing-to-see span {
    font-size: 17px;
}

.main-list .popover {
    width: 375px;
}

.list-header .summary .filter-action:hover {
    color: #36a7c4;
}

.main-list .list-subheader {
    margin-top: 40px;
    position: relative;
    z-index: 1;
    border-bottom: 1px solid #f2f2f2;
}

.main-list .list-subheader > .btn-group {
    margin: 10px 5px 10px 10px;
}

.full-list li.more {
    padding: 10px 15px;
    background-color: #ffffff;
    color: #666666;
}

.full-list li.more i.icon {
    font-size: 70%;
}

.full-list {
    list-style: none;
    padding: 0;
    margin-bottom: 0;
}

.full-list li {
    position: relative;
    display: block;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
    padding: 2px 0px 2px 0px;
}

.full-list li .badge, .simple-list li .badge {
    font-size: 12px;
    line-height: 12px;

    padding-right: 0;
    border-radius: 3px;
    background-color: #e8edf1;
    color: #36a7c4;
    background-color: transparent;
    font-weight: normal;

}


.full-list li.active {
    background-color: #fafafb;
}

.full-list li:hover {
    background-color: #fafafb;
}

.full-list li > div:hover {
    border-color: #d8dde1;
}

.full-list li > div {
    margin: 0 6px 0 4px;
    border-left: 4px solid #e8edf1;
    min-height: 50px;
    padding: 5px 5px 5px 5px;
}

.full-list li.active > div {
    border-left-color: #36a7c4;
}

.full-list li .title {
    font-size: 16px;
    margin: 0 0 0 5px;

    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.full-list li .summary {
    clear: both;
    margin: 3px 5px 0px 5px;
    font-size: 13px;
    color: #1a1a1a;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.full-list li .detail {
    margin: 0 5px;
    font-size: 12px;
    color: #999999;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

.main-content {
    border: 1px solid #cccccc;


    height: 100%;
    max-height: 100%;
    overflow: hidden;
    padding-bottom: 10px;
}

.main-content-wrapper {
    height: 100%;
    max-height: 100%;
    overflow: hidden;
    -webkit-box-shadow: 2px 2px 2px 0px rgba(220, 220, 220, 0.50);
    -moz-box-shadow: 2px 2px 2px 0px rgba(220, 220, 220, 0.50);
    box-shadow: 2px 2px 2px 0px rgba(220, 220, 220, 0.50);
}

.main-content > .header {
    background-color: #e8edf1;
    min-height: 60px;
    border-bottom: 1px solid #a4acb9;
    padding: 15px 15px;
}

.main-content > .header h2 {
    margin: 0 0 5px 0;
    font-size: 26px;
}

.main-content > .header .btn:hover, .main-content > .header .btn:focus {
    border-color: #e8edf1;
    color: #ffffff;
}

.modal-header .label, .header .label {
    padding: 0 3px 0 15px;
    color: #1a1a1a;
    font-weight: normal;
    font-size: 13px;
    color: #666666;
}

.header > .detail > .label:first-child {
    padding-left: 0;
}

.header .detail a {
    color: #1a1a1a;
}

.header .detail a:hover {
    color: #36a7c4;
    text-decoration: underline;
}

.jumpers {
    list-style: none inside;
    padding: 0 10px 10px 10px;
    margin: 5px 0px 0 0px;
    border-bottom: 1px solid #eeeeee;
}

.jumpers li {
    display: inline-block;
    border: 1px solid #e8edf1;
    margin: 5px 0 0 2px;
    padding: 5px 25px;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    cursor: pointer;
}

.jumpers li.pending {
    border: 1px dotted #d8dde1;
}

.jumpers li:hover {
    background-color: #f8f8f9;
}

.jumpers li.selected {
    color: white;
    background-color: #36a7c4;
}


.jumpers li span {
    background-color: #f2f2f2;
    padding: 1px 5px;
    margin-left: 5px;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    color: #999999;
}

.jumpers li:hover span {
    background-color: #e2e2e2;
}


.section {
    margin: 10px 0;
    padding: 10px 0 0px 0;
    min-height: 120px;
}

.section.pack {
    min-height: inherit;
}

.col-xs-12.seperator {
    height: 1px;
    border-top: 1px solid #eeeeee;
    margin: 5px 0;
}

.section > h3 {
    position: relative;
    margin: 0px;
    font-size: 18px;
    cursor: pointer;
}

.section > h3 .action > a {
    font-weight: bold;
    padding-left: 5px;
    color: #999999;
}


.section > .form-group, .section > div > .form-group {
    margin: 5px 0;
}

.modal-backdrop {
    background-color: #999999; /** Non alpha-supporting browser fallback */
    background-color: rgba(100, 100, 100, .75);
    background-image: url('../../images/glasspane.png');
}

.modal-content {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    border: none;
}

.modal-dialog.wide {
    margin-left: auto;
    margin-right: auto;
    width: 80%;
    max-width: 1200px;
}

.modal-body {
}


.modal .modal-header {
    position: relative;
    background-color: #e8edf1;
    min-height: 60px;
    border-bottom: 1px solid #a4acb9;
    padding: 15px 15px;
}

.modal-header .actions {
    margin-right: 20px;
}

.modal-body .form-actions {
    border-top: 1px solid #eeeeee;
    margin: 0 -30px;
    padding: 10px 10px 10px 10px;
}

.modal-body.includes-footer {
    padding-bottom: 0px;
}

.fullscreen .modal-header h3 {
    margin: 0 0 5px 0px;
    font-size: 22px;
}

.fullscreen .modal-header h3 .summary {
    font-size: 13px;
}