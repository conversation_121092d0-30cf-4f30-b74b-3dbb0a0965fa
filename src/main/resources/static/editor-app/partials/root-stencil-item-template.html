<span class="stencil-item root-stencil-item"
      data-drag="true"
      data-jqyoui-options="{revert: 'invalid', helper: 'clone', opacity : 0.5}"
      id="{{group.id}}"
      jqyoui-draggable="{onStart:'startDragCallback', onDrag:'dragCallback'}"
      ng-model="draggedElement"
      title="{{group.description}}">
        
        <img height="16px;" ng-src="../../editor-app/stencilsets/bpmn2.0/icons/{{group.icon}}" width="16px;"/>
        {{group.name}}
</span>