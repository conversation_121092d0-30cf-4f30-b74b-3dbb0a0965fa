<span ng-click="group.expanded = !group.expanded">
        <i class="glyphicon"
           ng-class="{'glyphicon-chevron-right': !group.expanded, 'glyphicon-chevron-down': group.expanded}"></i>
        {{group.name}}
    </span>

<!-- Child groups -->
<ul class="stencil-group stencil-group-non-root"
    ng-class="{collapsed: !group.expanded, 'first': $first}"
    ng-include="'../../editor-app/partials/stencil-item-template.html?version=4'"
    ng-repeat="group in group.groups">
</ul>

<!-- Group items -->
<ul>
    <li class="stencil-item" data-drag="true"
        data-jqyoui-options="{revert: 'invalid', helper: 'clone', opacity : 0.5}"
        id="{{item.id}}"
        jqyoui-draggable="{onStart:'startDragCallback', onDrag:'dragCallback'}"
        ng-model="draggedElement"
        ng-repeat="item in group.paletteItems"
        title="{{item.description}}">

        <img height="16px;" ng-src="../../editor-app/stencilsets/bpmn2.0/icons/{{item.icon}}" width="16px;"/>
        {{item.name}}
    </li>
</ul>