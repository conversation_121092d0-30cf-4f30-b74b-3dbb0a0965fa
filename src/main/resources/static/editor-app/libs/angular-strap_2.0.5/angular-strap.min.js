/**
 * angular-strap
 * @version v2.0.5 - 2014-09-03
 * @link http://mgcrea.github.io/angular-strap
 * <AUTHOR> (<EMAIL>)
 * @license MIT License, http://www.opensource.org/licenses/MIT
 */
!function(e,t){"use strict";angular.module("mgcrea.ngStrap",["mgcrea.ngStrap.modal","mgcrea.ngStrap.aside","mgcrea.ngStrap.alert","mgcrea.ngStrap.button","mgcrea.ngStrap.select","mgcrea.ngStrap.datepicker","mgcrea.ngStrap.timepicker","mgcrea.ngStrap.navbar","mgcrea.ngStrap.tooltip","mgcrea.ngStrap.popover","mgcrea.ngStrap.dropdown","mgcrea.ngStrap.typeahead","mgcrea.ngStrap.scrollspy","mgcrea.ngStrap.affix","mgcrea.ngStrap.tab"]),angular.module("mgcrea.ngStrap.affix",["mgcrea.ngStrap.helpers.dimensions","mgcrea.ngStrap.helpers.debounce"]).provider("$affix",function(){var e=this.defaults={offsetTop:"auto"};this.$get=["$window","debounce","dimensions",function(t,n,a){function o(o,s){function l(e,t,n){var a=u(),o=c();return h>=a?"top":null!==e&&a+e<=t.top?"middle":null!==v&&t.top+n+g>=o-v?"bottom":"middle"}function u(){return p[0]===t?t.pageYOffset:p[0].scrollTop}function c(){return p[0]===t?t.document.body.scrollHeight:p[0].scrollHeight}var d={},f=angular.extend({},e,s),p=f.target,m="affix affix-top affix-bottom",g=0,$=0,h=0,v=0,y=null,w=null,b=o.parent();if(f.offsetParent)if(f.offsetParent.match(/^\d+$/))for(var D=0;D<1*f.offsetParent-1;D++)b=b.parent();else b=angular.element(f.offsetParent);return d.init=function(){d.$parseOffsets(),$=a.offset(o[0]).top+g,p.on("scroll",d.checkPosition),p.on("click",d.checkPositionWithEventLoop),r.on("resize",d.$debouncedOnResize),d.checkPosition(),d.checkPositionWithEventLoop()},d.destroy=function(){p.off("scroll",d.checkPosition),p.off("click",d.checkPositionWithEventLoop),r.off("resize",d.$debouncedOnResize)},d.checkPositionWithEventLoop=function(){setTimeout(d.checkPosition,1)},d.checkPosition=function(){var e=u(),t=a.offset(o[0]),n=a.height(o[0]),r=l(w,t,n);y!==r&&(y=r,o.removeClass(m).addClass("affix"+("middle"!==r?"-"+r:"")),"top"===r?(w=null,o.css("position",f.offsetParent?"":"relative"),o.css("top","")):"bottom"===r?(w=f.offsetUnpin?-(1*f.offsetUnpin):t.top-e,o.css("position",f.offsetParent?"":"relative"),o.css("top",f.offsetParent?"":i[0].offsetHeight-v-n-$+"px")):(w=null,o.css("position","fixed"),o.css("top",g+"px")))},d.$onResize=function(){d.$parseOffsets(),d.checkPosition()},d.$debouncedOnResize=n(d.$onResize,50),d.$parseOffsets=function(){o.css("position",f.offsetParent?"":"relative"),f.offsetTop&&("auto"===f.offsetTop&&(f.offsetTop="+0"),f.offsetTop.match(/^[-+]\d+$/)?(g=1*-f.offsetTop,h=f.offsetParent?a.offset(b[0]).top+1*f.offsetTop:a.offset(o[0]).top-a.css(o[0],"marginTop",!0)+1*f.offsetTop):h=1*f.offsetTop),f.offsetBottom&&(v=f.offsetParent&&f.offsetBottom.match(/^[-+]\d+$/)?c()-(a.offset(b[0]).top+a.height(b[0]))+1*f.offsetBottom+1:1*f.offsetBottom)},d.init(),d}var i=angular.element(t.document.body),r=angular.element(t);return o}]}).directive("bsAffix",["$affix","$window",function(e,t){return{restrict:"EAC",require:"^?bsAffixTarget",link:function(n,a,o,i){var r={scope:n,offsetTop:"auto",target:i?i.$element:angular.element(t)};angular.forEach(["offsetTop","offsetBottom","offsetParent","offsetUnpin"],function(e){angular.isDefined(o[e])&&(r[e]=o[e])});var s=e(a,r);n.$on("$destroy",function(){s&&s.destroy(),r=null,s=null})}}}]).directive("bsAffixTarget",function(){return{controller:["$element",function(e){this.$element=e}]}}),angular.module("mgcrea.ngStrap.alert",["mgcrea.ngStrap.modal"]).provider("$alert",function(){var e=this.defaults={animation:"am-fade",prefixClass:"alert",placement:null,template:"alert/alert.tpl.html",container:!1,element:null,backdrop:!1,keyboard:!0,show:!0,duration:!1,type:!1,dismissable:!0};this.$get=["$modal","$timeout",function(t,n){function a(a){var o={},i=angular.extend({},e,a);o=t(i),o.$scope.dismissable=!!i.dismissable,i.type&&(o.$scope.type=i.type);var r=o.show;return i.duration&&(o.show=function(){r(),n(function(){o.hide()},1e3*i.duration)}),o}return a}]}).directive("bsAlert",["$window","$sce","$alert",function(e,t,n){e.requestAnimationFrame||e.setTimeout;return{restrict:"EAC",scope:!0,link:function(e,a,o){var i={scope:e,element:a,show:!1};angular.forEach(["template","placement","keyboard","html","container","animation","duration","dismissable"],function(e){angular.isDefined(o[e])&&(i[e]=o[e])}),angular.forEach(["title","content","type"],function(n){o[n]&&o.$observe(n,function(a){e[n]=t.trustAsHtml(a)})}),o.bsAlert&&e.$watch(o.bsAlert,function(t){angular.isObject(t)?angular.extend(e,t):e.content=t},!0);var r=n(i);a.on(o.trigger||"click",r.toggle),e.$on("$destroy",function(){r&&r.destroy(),i=null,r=null})}}}]),angular.module("mgcrea.ngStrap.aside",["mgcrea.ngStrap.modal"]).provider("$aside",function(){var e=this.defaults={animation:"am-fade-and-slide-right",prefixClass:"aside",placement:"right",template:"aside/aside.tpl.html",contentTemplate:!1,container:!1,element:null,backdrop:!0,keyboard:!0,html:!1,show:!0};this.$get=["$modal",function(t){function n(n){var a={},o=angular.extend({},e,n);return a=t(o)}return n}]}).directive("bsAside",["$window","$sce","$aside",function(e,t,n){e.requestAnimationFrame||e.setTimeout;return{restrict:"EAC",scope:!0,link:function(e,a,o){var i={scope:e,element:a,show:!1};angular.forEach(["template","contentTemplate","placement","backdrop","keyboard","html","container","animation"],function(e){angular.isDefined(o[e])&&(i[e]=o[e])}),angular.forEach(["title","content"],function(n){o[n]&&o.$observe(n,function(a){e[n]=t.trustAsHtml(a)})}),o.bsAside&&e.$watch(o.bsAside,function(t){angular.isObject(t)?angular.extend(e,t):e.content=t},!0);var r=n(i);a.on(o.trigger||"click",r.toggle),e.$on("$destroy",function(){r&&r.destroy(),i=null,r=null})}}}]),angular.module("mgcrea.ngStrap.button",[]).provider("$button",function(){var e=this.defaults={activeClass:"active",toggleEvent:"click"};this.$get=function(){return{defaults:e}}}).directive("bsCheckboxGroup",function(){return{restrict:"A",require:"ngModel",compile:function(e,t){e.attr("data-toggle","buttons"),e.removeAttr("ng-model");var n=e[0].querySelectorAll('input[type="checkbox"]');angular.forEach(n,function(e){var n=angular.element(e);n.attr("bs-checkbox",""),n.attr("ng-model",t.ngModel+"."+n.attr("value"))})}}}).directive("bsCheckbox",["$button","$$rAF",function(e,t){var n=e.defaults,a=/^(true|false|\d+)$/;return{restrict:"A",require:"ngModel",link:function(e,o,i,r){var s=n,l="INPUT"===o[0].nodeName,u=l?o.parent():o,c=angular.isDefined(i.trueValue)?i.trueValue:!0;a.test(i.trueValue)&&(c=e.$eval(i.trueValue));var d=angular.isDefined(i.falseValue)?i.falseValue:!1;a.test(i.falseValue)&&(d=e.$eval(i.falseValue));var f="boolean"!=typeof c||"boolean"!=typeof d;f&&(r.$parsers.push(function(e){return e?c:d}),e.$watch(i.ngModel,function(){r.$render()})),r.$render=function(){var e=angular.equals(r.$modelValue,c);t(function(){l&&(o[0].checked=e),u.toggleClass(s.activeClass,e)})},o.bind(s.toggleEvent,function(){e.$apply(function(){l||r.$setViewValue(!u.hasClass("active")),f||r.$render()})})}}}]).directive("bsRadioGroup",function(){return{restrict:"A",require:"ngModel",compile:function(e,t){e.attr("data-toggle","buttons"),e.removeAttr("ng-model");var n=e[0].querySelectorAll('input[type="radio"]');angular.forEach(n,function(e){angular.element(e).attr("bs-radio",""),angular.element(e).attr("ng-model",t.ngModel)})}}}).directive("bsRadio",["$button","$$rAF",function(e,t){var n=e.defaults,a=/^(true|false|\d+)$/;return{restrict:"A",require:"ngModel",link:function(e,o,i,r){var s=n,l="INPUT"===o[0].nodeName,u=l?o.parent():o,c=a.test(i.value)?e.$eval(i.value):i.value;r.$render=function(){var e=angular.equals(r.$modelValue,c);t(function(){l&&(o[0].checked=e),u.toggleClass(s.activeClass,e)})},o.bind(s.toggleEvent,function(){e.$apply(function(){r.$setViewValue(c),r.$render()})})}}}]),angular.module("mgcrea.ngStrap.datepicker",["mgcrea.ngStrap.helpers.dateParser","mgcrea.ngStrap.tooltip"]).provider("$datepicker",function(){var e=this.defaults={animation:"am-fade",prefixClass:"datepicker",placement:"bottom-left",template:"datepicker/datepicker.tpl.html",trigger:"focus",container:!1,keyboard:!0,html:!1,delay:0,useNative:!1,dateType:"date",dateFormat:"shortDate",modelDateFormat:null,dayFormat:"dd",strictFormat:!1,autoclose:!1,minDate:-1/0,maxDate:+1/0,startView:0,minView:0,startWeek:0,daysOfWeekDisabled:"",iconLeft:"glyphicon glyphicon-chevron-left",iconRight:"glyphicon glyphicon-chevron-right"};this.$get=["$window","$document","$rootScope","$sce","$locale","dateFilter","datepickerViews","$tooltip",function(t,n,a,o,i,r,s,l){function u(t,n,a){function o(e){e.selected=r.$isSelected(e.date)}function i(){t[0].focus()}var r=l(t,angular.extend({},e,a)),u=a.scope,f=r.$options,p=r.$scope;f.startView&&(f.startView-=f.minView);var m=s(r);r.$views=m.views;var g=m.viewDate;p.$mode=f.startView,p.$iconLeft=f.iconLeft,p.$iconRight=f.iconRight;var $=r.$views[p.$mode];p.$select=function(e){r.select(e)},p.$selectPane=function(e){r.$selectPane(e)},p.$toggleMode=function(){r.setMode((p.$mode+1)%r.$views.length)},r.update=function(e){angular.isDate(e)&&!isNaN(e.getTime())&&(r.$date=e,$.update.call($,e)),r.$build(!0)},r.updateDisabledDates=function(e){f.disabledDateRanges=e;for(var t=0,n=p.rows.length;n>t;t++)angular.forEach(p.rows[t],r.$setDisabledEl)},r.select=function(e,t){angular.isDate(n.$dateValue)||(n.$dateValue=new Date(e)),!p.$mode||t?(n.$setViewValue(angular.copy(e)),n.$render(),f.autoclose&&!t&&r.hide(!0)):(angular.extend(g,{year:e.getFullYear(),month:e.getMonth(),date:e.getDate()}),r.setMode(p.$mode-1),r.$build())},r.setMode=function(e){p.$mode=e,$=r.$views[p.$mode],r.$build()},r.$build=function(e){e===!0&&$.built||(e!==!1||$.built)&&$.build.call($)},r.$updateSelected=function(){for(var e=0,t=p.rows.length;t>e;e++)angular.forEach(p.rows[e],o)},r.$isSelected=function(e){return $.isSelected(e)},r.$setDisabledEl=function(e){e.disabled=$.isDisabled(e.date)},r.$selectPane=function(e){var t=$.steps,n=new Date(Date.UTC(g.year+(t.year||0)*e,g.month+(t.month||0)*e,g.date+(t.day||0)*e));angular.extend(g,{year:n.getUTCFullYear(),month:n.getUTCMonth(),date:n.getUTCDate()}),r.$build()},r.$onMouseDown=function(e){if(e.preventDefault(),e.stopPropagation(),d){var t=angular.element(e.target);"button"!==t[0].nodeName.toLowerCase()&&(t=t.parent()),t.triggerHandler("click")}},r.$onKeyDown=function(e){if(/(38|37|39|40|13)/.test(e.keyCode)&&!e.shiftKey&&!e.altKey){if(e.preventDefault(),e.stopPropagation(),13===e.keyCode)return p.$mode?p.$apply(function(){r.setMode(p.$mode-1)}):r.hide(!0);$.onKeyDown(e),u.$digest()}};var h=r.init;r.init=function(){return c&&f.useNative?(t.prop("type","date"),t.css("-webkit-appearance","textfield"),void 0):(d&&(t.prop("type","text"),t.attr("readonly","true"),t.on("click",i)),h(),void 0)};var v=r.destroy;r.destroy=function(){c&&f.useNative&&t.off("click",i),v()};var y=r.show;r.show=function(){y(),setTimeout(function(){r.$element.on(d?"touchstart":"mousedown",r.$onMouseDown),f.keyboard&&t.on("keydown",r.$onKeyDown)})};var w=r.hide;return r.hide=function(e){r.$element.off(d?"touchstart":"mousedown",r.$onMouseDown),f.keyboard&&t.off("keydown",r.$onKeyDown),w(e)},r}var c=(angular.element(t.document.body),/(ip(a|o)d|iphone|android)/gi.test(t.navigator.userAgent)),d="createTouch"in t.document&&c;return e.lang||(e.lang=i.id),u.defaults=e,u}]}).directive("bsDatepicker",["$window","$parse","$q","$locale","dateFilter","$datepicker","$dateParser","$timeout",function(e,t,n,a,o,i,r){var s=(i.defaults,/(ip(a|o)d|iphone|android)/gi.test(e.navigator.userAgent)),l=function(e){return!isNaN(parseFloat(e))&&isFinite(e)};return{restrict:"EAC",require:"ngModel",link:function(e,t,n,a){function u(e){return e&&e.length?e:null}var c={scope:e,controller:a};angular.forEach(["placement","container","delay","trigger","keyboard","html","animation","template","autoclose","dateType","dateFormat","modelDateFormat","dayFormat","strictFormat","startWeek","startDate","useNative","lang","startView","minView","iconLeft","iconRight","daysOfWeekDisabled"],function(e){angular.isDefined(n[e])&&(c[e]=n[e])}),n.bsShow&&e.$watch(n.bsShow,function(e){d&&angular.isDefined(e)&&(angular.isString(e)&&(e=!!e.match(",?(datepicker),?")),e===!0?d.show():d.hide())}),s&&c.useNative&&(c.dateFormat="yyyy-MM-dd");var d=i(t,a,c);c=d.$options,angular.forEach(["minDate","maxDate"],function(e){angular.isDefined(n[e])&&n.$observe(e,function(t){if("today"===t){var n=new Date;d.$options[e]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()+("maxDate"===e?1:0),0,0,0,"minDate"===e?0:-1)}else d.$options[e]=angular.isString(t)&&t.match(/^".+"$/)?+new Date(t.substr(1,t.length-2)):l(t)?+new Date(parseInt(t,10)):angular.isString(t)&&0===t.length?"maxDate"===e?+1/0:-1/0:+new Date(t);!isNaN(d.$options[e])&&d.$build(!1)})}),e.$watch(n.ngModel,function(){d.update(a.$dateValue)},!0),angular.isDefined(n.disabledDates)&&e.$watch(n.disabledDates,function(e,t){e=u(e),t=u(t),e!==t&&d.updateDisabledDates(e)});var f=r({format:c.dateFormat,lang:c.lang,strict:c.strictFormat});a.$parsers.unshift(function(e){if(!e)return a.$setValidity("date",!0),void 0;var t=f.parse(e,a.$dateValue);if(!t||isNaN(t.getTime()))return a.$setValidity("date",!1),void 0;var n=isNaN(d.$options.minDate)||t.getTime()>=d.$options.minDate,i=isNaN(d.$options.maxDate)||t.getTime()<=d.$options.maxDate,r=n&&i;return a.$setValidity("date",r),a.$setValidity("min",n),a.$setValidity("max",i),r&&(a.$dateValue=t),"string"===c.dateType?o(t,c.modelDateFormat||c.dateFormat):"number"===c.dateType?a.$dateValue.getTime():"iso"===c.dateType?a.$dateValue.toISOString():new Date(a.$dateValue)}),a.$formatters.push(function(e){var t;return t=angular.isUndefined(e)||null===e?0/0:angular.isDate(e)?e:"string"===c.dateType?f.parse(e,null,c.modelDateFormat):new Date(e),a.$dateValue=t,a.$dateValue}),a.$render=function(){t.val(!a.$dateValue||isNaN(a.$dateValue.getTime())?"":o(a.$dateValue,c.dateFormat))},e.$on("$destroy",function(){d&&d.destroy(),c=null,d=null})}}}]).provider("datepickerViews",function(){function e(e,t){for(var n=[];e.length>0;)n.push(e.splice(0,t));return n}function t(e,t){return(e%t+t)%t}this.defaults={dayFormat:"dd",daySplit:7};this.$get=["$locale","$sce","dateFilter",function(n,a,o){return function(i){var r=i.$scope,s=i.$options,l=n.DATETIME_FORMATS.SHORTDAY,u=l.slice(s.startWeek).concat(l.slice(0,s.startWeek)),c=a.trustAsHtml('<th class="dow text-center">'+u.join('</th><th class="dow text-center">')+"</th>"),d=i.$date||(s.startDate?new Date(s.startDate):new Date),f={year:d.getFullYear(),month:d.getMonth(),date:d.getDate()},p=(6e4*d.getTimezoneOffset(),[{format:s.dayFormat,split:7,steps:{month:1},update:function(e,t){!this.built||t||e.getFullYear()!==f.year||e.getMonth()!==f.month?(angular.extend(f,{year:i.$date.getFullYear(),month:i.$date.getMonth(),date:i.$date.getDate()}),i.$build()):e.getDate()!==f.date&&(f.date=i.$date.getDate(),i.$updateSelected())},build:function(){var n=new Date(f.year,f.month,1),a=n.getTimezoneOffset(),l=new Date(+n-864e5*t(n.getDay()-s.startWeek,7)),u=l.getTimezoneOffset(),d=(new Date).toDateString();u!==a&&(l=new Date(+l+6e4*(u-a)));for(var p,m=[],g=0;42>g;g++)p=new Date(l.getFullYear(),l.getMonth(),l.getDate()+g),m.push({date:p,isToday:p.toDateString()===d,label:o(p,this.format),selected:i.$date&&this.isSelected(p),muted:p.getMonth()!==f.month,disabled:this.isDisabled(p)});r.title=o(n,"MMMM yyyy"),r.showLabels=!0,r.labels=c,r.rows=e(m,this.split),this.built=!0},isSelected:function(e){return i.$date&&e.getFullYear()===i.$date.getFullYear()&&e.getMonth()===i.$date.getMonth()&&e.getDate()===i.$date.getDate()},isDisabled:function(e){var t=e.getTime();if(t<s.minDate||t>s.maxDate)return!0;if(-1!==s.daysOfWeekDisabled.indexOf(e.getDay()))return!0;if(s.disabledDateRanges)for(var n=0;n<s.disabledDateRanges.length;n++)if(t>=s.disabledDateRanges[n].start)return t<=s.disabledDateRanges[n].end?!0:!1;return!1},onKeyDown:function(e){var t,n=i.$date.getTime();37===e.keyCode?t=new Date(n-864e5):38===e.keyCode?t=new Date(n-6048e5):39===e.keyCode?t=new Date(n+864e5):40===e.keyCode&&(t=new Date(n+6048e5)),this.isDisabled(t)||i.select(t,!0)}},{name:"month",format:"MMM",split:4,steps:{year:1},update:function(e){this.built&&e.getFullYear()===f.year?e.getMonth()!==f.month&&(angular.extend(f,{month:i.$date.getMonth(),date:i.$date.getDate()}),i.$updateSelected()):(angular.extend(f,{year:i.$date.getFullYear(),month:i.$date.getMonth(),date:i.$date.getDate()}),i.$build())},build:function(){for(var t,n=(new Date(f.year,0,1),[]),a=0;12>a;a++)t=new Date(f.year,a,1),n.push({date:t,label:o(t,this.format),selected:i.$isSelected(t),disabled:this.isDisabled(t)});r.title=o(t,"yyyy"),r.showLabels=!1,r.rows=e(n,this.split),this.built=!0},isSelected:function(e){return i.$date&&e.getFullYear()===i.$date.getFullYear()&&e.getMonth()===i.$date.getMonth()},isDisabled:function(e){var t=+new Date(e.getFullYear(),e.getMonth()+1,0);return t<s.minDate||e.getTime()>s.maxDate},onKeyDown:function(e){var t=i.$date.getMonth(),n=new Date(i.$date);37===e.keyCode?n.setMonth(t-1):38===e.keyCode?n.setMonth(t-4):39===e.keyCode?n.setMonth(t+1):40===e.keyCode&&n.setMonth(t+4),this.isDisabled(n)||i.select(n,!0)}},{name:"year",format:"yyyy",split:4,steps:{year:12},update:function(e,t){!this.built||t||parseInt(e.getFullYear()/20,10)!==parseInt(f.year/20,10)?(angular.extend(f,{year:i.$date.getFullYear(),month:i.$date.getMonth(),date:i.$date.getDate()}),i.$build()):e.getFullYear()!==f.year&&(angular.extend(f,{year:i.$date.getFullYear(),month:i.$date.getMonth(),date:i.$date.getDate()}),i.$updateSelected())},build:function(){for(var t,n=f.year-f.year%(3*this.split),a=[],s=0;12>s;s++)t=new Date(n+s,0,1),a.push({date:t,label:o(t,this.format),selected:i.$isSelected(t),disabled:this.isDisabled(t)});r.title=a[0].label+"-"+a[a.length-1].label,r.showLabels=!1,r.rows=e(a,this.split),this.built=!0},isSelected:function(e){return i.$date&&e.getFullYear()===i.$date.getFullYear()},isDisabled:function(e){var t=+new Date(e.getFullYear()+1,0,0);return t<s.minDate||e.getTime()>s.maxDate},onKeyDown:function(e){var t=i.$date.getFullYear(),n=new Date(i.$date);37===e.keyCode?n.setYear(t-1):38===e.keyCode?n.setYear(t-4):39===e.keyCode?n.setYear(t+1):40===e.keyCode&&n.setYear(t+4),this.isDisabled(n)||i.select(n,!0)}}]);return{views:s.minView?Array.prototype.slice.call(p,s.minView):p,viewDate:f}}}]}),angular.module("mgcrea.ngStrap.dropdown",["mgcrea.ngStrap.tooltip"]).provider("$dropdown",function(){var e=this.defaults={animation:"am-fade",prefixClass:"dropdown",placement:"bottom-left",template:"dropdown/dropdown.tpl.html",trigger:"click",container:!1,keyboard:!0,html:!1,delay:0};this.$get=["$window","$rootScope","$tooltip",function(t,n,a){function o(t,o){function s(e){return e.target!==t[0]?e.target!==t[0]&&l.hide():void 0}{var l={},u=angular.extend({},e,o);l.$scope=u.scope&&u.scope.$new()||n.$new()}l=a(t,u);var c=t.parent();l.$onKeyDown=function(e){if(/(38|40)/.test(e.keyCode)){e.preventDefault(),e.stopPropagation();var t=angular.element(l.$element[0].querySelectorAll("li:not(.divider) a"));if(t.length){var n;angular.forEach(t,function(e,t){r&&r.call(e,":focus")&&(n=t)}),38===e.keyCode&&n>0?n--:40===e.keyCode&&n<t.length-1?n++:angular.isUndefined(n)&&(n=0),t.eq(n)[0].focus()}}};var d=l.show;l.show=function(){d(),setTimeout(function(){u.keyboard&&l.$element.on("keydown",l.$onKeyDown),i.on("click",s)}),c.hasClass("dropdown")&&c.addClass("open")};var f=l.hide;return l.hide=function(){u.keyboard&&l.$element.off("keydown",l.$onKeyDown),i.off("click",s),c.hasClass("dropdown")&&c.removeClass("open"),f()},l}var i=angular.element(t.document.body),r=Element.prototype.matchesSelector||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector;return o}]}).directive("bsDropdown",["$window","$sce","$dropdown",function(e,t,n){return{restrict:"EAC",scope:!0,link:function(e,t,a){var o={scope:e};angular.forEach(["placement","container","delay","trigger","keyboard","html","animation","template"],function(e){angular.isDefined(a[e])&&(o[e]=a[e])}),a.bsDropdown&&e.$watch(a.bsDropdown,function(t){e.content=t},!0),a.bsShow&&e.$watch(a.bsShow,function(e){i&&angular.isDefined(e)&&(angular.isString(e)&&(e=!!e.match(",?(dropdown),?")),e===!0?i.show():i.hide())});var i=n(t,o);e.$on("$destroy",function(){i&&i.destroy(),o=null,i=null})}}}]),angular.module("mgcrea.ngStrap.helpers.dateParser",[]).provider("$dateParser",["$localeProvider",function(){function e(){}var t=Date.prototype,n=this.defaults={format:"shortDate",strict:!1};this.$get=["$locale","dateFilter",function(a,o){var i=function(i){function r(e){var t,n=Object.keys(m),a=[],o=[],i=e;for(t=0;t<n.length;t++)if(e.split(n[t]).length>1){var r=i.search(n[t]);e=e.split(n[t]).join(""),m[n[t]]&&(a[r]=m[n[t]])}return angular.forEach(a,function(e){e&&o.push(e)}),o}function s(e){return e.replace(/\//g,"[\\/]").replace("/-/g","[-]").replace(/\./g,"[.]").replace(/\\s/g,"[\\s]")}function l(e){var t,n=Object.keys(p),a=e;for(t=0;t<n.length;t++)a=a.split(n[t]).join("${"+t+"}");for(t=0;t<n.length;t++)a=a.split("${"+t+"}").join("("+p[n[t]]+")");return e=s(e),new RegExp("^"+a+"$",["i"])}var u,c,d=angular.extend({},n,i),f={},p={sss:"[0-9]{3}",ss:"[0-5][0-9]",s:d.strict?"[1-5]?[0-9]":"[0-9]|[0-5][0-9]",mm:"[0-5][0-9]",m:d.strict?"[1-5]?[0-9]":"[0-9]|[0-5][0-9]",HH:"[01][0-9]|2[0-3]",H:d.strict?"1?[0-9]|2[0-3]":"[01]?[0-9]|2[0-3]",hh:"[0][1-9]|[1][012]",h:d.strict?"[1-9]|1[012]":"0?[1-9]|1[012]",a:"AM|PM",EEEE:a.DATETIME_FORMATS.DAY.join("|"),EEE:a.DATETIME_FORMATS.SHORTDAY.join("|"),dd:"0[1-9]|[12][0-9]|3[01]",d:d.strict?"[1-9]|[1-2][0-9]|3[01]":"0?[1-9]|[1-2][0-9]|3[01]",MMMM:a.DATETIME_FORMATS.MONTH.join("|"),MMM:a.DATETIME_FORMATS.SHORTMONTH.join("|"),MM:"0[1-9]|1[012]",M:d.strict?"[1-9]|1[012]":"0?[1-9]|1[012]",yyyy:"[1]{1}[0-9]{3}|[2]{1}[0-9]{3}",yy:"[0-9]{2}",y:d.strict?"-?(0|[1-9][0-9]{0,3})":"-?0*[0-9]{1,4}"},m={sss:t.setMilliseconds,ss:t.setSeconds,s:t.setSeconds,mm:t.setMinutes,m:t.setMinutes,HH:t.setHours,H:t.setHours,hh:t.setHours,h:t.setHours,EEEE:e,EEE:e,dd:t.setDate,d:t.setDate,a:function(e){var t=this.getHours();return this.setHours(e.match(/pm/i)?t+12:t)},MMMM:function(e){return this.setMonth(a.DATETIME_FORMATS.MONTH.indexOf(e))},MMM:function(e){return this.setMonth(a.DATETIME_FORMATS.SHORTMONTH.indexOf(e))},MM:function(e){return this.setMonth(1*e-1)},M:function(e){return this.setMonth(1*e-1)},yyyy:t.setFullYear,yy:function(e){return this.setFullYear(2e3+1*e)},y:t.setFullYear};return f.init=function(){f.$format=a.DATETIME_FORMATS[d.format]||d.format,u=l(f.$format),c=r(f.$format)},f.isValid=function(e){return angular.isDate(e)?!isNaN(e.getTime()):u.test(e)},f.parse=function(e,t,n){angular.isDate(e)&&(e=o(e,n||f.$format));var a=n?l(n):u,i=n?r(n):c,s=a.exec(e);if(!s)return!1;for(var d=t||new Date(0,0,1),p=0;p<s.length-1;p++)i[p]&&i[p].call(d,s[p+1]);return d},f.init(),f};return i}]}]),angular.module("mgcrea.ngStrap.helpers.debounce",[]).constant("debounce",function(e,t,n){var a,o,i,r,s;return function(){i=this,o=arguments,r=new Date;var l=function(){var u=new Date-r;t>u?a=setTimeout(l,t-u):(a=null,n||(s=e.apply(i,o)))},u=n&&!a;return a||(a=setTimeout(l,t)),u&&(s=e.apply(i,o)),s}}).constant("throttle",function(e,t,n){var a,o,i,r=null,s=0;n||(n={});var l=function(){s=n.leading===!1?0:new Date,r=null,i=e.apply(a,o)};return function(){var u=new Date;s||n.leading!==!1||(s=u);var c=t-(u-s);return a=this,o=arguments,0>=c?(clearTimeout(r),r=null,s=u,i=e.apply(a,o)):r||n.trailing===!1||(r=setTimeout(l,c)),i}}),angular.module("mgcrea.ngStrap.helpers.dimensions",[]).factory("dimensions",["$document","$window",function(){var t=(angular.element,{}),n=t.nodeName=function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()};t.css=function(t,n,a){var o;return o=t.currentStyle?t.currentStyle[n]:e.getComputedStyle?e.getComputedStyle(t)[n]:t.style[n],a===!0?parseFloat(o)||0:o},t.offset=function(t){var n=t.getBoundingClientRect(),a=t.ownerDocument;return{width:n.width||t.offsetWidth,height:n.height||t.offsetHeight,top:n.top+(e.pageYOffset||a.documentElement.scrollTop)-(a.documentElement.clientTop||0),left:n.left+(e.pageXOffset||a.documentElement.scrollLeft)-(a.documentElement.clientLeft||0)}},t.position=function(e){var o,i,r={top:0,left:0};return"fixed"===t.css(e,"position")?i=e.getBoundingClientRect():(o=a(e),i=t.offset(e),i=t.offset(e),n(o,"html")||(r=t.offset(o)),r.top+=t.css(o,"borderTopWidth",!0),r.left+=t.css(o,"borderLeftWidth",!0)),{width:e.offsetWidth,height:e.offsetHeight,top:i.top-r.top-t.css(e,"marginTop",!0),left:i.left-r.left-t.css(e,"marginLeft",!0)}};var a=function(e){var a=e.ownerDocument,o=e.offsetParent||a;if(n(o,"#document"))return a.documentElement;for(;o&&!n(o,"html")&&"static"===t.css(o,"position");)o=o.offsetParent;return o||a.documentElement};return t.height=function(e,n){var a=e.offsetHeight;return n?a+=t.css(e,"marginTop",!0)+t.css(e,"marginBottom",!0):a-=t.css(e,"paddingTop",!0)+t.css(e,"paddingBottom",!0)+t.css(e,"borderTopWidth",!0)+t.css(e,"borderBottomWidth",!0),a},t.width=function(e,n){var a=e.offsetWidth;return n?a+=t.css(e,"marginLeft",!0)+t.css(e,"marginRight",!0):a-=t.css(e,"paddingLeft",!0)+t.css(e,"paddingRight",!0)+t.css(e,"borderLeftWidth",!0)+t.css(e,"borderRightWidth",!0),a},t}]),angular.module("mgcrea.ngStrap.helpers.parseOptions",[]).provider("$parseOptions",function(){var e=this.defaults={regexp:/^\s*(.*?)(?:\s+as\s+(.*?))?(?:\s+group\s+by\s+(.*))?\s+for\s+(?:([\$\w][\$\w]*)|(?:\(\s*([\$\w][\$\w]*)\s*,\s*([\$\w][\$\w]*)\s*\)))\s+in\s+(.*?)(?:\s+track\s+by\s+(.*?))?$/};this.$get=["$parse","$q",function(t,n){function a(a,o){function i(e,t){return e.map(function(e,n){var a,o,i={};return i[c]=e,a=u(t,i),o=p(t,i)||n,{label:a,value:o}})}var r={},s=angular.extend({},e,o);r.$values=[];var l,u,c,d,f,p,m;return r.init=function(){r.$match=l=a.match(s.regexp),u=t(l[2]||l[1]),c=l[4]||l[6],d=l[5],f=t(l[3]||""),p=t(l[2]?l[1]:c),m=t(l[7])},r.valuesFn=function(e,t){return n.when(m(e,t)).then(function(t){return r.$values=t?i(t,e):{},r.$values})},r.init(),r}return a}]}),angular.version.minor<3&&angular.version.dot<14&&angular.module("ng").factory("$$rAF",["$window","$timeout",function(e,t){var n=e.requestAnimationFrame||e.webkitRequestAnimationFrame||e.mozRequestAnimationFrame,a=e.cancelAnimationFrame||e.webkitCancelAnimationFrame||e.mozCancelAnimationFrame||e.webkitCancelRequestAnimationFrame,o=!!n,i=o?function(e){var t=n(e);return function(){a(t)}}:function(e){var n=t(e,16.66,!1);return function(){t.cancel(n)}};return i.supported=o,i}]),angular.module("mgcrea.ngStrap.modal",["mgcrea.ngStrap.helpers.dimensions"]).provider("$modal",function(){var e=this.defaults={animation:"am-fade",backdropAnimation:"am-fade",prefixClass:"modal",prefixEvent:"modal",placement:"top",template:"modal/modal.tpl.html",contentTemplate:!1,container:!1,element:null,backdrop:!0,keyboard:!0,html:!1,show:!0};this.$get=["$window","$rootScope","$compile","$q","$templateCache","$http","$animate","$timeout","$sce","dimensions",function(n,a,o,i,r,s,l,u,c){function d(t){function n(e){e.target===e.currentTarget&&("static"===r.backdrop?i.focus():i.hide())}var i={},r=i.$options=angular.extend({},e,t);i.$promise=p(r.template);var s=i.$scope=r.scope&&r.scope.$new()||a.$new();r.element||r.container||(r.container="body"),m(["title","content"],function(e){r[e]&&(s[e]=c.trustAsHtml(r[e]))}),s.$hide=function(){s.$$postDigest(function(){i.hide()})},s.$show=function(){s.$$postDigest(function(){i.show()})},s.$toggle=function(){s.$$postDigest(function(){i.toggle()})},r.contentTemplate&&(i.$promise=i.$promise.then(function(e){var n=angular.element(e);return p(r.contentTemplate).then(function(e){var a=f('[ng-bind="content"]',n[0]).removeAttr("ng-bind").html(e);return t.template||a.next().remove(),n[0].outerHTML})}));var u,d,y=angular.element('<div class="'+r.prefixClass+'-backdrop"/>');return i.$promise.then(function(e){angular.isObject(e)&&(e=e.data),r.html&&(e=e.replace(v,'ng-bind-html="')),e=g.apply(e),u=o(e),i.init()}),i.init=function(){r.show&&s.$$postDigest(function(){i.show()})},i.destroy=function(){d&&(d.remove(),d=null),y&&(y.remove(),y=null),s.$destroy()},i.show=function(){s.$emit(r.prefixEvent+".show.before",i);var e;e=angular.isElement(r.container)?r.container:r.container?f(r.container):null;var t=r.container?null:r.element;d=i.$element=u(s,function(){}),d.css({display:"block"}).addClass(r.placement),r.animation&&(r.backdrop&&y.addClass(r.backdropAnimation),d.addClass(r.animation)),r.backdrop&&l.enter(y,h,null,function(){}),l.enter(d,e,t,function(){s.$emit(r.prefixEvent+".show",i)}),s.$isShown=!0,s.$$phase||s.$root&&s.$root.$$phase||s.$digest();var a=d[0];$(function(){a.focus()}),h.addClass(r.prefixClass+"-open"),r.animation&&h.addClass(r.prefixClass+"-with-"+r.animation),r.backdrop&&(d.on("click",n),y.on("click",n)),r.keyboard&&d.on("keyup",i.$onKeyUp)},i.hide=function(){s.$emit(r.prefixEvent+".hide.before",i),l.leave(d,function(){s.$emit(r.prefixEvent+".hide",i),h.removeClass(r.prefixClass+"-open"),r.animation&&h.removeClass(r.prefixClass+"-with-"+r.animation)}),r.backdrop&&l.leave(y,function(){}),s.$isShown=!1,s.$$phase||s.$root&&s.$root.$$phase||s.$digest(),r.backdrop&&(d.off("click",n),y.off("click",n)),r.keyboard&&d.off("keyup",i.$onKeyUp)},i.toggle=function(){s.$isShown?i.hide():i.show()},i.focus=function(){d[0].focus()},i.$onKeyUp=function(e){27===e.which&&s.$isShown&&(i.hide(),e.stopPropagation())},i}function f(e,n){return angular.element((n||t).querySelectorAll(e))}function p(e){return i.when(r.get(e)||s.get(e)).then(function(t){return angular.isObject(t)?(r.put(e,t.data),t.data):t})}var m=angular.forEach,g=String.prototype.trim,$=n.requestAnimationFrame||n.setTimeout,h=angular.element(n.document.body),v=/ng-bind="/gi;return d}]}).directive("bsModal",["$window","$sce","$modal",function(e,t,n){return{restrict:"EAC",scope:!0,link:function(e,a,o){var i={scope:e,element:a,show:!1};angular.forEach(["template","contentTemplate","placement","backdrop","keyboard","html","container","animation"],function(e){angular.isDefined(o[e])&&(i[e]=o[e])}),angular.forEach(["title","content"],function(n){o[n]&&o.$observe(n,function(a){e[n]=t.trustAsHtml(a)})}),o.bsModal&&e.$watch(o.bsModal,function(t){angular.isObject(t)?angular.extend(e,t):e.content=t},!0);var r=n(i);a.on(o.trigger||"click",r.toggle),e.$on("$destroy",function(){r&&r.destroy(),i=null,r=null})}}}]),angular.module("mgcrea.ngStrap.navbar",[]).provider("$navbar",function(){var e=this.defaults={activeClass:"active",routeAttr:"data-match-route",strict:!1};this.$get=function(){return{defaults:e}}}).directive("bsNavbar",["$window","$location","$navbar",function(e,t,n){var a=n.defaults;return{restrict:"A",link:function(e,n,o){var i=angular.copy(a);angular.forEach(Object.keys(a),function(e){angular.isDefined(o[e])&&(i[e]=o[e])}),e.$watch(function(){return t.path()},function(e){var t=n[0].querySelectorAll("li["+i.routeAttr+"]");angular.forEach(t,function(t){var n=angular.element(t),a=n.attr(i.routeAttr).replace("/","\\/");i.strict&&(a="^"+a+"$");var o=new RegExp(a,["i"]);o.test(e)?n.addClass(i.activeClass):n.removeClass(i.activeClass)})})}}}]),angular.module("mgcrea.ngStrap.popover",["mgcrea.ngStrap.tooltip"]).provider("$popover",function(){var e=this.defaults={animation:"am-fade",customClass:"",container:!1,target:!1,placement:"right",template:"popover/popover.tpl.html",contentTemplate:!1,trigger:"click",keyboard:!0,html:!1,title:"",content:"",delay:0};this.$get=["$tooltip",function(t){function n(n,a){var o=angular.extend({},e,a),i=t(n,o);return o.content&&(i.$scope.content=o.content),i}return n}]}).directive("bsPopover",["$window","$sce","$popover",function(e,t,n){var a=e.requestAnimationFrame||e.setTimeout;return{restrict:"EAC",scope:!0,link:function(e,o,i){var r={scope:e};angular.forEach(["template","contentTemplate","placement","container","target","delay","trigger","keyboard","html","animation","customClass"],function(e){angular.isDefined(i[e])&&(r[e]=i[e])}),angular.forEach(["title","content"],function(n){i[n]&&i.$observe(n,function(o,i){e[n]=t.trustAsHtml(o),angular.isDefined(i)&&a(function(){s&&s.$applyPlacement()
})})}),i.bsPopover&&e.$watch(i.bsPopover,function(t,n){angular.isObject(t)?angular.extend(e,t):e.content=t,angular.isDefined(n)&&a(function(){s&&s.$applyPlacement()})},!0),i.bsShow&&e.$watch(i.bsShow,function(e){s&&angular.isDefined(e)&&(angular.isString(e)&&(e=!!e.match(",?(popover),?")),e===!0?s.show():s.hide())});var s=n(o,r);e.$on("$destroy",function(){s&&s.destroy(),r=null,s=null})}}}]),angular.module("mgcrea.ngStrap.scrollspy",["mgcrea.ngStrap.helpers.debounce","mgcrea.ngStrap.helpers.dimensions"]).provider("$scrollspy",function(){var e=this.$$spies={},n=this.defaults={debounce:150,throttle:100,offset:100};this.$get=["$window","$document","$rootScope","dimensions","debounce","throttle",function(a,o,i,r,s,l){function u(e,t){return e[0].nodeName&&e[0].nodeName.toLowerCase()===t.toLowerCase()}function c(o){var c=angular.extend({},n,o);c.element||(c.element=p);var m=u(c.element,"body"),g=m?d:c.element,$=m?"window":c.id;if(e[$])return e[$].$$count++,e[$];var h,v,y,w,b,D,k,S,T={},x=T.$trackedElements=[],M=[];return T.init=function(){this.$$count=1,w=s(this.checkPosition,c.debounce),b=l(this.checkPosition,c.throttle),g.on("click",this.checkPositionWithEventLoop),d.on("resize",w),g.on("scroll",b),D=s(this.checkOffsets,c.debounce),h=i.$on("$viewContentLoaded",D),v=i.$on("$includeContentLoaded",D),D(),$&&(e[$]=T)},T.destroy=function(){this.$$count--,this.$$count>0||(g.off("click",this.checkPositionWithEventLoop),d.off("resize",w),g.off("scroll",w),h(),v(),$&&delete e[$])},T.checkPosition=function(){if(M.length){if(S=(m?a.pageYOffset:g.prop("scrollTop"))||0,k=Math.max(a.innerHeight,f.prop("clientHeight")),S<M[0].offsetTop&&y!==M[0].target)return T.$activateElement(M[0]);for(var e=M.length;e--;)if(!angular.isUndefined(M[e].offsetTop)&&null!==M[e].offsetTop&&y!==M[e].target&&!(S<M[e].offsetTop||M[e+1]&&S>M[e+1].offsetTop))return T.$activateElement(M[e])}},T.checkPositionWithEventLoop=function(){setTimeout(this.checkPosition,1)},T.$activateElement=function(e){if(y){var t=T.$getTrackedElement(y);t&&(t.source.removeClass("active"),u(t.source,"li")&&u(t.source.parent().parent(),"li")&&t.source.parent().parent().removeClass("active"))}y=e.target,e.source.addClass("active"),u(e.source,"li")&&u(e.source.parent().parent(),"li")&&e.source.parent().parent().addClass("active")},T.$getTrackedElement=function(e){return x.filter(function(t){return t.target===e})[0]},T.checkOffsets=function(){angular.forEach(x,function(e){var n=t.querySelector(e.target);e.offsetTop=n?r.offset(n).top:null,c.offset&&null!==e.offsetTop&&(e.offsetTop-=1*c.offset)}),M=x.filter(function(e){return null!==e.offsetTop}).sort(function(e,t){return e.offsetTop-t.offsetTop}),w()},T.trackElement=function(e,t){x.push({target:e,source:t})},T.untrackElement=function(e,t){for(var n,a=x.length;a--;)if(x[a].target===e&&x[a].source===t){n=a;break}x=x.splice(n,1)},T.activate=function(e){x[e].addClass("active")},T.init(),T}var d=angular.element(a),f=angular.element(o.prop("documentElement")),p=angular.element(a.document.body);return c}]}).directive("bsScrollspy",["$rootScope","debounce","dimensions","$scrollspy",function(e,t,n,a){return{restrict:"EAC",link:function(e,t,n){var o={scope:e};angular.forEach(["offset","target"],function(e){angular.isDefined(n[e])&&(o[e]=n[e])});var i=a(o);i.trackElement(o.target,t),e.$on("$destroy",function(){i&&(i.untrackElement(o.target,t),i.destroy()),o=null,i=null})}}}]).directive("bsScrollspyList",["$rootScope","debounce","dimensions","$scrollspy",function(){return{restrict:"A",compile:function(e){var t=e[0].querySelectorAll("li > a[href]");angular.forEach(t,function(e){var t=angular.element(e);t.parent().attr("bs-scrollspy","").attr("data-target",t.attr("href"))})}}}]),angular.module("mgcrea.ngStrap.select",["mgcrea.ngStrap.tooltip","mgcrea.ngStrap.helpers.parseOptions"]).provider("$select",function(){var e=this.defaults={animation:"am-fade",prefixClass:"select",prefixEvent:"$select",placement:"bottom-left",template:"select/select.tpl.html",trigger:"focus",container:!1,keyboard:!0,html:!1,delay:0,multiple:!1,allNoneButtons:!1,sort:!0,caretHtml:'&nbsp;<span class="caret"></span>',placeholder:"Choose among the following...",maxLength:3,maxLengthHtml:"selected",iconCheckmark:"glyphicon glyphicon-ok"};this.$get=["$window","$document","$rootScope","$tooltip",function(t,n,a,o){function i(t,n,a){var i={},r=angular.extend({},e,a);i=o(t,r);var l=i.$scope;l.$matches=[],l.$activeIndex=0,l.$isMultiple=r.multiple,l.$showAllNoneButtons=r.allNoneButtons&&r.multiple,l.$iconCheckmark=r.iconCheckmark,l.$activate=function(e){l.$$postDigest(function(){i.activate(e)})},l.$select=function(e){l.$$postDigest(function(){i.select(e)})},l.$isVisible=function(){return i.$isVisible()},l.$isActive=function(e){return i.$isActive(e)},l.$selectAll=function(){for(var e=0;e<l.$matches.length;e++)l.$isActive(e)||l.$select(e)},l.$selectNone=function(){for(var e=0;e<l.$matches.length;e++)l.$isActive(e)&&l.$select(e)},i.update=function(e){l.$matches=e,i.$updateActiveIndex()},i.activate=function(e){return r.multiple?(l.$activeIndex.sort(),i.$isActive(e)?l.$activeIndex.splice(l.$activeIndex.indexOf(e),1):l.$activeIndex.push(e),r.sort&&l.$activeIndex.sort()):l.$activeIndex=e,l.$activeIndex},i.select=function(e){var t=l.$matches[e].value;l.$apply(function(){i.activate(e),r.multiple?n.$setViewValue(l.$activeIndex.map(function(e){return l.$matches[e].value})):(n.$setViewValue(t),i.hide())}),l.$emit(r.prefixEvent+".select",t,e)},i.$updateActiveIndex=function(){n.$modelValue&&l.$matches.length?l.$activeIndex=r.multiple&&angular.isArray(n.$modelValue)?n.$modelValue.map(function(e){return i.$getIndex(e)}):i.$getIndex(n.$modelValue):l.$activeIndex>=l.$matches.length&&(l.$activeIndex=r.multiple?[]:0)},i.$isVisible=function(){return r.minLength&&n?l.$matches.length&&n.$viewValue.length>=r.minLength:l.$matches.length},i.$isActive=function(e){return r.multiple?-1!==l.$activeIndex.indexOf(e):l.$activeIndex===e},i.$getIndex=function(e){var t=l.$matches.length,n=t;if(t){for(n=t;n--&&l.$matches[n].value!==e;);if(!(0>n))return n}},i.$onMouseDown=function(e){if(e.preventDefault(),e.stopPropagation(),s){var t=angular.element(e.target);t.triggerHandler("click")}},i.$onKeyDown=function(e){if(/(9|13|38|40)/.test(e.keyCode)){if(e.preventDefault(),e.stopPropagation(),!r.multiple&&(13===e.keyCode||9===e.keyCode))return i.select(l.$activeIndex);38===e.keyCode&&l.$activeIndex>0?l.$activeIndex--:40===e.keyCode&&l.$activeIndex<l.$matches.length-1?l.$activeIndex++:angular.isUndefined(l.$activeIndex)&&(l.$activeIndex=0),l.$digest()}};var u=i.show;i.show=function(){u(),r.multiple&&i.$element.addClass("select-multiple"),setTimeout(function(){i.$element.on(s?"touchstart":"mousedown",i.$onMouseDown),r.keyboard&&t.on("keydown",i.$onKeyDown)})};var c=i.hide;return i.hide=function(){i.$element.off(s?"touchstart":"mousedown",i.$onMouseDown),r.keyboard&&t.off("keydown",i.$onKeyDown),c(!0)},i}var r=(angular.element(t.document.body),/(ip(a|o)d|iphone|android)/gi.test(t.navigator.userAgent)),s="createTouch"in t.document&&r;return i.defaults=e,i}]}).directive("bsSelect",["$window","$parse","$q","$select","$parseOptions",function(e,t,n,a,o){var i=a.defaults;return{restrict:"EAC",require:"ngModel",link:function(e,t,n,r){var s={scope:e};if(angular.forEach(["placement","container","delay","trigger","keyboard","html","animation","template","placeholder","multiple","allNoneButtons","maxLength","maxLengthHtml"],function(e){angular.isDefined(n[e])&&(s[e]=n[e])}),"select"===t[0].nodeName.toLowerCase()){var l=t;l.css("display","none"),t=angular.element('<button type="button" class="btn btn-default"></button>'),l.after(t)}var u=o(n.ngOptions),c=a(t,r,s),d=u.$match[7].replace(/\|.+/,"").trim();e.$watch(d,function(){u.valuesFn(e,r).then(function(e){c.update(e),r.$render()})},!0),e.$watch(n.ngModel,function(){c.$updateActiveIndex(),r.$render()},!0),r.$render=function(){var e,a;s.multiple&&angular.isArray(r.$modelValue)?(e=r.$modelValue.map(function(e){return a=c.$getIndex(e),angular.isDefined(a)?c.$scope.$matches[a].label:!1}).filter(angular.isDefined),e=e.length>(s.maxLength||i.maxLength)?e.length+" "+(s.maxLengthHtml||i.maxLengthHtml):e.join(", ")):(a=c.$getIndex(r.$modelValue),e=angular.isDefined(a)?c.$scope.$matches[a].label:!1),t.html((e?e:n.placeholder||i.placeholder)+i.caretHtml)},e.$on("$destroy",function(){c&&c.destroy(),s=null,c=null})}}}]),angular.module("mgcrea.ngStrap.tab",[]).provider("$tab",function(){var e=this.defaults={animation:"am-fade",template:"tab/tab.tpl.html",navClass:"nav-tabs"},t=this.controller=function(t,n,a){var o=this;o.$options=angular.copy(e),angular.forEach(["animation"],function(e){angular.isDefined(a[e])&&(o.$options[e]=a[e])}),o.$panes=t.$panes=[],o.$viewChangeListeners=[],o.$push=function(e){o.$panes.push(e)},o.$panes.$active=0,o.$setActive=t.$setActive=function(e){o.$panes.$active=e,o.$viewChangeListeners.forEach(function(e){e()})}};this.$get=function(){var n={};return n.defaults=e,n.controller=t,n}}).directive("bsTabs",["$window","$animate","$tab",function(e,t,n){var a=n.defaults;return{require:["?ngModel","bsTabs"],transclude:!0,scope:!0,controller:n.controller,templateUrl:function(e,t){return t.template||a.template},link:function(e,t,n,o){var i=o[0],r=o[1];e.$navClass=n.navClass||a.navClass,i&&(r.$viewChangeListeners.push(function(){i.$setViewValue(r.$panes.$active)}),i.$formatters.push(function(e){return r.$setActive(1*e),e}))}}}]).directive("bsPane",["$window","$animate","$sce",function(e,t,n){return{require:["^?ngModel","^bsTabs"],scope:!0,link:function(e,a,o,i){function r(){var n=s.$panes.indexOf(e),o=s.$panes.$active;t[n===o?"addClass":"removeClass"](a,"active")}var s=(i[0],i[1]);a.addClass("tab-pane"),o.$observe("title",function(t){e.title=n.trustAsHtml(t)}),s.$options.animation&&a.addClass(s.$options.animation),s.$push(e),s.$viewChangeListeners.push(function(){r()}),r()}}}]),angular.module("mgcrea.ngStrap.timepicker",["mgcrea.ngStrap.helpers.dateParser","mgcrea.ngStrap.tooltip"]).provider("$timepicker",function(){var e=this.defaults={animation:"am-fade",prefixClass:"timepicker",placement:"bottom-left",template:"timepicker/timepicker.tpl.html",trigger:"focus",container:!1,keyboard:!0,html:!1,delay:0,useNative:!0,timeType:"date",timeFormat:"shortTime",modelTimeFormat:null,autoclose:!1,minTime:-1/0,maxTime:+1/0,length:5,hourStep:1,minuteStep:5,iconUp:"glyphicon glyphicon-chevron-up",iconDown:"glyphicon glyphicon-chevron-down",arrowBehavior:"pager"};this.$get=["$window","$document","$rootScope","$sce","$locale","dateFilter","$tooltip",function(t,n,a,o,i,r,s){function l(t,n,a){function o(e,n){if(t[0].createTextRange){var a=t[0].createTextRange();a.collapse(!0),a.moveStart("character",e),a.moveEnd("character",n),a.select()}else t[0].setSelectionRange?t[0].setSelectionRange(e,n):angular.isUndefined(t[0].selectionStart)&&(t[0].selectionStart=e,t[0].selectionEnd=n)}function l(){t[0].focus()}var d=s(t,angular.extend({},e,a)),f=a.scope,p=d.$options,m=d.$scope,g=0,$=n.$dateValue||new Date,h={hour:$.getHours(),meridian:$.getHours()<12,minute:$.getMinutes(),second:$.getSeconds(),millisecond:$.getMilliseconds()},v=i.DATETIME_FORMATS[p.timeFormat]||p.timeFormat,y=/(h+)([:\.])?(m+)[ ]?(a?)/i.exec(v).slice(1);m.$iconUp=p.iconUp,m.$iconDown=p.iconDown,m.$select=function(e,t){d.select(e,t)},m.$moveIndex=function(e,t){d.$moveIndex(e,t)},m.$switchMeridian=function(e){d.switchMeridian(e)},d.update=function(e){angular.isDate(e)&&!isNaN(e.getTime())?(d.$date=e,angular.extend(h,{hour:e.getHours(),minute:e.getMinutes(),second:e.getSeconds(),millisecond:e.getMilliseconds()}),d.$build()):d.$isBuilt||d.$build()},d.select=function(e,t,a){(!n.$dateValue||isNaN(n.$dateValue.getTime()))&&(n.$dateValue=new Date(1970,0,1)),angular.isDate(e)||(e=new Date(e)),0===t?n.$dateValue.setHours(e.getHours()):1===t&&n.$dateValue.setMinutes(e.getMinutes()),n.$setViewValue(n.$dateValue),n.$render(),p.autoclose&&!a&&d.hide(!0)},d.switchMeridian=function(e){var t=(e||n.$dateValue).getHours();n.$dateValue.setHours(12>t?t+12:t-12),n.$setViewValue(n.$dateValue),n.$render()},d.$build=function(){var e,t,n=m.midIndex=parseInt(p.length/2,10),a=[];for(e=0;e<p.length;e++)t=new Date(1970,0,1,h.hour-(n-e)*p.hourStep),a.push({date:t,label:r(t,y[0]),selected:d.$date&&d.$isSelected(t,0),disabled:d.$isDisabled(t,0)});var o,i=[];for(e=0;e<p.length;e++)o=new Date(1970,0,1,0,h.minute-(n-e)*p.minuteStep),i.push({date:o,label:r(o,y[2]),selected:d.$date&&d.$isSelected(o,1),disabled:d.$isDisabled(o,1)});var s=[];for(e=0;e<p.length;e++)s.push([a[e],i[e]]);m.rows=s,m.showAM=!!y[3],m.isAM=(d.$date||a[n].date).getHours()<12,m.timeSeparator=y[1],d.$isBuilt=!0},d.$isSelected=function(e,t){return d.$date?0===t?e.getHours()===d.$date.getHours():1===t?e.getMinutes()===d.$date.getMinutes():void 0:!1},d.$isDisabled=function(e,t){var n;return 0===t?n=e.getTime()+6e4*h.minute:1===t&&(n=e.getTime()+36e5*h.hour),n<1*p.minTime||n>1*p.maxTime},m.$arrowAction=function(e,t){"picker"===p.arrowBehavior?d.$setTimeByStep(e,t):d.$moveIndex(e,t)},d.$setTimeByStep=function(e,t){{var n=new Date(d.$date),a=n.getHours(),o=(r(n,"h").length,n.getMinutes());r(n,"mm").length}0===t?n.setHours(a-parseInt(p.hourStep,10)*e):n.setMinutes(o-parseInt(p.minuteStep,10)*e),d.select(n,t,!0),f.$digest()},d.$moveIndex=function(e,t){var n;0===t?(n=new Date(1970,0,1,h.hour+e*p.length,h.minute),angular.extend(h,{hour:n.getHours()})):1===t&&(n=new Date(1970,0,1,h.hour,h.minute+e*p.length*p.minuteStep),angular.extend(h,{minute:n.getMinutes()})),d.$build()},d.$onMouseDown=function(e){if("input"!==e.target.nodeName.toLowerCase()&&e.preventDefault(),e.stopPropagation(),c){var t=angular.element(e.target);"button"!==t[0].nodeName.toLowerCase()&&(t=t.parent()),t.triggerHandler("click")}},d.$onKeyDown=function(e){if(/(38|37|39|40|13)/.test(e.keyCode)&&!e.shiftKey&&!e.altKey){if(e.preventDefault(),e.stopPropagation(),13===e.keyCode)return d.hide(!0);var t=new Date(d.$date),n=t.getHours(),a=r(t,"h").length,i=t.getMinutes(),s=r(t,"mm").length,l=/(37|39)/.test(e.keyCode),u=2+1*!!y[3];l&&(37===e.keyCode?g=1>g?u-1:g-1:39===e.keyCode&&(g=u-1>g?g+1:0));var c=[0,a];0===g?(38===e.keyCode?t.setHours(n-parseInt(p.hourStep,10)):40===e.keyCode&&t.setHours(n+parseInt(p.hourStep,10)),c=[0,a]):1===g?(38===e.keyCode?t.setMinutes(i-parseInt(p.minuteStep,10)):40===e.keyCode&&t.setMinutes(i+parseInt(p.minuteStep,10)),c=[a+1,a+1+s]):2===g&&(l||d.switchMeridian(),c=[a+1+s+1,a+1+s+3]),d.select(t,g,!0),o(c[0],c[1]),f.$digest()}};var w=d.init;d.init=function(){return u&&p.useNative?(t.prop("type","time"),t.css("-webkit-appearance","textfield"),void 0):(c&&(t.prop("type","text"),t.attr("readonly","true"),t.on("click",l)),w(),void 0)};var b=d.destroy;d.destroy=function(){u&&p.useNative&&t.off("click",l),b()};var D=d.show;d.show=function(){D(),setTimeout(function(){d.$element.on(c?"touchstart":"mousedown",d.$onMouseDown),p.keyboard&&t.on("keydown",d.$onKeyDown)})};var k=d.hide;return d.hide=function(e){d.$element.off(c?"touchstart":"mousedown",d.$onMouseDown),p.keyboard&&t.off("keydown",d.$onKeyDown),k(e)},d}var u=(angular.element(t.document.body),/(ip(a|o)d|iphone|android)/gi.test(t.navigator.userAgent)),c="createTouch"in t.document&&u;return e.lang||(e.lang=i.id),l.defaults=e,l}]}).directive("bsTimepicker",["$window","$parse","$q","$locale","dateFilter","$timepicker","$dateParser","$timeout",function(e,t,n,a,o,i,r){{var s=i.defaults,l=/(ip(a|o)d|iphone|android)/gi.test(e.navigator.userAgent);e.requestAnimationFrame||e.setTimeout}return{restrict:"EAC",require:"ngModel",link:function(e,t,n,a){var u={scope:e,controller:a};angular.forEach(["placement","container","delay","trigger","keyboard","html","animation","template","autoclose","timeType","timeFormat","modelTimeFormat","useNative","hourStep","minuteStep","length","arrowBehavior"],function(e){angular.isDefined(n[e])&&(u[e]=n[e])}),n.bsShow&&e.$watch(n.bsShow,function(e){c&&angular.isDefined(e)&&(angular.isString(e)&&(e=!!e.match(",?(timepicker),?")),e===!0?c.show():c.hide())}),l&&(u.useNative||s.useNative)&&(u.timeFormat="HH:mm");var c=i(t,a,u);u=c.$options;var d=r({format:u.timeFormat,lang:u.lang});angular.forEach(["minTime","maxTime"],function(e){angular.isDefined(n[e])&&n.$observe(e,function(t){c.$options[e]="now"===t?(new Date).setFullYear(1970,0,1):angular.isString(t)&&t.match(/^".+"$/)?+new Date(t.substr(1,t.length-2)):d.parse(t,new Date(1970,0,1,0)),!isNaN(c.$options[e])&&c.$build()})}),e.$watch(n.ngModel,function(){c.update(a.$dateValue)},!0),a.$parsers.unshift(function(e){if(!e)return a.$setValidity("date",!0),void 0;var t=d.parse(e,a.$dateValue);if(!t||isNaN(t.getTime()))a.$setValidity("date",!1);else{var n=t.getTime()>=u.minTime&&t.getTime()<=u.maxTime;a.$setValidity("date",n),n&&(a.$dateValue=t)}return"string"===u.timeType?o(t,u.modelTimeFormat||u.timeFormat):"number"===u.timeType?a.$dateValue.getTime():"iso"===u.timeType?a.$dateValue.toISOString():new Date(a.$dateValue)}),a.$formatters.push(function(e){var t;return t=angular.isUndefined(e)||null===e?0/0:angular.isDate(e)?e:"string"===u.timeType?d.parse(e,null,u.modelTimeFormat):new Date(e),a.$dateValue=t,a.$dateValue}),a.$render=function(){t.val(!a.$dateValue||isNaN(a.$dateValue.getTime())?"":o(a.$dateValue,u.timeFormat))},e.$on("$destroy",function(){c&&c.destroy(),u=null,c=null})}}}]),angular.module("mgcrea.ngStrap.tooltip",["mgcrea.ngStrap.helpers.dimensions"]).provider("$tooltip",function(){var e=this.defaults={animation:"am-fade",customClass:"",prefixClass:"tooltip",prefixEvent:"tooltip",container:!1,target:!1,placement:"top",template:"tooltip/tooltip.tpl.html",contentTemplate:!1,trigger:"hover focus",keyboard:!1,html:!1,show:!1,title:"",type:"",delay:0};this.$get=["$window","$rootScope","$compile","$q","$templateCache","$http","$animate","dimensions","$$rAF",function(n,a,o,i,r,s,l,u,c){function d(t,n){function i(){return"body"===h.container?u.offset(h.target[0]||t[0]):u.position(h.target[0]||t[0])}function r(e,t,n,a){var o,i=e.split("-");switch(i[0]){case"right":o={top:t.top+t.height/2-a/2,left:t.left+t.width};break;case"bottom":o={top:t.top+t.height,left:t.left+t.width/2-n/2};break;case"left":o={top:t.top+t.height/2-a/2,left:t.left-n};break;default:o={top:t.top-a,left:t.left+t.width/2-n/2}}if(!i[1])return o;if("top"===i[0]||"bottom"===i[0])switch(i[1]){case"left":o.left=t.left;break;case"right":o.left=t.left+t.width-n}else if("left"===i[0]||"right"===i[0])switch(i[1]){case"top":o.top=t.top-a;break;case"bottom":o.top=t.top+t.height}return o}var s={},d=t[0].nodeName.toLowerCase(),h=s.$options=angular.extend({},e,n);s.$promise=p(h.template);var v=s.$scope=h.scope&&h.scope.$new()||a.$new();h.delay&&angular.isString(h.delay)&&(h.delay=parseFloat(h.delay)),h.title&&(s.$scope.title=h.title),v.$hide=function(){v.$$postDigest(function(){s.hide()})},v.$show=function(){v.$$postDigest(function(){s.show()})},v.$toggle=function(){v.$$postDigest(function(){s.toggle()})},s.$isShown=v.$isShown=!1;var y,w;h.contentTemplate&&(s.$promise=s.$promise.then(function(e){var t=angular.element(e);return p(h.contentTemplate).then(function(e){var n=f('[ng-bind="content"]',t[0]);return n.length||(n=f('[ng-bind="title"]',t[0])),n.removeAttr("ng-bind").html(e),t[0].outerHTML})}));var b,D,k,S;return s.$promise.then(function(e){angular.isObject(e)&&(e=e.data),h.html&&(e=e.replace($,'ng-bind-html="')),e=m.apply(e),k=e,b=o(e),s.init()}),s.init=function(){h.delay&&angular.isNumber(h.delay)&&(h.delay={show:h.delay,hide:h.delay}),"self"===h.container?S=t:angular.isElement(h.container)?S=h.container:h.container&&(S=f(h.container));var e=h.trigger.split(" ");angular.forEach(e,function(e){"click"===e?t.on("click",s.toggle):"manual"!==e&&(t.on("hover"===e?"mouseenter":"focus",s.enter),t.on("hover"===e?"mouseleave":"blur",s.leave),"button"===d&&"hover"!==e&&t.on(g?"touchstart":"mousedown",s.$onFocusElementMouseDown))}),h.target&&(h.target=angular.isElement(h.target)?h.target:f(h.target)),h.show&&v.$$postDigest(function(){"focus"===h.trigger?t[0].focus():s.show()})},s.destroy=function(){for(var e=h.trigger.split(" "),n=e.length;n--;){var a=e[n];"click"===a?t.off("click",s.toggle):"manual"!==a&&(t.off("hover"===a?"mouseenter":"focus",s.enter),t.off("hover"===a?"mouseleave":"blur",s.leave),"button"===d&&"hover"!==a&&t.off(g?"touchstart":"mousedown",s.$onFocusElementMouseDown))}D&&(D.remove(),D=null),clearTimeout(y),v.$destroy()},s.enter=function(){return clearTimeout(y),w="in",h.delay&&h.delay.show?(y=setTimeout(function(){"in"===w&&s.show()},h.delay.show),void 0):s.show()},s.show=function(){v.$emit(h.prefixEvent+".show.before",s);var e=h.container?S:null,n=h.container?null:t;D&&D.remove(),D=s.$element=b(v,function(){}),D.css({top:"-9999px",left:"-9999px",display:"block",visibility:"hidden"}).addClass(h.placement),h.animation&&D.addClass(h.animation),h.type&&D.addClass(h.prefixClass+"-"+h.type),h.customClass&&D.addClass(h.customClass),l.enter(D,e,n,function(){v.$emit(h.prefixEvent+".show",s)}),s.$isShown=v.$isShown=!0,v.$$phase||v.$root&&v.$root.$$phase||v.$digest(),c(function(){s.$applyPlacement(),D.css({visibility:"visible"})}),h.keyboard&&("focus"!==h.trigger?(s.focus(),D.on("keyup",s.$onKeyUp)):t.on("keyup",s.$onFocusKeyUp))},s.leave=function(){return clearTimeout(y),w="out",h.delay&&h.delay.hide?(y=setTimeout(function(){"out"===w&&s.hide()},h.delay.hide),void 0):s.hide()},s.hide=function(e){s.$isShown&&(v.$emit(h.prefixEvent+".hide.before",s),l.leave(D,function(){return v.$emit(h.prefixEvent+".hide",s),e&&"focus"===h.trigger?t[0].blur():void 0}),s.$isShown=v.$isShown=!1,v.$$phase||v.$root&&v.$root.$$phase||v.$digest(),h.keyboard&&null!==D&&D.off("keyup",s.$onKeyUp))},s.toggle=function(){s.$isShown?s.leave():s.enter()},s.focus=function(){D[0].focus()},s.$applyPlacement=function(){if(D){var e=i(),t=D.prop("offsetWidth"),n=D.prop("offsetHeight"),a=r(h.placement,e,t,n);a.top+="px",a.left+="px",D.css(a)}},s.$onKeyUp=function(e){27===e.which&&s.$isShown&&(s.hide(),e.stopPropagation())},s.$onFocusKeyUp=function(e){27===e.which&&(t[0].blur(),e.stopPropagation())},s.$onFocusElementMouseDown=function(e){e.preventDefault(),e.stopPropagation(),s.$isShown?t[0].blur():t[0].focus()},s}function f(e,n){return angular.element((n||t).querySelectorAll(e))}function p(e){return i.when(r.get(e)||s.get(e)).then(function(t){return angular.isObject(t)?(r.put(e,t.data),t.data):t})}var m=String.prototype.trim,g="createTouch"in n.document,$=/ng-bind="/gi;return d}]}).directive("bsTooltip",["$window","$location","$sce","$tooltip","$$rAF",function(e,t,n,a,o){return{restrict:"EAC",scope:!0,link:function(e,t,i){var r={scope:e};angular.forEach(["template","contentTemplate","placement","container","target","delay","trigger","keyboard","html","animation","type","customClass"],function(e){angular.isDefined(i[e])&&(r[e]=i[e])}),angular.forEach(["title"],function(t){i.$observe(t,function(a,i){e[t]=n.trustAsHtml(a),angular.isDefined(i)&&o(function(){s&&s.$applyPlacement()})})}),i.bsTooltip&&e.$watch(i.bsTooltip,function(t,n){angular.isObject(t)?angular.extend(e,t):e.title=t,angular.isDefined(n)&&o(function(){s&&s.$applyPlacement()})},!0),i.bsShow&&e.$watch(i.bsShow,function(e){s&&angular.isDefined(e)&&(angular.isString(e)&&(e=!!e.match(",?(tooltip),?")),e===!0?s.show():s.hide())});var s=a(t,r);e.$on("$destroy",function(){s&&s.destroy(),r=null,s=null})}}}]),angular.module("mgcrea.ngStrap.typeahead",["mgcrea.ngStrap.tooltip","mgcrea.ngStrap.helpers.parseOptions"]).provider("$typeahead",function(){var e=this.defaults={animation:"am-fade",prefixClass:"typeahead",prefixEvent:"$typeahead",placement:"bottom-left",template:"typeahead/typeahead.tpl.html",trigger:"focus",container:!1,keyboard:!0,html:!1,delay:0,minLength:1,filter:"filter",limit:6};this.$get=["$window","$rootScope","$tooltip",function(t,n,a){function o(t,n,o){var i={},r=angular.extend({},e,o);i=a(t,r);var s=o.scope,l=i.$scope;l.$resetMatches=function(){l.$matches=[],l.$activeIndex=0},l.$resetMatches(),l.$activate=function(e){l.$$postDigest(function(){i.activate(e)})},l.$select=function(e){l.$$postDigest(function(){i.select(e)})},l.$isVisible=function(){return i.$isVisible()},i.update=function(e){l.$matches=e,l.$activeIndex>=e.length&&(l.$activeIndex=0)},i.activate=function(e){l.$activeIndex=e},i.select=function(e){var t=l.$matches[e].value;n.$setViewValue(t),n.$render(),l.$resetMatches(),s&&s.$digest(),l.$emit(r.prefixEvent+".select",t,e)},i.$isVisible=function(){return r.minLength&&n?l.$matches.length&&angular.isString(n.$viewValue)&&n.$viewValue.length>=r.minLength:!!l.$matches.length},i.$getIndex=function(e){var t=l.$matches.length,n=t;if(t){for(n=t;n--&&l.$matches[n].value!==e;);if(!(0>n))return n}},i.$onMouseDown=function(e){e.preventDefault(),e.stopPropagation()},i.$onKeyDown=function(e){/(38|40|13)/.test(e.keyCode)&&(i.$isVisible()&&(e.preventDefault(),e.stopPropagation()),13===e.keyCode&&l.$matches.length?i.select(l.$activeIndex):38===e.keyCode&&l.$activeIndex>0?l.$activeIndex--:40===e.keyCode&&l.$activeIndex<l.$matches.length-1?l.$activeIndex++:angular.isUndefined(l.$activeIndex)&&(l.$activeIndex=0),l.$digest())};var u=i.show;i.show=function(){u(),setTimeout(function(){i.$element.on("mousedown",i.$onMouseDown),r.keyboard&&t.on("keydown",i.$onKeyDown)})};var c=i.hide;return i.hide=function(){i.$element.off("mousedown",i.$onMouseDown),r.keyboard&&t.off("keydown",i.$onKeyDown),c()},i}angular.element(t.document.body);return o.defaults=e,o}]}).directive("bsTypeahead",["$window","$parse","$q","$typeahead","$parseOptions",function(e,t,n,a,o){var i=a.defaults;return{restrict:"EAC",require:"ngModel",link:function(e,t,n,r){var s={scope:e};angular.forEach(["placement","container","delay","trigger","keyboard","html","animation","template","filter","limit","minLength","watchOptions","selectMode"],function(e){angular.isDefined(n[e])&&(s[e]=n[e])});var l=s.filter||i.filter,u=s.limit||i.limit,c=n.ngOptions;l&&(c+=" | "+l+":$viewValue"),u&&(c+=" | limitTo:"+u);var d=o(c),f=a(t,r,s);if(s.watchOptions){var p=d.$match[7].replace(/\|.+/,"").replace(/\(.*\)/g,"").trim();e.$watch(p,function(){d.valuesFn(e,r).then(function(e){f.update(e),r.$render()})},!0)}e.$watch(n.ngModel,function(t){e.$modelValue=t,d.valuesFn(e,r).then(function(e){if(s.selectMode&&!e.length&&t.length>0)return r.$setViewValue(r.$viewValue.substring(0,r.$viewValue.length-1)),void 0;e.length>u&&(e=e.slice(0,u));var n=f.$isVisible();n&&f.update(e),(1!==e.length||e[0].value!==t)&&(!n&&f.update(e),r.$render())})}),r.$render=function(){if(r.$isEmpty(r.$viewValue))return t.val("");var e=f.$getIndex(r.$modelValue),n=angular.isDefined(e)?f.$scope.$matches[e].label:r.$viewValue;n=angular.isObject(n)?n.label:n,t.val(n.replace(/<(?:.|\n)*?>/gm,"").trim())},e.$on("$destroy",function(){f&&f.destroy(),s=null,f=null})}}}])}(window,document);