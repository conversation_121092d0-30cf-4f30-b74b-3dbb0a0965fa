<!DOCTYPE HTML>
<html>
<head>
    <title>Jasmine Spec Runner</title>

    <link href="lib/jasmine_favicon.png" rel="shortcut icon" type="image/png">

    <link href="lib/jasmine.css" rel="stylesheet" type="text/css">
    <script src="lib/jasmine.js" type="text/javascript"></script>
    <script src="lib/jasmine-html.js" type="text/javascript"></script>
    <script src="lib/json2.js" type="text/javascript"></script>

    <!-- include helper files here... -->
    <script src="helpers/h.js"></script>
    <script src="helpers/h-kill.js"></script>
    <script src="helpers/h-matchers.js"></script>

    <!-- include source files here... -->
    <script src="../es5-shim.js"></script>
    <script src="../es5-sham.js"></script>

    <!-- include spec files here... -->
    <script src="spec/s-array.js"></script>
    <script src="spec/s-function.js"></script>
    <script src="spec/s-string.js"></script>
    <script src="spec/s-object.js"></script>
    <script src="spec/s-number.js"></script>
    <script src="spec/s-date.js"></script>


    <script type="text/javascript">
        (function () {
            var jasmineEnv = jasmine.getEnv();
            jasmineEnv.updateInterval = 1000;

            var trivialReporter = new jasmine.TrivialReporter();

            jasmineEnv.addReporter(trivialReporter);

            jasmineEnv.specFilter = function (spec) {
                return trivialReporter.specFilter(spec);
            };

            var currentWindowOnload = window.onload;

            window.onload = function () {
                if (currentWindowOnload) {
                    currentWindowOnload();
                }
                execJasmine();
            };

            function execJasmine() {
                jasmineEnv.execute();
            }

        })();
    </script>

</head>

<body>
</body>
</html>
