/*!
 * angular-translate - v2.4.2 - 2014-10-21
 * http://github.com/angular-translate/angular-translate
 * Copyright (c) 2014 ; Licensed MIT
 */
angular.module('pascalprecht.translate').factory('$translateStaticFilesLoader', [
    '$q',
    '$http',
    function ($q, $http) {
        return function (options) {
            if (!options || (!angular.isString(options.prefix) || !angular.isString(options.suffix))) {
                throw new Error('Couldn\'t load static files, no prefix or suffix specified!');
            }
            var deferred = $q.defer();
            $http(angular.extend({
                url: [
                    options.prefix,
                    options.key,
                    options.suffix
                ].join(''),
                method: 'GET',
                params: ''
            }, options.$http)).success(function (data) {
                deferred.resolve(data);
            }).error(function (data) {
                deferred.reject(options.key);
            });
            return deferred.promise;
        };
    }
]);