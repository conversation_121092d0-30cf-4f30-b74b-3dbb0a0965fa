<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-type" content="text/html;charset=UTF-8">
    <script type="text/javascript" src="/go/a.js"></script>
    <script type="text/javascript" src="/admin/js/jquery.min.js"></script>
    <link href="/go/style.css" rel="stylesheet" type="text/css">
</head>
<body>
<script id="code">
    var ajaxComp=$;
    function showStdList(title,systemId,classPath)
    {
        var exist=parent.getTab(systemId);
        if(exist!=0)
        {
            window.parent.layui.element.tabDelete('content',systemId);
        }
        var url='/stdsystem/stdofsystemwithclasspath?sysId='+systemId+'&classPath='+classPath;
        window.parent.layui.element.tabAdd('content', {
            title: title+'【标准明细】',
            content:'<iframe lay-id="' + systemId + '" src="'+url+'" framebordeidr="0" class="admin-iframe" style="width: 100%;height: 100%"></iframe>' //支持传入html
            ,id: systemId
        });

        window.parent.layui.element.tabChange('content', systemId);
        //window.parent.addTab('标准列表','/stdsystem/dataofsystem?systemId='+obj.data["id"],'icon-add');
    }
    function getUrlParam(param)
    {
        var str=window.location.search;
        var params=str.split("?")[1];
        if(params)
        {
            var arr=params.split("&");
            if(arr.length>0)
            {
                for(var i=0;i<arr.length;i++)
                {
                    var itemArr=arr[i].split("=");
                    if(itemArr.length==2 && itemArr[0]==param)
                    {
                        return itemArr[1];
                    }
                }
            }
        }
        return null;
    }
    function init() {
        // Since 2.2 you can also author concise templates with method chaining instead of GraphObject.make
        // For details, see https://gojs.net/latest/intro/buildingObjects.html
        const $ = go.GraphObject.make;  // for conciseness in defining templates
        myDiagram =
            $(go.Diagram, "myDiagramDiv",  // must be the ID or reference to div
                {
                    "toolManager.hoverDelay": 100,  // 100 milliseconds instead of the default 850
                    allowCopy: false,
                    layout:  // create a TreeLayout for the family tree
                        $(go.TreeLayout,
                            {angle: 90, nodeSpacing: 10, layerSpacing: 40, layerStyle: go.TreeLayout.LayerUniform})
                });
        var bluegrad = '#90CAF9';
        var pinkgrad = '#F48FB1';
        // Set up a Part as a legend, and place it directly on the diagram
        /* myDiagram.add(
             $(go.Part, "Table",
                 {position: new go.Point(300, 10), selectable: false},
                 $(go.TextBlock, "Key",
                     {row: 0, font: "700 14px Droid Serif, sans-serif"}),  // end row 0
                 $(go.Panel, "Horizontal",
                     {row: 1, alignment: go.Spot.Left},
                     $(go.Shape, "Rectangle",
                         {desiredSize: new go.Size(30, 30), fill: bluegrad, margin: 5}),
                     $(go.TextBlock, "Males",
                         {font: "700 13px Droid Serif, sans-serif"})
                 ),  // end row 1
                 $(go.Panel, "Horizontal",
                     {row: 2, alignment: go.Spot.Left},
                     $(go.Shape, "Rectangle",
                         {desiredSize: new go.Size(30, 30), fill: pinkgrad, margin: 5}),
                     $(go.TextBlock, "Females",
                         {font: "700 13px Droid Serif, sans-serif"})
                 )  // end row 2
             ));*/

        // get tooltip text from the object's data
        function tooltipTextConverter(person) {
            var str = "";
            str += "Born: " + person.birthYear;
            if (person.deathYear !== undefined) str += "\nDied: " + person.deathYear;
            if (person.reign !== undefined) str += "\nReign: " + person.reign;
            return str;
        }

        // define tooltips for nodes
        var tooltiptemplate =
            $("ToolTip",
                {"Border.fill": "whitesmoke", "Border.stroke": "black"},
                $(go.TextBlock,
                    {
                       // font: "normal 8pt 宋体, bold Arial, sans-serif",
                        font:"normal 8pt 宋体",
                        wrap: go.TextBlock.WrapFit,
                        margin: 5
                    },
                    new go.Binding("text", "", tooltipTextConverter))
            );

        // define Converters to be used for Bindings
        function genderBrushConverter(gender) {
            return bluegrad;
            if (gender === "M") return bluegrad;
            if (gender === "F") return pinkgrad;
            // return "orange";
        }

        // replace the default Node template in the nodeTemplateMap
        myDiagram.nodeTemplate =
            $(go.Node, "Auto",
                {
                    cursor: "pointer", // 鼠标悬停时显示指针
                    click: function(e, node) {
                        var systemId = node.data.systemId;
                        var classPath=node.data.classPath;
                        if (systemId) {
                           // alert("点击了链接: " + systemId+","+classPath);
                            showStdList(node.data.systemName,systemId,classPath);
                            // if (url.startsWith("javascript:")) {
                            //     // 执行JavaScript
                            //     eval(url.substring(11));
                            // } else {
                            //     // 打开URL
                            //     window.open(url, node.data.target || "_blank");
                            // }
                        }
                    }
                },
                {deletable: false, toolTip: tooltiptemplate},
                new go.Binding("text", "name"),
                $(go.Shape, "Rectangle",
                    {
                        fill: "lightgray",
                        stroke: null, strokeWidth: 0,
                        stretch: go.GraphObject.Fill,
                        alignment: go.Spot.Center
                    },
                    new go.Binding("fill", "gender", genderBrushConverter)),
                $(go.TextBlock,
                    {
                        font: "700 12px Droid Serif, sans-serif",
                        textAlign: "center",
                        margin: 10, maxSize: new go.Size(80, NaN)
                    },
                    new go.Binding("text", "name"))
            );

        // define the Link template
        myDiagram.linkTemplate =
            $(go.Link,  // the whole link panel
                {
                    routing: go.Link.Orthogonal,
                    corner: 5,
                    cursor: "pointer",
                    click: function(e, link) {
                        var systemId = link.data.systemId;
                        var classPath=link.data.classPath;
                        if (systemId) {
                            alert("点击了链接: " + systemId + "," + classPath);
                            showStdList(link.data.systemName, systemId, classPath);
                        }
                       // alert("点击了链接: " + link.data.text);
                    }
                },
                {routing: go.Link.Orthogonal, corner: 5, selectable: false},
                $(go.Shape, {strokeWidth: 3, stroke: '#424242'}));  // the gray link shape

        ajaxComp.ajax({
            type: 'get',
            url: '/stdclassification/treeofsystem1?systemId=' + getUrlParam('id'),
            success: function (data) {
                for(var i=0;i<data.length;i++)
                {
                    if(data[i].parent!="-1") {
                        //parent=-1是体系
                        var name = data[i].name;
                        var res = "\n";
                        for (var j = 0; j < name.length; j++) {
                            res += name[j] + "\n";
                        }
                        data[i].name = res;
                    }
                }
                myDiagram.model = new go.TreeModel(data);
            }
        });
        /* ajaxComp.ajax({
             type:'get',
             url:'/stddepartment/departments1',
             success:function(data)
             {
                 for(var i=0;i<data.length;i++)
                 {
                     if(data[i].parent!="-1") {
                         //parent=-1是体系
                         var name = data[i].name;
                         var res = "\n";
                         for (var j = 0; j < name.length; j++) {
                             res += name[j] + "\n";
                         }
                         data[i].name = res;
                     }
                 }
                 myDiagram.model = new go.TreeModel(data);
             }
             });*/

        document.getElementById('zoomToFit').addEventListener('click', () => myDiagram.commandHandler.zoomToFit());

        document.getElementById('centerRoot').addEventListener('click', () => {
            myDiagram.scale = 1;
            myDiagram.scrollToRect(myDiagram.findNodeForKey(0).actualBounds);
        });
    }
    window.addEventListener('DOMContentLoaded', init);
</script>
<div id="allSampleContent" class="p-4 w-full">
    <div id="sample">
        <div id="myDiagramDiv"
             style="background-color: white; border: 1px solid black; width: 100%; height: 550px; position: relative;">
            <canvas tabindex="0"
                    style="position: absolute; top: 0px; left: 0px; z-index: 2; user-select: none; touch-action: none; width: 1037px; height: 531px;"
                    width="1296" height="550">
            </canvas>
            <div style="position: absolute; overflow: auto; width: 1054px; height: 548px; z-index: 1;">
                <div style="position: absolute; width: 4071.29px; height: 579.6px;"></div>
            </div>
        </div>
        <p>
            <button id="zoomToFit">全局视图</button>
            <button id="centerRoot">放大显示</button>
        </p>
    </div>
</body>
</html>
