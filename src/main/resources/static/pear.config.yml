## 网站配置
logo:
  ## 网站名称
  title: "集团公司标准资源服务平台"
  ## 网站图标
  image: "admin/images/logo.png"
## 菜单配置
menu:
  ## 菜单数据来源
  data: "/system/menu/data"
  ## 菜单接口的请求方式 GET / POST
  method: "GET"
  ## 是否同时只打开一个菜单目录
  accordion: true
  ## 侧边默认折叠状态
  collaspe: false
  ## 是否开启多系统菜单模式
  control: false
  ## 顶部菜单宽度 PX
  controlWidth: 500
  ## 默认选中的菜单项
  select: "10"
  ## 是否开启异步菜单，false 时 data 属性设置为静态数据，true 时为后端接口
  async: true
## 视图内容配置
tab:
  ## 是否开启多选项卡
  enable: true
  ## 保持视图状态
  keepState: true
  ## 开启选项卡记忆
  session: true
  ## 最大可打开的选项卡数量
  max: "30"
  ## 首页
  index:
    id: "10" ## 标识 ID , 建议与菜单项中的 ID 一致
    href: "/chinastd/statistics" ## 页面地址
    title: "首页" ## 标题
## 主题配置
theme:
  ## 默认主题色，对应 colors 配置中的 ID 标识
  defaultColor: "2"
  ## 默认的菜单主题 dark-theme 黑 / light-theme 白
  defaultMenu: "dark-theme"
  ## 默认的顶部主题 dark-theme 黑 / light-theme 白
  defaultHeader: "light-theme"
  ## 是否允许用户切换主题，false 时关闭自定义主题面板
  allowCustom: true
  ## 通栏配置
  banner: false
## 主题色配置列表
colors:
  - id: "1"
    color: "#2d8cf0"
    second: "#ecf5ff"
  - id: "2"
    color: "#36b368"
    second: "#f0f9eb"
  - id: "3"
    color: "#f6ad55"
    second: "#fdf6ec"
  - id: "4"
    color: "#f56c6c"
    second: "#fef0f0"
  - id: "5"
    color: "#3963bc"
    second: "#ecf5ff"
## 其他配置
other:
  ## 主页动画时长
  keepLoad: "1200"
  ## 布局顶部主题
  autoHead: false
## 头部配置
header:
  ## 站内消息，通过 false 设置关闭
  message: "/system/notice/notice"
