.dept-tree {
    width: 100%;
    height: -webkit-calc(100vh - 247px);
    height: -moz-calc(100vh - 247px);
    height: calc(100vh - 247px);
    margin-top: 20px;
}

.dtree-laySimple-item-this {
    background-color: transparent !important;
}

.dtree-nav-div:hover {
    background-color: transparent !important;
}

.button {
    margin-top: 10px;
    width: 94%;
    margin-left: 3%;
    display: block;
    height: 40px;
    line-height: 40px;
    padding: 0 15px;
    white-space: nowrap;
    text-align: center;
    font-size: 14.5px;
    border: none;
    cursor: pointer;
    box-sizing: border-box;
    display: inline-block;
    outline: 0;
    border-radius: 2px;
    -webkit-appearance: none;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.15);
}

.button-primary {
    background-color: #5FB878;
    color: white;
}

.button-default {
    color: #2f495e;
    background-color: #edf2f7;
}

.user-main {
    width: calc(100% - 312px);
    float: right;
}

.user-left {
    width: 300px;
    float: left;
}

.user-collasped.user-main {
    width: 100%;
}

.user-collasped.user-left {
    width: 0px;
}

.user-collasped.user-left .user-group {
    display: none;
}