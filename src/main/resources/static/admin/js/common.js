window.commonSmartSearch=function(table,conditionArr)
{
    if($(".simple-container").css("display")=="flex" || $(".simple-container").css("display")=="block")
    {
        var key = $('#searchKey').val();
        table.reload('role-table', {where: {simpleSearchKey:key},page: { curr: 1 }});
        $("#clearSimpleCon").show();
        window.createKeywords();
    }
    else if($(".advance-container").css("display")=="flex" || $(".advance-container").css("display")=="block")
    {
        var fields=[];
        for(var i=0;i<conditionArr.length;i++)
        {
            var obj={};
            obj.logic="AND";
            if(conditionArr[i]>1)
            {
                obj.logic= $("#conAndOr"+conditionArr[i]).val();
            }
            obj.field=$("#conField"+conditionArr[i]).val();
            obj.val=$("#conVal"+conditionArr[i]).val();
            obj.type=$("#conQueryType"+conditionArr[i]).val();
            fields.push(obj);
        }
        table.reload('role-table', {where: {advSearchKey:JSON.stringify(fields)},page: { curr: 1 }});
        window.createKeywords();
    }
};
window.collect=function(obj,logType,columnId,columnName,callback)
{
    var data=obj.data;
    console.log(obj);
    //取消收藏
    if(obj.data["isCollected"]=="0")
    {
        $.ajax({
            url: "/personalcoll/collect",
            dataType: 'json',
            type: 'post',
            data:JSON.stringify({stdid: obj.data['id']}),
            contentType: 'application/json',
            success: function (result) {
                if (result.success) {
                    layer.msg("收藏成功，请在个人收藏夹中查看！", {icon: 1, time: 1000});
                    if(callback)
                    {
                        callback();
                    }
                } else {
                    layer.msg("收藏失败！", {icon: 5, time: 1000});
                }
            }
        });
        if(!logType)
        {
            logType='Standard_Collect';
        }
        window.writelog(logType,obj.data["stdNo"],obj.data["stdOrgName"],columnId,columnName,"");
    }
    else  if(obj.data["isCollected"]=="1")
    {
        //取消收藏
        $.ajax({
            url: "/personalcoll/discollect",
            dataType: 'json',
            type: 'post',
            data:JSON.stringify({stdid: obj.data['id']}),
            contentType: 'application/json',
            success: function (result) {
                if (result.success) {
                    layer.msg("已取消收藏!", {icon: 1, time: 1000});
                    if(callback)
                    {
                        callback();
                    }
                } else {
                    layer.msg("取消收藏失败！", {icon: 5, time: 1000});
                }
            }
        })
    }
};
window.discollect=function(obj,callback)
{
    $.ajax({
        url: "/personalcoll/discollect",
        dataType: 'json',
        type: 'post',
        data:JSON.stringify({stdid: obj.data['id']}),
        contentType: 'application/json',
        success: function (result) {
            if (result.success) {
                layer.msg("已取消收藏！", {icon: 1, time: 1000},function(){
                    if(callback)
                    {
                        callback();
                    }
                });
            } else {
                layer.msg("取消失败，请重试！", {icon: 5, time: 1000});
            }
        }
    })
};

window.writelog=function(_logTypeName, _itemId, _itemName, _columnId, _columnName, _param)
{
    var data={logTypeName:_logTypeName,itemId:_itemId,itemName:_itemName,columnId:_columnId,columnName:_columnName,param:_param};
    console.log('日志记录');
    console.log(data);
    $.ajax({
        url: "/stdlog/log",
        dataType: 'json',
        type: 'post',
        data:JSON.stringify(data),
        contentType: 'application/json',
        success: function (result) {
            if (result.success) {
                 console.log('日志记录成功')
            } else {
                layer.msg("日志记录失败"+result.msg, {icon: 5, time: 1000});
            }
        }
    })
};

window.onlineRead=function(obj,logType,columnId,columnName)
{
    $.ajax({
        url: "/stddownload/canPreview?stdInfoId="+obj.data["id"],
        dataType: 'json',
        type: 'get',
        success: function (result) {
            if (result.success) {
                if(result.data)
                {
                    $.ajax({
                        url: "/source/isBigFile?id=" + obj.data["id"],
                        dataType: 'json',
                        type: 'get',
                        success: function (result) {
                            if (result.success && result.data) {
                                if(result.data*1>104857600)
                                {
                                    //大文件，要先等待对应时间再加载
                                    var waitSeconds= Math.ceil(result.data*1/14898831);
                                    layer.msg("文件较大，正在加载中，请耐心等待...", {icon: 1, time: (waitSeconds+14)*1000});
                                    setTimeout(function(){ window.open('/pdf/viewer.html?file=/source/getStandardPdf/preview/' + obj.data["id"],'blank',null,true);},(waitSeconds+14)*1000);
                                }
                                else
                                {
                                    window.open('/pdf/viewer.html?file=/source/getStandardPdf/preview/' + obj.data["id"],'blank',null,true);
                                }
                                /* parent.layer.open({
                                     type: 2,
                                     title: 'pdf预览',
                                     shade: 0.1,
                                     area: ['100%', '100%'],
                                     content:  '/pdf/viewer.html?file=/source/getStandardPdf/' + result.data
                                 });*/
                            } else {
                                popup.failure(result.msg);
                            }
                        }
                    })
                    if(!logType)
                    {
                        logType='Standard_Online_Read';
                    }
                    window.writelog(logType,obj.data["stdNo"],obj.data["stdOrgName"],columnId,columnName,"");
                }
                else{
                    var index= layer.alert('你当日预览量已达上限，无法继续预览。', {
                        title: '提示', // 提示框标题
                        icon: 1, // icon图标类型，0-6可选
                        btn: ['确定'] // 按钮文字，不设置则默认为“是”
                    }, function(){
                        // 点击确定后的回调函数
                        layer.close(index);
                    });
                }
            }
        }

    });

};
//solr搜索页面：查看一个标准的更多匹配结果
window.showMore = function (id,stdNo,stdOrgName) {
    $('#light_'+id).slideToggle();
    console.log('id_'+id+':'+$('#light_'+id).css("display"));
    window.writelog("Solr_Query_More_Result",stdNo,stdOrgName,'','',$("#txtKey").val());
};
window.download = function (obj) {
    $.ajax({
        url: "/stddownload/canDownload?stdInfoId="+obj.data["id"],
        dataType: 'json',
        type: 'get',
        success: function (result) {
            if (result.success) {
                if(result.data)
                {
                    window.open('/source/getStandardPdf/download/'+encodeURIComponent(obj.data["id"]));
                    // window.open('/source/download?fileName='+encodeURIComponent(obj.data["pdfFileName"]));
                    window.writelog('Standard_Download',obj.data["stdNo"],obj.data["stdOrgName"],'','','');
                }
                else{
                   var index= layer.alert('你当日下载量已达上限，无法继续下载。', {
                    title: '提示', // 提示框标题
                    icon: 1, // icon图标类型，0-6可选
                    btn: ['确定'] // 按钮文字，不设置则默认为“是”
                }, function(){
                    // 点击确定后的回调函数
                       layer.close(index);
                   });
                }
            }
        }

    });

}
window.preview = function (obj) {
    console.log("Preview function called with:", obj);
    $.ajax({
        url: "/stddownload/canPreview?stdInfoId="+obj.data["id"],
        dataType: 'json',
        type: 'get',
        success: function (result) {
            console.log("canPreview result:", result);
            if (result.success) {
                if(result.data)
                {
                    $.ajax({
                        url: "/source/isBigFile?id=" + obj.data["id"],
                        dataType: 'json',
                        type: 'get',
                        success: function (result) {
                            if (result.success && result.data) {
                                if(result.data*1>104857600)
                                {
                                    //大文件，要先等待对应时间再加载
                                    var waitSeconds= Math.ceil(result.data*1/14898831);
                                    layer.msg("文件较大，正在加载中，请耐心等待...", {icon: 1, time: (waitSeconds+14)*1000});
                                    setTimeout(function(){ window.open('/pdf/viewer.html?file=/source/previewStandardPdf/' + obj.data["id"],'blank',null,true);},(waitSeconds+14)*1000);
                                }
                                else
                                {
                                    window.open('/pdf/viewer.html?file=/source/previewStandardPdf/' + obj.data["id"],'blank',null,true);
                                }
                            } else {
                                var errorMsg = result.msg || '获取文件信息失败';
                                layer.alert(errorMsg, {
                                    title: '错误',
                                    icon: 2,
                                    btn: ['确定']
                                });
                            }
                        }
                    });
                    window.writelog('Standard_Preview',obj.data["stdNo"],obj.data["stdOrgName"],'','','');
                }
                else{
                   var message = result.msg || '你当日预览量已达上限，无法继续预览。';
                   var index= layer.alert(message, {
                    title: '提示', // 提示框标题
                    icon: 1, // icon图标类型，0-6可选
                    btn: ['确定'] // 按钮文字，不设置则默认为"是"
                   }, function(){
                    // 点击确定后的回调函数
                       layer.close(index);
                   });
                }
            } else {
                var errorMsg = result.msg || '检查预览权限时发生错误';
                layer.alert(errorMsg, {
                    title: '错误',
                    icon: 2,
                    btn: ['确定']
                });
            }
        },
        error: function (xhr, status, error) {
            console.error("canPreview AJAX error:", error);
            layer.alert('检查预览权限时发生错误: ' + error, {
                title: '错误',
                icon: 2,
                btn: ['确定']
            });
        }
    });
}

//根据标准号获取标准信息
window.getStdInfoByNo=function(stdNo,callback)
{
    $.ajax({
        url: "/chinastd/getByNo?stdNo=" + stdNo,
        dataType: 'json',
        type: 'get',
        success: function (result) {
            if (result.success) {
                if(callback && typeof(callback)==="function")
                {
                    callback(result);
                }
            }
        }
    });
}
//格式化数字以国际化的形式展示（三位数字一个逗号）
window.formatNumber=function(num) {
    return new Intl.NumberFormat().format(num);
}
