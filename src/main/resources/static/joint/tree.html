<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <head>
        <link rel="stylesheet" type="text/css" href="/joint/css/joint.css" />
        <!-- dependencies -->
        <script type="text/javascript" src="/joint/js/jquery.js"></script>
        <script  type="text/javascript"  src="/joint/js/lodash.js"></script>
        <script  type="text/javascript"  src="/joint/js/backbone.js"></script>
        <script type="text/javascript"   src="/joint/js/joint.js"></script>
    </head>
</head>
<body>
<div id="mycanvas"></div>
<!-- code -->
<script type="text/javascript">
    var graph=new joint.dia.Graph;  //创建模型
    var paperWidth=1900;
    var  paper=new joint.dia.Paper({
        el: $('#mycanvas'),      //纸放在哪
        width: paperWidth,            //纸多大
        height: 1000,
        model: graph,       //纸里有什么
        gridSize: 1
    });
    function splitTitle(title)
    {
        var result="\n\n";
        for(var i=0;i<title.length;i++)
        {
            result+=title[i]+"\n";
        }
        result+="\n";
        return result;
    }
    $.ajax({
        type:'get',
        url:'/stdclassification/treeofsystem?systemId='+getUrlParam('id'),
        success:function(data)
        {
            try
            {
                var y=30;
                var x=0;
                //最上层
                var root = new joint.shapes.basic.Rect({
                    position: { x: paperWidth/2-16*(data[0].title.length)/2, y: y },
                    size: { width: 26*(data[0].title.length), height:30 },
                    attrs: {
                        text: { text: data[0].title }}
                });
                paper.model.addCell(root);
                //第一层
                var son1Arr=data[0].children;
                var itemWidth=200;
                if(son1Arr.length>0)
                {
                    itemWidth=paperWidth*1.0/son1Arr.length;
                }
                for(var i=0;i<son1Arr.length;i++)
                {
                    var subY=y+80;
                    var subX=itemWidth*i+(itemWidth-35)/2;
                    var itemLeft=itemWidth*i;
                    var item=son1Arr[i];
                   /* var wraptext = joint.util.breakText(item.title, {
                        width: 30,
                        styles:{fontFamily:'宋体',fontSize:'12px',lineHeight:'16px'}
                    });*/
                    var parent=new joint.shapes.basic.Rect({
                        position: { x: subX, y: subY },
                        size: { width: 35, height:20*(item.title.length)  },
                        attrs: {
                            position: { x: x+10, y: subY+15},
                            rect: { fill: 'white' },
                            text: { text: splitTitle(item.title),fill:'black' }
                        },
                       /* attrs:{
                            text:wraptext,
                        }*/
                    });
                   /* parent.attr('text', {
                        textWrap: true,
                        maxWidth: 15
                    });*/
                    var link = new  joint.dia.Link({
                        source:root,
                        target:parent,
                        /*  smooth: true,
                        manhattan: true,*/
                        attrs: {
                            /*'.marker-source': { d: 'M 10 0 L 0 5 L 10 10 z' },*/
                            '.marker-target': { d: 'M 10 0 L 0 5 L 10 10 z' }
                        }
                    });
                    paper.model.addCell([parent,link]);
                   /* var textArray=[];
                    for(var wi=0;wi<item.title.length;wi++)
                    {
                        var textItem=new joint.shapes.basic.Text({
                            position: { x: x+10, y: subY},
                            size: { width: 30, height: 10 },
                            attrs: { text: { text: item.title[wi],'font-size':9,'font-family:':'宋体'} }
                        });
                        textItem.embed(parent);
                        textArray.push(textItem);
                        subY+=17;
                    }
                    paper.model.addCell([parent,...textArray]);
                     var link = new  joint.dia.Link({
                        source:root.id,
                        target:parent.id,
                    });
                    link.addTo(graph);*/
                    dealSubChild(item,parent,itemLeft,y+80+20*(item.title.length),itemWidth);
                }
            }
            finally
            {
                // Updates the display
             /*   paper.render();*/
            }
        }
    });
    function dealSubChild(classify,parent,x,y,parentWidth)
    {
        var son1Arr=classify.children;
        var itemWidth=200;
        if(son1Arr.length>0)
        {
            itemWidth=parentWidth/son1Arr.length;
        }
        for(var i=0;i<son1Arr.length;i++) {
            var subY=y+80;
            var subX=x+itemWidth*i+(itemWidth-35)/2;
            var itemLeft=x+itemWidth*i;
            var item = son1Arr[i];
            var sub = new joint.shapes.basic.Rect({
                position: {x: subX, y:subY},
                size: {width: 35, height: 20 * (item.title.length)},
                attrs: {
                    position: { x: x+10, y: y+15},
                    rect: { fill: 'white' },
                    text: { text: splitTitle(item.title),fill:'black' }
                },
            });
            paper.model.addCell(sub);
            /* var textArray = [];
             for (var wi = 0; wi < item.title.length; wi++) {
                 y += 30;
                 var textItem = new joint.shapes.basic.Text({
                     position: {x: x+10, y: y},
                     size: {width: 30, height: 10},
                     attrs: {text: {text: item.title[wi],'font-size':9,'font-family:':'宋体'}}
                 });
                 textItem.embed(sub);
                 textArray.push(textItem);
               paper.model.addCell([sub, ...textArray]);
            }*/
             var link = new  joint.dia.Link({
                 source:parent,
                 target:sub,
                 /*  smooth: true,
                 manhattan: true,*/
                 attrs: {
                     /*'.marker-source': { d: 'M 10 0 L 0 5 L 10 10 z' },*/
                     '.marker-target': { d: 'M 10 0 L 0 5 L 10 10 z' }
                 }
             });
            paper.model.addCell(link);
            dealSubChild(item, sub,itemLeft,y+80+20*(item.title.length),itemWidth);
        }
    }

    function getUrlParam(param)
    {
        var str=window.location.search;
        var params=str.split("?")[1];
        if(params)
        {
            var arr=params.split("&");
            if(arr.length>0)
            {
                for(var i=0;i<arr.length;i++)
                {
                    var itemArr=arr[i].split("=");
                    if(itemArr.length==2 && itemArr[0]==param)
                    {
                        return itemArr[1];
                    }
                }
            }
        }
        return null;
    };
</script>
</body>
<style>
  /*  tspan
    {
        font-size:4px;
        font-family: 宋体;
    }
    g{
        transform:scale(1,1);
    }*/
</style>
</html>
