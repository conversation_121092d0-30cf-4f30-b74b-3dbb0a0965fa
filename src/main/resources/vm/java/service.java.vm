package ${packageName}.service;

import java.util.List;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.web.domain.request.PageDomain;
import ${packageName}.domain.${ClassName};
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service extends IService<${ClassName}> {

    /**
     * 查询${functionName}
     * @param ${className} ${functionName}
     * @param pageDomain
     * @return ${functionName} 分页集合
     * */
    PageInfo<${ClassName}> select${ClassName}Page(${ClassName} ${className}, PageDomain pageDomain);

}
