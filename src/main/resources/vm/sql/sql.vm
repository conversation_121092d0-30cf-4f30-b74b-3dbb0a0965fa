-- 菜单 SQL
INSERT INTO `sys_power`
VALUES ('${ids.get(0)}', '${functionName}菜单', '1', '${permissionPrefix}:main', '/${moduleName}/${businessName}/main', '_iframe', '${parentMenuId}', 'layui-icon layui-icon-set-fill', 1, NULL, NULL, NULL, NULL, NULL, '1');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
INSERT INTO `sys_power`
VALUES ('${ids.get(1)}', '${functionName}列表', '2', '${permissionPrefix}:data', '', NULL, '${ids.get(0)}', 'layui-icon layui-icon-set-fill', 1, NULL, NULL, NULL, NULL, NULL, '1');

INSERT INTO `sys_power`
VALUES ('${ids.get(2)}', '${functionName}新增', '2', '${permissionPrefix}:add', '', NULL, '${ids.get(0)}', 'layui-icon layui-icon-set-fill', 1, NULL, NULL, NULL, NULL, NULL, '1');

INSERT INTO `sys_power`
VALUES ('${ids.get(3)}', '${functionName}修改', '2', '${permissionPrefix}:edit', '', NULL, '${ids.get(0)}', 'layui-icon layui-icon-set-fill', 1, NULL, NULL, NULL, NULL, NULL, '1');

INSERT INTO `sys_power`
VALUES ('${ids.get(4)}', '${functionName}删除', '2', '${permissionPrefix}:remove', '', NULL, '${ids.get(0)}', 'layui-icon layui-icon-set-fill', 1, NULL, NULL, NULL, NULL, NULL, '1');
