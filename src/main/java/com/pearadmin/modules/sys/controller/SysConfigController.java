package com.pearadmin.modules.sys.controller;

import com.github.pagehelper.PageInfo;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.tools.SequenceUtil;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.sys.domain.SysConfig;
import com.pearadmin.modules.sys.service.SysConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.var;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import sun.security.krb5.Config;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * 系统配置控制器
 * <p>
 * @serial 2.0.0
 * <AUTHOR>
 */
@RestController
@Api(tags = {"全局配置"})
@RequestMapping(ControllerConstant.API_SYSTEM_PREFIX + "config")
public class SysConfigController extends BaseController {

    /**
     * 基础路径
     */
    private final String MODULE_PATH = "system/config/";

    /**
     * 引入服务
     */
    @Resource
    private SysConfigService sysConfigService;

    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("main")
    @PreAuthorize("hasPermission('/system/config/main','sys:config:main')")
    public ModelAndView main() {
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysConfig
     * Return: ResultTable
     */
    @GetMapping("data")
    @PreAuthorize("hasPermission('/system/config/data','sys:config:data')")
    public ResultTable data(SysConfig param, PageDomain pageDomain) {
        PageInfo<SysConfig> pageInfo = sysConfigService.page(param, pageDomain);
        return pageTable(pageInfo.getList(), pageInfo.getTotal());
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysConfig
     * Return: ModelAndView
     */
    @GetMapping("editencryptdisk")
    @PreAuthorize("hasPermission('/system/config/edit','sys:config:edit')")
    public ModelAndView editencryptdisk() {
        //model.addAttribute("sysConfig", sysConfigService.getById(configId));
        return jumpPage(MODULE_PATH + "downloaddisk");
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysConfig
     * Return: ResultTable
     */
    @GetMapping("encryptdisk")
    @PreAuthorize("hasPermission('/system/config/data','sys:config:data')")
    public Result<SysConfig> encryptdisk() {
        SysConfig config = sysConfigService.getByCode("security_file_path");
        var result=new Result<SysConfig>();
        result.setCode(0);
        result.setData(config);
        result.setSuccess(true);
        return result;
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysConfig
     * Return: ResultBean
     */
    @PostMapping("saveencryptdisk")
    @PreAuthorize("hasPermission('/system/config/add','sys:config:add')")
    public Result saveEncryptDisk(@RequestBody SysConfig sysConfig) {
        var config=sysConfigService.getByCode("security_file_path");
        config.setConfigValue(sysConfig.getConfigValue());
        boolean result = sysConfigService.updateById(config);
        return decide(result);
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysConfig
     * Return: ModelAndView
     */
    @GetMapping("editdownloadlimit")
    @PreAuthorize("hasPermission('/system/config/edit','sys:config:edit')")
    public ModelAndView editdownloadlimit() {
        //model.addAttribute("sysConfig", sysConfigService.getById(configId));
        return jumpPage(MODULE_PATH + "downloadlimit");
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysConfig
     * Return: ResultTable
     */
    @GetMapping("downloadlimitperday")
    @PreAuthorize("hasPermission('/system/config/data','sys:config:data')")
    public Result<SysConfig> downloadlimitperday() {
        SysConfig config = sysConfigService.getByCode("download_limit");
        var result=new Result<SysConfig>();
        result.setCode(0);
        result.setData(config);
        result.setSuccess(true);
        return result;
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysConfig
     * Return: ResultBean
     */
    @PostMapping("savedownloadlimit")
    @PreAuthorize("hasPermission('/system/config/add','sys:config:add')")
    public Result savedownloadlimit(@RequestBody SysConfig sysConfig) {
        var config=sysConfigService.getByCode("download_limit");
        config.setConfigValue(sysConfig.getConfigValue());
        boolean result = sysConfigService.updateById(config);
        return decide(result);
    }

    /**
     * Describe: 数据字典类型新增视图
     * Param: sysConfig
     * Return: ModelAndView
     */
    @GetMapping("add")
    @PreAuthorize("hasPermission('/system/config/add','sys:config:add')")
    public ModelAndView add() {
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysConfig
     * Return: ResultBean
     */
    @PostMapping("save")
    @PreAuthorize("hasPermission('/system/config/add','sys:config:add')")
    public Result save(@RequestBody SysConfig sysConfig) {
        sysConfig.setConfigId(SequenceUtil.makeStringId());
        sysConfig.setConfigType("custom");
        boolean result = sysConfigService.save(sysConfig);
        return decide(result);
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysConfig
     * Return: ModelAndView
     */
    @GetMapping("edit")
    @PreAuthorize("hasPermission('/system/config/edit','sys:config:edit')")
    public ModelAndView edit(Model model, String configId) {
        model.addAttribute("sysConfig", sysConfigService.getById(configId));
        return jumpPage(MODULE_PATH + "edit");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysConfig
     * Return: ModelAndView
     */
    @PutMapping("update")
    @PreAuthorize("hasPermission('/system/config/edit','sys:config:edit')")
    public Result update(@RequestBody SysConfig sysConfig) {
        boolean result = sysConfigService.updateById(sysConfig);
        return decide(result);
    }

    /**
     * Describe: 数据字典删除
     * Param: sysConfig
     * Return: ModelAndView
     */
    @DeleteMapping("remove/{id}")
    @PreAuthorize("hasPermission('/system/config/remove','sys:config:remove')")
    public Result remove(@PathVariable("id") String id) {
        Boolean result = sysConfigService.removeById(id);
        return decide(result);
    }

    /**
     * Describe: 系统配置批量删除接口
     * Param: ids
     * Return: Result
     */
    @DeleteMapping("batchRemove/{ids}")
    @ApiOperation(value = "批量删除系统配置数据")
    @PreAuthorize("hasPermission('/system/config/remove','sys:config:remove')")
    public Result batchRemove(@PathVariable String ids) {
        boolean result = sysConfigService.removeByIds(Arrays.asList(ids.split(",")));
        return decide(result);
    }

}
