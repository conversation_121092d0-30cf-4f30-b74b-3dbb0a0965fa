package com.pearadmin.modules.sys.controller;

import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.modules.sys.domain.SysConfig;
import com.pearadmin.modules.sys.service.SysConfigService;
import io.swagger.annotations.Api;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;

/**
 * Describe: 预览限制控制器
 * Author: 系统生成
 * CreateTime: 2023/5/20
 */
@RestController
@Api(tags = {"预览限制"})
@RequestMapping(ControllerConstant.API_SYSTEM_PREFIX + "previewlimit")
public class SysPreviewLimitController extends BaseController {

    /**
     * 基础路径
     */
    private static final String MODULE_PATH = "system/previewlimit/";

    /**
     * 配置服务
     */
    @Resource
    private SysConfigService sysConfigService;

    /**
     * Describe: 获取预览限制配置视图
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("main")
    @PreAuthorize("hasPermission('/system/previewlimit/main','sys:previewlimit:main')")
    public ModelAndView main(Model model) {
        SysConfig config = sysConfigService.getByCode("preview_limit");
        if (config == null) {
            config = new SysConfig();
            config.setConfigValue("10"); // 默认每日预览次数为10次
        }
        model.addAttribute("config", config);
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 保存预览限制配置
     * Param: SysConfig
     * Return: Result
     */
    @PostMapping("save")
    @PreAuthorize("hasPermission('/system/previewlimit/save','sys:previewlimit:save')")
    public Result save(@RequestBody SysConfig sysConfig) {
        SysConfig config = sysConfigService.getByCode("preview_limit");
        if (config != null) {
            config.setConfigValue(sysConfig.getConfigValue());
            boolean result = sysConfigService.updateById(config);
            return decide(result);
        } else {
            // 如果配置不存在，则创建新配置
            SysConfig newConfig = new SysConfig();
            newConfig.setConfigName("预览限制");
            newConfig.setConfigCode("preview_limit");
            newConfig.setConfigType("system");
            newConfig.setConfigValue(sysConfig.getConfigValue());
            boolean result = sysConfigService.save(newConfig);
            return decide(result);
        }
    }
}
