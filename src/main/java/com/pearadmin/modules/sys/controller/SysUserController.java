package com.pearadmin.modules.sys.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.aop.annotation.Log;
import com.pearadmin.common.aop.enums.BusinessType;
import com.pearadmin.common.aop.annotation.Repeat;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.tools.CResult;
import com.pearadmin.common.tools.StandardTypeEnum;
import com.pearadmin.common.tools.Tool;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.FileUploadResult;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.sys.domain.SysUser;
import com.pearadmin.modules.sys.domain.SysPassword;
import com.pearadmin.modules.sys.domain.SysUserRole;
import com.pearadmin.modules.sys.service.SysRoleService;
import com.pearadmin.modules.sys.service.SysUserService;
import com.pearadmin.modules.sys.service.SysLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.var;
import org.apache.commons.io.FileUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.InetAddress;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Describe: 用户控制器
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
@RestController
@Api(tags = {"用户管理"})
@RequestMapping(ControllerConstant.API_SYSTEM_PREFIX + "user")
public class SysUserController extends BaseController {

    /**
     * Describe: 基础路径
     */
    private static String MODULE_PATH = "system/user/";

    /**
     * Describe: 用户模块服务
     */
    @Resource
    private SysUserService sysUserService;

    /**
     * Describe: 角色模块服务
     */
    @Resource
    private SysRoleService sysRoleService;

    /**
     * Describe: 日志模块服务
     */
    @Resource
    private SysLogService sysLogService;

    /**
     * Describe: 密码加密
     * */
    @Resource
    private PasswordEncoder passwordEncoder;


    /**
     * Describe: 获取用户列表视图
     * Param ModelAndView
     * Return 用户列表视图
     */
    @GetMapping("main")
    @ApiOperation(value = "获取用户列表视图")
    @PreAuthorize("hasPermission('/system/user/main','sys:user:main')")
    public ModelAndView main() {
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 获取用户列表数据
     * Param ModelAndView
     * Return 用户列表数据
     */
    @GetMapping("data")
    @ApiOperation(value = "获取用户列表数据")
    @PreAuthorize("hasPermission('/system/user/data','sys:user:data')")
    @Log(title = "查询用户", describe = "查询用户", type = BusinessType.QUERY)
    public ResultTable data(PageDomain pageDomain, SysUser param) {
        if(StringUtils.isBlank(param.getUsername()))
        {
            param.setUsername(null);
        }
        if(StringUtils.isBlank(param.getRealName()))
        {
            param.setRealName(null);
        }
        PageInfo<SysUser> pageInfo = sysUserService.page(param, pageDomain);
        var admin=pageInfo.getList().stream().filter(d->d.getUsername().equals("admin")).findFirst();
        if(admin.isPresent())
        {
             pageInfo.getList().remove(admin.get());
        }
        return pageTable(pageInfo.getList(), pageInfo.getTotal());
    }

    /**
     * Describe: 用户新增视图
     * Param ModelAndView
     * Return 返回用户新增视图
     */
    @GetMapping("add")
    @ApiOperation(value = "获取用户新增视图")
    @PreAuthorize("hasPermission('/system/user/add','sys:user:add')")
    public ModelAndView add(Model model) {
        model.addAttribute("sysRoles", sysRoleService.list(null));
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * Describe: 用户新增视图
     * Param ModelAndView
     * Return 返回用户新增视图
     */
    @GetMapping("test")
    @ApiOperation(value = "获取用户新增视图")
    public ModelAndView test(Model model) {
        model.addAttribute("sysRoles", sysRoleService.list(null));
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * Describe: 用户新增接口
     * Param ModelAndView
     * Return 操作结果
     */
    @Repeat
    @PostMapping("save")
    @ApiOperation(value = "保存用户数据")
    @PreAuthorize("hasPermission('/system/user/add','sys:user:add')")
    @Log(title = "新增用户", describe = "新增用户", type = BusinessType.ADD)
    public Result save(@RequestBody SysUser sysUser) {
        sysUser.setLogin("0");
        sysUser.setEnable("1");
        sysUser.setStatus("1");
        sysUser.setPassword(passwordEncoder.encode(sysUser.getPassword()));
        sysUserService.save(sysUser);
        sysUserService.saveUserRole(sysUser.getUserId(), Arrays.asList(sysUser.getRoleIds().split(",")));
        return success("保存成功");
    }

    /**
     * Describe: 用户修改视图
     * Param ModelAndView
     * Return 返回用户修改视图
     */
    @GetMapping("edit")
    @ApiOperation(value = "获取用户修改视图")
    @PreAuthorize("hasPermission('/system/user/edit','sys:user:edit')")
    public ModelAndView edit(Model model, String userId) {
        model.addAttribute("sysRoles", sysUserService.getUserRole(userId));
        model.addAttribute("sysUser", sysUserService.getById(userId));
        return jumpPage(MODULE_PATH + "edit");
    }

    /**
     * Describe: 用户密码修改视图
     * Param ModelAndView
     * Return 返回用户密码修改视图
     */
    @GetMapping("editpasswordadmin")
    @ApiOperation(value = "获取管理员修改用户密码视图")
    @PreAuthorize("hasPermission('/system/user/edit','sys:user:edit')")
    public ModelAndView editPasswordAdminView(Model model, String userId) {
        model.addAttribute("userId", userId);
        return jumpPage(MODULE_PATH + "editPasswordAdmin");
    }

    /**
     * Describe: 管理员修改用户密码接口
     * Param editPassword
     * Return: Result
     */
    @PutMapping("editPasswordAdmin")
    @ApiOperation(value = "管理员修改用户密码")
    @PreAuthorize("hasPermission('/system/user/edit','sys:user:edit')")
    public Result editPasswordAdmin(@RequestBody SysUser sysUser) {
        sysUser.setPassword(passwordEncoder.encode(sysUser.getPassword()));
        return decide(sysUserService.updateById(sysUser), "修改成功", "修改失败");
    }

    /**
     * Describe: 用户密码修改视图
     * Param ModelAndView
     * Return 返回用户密码修改视图
     */
    @GetMapping("editPassword")
    public ModelAndView editPasswordView() {
        return jumpPage(MODULE_PATH + "password");
    }

    /**
     * Describe: 用户密码修改接口
     * Param editPassword
     * Return: Result
     */
    @PutMapping("editPassword")
    public Result editPassword(@RequestBody SysPassword sysPassword) {
        String oldPassword = sysPassword.getOldPassword();
        String newPassword = sysPassword.getNewPassword();
        String confirmPassword = sysPassword.getConfirmPassword();
        SysUser sysUser = UserContext.currentUser();
        SysUser editUser = sysUserService.getById(sysUser.getUserId());
        if (Strings.isBlank(confirmPassword) || Strings.isBlank(newPassword) || Strings.isBlank(oldPassword)) {
            return failure("输入不能为空");
        }
        if (!new BCryptPasswordEncoder().matches(oldPassword, editUser.getPassword())) {
            return failure("密码验证失败");
        }
        if (!newPassword.equals(confirmPassword)) {
            return failure("两次密码输入不一致");
        }
        editUser.setPassword(passwordEncoder.encode(newPassword));
        boolean result = sysUserService.updateById(editUser);
        return decide(result, "修改成功", "修改失败");
    }

    /**
     * Describe: 用户修改接口
     * Param ModelAndView
     * Return 返回用户修改接口
     */
    @PutMapping("update")
    @ApiOperation(value = "修改用户数据")
    @PreAuthorize("hasPermission('/system/user/edit','sys:user:edit')")
    @Log(title = "修改用户", describe = "修改用户", type = BusinessType.EDIT)
    public Result update(@RequestBody SysUser sysUser) {
        sysUserService.saveUserRole(sysUser.getUserId(), Arrays.asList(sysUser.getRoleIds().split(",")));
        sysUserService.updateById(sysUser);
        return success("修改成功");
    }

    /**
     * Describe: 头像修改接口
     * Param: SysUser
     * Return: Result
     */
    @PutMapping("updateAvatar")
    @ApiOperation(value = "修改用户头像")
    @Log(title = "修改头像", describe = "修改头像", type = BusinessType.EDIT)
    public Result updateAvatar(@RequestBody SysUser sysUser) {
        String userId = UserContext.currentUser().getUserId();
        sysUser.setUserId(userId);
        return decide(sysUserService.updateById(sysUser));
    }

    /**
     * Describe: 用户批量删除接口
     * Param: ids
     * Return: Result
     */
    @DeleteMapping("batchRemove/{ids}")
    @ApiOperation(value = "批量删除用户")
    @PreAuthorize("hasPermission('/system/user/remove','sys:user:remove')")
    @Log(title = "删除用户", describe = "删除用户", type = BusinessType.REMOVE)
    public Result batchRemove(@PathVariable String ids) {
        return decide(sysUserService.batchRemove(ids.split(",")));
    }

    /**
     * Describe: 用户删除接口
     * Param: id
     * Return: Result
     */
    @Transactional(rollbackFor = Exception.class)
    @DeleteMapping("remove/{id}")
    @ApiOperation(value = "删除用户数据")
    @PreAuthorize("hasPermission('/system/user/remove','sys:user:remove')")
    @Log(title = "删除用户", describe = "删除用户", type = BusinessType.REMOVE)
    public Result remove(@PathVariable String id) {
        return decide(sysUserService.remove(id));
    }

    /**
     * Describe: 根据 userId 开启用户
     * Param: SysUser
     * Return: 执行结果
     */
    @PutMapping("enable")
    @ApiOperation(value = "开启用户登录")
    public Result enable(@RequestBody SysUser sysUser) {
        sysUser.setEnable("1");
        return decide(sysUserService.updateById(sysUser));
    }

    /**
     * Describe: 根据 userId 禁用用户
     * Param: SysUser
     * Return: 执行结果
     */
    @PutMapping("disable")
    @ApiOperation(value = "禁用用户登录")
    public Result disable(@RequestBody SysUser sysUser) {
        sysUser.setEnable("0");
        return decide(sysUserService.updateById(sysUser));
    }

    /**
     * Describe: 个人资料
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("center")
    @ApiOperation(value = "个人资料")
    public ModelAndView center(Model model) {
        SysUser sysUser = UserContext.currentUser();
        model.addAttribute("userInfo", sysUserService.getById(sysUser.getUserId()));
        model.addAttribute("logs", sysLogService.selectTopLoginLog(sysUser.getUsername()));
        return jumpPage(MODULE_PATH + "center");
    }

    /**
     * Describe: 用户修改接口
     * Param ModelAndView
     * Return 返回用户修改接口
     */
    @PutMapping("updateInfo")
    @ApiOperation(value = "修改用户数据")
    public Result updateInfo(@RequestBody SysUser sysUser) {
        return decide(sysUserService.updateById(sysUser));
    }

    /**
     * Describe: 更换头像
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("profile/{id}")
    public ModelAndView profile(Model model, @PathVariable("id") String userId) {
        model.addAttribute("userId", userId);
        return jumpPage(MODULE_PATH + "profile");
    }


    /**
     *  excel文件上传
     */
    @PostMapping("/file-upload")
    @ApiOperation(value = "excel文件上传")
    public CResult<?> fileUpload(HttpServletRequest req,HttpServletResponse response) throws Exception {
        var result=new CResult<Boolean>();
        result.setResult(true);
        result.setSuccess(true);
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) req;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        var errList=new ArrayList<String>();
        try {
            var res=dealImportExcel(file.getInputStream(),response);
            result.setSuccess(res.isSuccess());
            result.setResult(res.isSuccess());
            if(res.getResult()!=null && res.getResult().size()>0)
            {
                result.setMessage("有"+res.getResult().size()+"条用户数据导入失败，分别是："+res.getResult().stream().collect(Collectors.joining(",")));
            }
            if(StringUtils.isNotBlank(res.getMessage()))
            {
                result.setMessage((StringUtils.isNotBlank(result.getMessage())?result.getMessage()+"\n":"")+res.getMessage());
            }
        }
        catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
            result.setResult(false);
            result.setSuccess(false);
            result.setMessage("上传失败，"+ex.getMessage());
            return result;
        }
        return result;
    }

    public CResult<List<String>> dealImportExcel(InputStream inputStream, HttpServletResponse response) throws IOException {
        var opResult=new CResult<List<String>>();
        opResult.setSuccess(true);
        var list=new ArrayList<String>();
        try{
            //创建
            XSSFWorkbook book = new XSSFWorkbook(inputStream);
            XSSFSheet sheet = book.getSheetAt(0);
            // add some validation here
            // parse data
            var positionRow=1;
            //账号名所在列
            var accountColumn=1;
                for (int i = positionRow; i <= sheet.getLastRowNum(); i++) {
                //Thread.sleep(2000);
                XSSFRow row = sheet.getRow(i); // 表头不算

                var model = new SysUser();
                var userName=(Tool.getCellStringValue(row.getCell(0)));
                if(StringUtils.isNotBlank(userName))
                {
                    //提取拼音
                    var pinyingName=Tool.getPinyin(userName);
                    var index=0;
                    if(StringUtils.isNotBlank(pinyingName)) {
                        var user = sysUserService.getUserByUsername(pinyingName);
                        while (user != null && user.getUsername().equals(pinyingName)) {
                            index++;
                            user = sysUserService.getUserByUsername(pinyingName + index);
                        }
                        model.setUsername(pinyingName + (index == 0 ? "" : index));
                        //将用户名放到excel中
                        row.createCell(accountColumn).setCellValue(model.getUsername());
                        model.setPassword(passwordEncoder.encode("000000"));
                        model.setRealName(userName);
                        model.setStatus("1");
                        model.setEmail(pinyingName + "@163.com");
                        model.setSex("0");
                        model.setPhone("***********");
                        model.setEnable("1");
                        model.setDeptId("1");
                        var res = sysUserService.save(model);
                        if (!res)
                        {
                            list.add(userName);
                            opResult.setSuccess(false);
                        }
                        //设置用户角色
                        sysUserService.saveUserRole(model.getUserId(), Collections.singletonList("1592774449041432577"));//Collections.singletonList("1592774449041432577"));
                    }
                }
            }
            FileOutputStream fos = null;
            try {
                fos = new FileOutputStream(new File("D:\\account.xlsx"));  //后缀不同
                book.write(fos);
                fos.flush();
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }finally {
                if (fos != null){
                    try {
                        fos.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (book != null){
                    try {
                        book.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            opResult.setResult(list);
            return opResult;
        }
        catch (Exception ex)
        {
            opResult.setMessage(ex.getStackTrace().toString());
            opResult.setSuccess(false);
            return opResult;
        }
    }

}
