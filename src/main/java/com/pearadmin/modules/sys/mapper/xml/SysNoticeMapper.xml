<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pearadmin.modules.sys.mapper.SysNoticeMapper">

    <sql id="selectSysNoticeVo">
        select id,
               title,
               content,
               sender,
               accept,
               type,
               create_by,
               create_time,
               update_by,
               update_time,
               remark
        from sys_notice
    </sql>

    <select id="selectSysNoticeList" parameterType="com.pearadmin.modules.sys.domain.SysNotice" resultType="com.pearadmin.modules.sys.domain.SysNotice">
        select acceptUser.real_name as acceptName,senderUser.real_name as senderName,notice.id, notice.title,
        notice.content, notice.sender, notice.accept, notice.type, notice.create_by, notice.create_time as createTime,
        notice.update_by, notice.update_time, notice.remark from sys_notice
        notice
        left join sys_user acceptUser
        on notice.accept = acceptUser.user_id
        left join sys_user senderUser
        on notice.sender = senderUser.user_id
        <where>
            <if test="title != null  and title != ''">and notice.title like concat('%', #{title}, '%')</if>
            <if test="type != null  and type != ''">and notice.type = #{type}</if>
            <if test="accept != null and accept !=''">and notice.accept = #{accept}</if>
        </where>
    </select>

</mapper>
