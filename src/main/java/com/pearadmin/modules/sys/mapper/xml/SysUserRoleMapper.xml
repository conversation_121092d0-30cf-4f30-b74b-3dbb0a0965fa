<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pearadmin.modules.sys.mapper.SysUserRoleMapper">

    <resultMap id="SysUserRoleMap" type="com.pearadmin.modules.sys.domain.SysUserRole">
        <id column="id" property="id"></id>
        <result column="user_id" property="userId"></result>
        <result column="role_id" property="roleId"></result>
    </resultMap>

    <insert id="batchInsert" parameterType="list">
        insert into sys_user_role
        ( id, user_id, role_id )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.userId}, #{item.roleId})
        </foreach>
    </insert>

    <!-- 根据 Id 删除用户数据 -->
    <delete id="deleteByUserId" parameterType="string">
        delete
        from sys_user_role
        where user_id = #{userId}
    </delete>

    <!-- 根据 Id 批量删除用户数据 -->
    <delete id="deleteByUserIds" parameterType="string">
        delete from sys_user_role where user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <!-- 根据 Id 删除用户数据 -->
    <delete id="deleteByRoleId" parameterType="string">
        delete
        from sys_user_role
        where role_id = #{roleId}
    </delete>

    <!-- 根据 Id 批量删除用户数据 -->
    <delete id="deleteByRoleIds" parameterType="string">
        delete from sys_user_role where role_id in
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <select id="selectByUserId" parameterType="string" resultMap="SysUserRoleMap">
        select *
        from sys_user_role
        where user_id = #{userId}
    </select>

</mapper>
