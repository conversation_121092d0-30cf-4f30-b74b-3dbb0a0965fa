package com.pearadmin.modules.standard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pearadmin.modules.standard.domain.StdClassStatistics;
import com.pearadmin.modules.standard.domain.StdInfoStatictics;
import com.pearadmin.modules.standard.mapper.StdClassStatisticsMapper;
import com.pearadmin.modules.standard.service.StdClassStatisticsService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service("StdClassStatisticsServiceImpl")
public class StdClassStatisticsServiceImpl extends ServiceImpl<StdClassStatisticsMapper, StdClassStatistics> implements StdClassStatisticsService {

    @Override
    public void updateByClassName(String className,String typeName,Integer counts) {
        var result=baseMapper.selectOne(new LambdaQueryWrapper<StdClassStatistics>().eq(StdClassStatistics::getStdClass,className).eq(StdClassStatistics::getStdType,typeName)  );
        if(result!=null && StringUtils.isNotBlank(result.getId())) {
             result.setCounts(counts);
             result.setUpdateTime(LocalDateTime.now());
             baseMapper.updateById(result);
        }
        else
        {
            baseMapper.insert(new StdClassStatistics(className,typeName,counts));
        }
    }
}
