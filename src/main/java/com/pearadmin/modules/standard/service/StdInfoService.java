package com.pearadmin.modules.standard.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.tools.CResult;
import com.pearadmin.common.tools.SystemTypeEnum;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.modules.standard.domain.FileUploadResult;
import com.pearadmin.modules.standard.domain.StdClassJoinStandard;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.domain.StdInfoOfSystem;
import com.pearadmin.modules.sys.domain.SysDept;
import com.pearadmin.modules.sys.domain.SysDict;
import com.pearadmin.modules.sys.domain.SysRole;
import com.pearadmin.modules.sys.domain.SysUser;
import lombok.var;
import org.apache.ibatis.annotations.Param;

import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Describe: 服务接口类
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
public interface StdInfoService extends IService<StdInfo> {
    /**
     * Describe: 根据条件查询字典类型列表数据
     * Param: SysDictType
     * Return: List<SysDictType>
     */
    List<StdInfo> list(StdInfo stdInfo);

    /**
     * Describe: 根据条件查询字典类型列表数据 分页
     * Param: SysDictType
     * Return: PageInfo<SysDictType>
     */
    PageInfo<StdInfo> page(StdInfo stdInfo, PageDomain pageDomain);
    FileUploadResult<StdInfo> upload(String customSessionId, String stdType, InputStream inputStream, List<String> errList) throws IOException, ParseException, InterruptedException;
    StdInfo AnalysisSortCondition(StdInfo model);
    StdInfo AnalysisSortConditionForUsa(StdInfo model);
    <E extends IPage<StdInfoOfSystem>> E pageBySystemInfo(E page, @Param(Constants.WRAPPER) Wrapper<StdInfoOfSystem> queryWrapper,@Param("orderSqlSegment") String orderSqlSegment);
    /**
     * 获取可以下载的pdf的数量
     *
     * @param queryWrapper
     * @return java.lang.Integer
     * @throws
     */
    Integer getPdfExistCount(@Param(Constants.WRAPPER) Wrapper<StdInfoOfSystem> queryWrapper);
    /**
     * 是否已收藏
     *
     * @param list
     * @return java.util.List<com.pearadmin.modules.standard.domain.StdInfo>
     * @throws
     */
    List<StdInfo> fillIsCollected(List<StdInfo> list);
    /**
     * 是否收藏
     *
     * @param ids 标准编号List
     * @return java.util.Map<java.lang.String, java.lang.Integer> 标准编号-是否收藏（0或1）
     * @throws
     */
    Map<String,Integer> IsCollected(List<String> ids);
    /**
     * 取被评价过的标准列表
     *
     * @param page
     * @param queryWrapper
     * @return E
     * @throws
     */
    <E extends IPage<StdInfo>> E pageByOpinion(E page, @Param(Constants.WRAPPER) Wrapper<StdInfo> queryWrapper);
    <E extends IPage<StdInfo>> E pageByFullTextSearch(E page,@Param(Constants.WRAPPER) Wrapper<StdInfo> queryWrapper, @Param("sqlSegment") String sqlSegment, @Param("stdType") String stdType, @Param("stdNo") String stdNo, @Param("stdOrgName") String stdOrgName, @Param("stdChineseName") String stdChineseName, @Param("stdEnglishName") String stdEnglishName, @Param("primaryCoverage") String primaryCoverage, @Param("pubDept") String pubDept, @Param("advanceDept") String advanceDept, @Param("draftingUnit") String draftingUnit, @Param("drafter") String drafter);

}

