package com.pearadmin.modules.standard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.tools.CResult;
import com.pearadmin.common.tools.Tool;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.modules.standard.domain.*;
import com.pearadmin.modules.standard.mapper.*;
import com.pearadmin.modules.standard.service.StdClassificationService;
import com.pearadmin.modules.standard.service.StdSystemService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * Describe: 文件服务接口实现
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
@Slf4j
@Service("StdClassificationServiceImpl")
public class StdClassificationServiceImpl extends ServiceImpl<StdClassificationMapper, StdClassification> implements StdClassificationService {
    @Resource
    private StdClassificationMapper modelMapper;
    @Resource
    private StdSystemMapper stdSystemMapper;


    @Override
    public List<StdClassification> list(StdClassification stdClassification) {
        return modelMapper.selectList(new QueryWrapper<StdClassification>(stdClassification));
    }

    @Override
    public PageInfo<StdClassification> page(StdClassification stdClassification, PageDomain pageDomain) {
        PageHelper.startPage(pageDomain.getPage(), pageDomain.getLimit());
        List<StdClassification> infoes = list(new QueryWrapper<>(stdClassification));
        return new PageInfo<>(infoes);
    }

    @Override
    public void statisticsLayerNumber(String systemId) {
        var maxModel = modelMapper.selectOne(new QueryWrapper<StdClassification>().eq("system_id", systemId).orderByDesc("length(class_code_path)").last("limit 1"));
        if (maxModel != null) {
            var layerNumber = maxModel.getClassCodePath().length();
            var system = new StdSystem();
            //体系算一层
            layerNumber += 1;
            system.setLayerNumber(layerNumber);
            stdSystemMapper.update(system, new QueryWrapper<StdSystem>().eq("id", systemId));
        }
    }

}
