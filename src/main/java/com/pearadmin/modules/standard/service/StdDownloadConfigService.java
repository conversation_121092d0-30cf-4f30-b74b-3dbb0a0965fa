package com.pearadmin.modules.standard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.pearadmin.modules.standard.domain.StdDownloadConfig;

/**
 * 标准下载配置 Service 接口
 *
 * <AUTHOR> Code
 * @date 2023/5/28
 */
public interface StdDownloadConfigService extends IService<StdDownloadConfig> {

    /**
     * 根据用户名获取最新的下载配置记录
     *
     * @param userName 用户名
     * @return 最新的下载配置记录
     */
    StdDownloadConfig getLatestByUserName(String userName);

    /**
     * 获取用户的下载限制数量
     * 如果用户没有配置，则返回系统默认值
     *
     * @param userName 用户名
     * @return 下载限制数量
     */
    Integer getDownloadLimit(String userName);

    /**
     * 获取用户的预览限制数量
     * 如果用户没有配置，则返回系统默认值
     *
     * @param userName 用户名
     * @return 预览限制数量
     */
    Integer getPreviewLimit(String userName);

    /**
     * 为用户创建或更新下载配置
     *
     * @param userName 用户名
     * @param downloadLimit 下载限制数量
     * @param previewLimit 预览限制数量
     * @param remark 备注
     * @return 是否成功
     */
    boolean saveOrUpdateConfig(String userName, Integer downloadLimit, Integer previewLimit, String remark);
}
