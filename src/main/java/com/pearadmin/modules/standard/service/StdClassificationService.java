package com.pearadmin.modules.standard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.tools.CResult;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.modules.standard.domain.StdClassification;
import com.pearadmin.modules.standard.domain.StdSystem;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * Describe: 服务接口类
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
public interface StdClassificationService extends IService<StdClassification> {
    /**
     * Describe: 根据条件查询字典类型列表数据
     * Param: SysDictType
     * Return: List<SysDictType>
     */
    List<StdClassification> list(StdClassification stdClass);

    /**
     * Describe: 根据条件查询字典类型列表数据 分页
     * Param: SysDictType
     * Return: PageInfo<SysDictType>
     */
    PageInfo<StdClassification> page(StdClassification stdClass, PageDomain pageDomain);

    void statisticsLayerNumber(String systemId);

 }

