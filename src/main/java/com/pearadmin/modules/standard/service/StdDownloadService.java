package com.pearadmin.modules.standard.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.modules.standard.domain.FileUploadResult;
import com.pearadmin.modules.standard.domain.StdDownload;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.domain.StdInfoOfSystem;
import org.apache.ibatis.annotations.Param;

import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * Describe: 服务接口类
 * Author: 崇义波
 * CreateTime: 2025/10/23
 */
public interface StdDownloadService extends IService<StdDownload> {
    /**
     * Describe: 根据条件查询字典类型列表数据
     * Param: SysDictType
     * Return: List<SysDictType>
     */
    List<StdDownload> list(StdDownload model);

    /**
     * Describe: 根据条件查询字典类型列表数据 分页
     * Param: SysDictType
     * Return: PageInfo<SysDictType>
     */
    PageInfo<StdDownload> page(StdDownload model, PageDomain pageDomain);
    /**
     * 获取可以下载的pdf的数量
     *
     * @param userName
     * @return java.lang.Integer
     * @throws
     */
    Integer getDownloadCount(@Param("userName") String userName);

    /**
     * 获取当前用户当天的预览数量
     *
     * @param userName
     * @return java.lang.Integer
     * @throws
     */
    Integer getPreviewCount(@Param("userName") String userName);
}

