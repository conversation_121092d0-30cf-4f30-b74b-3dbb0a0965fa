package com.pearadmin.modules.standard.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.tools.CResult;
import com.pearadmin.common.tools.SystemTypeEnum;
import com.pearadmin.common.tools.Tool;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.modules.standard.domain.*;
import com.pearadmin.modules.standard.mapper.StdInfoMapper;
import com.pearadmin.modules.standard.service.StdClassJoinStandardService;
import com.pearadmin.modules.standard.service.StdInfoService;
import com.pearadmin.modules.standard.service.StdSystemService;
import com.pearadmin.modules.sys.domain.SysDict;
import freemarker.template.TemplateHashModelEx2;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jsoup.helper.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Describe: 文件服务接口实现
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
@Slf4j
@Service("StdInfoServiceImpl")
public class StdInfoServiceImpl extends ServiceImpl<StdInfoMapper, StdInfo> implements StdInfoService {
    @Resource
    private StdInfoMapper stdInfoMapper;
    @Autowired
    private StdClassJoinStandardService stdClassJoinStandardService;
    @Autowired
    private StdSystemService stdSystemService;
    @Override
    public List<StdInfo> list(StdInfo stdInfo) {
        return stdInfoMapper.selectList(new QueryWrapper<>(stdInfo));
    }

    @Autowired
    private com.pearadmin.common.tools.UploadProcess process;

    @Override
    public PageInfo<StdInfo> page(StdInfo stdInfo, PageDomain pageDomain) {
        PageHelper.startPage(pageDomain.getPage(), pageDomain.getLimit());
        List<StdInfo> infoes = list(new QueryWrapper<>(stdInfo));
        return new PageInfo<>(infoes);
    }

    /**
     * 通过excel导入数据
     *
     * @param inputStream
     * @return void
     * @throws
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public FileUploadResult<StdInfo> upload(String customSessionId,String stdType, InputStream inputStream,List<String> errList) throws IOException, ParseException, InterruptedException {
        var result=new FileUploadResult<StdInfo>();
        var opResult=new CResult<Boolean>();
        opResult.setSuccess(true);
        opResult.setResult(true);
        result.setResult(opResult);
        var list=new ArrayList<StdInfo>();
        result.setList(list);
        //创建
        XSSFWorkbook book = new XSSFWorkbook(inputStream);
        XSSFSheet sheet = book.getSheetAt(0);
        // add some validation here
        // parse data
        var positionRow=0;
        var positionCol=0;
        int cols;
        //创建上传进度
        //var uploadInfo=new StdUploadProcess();
        //uploadInfo.setSession(customSessionId);
        //uploadInfo.setDealedCount(0);
        //uploadInfo.setTotalCount(sheet.getLastRowNum()-positionRow);
        //uploadInfo.setRestCount(0);
        //uploadInfo.setErr("");
        //infoMapper.insert(uploadInfo);
        process.setTotal(sheet.getLastRowNum());
        process.setDealed(0);
        process.setTitle("导入excel数据");
        //有下一个处理流程，即加密pdf文件
        process.setHasNextProcess(1);
        for (int i = positionRow; i < sheet.getLastRowNum(); i++) {
            //Thread.sleep(2000);
            XSSFRow row = sheet.getRow(i + 1); // 表头不算
            cols = positionCol;
            var model = new StdInfo();
            model.setStdIdentification(Tool.getCellStringValue(row.getCell(1)));
            model.setStdClass(Tool.getCellStringValue(row.getCell(2)));
            model.setStdNo(Tool.getCellStringValue(row.getCell(3)));
            model.setStdOrgName(Tool.getCellStringValue(row.getCell(4)));
            model.setStdEnglishName(Tool.getCellStringValue(row.getCell(5)));
            model.setStdChineseName(Tool.getCellStringValue(row.getCell(6)));
            model.setStdLangugage(Tool.getCellStringValue(row.getCell(7)));
            model.setSecurityClass(Tool.getCellStringValue(row.getCell(8)));
            model.setCatetoryNo(Tool.getCellStringValue(row.getCell(9)));
            model.setStdIcs(Tool.getCellStringValue(row.getCell(10)));
            model.setPubDept(Tool.getCellStringValue(row.getCell(11)));
            var strPubDate = Tool.getCellStringValue(row.getCell(12));
            if (StringUtils.isNotBlank(strPubDate)) {
                model.setPubDate(strPubDate);
            }
            var strImplementDate = Tool.getCellStringValue(row.getCell(13));
            if (StringUtils.isNotBlank(strImplementDate)) {
                model.setImplementationDate(strImplementDate);
            }
            model.setAdvanceDept(Tool.getCellStringValue(row.getCell(14)));
            model.setDraftingUnit(Tool.getCellStringValue(row.getCell(15)));
            model.setDrafter(Tool.getCellStringValue(row.getCell(16)));
            model.setPrimaryCoverage(Tool.getCellStringValue(row.getCell(17)));
            model.setPageCount(Tool.getCellStringValue(row.getCell(18)));
            model.setAlternateStdNo(Tool.getCellStringValue(row.getCell(19)));
            model.setSupersededStdNo(Tool.getCellStringValue(row.getCell(20)));
            model.setStdStatus(Tool.getCellStringValue(row.getCell(21)));
            model.setStdOcr(Tool.getCellStringValue(row.getCell(22)));
            model.setPdfFileName(Tool.getCellStringValue(row.getCell(23)));
            model.setCreateTime(LocalDateTime.now());
            model.setStdType(stdType);
            if(StringUtils.isNotBlank(model.getPdfFileName()))
            {
                model.setPdfIsExists("1");
            }
            else
            {
                model.setPdfIsExists("0");
            }
            //一行数据都是空，则进入下一行
            if(StringUtils.isBlank(model.getStdNo()) &&
                    StringUtils.isBlank(model.getStdOrgName()) &&
                    StringUtils.isBlank(model.getStdChineseName()) &&
                    StringUtils.isBlank(model.getStdEnglishName()) &&
                    StringUtils.isBlank(model.getPdfFileName()) &&
                    StringUtils.isBlank(model.getAdvanceDept()) &&
                    StringUtils.isBlank(model.getDrafter()) &&
                    StringUtils.isBlank(model.getDraftingUnit()) &&
                    StringUtils.isBlank(model.getPageCount()) &&
                    StringUtils.isBlank(model.getPrimaryCoverage()) &&
                    StringUtils.isBlank(model.getPubDept()) &&
                    StringUtils.isBlank(model.getAlternateStdNo()) &&
                    StringUtils.isBlank(model.getSecurityClass()) &&
                    StringUtils.isBlank(model.getStdEnglishName()) &&
                    StringUtils.isBlank(strPubDate))
            {
                continue;
            }
            //如果标准号不为null，则说明是有效数据
            if (StringUtils.isNotBlank(model.getStdNo())) {
                var exits= stdInfoMapper.selectOne(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,stdType).eq(StdInfo::getStdNo,model.getStdNo()).last("limit 1"));
                if(exits!=null)
                {
                    model.setId(exits.getId());
                    model= AnalysisSortCondition(model);
                }
                try {
                    var res=0;
                    if(StringUtils.isNotBlank(model.getId())) {
                        res = stdInfoMapper.updateById(model);
                    }
                    else {
                        res = stdInfoMapper.insert(model);
                    }
                    if(res==0)
                    {
                        opResult.setSuccess(false);
                        opResult.setMessage("第"+(i+2)+"行数据导入失败,请检查数据。");
                        opResult.setResult(false);
                        errList.add("第"+(i+2)+"行数据导入失败,请检查数据。");
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        //上传进度
                        //uploadInfo.setDealedCount(uploadInfo.getDealedCount()+1);
                        //uploadInfo.setRestCount(uploadInfo.getTotalCount()-uploadInfo.getDealedCount());
                        //uploadInfo.setErr(opResult.getMessage());
                        process.setDealed(process.getDealed()+1);
                        break;
                    }
                    else
                    {
                        list.add(model);
                    }
                    //上传进度
                    //uploadInfo.setDealedCount(uploadInfo.getDealedCount()+1);
                    //uploadInfo.setRestCount(uploadInfo.getTotalCount()-uploadInfo.getDealedCount());
                    //infoMapper.updateById(uploadInfo);
                    process.setDealed(process.getDealed()+1);
                }catch (Exception ex)
                {
                    opResult.setSuccess(false);
                    opResult.setMessage("第"+(i+2)+"行数据导入失败。堆栈跟踪："+ex.getMessage());
                    errList.add("第"+(i+2)+"行数据导入失败。堆栈跟踪："+ex.getMessage());
                    System.out.println(ex.getStackTrace());
                    opResult.setResult(false);
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    //上传进度
                    //uploadInfo.setDealedCount(uploadInfo.getDealedCount()+1);
                    //uploadInfo.setRestCount(uploadInfo.getTotalCount()-uploadInfo.getDealedCount());
                    //uploadInfo.setErr(opResult.getMessage());
                    //infoMapper.updateById(uploadInfo);
                    process.setErr(opResult.getMessage());
                    break;
                }
            }
            else
            {
                opResult.setSuccess(false);
                opResult.setMessage("第"+(i+2)+"行未提供有效标准号！");
                errList.add("第"+(i+2)+"行未提供有效标准号！");
                opResult.setResult(false);
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                //上传进度
                //uploadInfo.setDealedCount(uploadInfo.getDealedCount()+1);
                //uploadInfo.setRestCount(uploadInfo.getTotalCount()-uploadInfo.getDealedCount());
                //uploadInfo.setErr(opResult.getMessage());
                //infoMapper.updateById(uploadInfo);
                process.setErr(opResult.getMessage());
                break;
            }
        }
        //上传进度
        if(StringUtils.isBlank(process.getErr()))
        {
            //uploadInfo.setDealedCount(uploadInfo.getTotalCount());
            //uploadInfo.setRestCount(0);
            //infoMapper.updateById(uploadInfo);
            process.setDealed(process.getTotal());
        }
        book.close();
        return result;
    }

    /**
     * 从标准号中解析排序条件
     *
     * @param model
     * @return void
     * @throws
     */
    @Override
    public StdInfo AnalysisSortCondition(StdInfo model)
    {
        var stdNoArr=model.getStdNo().split(" ");
        var iMap= new  HashMap<String,Integer>();
//        iMap.put("GJB",1);
//        iMap.put("GJB/J",2);
//        iMap.put("GJB/Z",3);
//        iMap.put("GJBz",4);
//        iMap.put("KGJB",5);
//        iMap.put("KGJB/J",6);
//        iMap.put("KGJB/Z",7);
//        iMap.put("WJ",8);
//        iMap.put("WJ/T",9);
//        iMap.put("WJ/Z",10);
//        iMap.put("Q/CNG",11);
//        iMap.put("GB",12);
//        iMap.put("QC",13);
//        iMap.put("QJ",14);
//        iMap.put("HB",15);
//        iMap.put("HG",16);
//        iMap.put("JB",17);
//        iMap.put("JJF",18);
//        iMap.put("JJG",19);
//        iMap.put("SJ",20);
//        iMap.put("SY",21);
//        iMap.put("TB",22);
//        iMap.put("YD",23);
//        iMap.put("CB",24);
//        iMap.put("DL",25);
        iMap.put("WJ",1);
        iMap.put("WJ/T",2);
        iMap.put("WJ/Z",3);
        iMap.put("GJB",4);
        iMap.put("GJB/J",5);
        iMap.put("GJB/Z",6);
        iMap.put("GJBz",7);
        iMap.put("KGJB",8);
        iMap.put("KGJB/J",9);
        iMap.put("KGJB/Z",10);
        iMap.put("Q/CNG",11);
        iMap.put("GB",12);
        iMap.put("GB/T",13);
        iMap.put("GB/Z",14);
        iMap.put("CB",15);
        iMap.put("CB/T",16);
        iMap.put("CB/Z",17);

//        iMap.put("DL/T",18);
//        iMap.put("HB",19);
//        iMap.put("HB/Z",20);
//        iMap.put("HBJ",21);
//        iMap.put("HBm",22);
//        iMap.put("HG",23);
//        iMap.put("HG/H",24);
//        iMap.put("HG/T",25);
//        iMap.put("HGJ",26);
//        iMap.put("JB",27);
//        iMap.put("JB/T",28);
//        iMap.put("JB/Z",29);
//        iMap.put("JB/ZQ",30);
//        iMap.put("JBDQ",31);
//        iMap.put("JBDQZ",32);
//        iMap.put("JBJ",33);
//        iMap.put("JBJT",34);
//        iMap.put("JJF",35);
//        iMap.put("JJF(京)",36);
//        iMap.put("JJF(晋)",37);
//        iMap.put("JJF(浙)",38);
//        iMap.put("JJF(电子)",39);
//        iMap.put("JJF(纺织)",40);
//        iMap.put("JJF(辽)",41);
//        iMap.put("JJF(铁道)",42);
//        iMap.put("JJF(鲁)",43);
//        iMap.put("JJG",44);
//        iMap.put("JJG(交通)",45);
//        iMap.put("JJG(京)",46);
//        iMap.put("JJG(冀)",47);
//        iMap.put("JJG(化)",48);
//        iMap.put("JJG(建材)",49);
//        iMap.put("JJG(建设)",50);
//        iMap.put("JJG(教委)",51);
//        iMap.put("JJG(机械)",52);
//        iMap.put("JJG(民航)",53);
//        iMap.put("JJG(气象)",54);
//        iMap.put("JJG(沪)",55);
//        iMap.put("JJG(津)",56);
//        iMap.put("JJG(浙)",57);
//        iMap.put("JJG(烟草)",58);
//        iMap.put("JJG(煤炭)",59);
//        iMap.put("JJG(电力)",60);
//        iMap.put("JJG(电子)",61);
//        iMap.put("JJG(石油)",62);
//        iMap.put("JJG(纺织)",63);
//        iMap.put("JJG(航天)",64);
//        iMap.put("JJG(轻工)",65);
//        iMap.put("JJG(铁道)",66);
//        iMap.put("JJG(鲁)",67);
//        iMap.put("QC/T",68);
//        iMap.put("QCn",69);
//        iMap.put("QJ",70);
//        iMap.put("QJ/G",71);
//        iMap.put("QJ/T",72);
//        iMap.put("QJ/Z",73);
//        iMap.put("SJ",74);
//        iMap.put("SJ/T",75);
//        iMap.put("SJ/Z",76);
//        iMap.put("SY",77);
//        iMap.put("SY/T",78);
//        iMap.put("SYJ",79);
//        iMap.put("TB",80);
//        iMap.put("TB/T",81);
//        iMap.put("TB/Z",82);
//        iMap.put("YD",83);
//        iMap.put("YD/T",84);
//        iMap.put("YD/Z",85);
        //标准类型
        if(stdNoArr.length>0) {
            var identity = stdNoArr[0].trim();
            var intIdentity=18;
            if(iMap.containsKey(identity))
            {
                intIdentity=iMap.get(identity);
            }
            model.setSortIdentifaction(intIdentity);
            if(stdNoArr.length>1)
            {
                var part2=stdNoArr[1].split("-");
                if(part2.length>0)
                {
                    //顺序号中的字母
                    var charStr=part2[0];
                    charStr= charStr.replaceAll("\\s*","").replaceAll("[^(A-Za-z)]","");
                    if(StringUtils.isNotBlank(charStr))
                    {
                        model.setSortChar(charStr);
                    }
                    var sortNo=0.0f;
                    try {
                        //顺序号
                        var sortStr= part2[0];
                        if(sortStr.contains("."))
                        {
                           var sArr= sortStr.split("\\.");
                           if(sArr.length>0)
                           {
                               sortStr=sArr[0];
                           }
                           if(sArr.length>1){
                               var sortNumber=fetchFirstDigit(sArr[1]);

                               if(sortNumber!=-1)
                               {
                                   model.setSortNumber(sortNumber);
                               }
                           }
                        }
                        sortStr= sortStr.replaceAll("\\s*","").replaceAll("[^(0-9\\.)]","");
                        if(StringUtils.isNotBlank(sortStr))
                        {
                            sortNo= Float.parseFloat(sortStr);
                            model.setSortSerialNo(sortNo);
                        }
                    }
                    catch (Exception ex)
                    {
                        model.setSortSerialNo(0.0f);
                    }
                }
                //年份
                if(part2.length>1)
                {
                    var year=0;
                    var yearStr=part2[1];
                    yearStr= yearStr.replaceAll("\\s*","").replaceAll("[^(0-9)]","");
                    if(StringUtils.isNotBlank(yearStr))
                    {
                        try {
                            year = Integer.parseInt(yearStr);
                            model.setSortYear(year);
                        }
                        catch (Exception ex)
                        {
                            model.setSortYear(0);
                        }
                    }
                }
            }
        }
        return model;
    }

    /**
     * 从标准号中解析排序条件(美国标准专用）
     *
     * @param model
     * @return void
     * @throws
     */
    @Override
    public StdInfo AnalysisSortConditionForUsa(StdInfo model)
    {
        try {
            var stdNoArr = model.getStdNo().replaceAll("-", " ").split(" ");
            var iMap = new HashMap<String, Integer>();

            iMap.put("STANAG", 1);
            iMap.put("ATP", 2);
            iMap.put("ANEP", 3);
            iMap.put("AAP", 4);
            iMap.put("AJP", 5);
            iMap.put("AMEDP", 6);
            iMap.put("AETP", 7);
            iMap.put("SAE", 8);
            iMap.put("ASTM", 9);

            //标准类型
            if (stdNoArr.length > 0) {
                var identity = stdNoArr[0].trim();
                var intIdentity = 18;
                if (iMap.containsKey(identity)) {
                    intIdentity = iMap.get(identity);
                }
                model.setSortIdentifaction(intIdentity);
                if (stdNoArr.length > 1) {
                    var part2 = stdNoArr[1].split("-");
                    if (part2.length > 0) {
                        //顺序号中的字母
                        var charStr = part2[0];
                        charStr = charStr.replaceAll("\\s*", "").replaceAll("[^(A-Za-z)]", "");
                        if (StringUtils.isNotBlank(charStr)) {
                            model.setSortChar(charStr);
                        }
                        var sortNo = 0.0f;
                        try {
                            //顺序号
                            var sortStr = part2[0];
                            if (sortStr.contains(".")) {
                                var sArr = sortStr.split("\\.");
                                if (sArr.length > 0) {
                                    sortStr = sArr[0];
                                }
                                if (sArr.length > 1) {
                                    var sortNumber = fetchFirstDigit(sArr[1]);

                                    if (sortNumber != -1) {
                                        model.setSortNumber(sortNumber);
                                    }
                                }
                            }
                            sortStr = sortStr.replaceAll("\\s*", "").replaceAll("[^(0-9\\.)]", "");
                            if (StringUtils.isNotBlank(sortStr)) {
                                sortNo = Float.parseFloat(sortStr);
                                model.setSortSerialNo(sortNo);
                            }
                        } catch (Exception ex) {
                            model.setSortSerialNo(0.0f);
                        }
                    }
                    //年份
                    if (part2.length > 1) {
                        var year = 0;
                        var yearStr = part2[1];
                        yearStr = yearStr.replaceAll("\\s*", "").replaceAll("[^(0-9)]", "");
                        if (StringUtils.isNotBlank(yearStr)) {
                            try {
                                year = Integer.parseInt(yearStr);
                                model.setSortYear(year);
                            } catch (Exception ex) {
                                model.setSortYear(0);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.out.println("["+model.getStdNo()+"]出现错误:"+ex.getStackTrace());
        }
        return model;
    }

    /**
     * 从字符串中获取从下标零开始的数字部分
     * 如果没有返回-1
     *
     * @param str
     * @return int
     * @throws
     */
    public int fetchFirstDigit(String str) {
        System.out.println("sortNumber:"+str);
        int index = -1;
        int result=-1;
        var hasChar=false;
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if(Character.isDigit(c))
            {
                index=i;
            }
            else {
                index = i;
                hasChar=true;
                break;
            }
        }
        if(index>0)
        {
            String res=str;
            if(hasChar)
            {
                res=str.substring(0,index);
            }
            if(StringUtils.isNotBlank(res))
            {
                result=Integer.parseInt(res);
            }
        }
        return result;
    }

    @Override
    public <E extends IPage<StdInfoOfSystem>> E pageBySystemInfo(E page, Wrapper<StdInfoOfSystem> queryWrapper,String orderSqlSegment) {
         return baseMapper.pageBySystemInfo(page,queryWrapper,orderSqlSegment);
    }

    @Override
    public Integer getPdfExistCount(Wrapper<StdInfoOfSystem> queryWrapper) {
         return baseMapper.getPdfExistCount(queryWrapper);
    }

    @Override
    /**
     *  填充是否收藏标志
     *
     * @param list
     * @return java.util.List<com.pearadmin.modules.standard.domain.StdInfo>
     * @throws
     */
    public List<StdInfo> fillIsCollected(List<StdInfo> list)
    {
        if(list.size()==0)
        {
            return list;
        }
        var stdIdList = list.parallelStream().map(b ->b.getId()).collect(Collectors.toList());
        var ids="'"+ String.join("','",stdIdList)+"'";
        var user= UserContext.currentUser();
        var res=stdSystemService.getOrCreateSystem(SystemTypeEnum.PersonalCollect,user.getUserId(),user.getRealName());
        var sys=res.getData();
        var query=new LambdaQueryWrapper<StdClassJoinStandard>();
        query.in(StdClassJoinStandard::getStdId,stdIdList.toArray()).eq(StdClassJoinStandard::getSystemId,sys.getId());
        var resList=stdClassJoinStandardService.list(query);
        for(var i=0;i<list.size();i++)
        {
            list.get(i).setIsCollected("0");
            for(var j=0;j<resList.size();j++)
            {
                if(resList.get(j).getStdId().equals(list.get(i).getId()))
                {
                    list.get(i).setIsCollected("1");
                    break;
                }
            }
        }
        return list;
    }

    @Override
    public Map<String, Integer> IsCollected(List<String> ids) {
        HashMap<String,Integer> map=new HashMap<>();
        if(ids.size()==0)
        {
            return map;
        }
        var idList="'"+ String.join("','",ids)+"'";
        var user= UserContext.currentUser();
        var res=stdSystemService.getOrCreateSystem(SystemTypeEnum.PersonalCollect,user.getUserId(),user.getRealName());
        var sys=res.getData();
        var query=new LambdaQueryWrapper<StdClassJoinStandard>();
        query.in(StdClassJoinStandard::getStdId,ids.toArray()).eq(StdClassJoinStandard::getSystemId,sys.getId());
        var resList=stdClassJoinStandardService.list(query);
        for(var i=0;i<ids.size();i++)
        {
            int value=0;
            for(var j=0;j<resList.size();j++)
            {
                if(resList.get(j).getStdId().equals(ids.get(i)))
                {
                    value=1;
                    break;
                }
            }
            map.put(ids.get(i),value);
        }
        return map;
    }

    @Override
    public <E extends IPage<StdInfo>> E pageByOpinion(E page, Wrapper<StdInfo> queryWrapper) {
        return  baseMapper.pageByOpinion(page,queryWrapper);
    }
    @Override
    public <E extends IPage<StdInfo>> E pageByFullTextSearch(E page, Wrapper<StdInfo> queryWrapper,  String sqlSegment,  String stdType, String stdNo, String stdOrgName,  String stdChineseName, String stdEnglishName,  String primaryCoverage,  String pubDept,  String advanceDept, String draftingUnit, String drafter) {
        return  baseMapper.pageByFullTextSearch(page,queryWrapper,sqlSegment , stdType,  stdNo,  stdOrgName,   stdChineseName,  stdEnglishName,   primaryCoverage,   pubDept,   advanceDept,  draftingUnit,  drafter);
    }

}
