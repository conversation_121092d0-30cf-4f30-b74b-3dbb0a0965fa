package com.pearadmin.modules.standard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.modules.standard.domain.StdOpinion;
import com.pearadmin.modules.standard.mapper.StdOpinionMapper;
import com.pearadmin.modules.standard.service.StdOpinionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Describe: 文件服务接口实现
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
@Slf4j
@Service("StdOpinionServiceImpl")
public class StdOpinionServiceImpl extends ServiceImpl<StdOpinionMapper, StdOpinion> implements StdOpinionService {
    @Resource
    private StdOpinionMapper modelMapper;

    @Override
    public List<StdOpinion> list(StdOpinion stdSystemOpinion) {
        return modelMapper.selectList(new QueryWrapper<StdOpinion>(stdSystemOpinion));
    }

    @Override
    public PageInfo<StdOpinion> page(StdOpinion stdSystemOpinion, PageDomain pageDomain) {
        PageHelper.startPage(pageDomain.getPage(), pageDomain.getLimit());
        List<StdOpinion> infoes = list(new QueryWrapper<>(stdSystemOpinion));
        return new PageInfo<>(infoes);
    }

}
