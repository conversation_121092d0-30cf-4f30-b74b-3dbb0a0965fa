package com.pearadmin.modules.standard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.tools.CResult;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.modules.standard.domain.EquipmentAssessment;
import com.pearadmin.modules.standard.domain.FileUploadResult;
import com.pearadmin.modules.standard.domain.StdInfo;

import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.util.List;

/**
 * Describe: 服务接口类
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
public interface EquipmentAssessmentService extends IService<EquipmentAssessment> {
    /**
     * Describe: 根据条件查询字典类型列表数据
     * Param: SysDictType
     * Return: List<SysDictType>
     */
    List<EquipmentAssessment> list(EquipmentAssessment stdInfo);

    /**
     * Describe: 根据条件查询字典类型列表数据 分页
     * Param: SysDictType
     * Return: PageInfo<SysDictType>
     */
    PageInfo<EquipmentAssessment> page(EquipmentAssessment stdInfo, PageDomain pageDomain);

    FileUploadResult<EquipmentAssessment> upload(InputStream inputStream) throws IOException, ParseException;

}

