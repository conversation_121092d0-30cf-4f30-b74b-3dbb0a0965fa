package com.pearadmin.modules.standard.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.tools.CResult;
import com.pearadmin.common.tools.SystemTypeEnum;
import com.pearadmin.common.tools.Tool;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.modules.standard.domain.*;
import com.pearadmin.modules.standard.mapper.StdInfoMapper;
import com.pearadmin.modules.standard.mapper.StdLogMapper;
import com.pearadmin.modules.standard.service.StdClassJoinStandardService;
import com.pearadmin.modules.standard.service.StdInfoService;
import com.pearadmin.modules.standard.service.StdLogService;
import com.pearadmin.modules.standard.service.StdSystemService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Describe: 文件服务接口实现
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
@Slf4j
@Service("StdLogServiceImpl")
public class StdLogServiceImpl extends ServiceImpl<StdLogMapper, StdLog> implements StdLogService {
    @Autowired
    private StdSystemService stdSystemService;
    @Autowired
    private StdClassJoinStandardService stdClassJoinStandardService;
    @Override
    public <E extends IPage<StdInfoStatictics>> E getPageByLogType(E page, String logType, Wrapper<StdInfoStatictics> queryWrapper) {
        return baseMapper.getPageByLogType(page,logType,queryWrapper);
    }

    @Override
    public Integer getCountsByLogType(String logType, Wrapper<StdInfoStatictics> queryWrapper) {
        return baseMapper.getCountsByLogType(logType,queryWrapper);
    }

    @Override
    public <E extends IPage<StdInfoStatictics>> E getPageofReferedBySystem(E page,  String systemType, Wrapper<StdInfoStatictics> queryWrapper) {
        return baseMapper.getPageofReferedBySystem(page,systemType,queryWrapper);
    }

    @Override
    public Integer getCountofReferedBySystem(String systemType,Wrapper<StdInfoStatictics> queryWrapper) {
        return baseMapper.getCountofReferedBySystem(systemType,queryWrapper);
    }

    @Override
    public <E extends IPage<StdInfoStatictics>> E getPageOfOpinion(E page, Wrapper<StdInfoStatictics> queryWrapper) {
        return baseMapper.getPageOfOpinion(page,queryWrapper);
    }

    @Override
    public Integer getCountsOfOpinion(Wrapper<StdInfoStatictics> queryWrapper) {
        return baseMapper.getCountsOfOpinion(queryWrapper);
    }

    @Override
    /**
     *  填充是否收藏标志
     *
     * @param list
     * @return java.util.List<com.pearadmin.modules.standard.domain.StdInfo>
     * @throws
     */
    public List<StdInfoStatictics> fillIsCollected(List<StdInfoStatictics> list)
    {
        if(list.size()==0)
        {
            return list;
        }
        var stdNoList = list.parallelStream().map(b ->b.getItemId()).collect(Collectors.toList());
        var stdNos="'"+ String.join("','",stdNoList)+"'";
        var user= UserContext.currentUser();
        var res=stdSystemService.getOrCreateSystem(SystemTypeEnum.PersonalCollect,user.getUserId(),user.getRealName());
        var sys=res.getData();
        var query=new LambdaQueryWrapper<StdClassJoinStandard>();
        query.in(StdClassJoinStandard::getStdNo,stdNoList.toArray()).eq(StdClassJoinStandard::getSystemId,sys.getId());
        var resList=stdClassJoinStandardService.list(query);
        for(var i=0;i<list.size();i++)
        {
            list.get(i).setIsCollected("0");
            for(var j=0;j<resList.size();j++)
            {
                if(resList.get(j).getStdNo().equals(list.get(i).getItemId()))
                {
                    list.get(i).setIsCollected("1");
                    break;
                }
            }
        }
        return list;
    }

}
