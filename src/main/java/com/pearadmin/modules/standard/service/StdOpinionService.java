package com.pearadmin.modules.standard.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.domain.StdOpinion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Describe: 服务接口类
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
public interface StdOpinionService extends IService<StdOpinion> {
    /**
     * Describe: 根据条件查询字典类型列表数据
     * Param: SysDictType
     * Return: List<SysDictType>
     */
    List<StdOpinion> list(StdOpinion stdSystemOpinion);

    /**
     * Describe: 根据条件查询字典类型列表数据 分页
     * Param: SysDictType
     * Return: PageInfo<SysDictType>
     */
    PageInfo<StdOpinion> page(StdOpinion stdSystemOpinion, PageDomain pageDomain);
    /**
     * 取被评价列表
     *
     * @param page
     * @param queryWrapper
     * @return E
     * @throws
     */
    <E extends IPage<StdOpinion>> E pageByStdInfo(E page, @Param(Constants.WRAPPER) Wrapper<StdInfo> queryWrapper);

}

