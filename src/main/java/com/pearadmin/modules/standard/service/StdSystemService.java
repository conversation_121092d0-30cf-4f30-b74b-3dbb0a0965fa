package com.pearadmin.modules.standard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.tools.SystemTypeEnum;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.modules.standard.domain.FileUploadResult;
import com.pearadmin.modules.standard.domain.StdClassification;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.domain.StdSystem;

import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.util.List;

/**
 * Describe: 服务接口类
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
public interface StdSystemService extends IService<StdSystem> {
    /**
     * Describe: 根据条件查询字典类型列表数据
     * Param: SysDictType
     * Return: List<SysDictType>
     */
    List<StdSystem> list(StdSystem stdSystem);
    /**
     * Describe: 根据条件查询字典类型列表数据 分页
     * Param: SysDictType
     * Return: PageInfo<SysDictType>
     */
    PageInfo<StdSystem> page(StdSystem stdSystem, PageDomain pageDomain);
    Result<Boolean> upload(String systemType,String customSessionId, InputStream inputStream) throws IOException, ParseException;
    /**
     *根据系统类型获取对应体系，如果不存在则创建
     *
     * @param systemType
     * @param userId
     * @return com.pearadmin.common.web.domain.response.Result<com.pearadmin.modules.standard.domain.StdSystem>
     * @throws
     */
    Result<StdSystem> getOrCreateSystem(String systemType,String userId,String userName);
    /**
     * 获取个人收藏下的默认分类
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result<com.pearadmin.modules.standard.domain.StdClassification>
     * @throws
     */
    StdClassification getPersonalCollectDefaultClass();

}

