package com.pearadmin.modules.standard.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.modules.standard.domain.*;
import io.swagger.v3.oas.annotations.Parameter;
import org.apache.ibatis.annotations.Param;

import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * Describe: 服务接口类
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
public interface StdLogService extends IService<StdLog> {
     <E extends IPage<StdInfoStatictics>> E getPageByLogType(E page, @Param("logType") String logType, @Param(Constants.WRAPPER) Wrapper<StdInfoStatictics> queryWrapper);
     Integer getCountsByLogType(@Param("logType") String logType, @Param(Constants.WRAPPER) Wrapper<StdInfoStatictics> queryWrapper);
     <E extends IPage<StdInfoStatictics>> E getPageofReferedBySystem(E page, @Param("systemType") String systemType,@Param(Constants.WRAPPER) Wrapper<StdInfoStatictics> queryWrapper);
     Integer getCountofReferedBySystem(@Param("systemType") String systemType,@Param(Constants.WRAPPER) Wrapper<StdInfoStatictics> queryWrapper);
     <E extends IPage<StdInfoStatictics>> E getPageOfOpinion(E page, @Param(Constants.WRAPPER) Wrapper<StdInfoStatictics> queryWrapper);
     Integer getCountsOfOpinion(@Param(Constants.WRAPPER) Wrapper<StdInfoStatictics> queryWrapper);
     List<StdInfoStatictics> fillIsCollected(List<StdInfoStatictics> list);
}

