package com.pearadmin.modules.standard.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.modules.standard.domain.FileUploadResult;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.domain.StdInfoNull;
import com.pearadmin.modules.standard.domain.StdInfoOfSystem;
import org.apache.ibatis.annotations.Param;

import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * Describe: 服务接口类
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
public interface StdInfoNullService extends IService<StdInfoNull> {
    /**
     * Describe: 根据条件查询字典类型列表数据
     * Param: SysDictType
     * Return: List<SysDictType>
     */
    List<StdInfoNull> list(StdInfoNull stdInfo);

    /**
     * Describe: 根据条件查询字典类型列表数据 分页
     * Param: SysDictType
     * Return: PageInfo<SysDictType>
     */
    PageInfo<StdInfoNull> page(StdInfoNull stdInfo, PageDomain pageDomain);

  }

