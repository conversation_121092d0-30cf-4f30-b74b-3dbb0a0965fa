package com.pearadmin.modules.standard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.modules.standard.domain.*;
import com.pearadmin.modules.standard.mapper.StdInfoNullMapper;
import com.pearadmin.modules.standard.service.StdClassJoinStandardService;
import com.pearadmin.modules.standard.service.StdInfoNullService;
import com.pearadmin.modules.standard.service.StdSystemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Describe: 文件服务接口实现
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
@Slf4j
@Service("StdInfoNullServiceImpl")
public class StdInfoNullServiceImpl extends ServiceImpl<StdInfoNullMapper, StdInfoNull> implements StdInfoNullService {
    @Resource
    private StdInfoNullMapper stdInfonNullMapper;
    @Autowired
    private StdClassJoinStandardService stdClassJoinStandardService;
    @Autowired
    private StdSystemService stdSystemService;
    @Override
    public List<StdInfoNull> list(StdInfoNull stdInfo) {
        return stdInfonNullMapper.selectList(new QueryWrapper<>(stdInfo));
    }

    @Autowired
    private com.pearadmin.common.tools.UploadProcess process;

    @Override
    public PageInfo<StdInfoNull> page(StdInfoNull stdInfo, PageDomain pageDomain) {
        PageHelper.startPage(pageDomain.getPage(), pageDomain.getLimit());
        List<StdInfoNull> infoes = list(new QueryWrapper<>(stdInfo));
        return new PageInfo<StdInfoNull>(infoes);
    }





}
