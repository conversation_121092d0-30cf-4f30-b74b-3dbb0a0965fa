package com.pearadmin.modules.standard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.modules.standard.domain.StdDownloadConfig;
import com.pearadmin.modules.standard.mapper.StdDownloadConfigMapper;
import com.pearadmin.modules.standard.service.StdDownloadConfigService;
import com.pearadmin.modules.sys.domain.SysConfig;
import com.pearadmin.modules.sys.service.SysConfigService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 标准下载配置 Service 实现类
 *
 * <AUTHOR> Code
 * @date 2023/5/28
 */
@Service
public class StdDownloadConfigServiceImpl extends ServiceImpl<StdDownloadConfigMapper, StdDownloadConfig> implements StdDownloadConfigService {

    @Resource
    private SysConfigService sysConfigService;

    /**
     * 系统默认下载限制
     */
    private static final Integer DEFAULT_DOWNLOAD_LIMIT = 10;

    /**
     * 系统默认预览限制
     */
    private static final Integer DEFAULT_PREVIEW_LIMIT = 10;

    @Override
    public StdDownloadConfig getLatestByUserName(String userName) {
        if (userName == null || userName.trim().isEmpty()) {
            return null;
        }
        return baseMapper.getLatestByUserName(userName);
    }

    @Override
    public Integer getDownloadLimit(String userName) {
        // 首先查找用户特定配置（使用数字类型）
        String configValue = getConfigValue(userName, StdDownloadConfig.CONFIG_TYPE_DOWNLOAD);
        if (configValue != null) {
            try {
                return Integer.parseInt(configValue);
            } catch (NumberFormatException e) {
                // 如果配置值无效，继续查找系统配置
            }
        }

        // 如果没有用户特定配置，查找系统配置
        SysConfig sysConfig = sysConfigService.getByCode("download_limit");
        if (sysConfig != null && sysConfig.getConfigValue() != null) {
            try {
                return Integer.parseInt(sysConfig.getConfigValue());
            } catch (NumberFormatException e) {
                // 如果系统配置值无效，返回默认值
                return DEFAULT_DOWNLOAD_LIMIT;
            }
        }

        // 如果都没有配置，返回默认值
        return DEFAULT_DOWNLOAD_LIMIT;
    }

    @Override
    public Integer getPreviewLimit(String userName) {
        // 首先查找用户特定配置（使用数字类型）
        String configValue = getConfigValue(userName, StdDownloadConfig.CONFIG_TYPE_PREVIEW);
        if (configValue != null) {
            try {
                return Integer.parseInt(configValue);
            } catch (NumberFormatException e) {
                // 如果配置值无效，继续查找系统配置
            }
        }

        // 如果没有用户特定配置，查找系统配置
        SysConfig sysConfig = sysConfigService.getByCode("preview_limit");
        if (sysConfig != null && sysConfig.getConfigValue() != null) {
            try {
                return Integer.parseInt(sysConfig.getConfigValue());
            } catch (NumberFormatException e) {
                // 如果系统配置值无效，返回默认值
                return DEFAULT_PREVIEW_LIMIT;
            }
        }

        // 如果都没有配置，返回默认值
        return DEFAULT_PREVIEW_LIMIT;
    }

    @Override
    public String getConfigValue(String userName, String configType) {
        if (userName == null || userName.trim().isEmpty() || configType == null || configType.trim().isEmpty()) {
            return null;
        }
        return baseMapper.getConfigValue(userName, configType);
    }

    @Override
    public boolean saveOrUpdateConfig(String userName, Integer downloadLimit, Integer previewLimit, String remark) {
        if (userName == null || userName.trim().isEmpty()) {
            return false;
        }

        try {
            boolean downloadResult = true;
            boolean previewResult = true;

            // 保存下载限制配置
            if (downloadLimit != null) {
                downloadResult = saveOrUpdateConfigByType(userName, StdDownloadConfig.CONFIG_TYPE_DOWNLOAD,
                    downloadLimit.toString(), null, null, 1, remark);
            }

            // 保存预览限制配置
            if (previewLimit != null) {
                previewResult = saveOrUpdateConfigByType(userName, StdDownloadConfig.CONFIG_TYPE_PREVIEW,
                    previewLimit.toString(), null, null, 1, remark);
            }

            return downloadResult && previewResult;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean saveOrUpdateConfigByType(String userName, String configType, String configValue,
                                          LocalDateTime startTime, LocalDateTime endTime, Integer status, String remark) {
        if (userName == null || userName.trim().isEmpty() ||
            configType == null || configType.trim().isEmpty() ||
            configValue == null || configValue.trim().isEmpty()) {
            return false;
        }

        try {
            // 查找是否已存在该用户该类型的配置
            LambdaQueryWrapper<StdDownloadConfig> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StdDownloadConfig::getUserName, userName)
                       .eq(StdDownloadConfig::getConfigType, configType)
                       .eq(StdDownloadConfig::getStatus, 1)
                       .orderByDesc(StdDownloadConfig::getCreateTime)
                       .last("LIMIT 1");

            StdDownloadConfig existingConfig = getOne(queryWrapper);

            if (existingConfig != null) {
                // 更新现有配置
                existingConfig.setConfigValue(configValue);
                existingConfig.setStartTime(startTime);
                existingConfig.setEndTime(endTime);
                existingConfig.setRemark(remark);
                existingConfig.setUpdateTime(LocalDateTime.now());
                existingConfig.setUpdateBy(getCurrentUserName());
                return updateById(existingConfig);
            } else {
                // 创建新配置
                StdDownloadConfig newConfig = new StdDownloadConfig();
                newConfig.setUserName(userName);
                newConfig.setConfigType(configType);
                newConfig.setConfigValue(configValue);
                newConfig.setStartTime(startTime);
                newConfig.setEndTime(endTime);
                newConfig.setStatus(1);
                newConfig.setRemark(remark);
                newConfig.setCreateTime(LocalDateTime.now());
                newConfig.setUpdateTime(LocalDateTime.now());
                newConfig.setCreateBy(getCurrentUserName());
                newConfig.setUpdateBy(getCurrentUserName());
                return save(newConfig);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取当前用户名
     */
    private String getCurrentUserName() {
        try {
            return UserContext.currentUser() != null ? UserContext.currentUser().getUsername() : "system";
        } catch (Exception e) {
            return "system";
        }
    }
}
