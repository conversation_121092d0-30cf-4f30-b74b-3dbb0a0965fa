package com.pearadmin.modules.standard.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.tools.*;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.modules.standard.domain.*;
import com.pearadmin.modules.standard.mapper.*;
import com.pearadmin.modules.standard.service.StdClassificationService;
import com.pearadmin.modules.standard.service.StdSystemService;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.omg.PortableInterceptor.SYSTEM_EXCEPTION;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Describe: 文件服务接口实现
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
@Slf4j
@Service("StdSystemServiceImpl")
public class StdSystemServiceImpl extends ServiceImpl<StdSystemMapper, StdSystem> implements StdSystemService {
    @Resource
    private StdSystemMapper modelMapper;
    @Resource
    private StdClassificationMapper classMapper;
    @Resource
    private StdClassJoinStandardMapper joinMapper;
    @Resource
    private StdInfoMapper stdInfoMapper;
    @Resource
    private StdInfoNullMapper stdInfoNullMapper;

    @Autowired
    private com.pearadmin.common.tools.UploadProcess process;
    @Resource
    private com.pearadmin.common.tools.Tool tool;
    @Autowired
    private StdClassificationService stdClassificationService;

    @Override
    public List<StdSystem> list(StdSystem stdSystem) {
        return modelMapper.selectList(new QueryWrapper<StdSystem>(stdSystem));
    }

    @Override
    public PageInfo<StdSystem> page(StdSystem stdSystem, PageDomain pageDomain) {
        PageHelper.startPage(pageDomain.getPage(), pageDomain.getLimit());
        List<StdSystem> infoes = list(new QueryWrapper<>(stdSystem));
        return new PageInfo<>(infoes);
    }

    /**
     * 上传岗位及其标准
     * @param inputStream
     * @param customSessionId
     * @return
     * @throws IOException
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Result<Boolean> uploadPost(InputStream inputStream, String customSessionId) throws IOException {
        Result<Boolean> result = new Result<>();
        result.setSuccess(true);
        result.setMsg("");
        var hasError = false;
        try {
            // 读取Excel文件
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

            // 处理岗位信息表
            XSSFSheet postSheet = workbook.getSheet("岗位信息表");
            if (postSheet == null) {
                result.setData(false);
                result.setMsg("Excel文件中缺少'岗位信息表'标签页");
                hasError=true;
                return result;
            }
            // 读取岗位信息
            XSSFRow postRow = postSheet.getRow(1); // 第二行开始是数据
            if (postRow == null) {
                result.setData(false);
                result.setMsg("Excel文件‘岗位信息表’标签页中缺少岗位信息。");
                hasError=true;
                return result;
            }

            // 读取excel信息
            String postName =Tool.getCellStringValue(postRow.getCell(1)); // 岗位名称
            String departmentName = Tool.getCellStringValue(postRow.getCell(2));//所属部门
            String remark = Tool.getCellStringValue(postRow.getCell(3)); // 描述

            if(postName==null || StringUtils.isEmpty(postName.trim()))
            {
                result.setData(false);
                result.setMsg("岗位名称不能为空。");
                hasError=true;
                return result;
            }
            if(departmentName==null || StringUtils.isEmpty(departmentName.trim()))
            {
                result.setData(false);
                result.setMsg("部门名称不能为空。");
                hasError=true;
                return result;
            }
            // 获取岗位体系对象
            StdSystem postSystem = getOne(new QueryWrapper<StdSystem>().eq("system_type", 2).last("limit 1"));
            if (postSystem == null) {
                result.setData(false);
                result.setMsg("未找到stdsystem表中岗位体系根对象，请与管理员联系。");
                hasError=true;
                return result;
            }
            // 检查部门是否已存在
            QueryWrapper<StdClassification> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("class_name", departmentName)
                    .eq("class_type", 0)
                    .eq("system_id", postSystem.getId());
            StdClassification department = stdClassificationService.getOne(queryWrapper);
            if (department == null) {
                result.setData(false);
                result.setMsg("未找到【" + departmentName + "】部门信息，请在“岗位专题 -》部门管理界面中添加对应部门信息。");
                hasError=true;
                return result;
            }
            // 检查岗位是否已存在
            QueryWrapper<StdClassification> postWrapper = new QueryWrapper<>();
            postWrapper.eq("class_name", postName)
                    .eq("class_type", 1)
                    .eq("parent_id", department.getId())
                    .eq("system_id", postSystem.getId());
            StdClassification post =stdClassificationService.getOne(postWrapper);
            if (post == null) {
                post=new StdClassification();
                post.setClassName(postName);
                post.setSystemId(postSystem.getId());
                post.setParentFullCode(department.getClassCodePath());
                post.setParentId(department.getId());
                post.setParentName(department.getClassName());
                post.setStatus("1");
                post.setClassType("1");
                post.setRemark(remark);
                tool.fillClassCode(post);
                if (!stdClassificationService.save(post)) {
                    result.setData(false);
                    result.setMsg("保存岗位信息失败");
                    hasError=true;
                    return result;
                }
            } else {
                // 岗位已存在，更新描述信息
                post.setRemark(remark);
                stdClassificationService.updateById(post);
            }

            // 处理标准明细表
            ArrayList<StdClassJoinStandard> joinList = new ArrayList<>();
            XSSFSheet standardSheet = workbook.getSheet("标准明细表");
            if (standardSheet != null) {
                for (int i = 1; i <= standardSheet.getLastRowNum(); i++) {
                    XSSFRow row = standardSheet.getRow(i);
                    if (row == null) continue;
                    var model = new StdClassJoinStandard();
                    model.setStdNo(Tool.getCellStringValue(row.getCell(1)));
                    //一行数据都是空，则进入下一行
                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(model.getStdNo())) {
                        continue;
                    }
                    joinList.add(model);
                }
            }
            workbook.close();
            //取标准与分类的关联-----------------------------------------------------
            int positionRow = 0;
            int positionCol = 0;

            process.setTotal(joinList.size()+1);
            process.setDealed(1);
            //处理分类与标准的关联关系-------------------------------------
            process.setTitle("岗位专题");
            for (var i = 0; i < joinList.size(); i++) {
                var joinModel = joinList.get(i);
                joinModel.setSystemId(postSystem.getId());
                joinModel.setSystemName(postSystem.getName());
                joinModel.setClassCode(post.getClassCode());
                joinModel.setClassName(post.getClassName());
                joinModel.setClassCodePath(post.getClassCodePath());
                var std = stdInfoMapper.selectOne(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdNo, joinModel.getStdNo()).last("limit 1"));
                if (std == null || com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(std.getId())) {
                    var stdNull = stdInfoNullMapper.selectOne(new LambdaQueryWrapper<StdInfoNull>().eq(StdInfoNull::getStdNo, joinModel.getStdNo()).last("limit 1"));
                    if (stdNull == null || com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(stdNull.getId())) {
                        stdNull = new StdInfoNull();
                        stdNull.setStdNo(joinModel.getStdNo());
                        stdNull.setStdOrgName("");
                        stdNull.setStdEnglishName("");
                        stdNull.setStdChineseName("");
                        stdNull.setStdStatus("空");
                        var res = stdInfoNullMapper.insert(stdNull);
                        if (res == 0) {
                            stdNull = null;
                        }
                    }
                    if (stdNull != null && com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(stdNull.getId())) {
                        joinModel.setStdName("");
                        joinModel.setStdId(stdNull.getId());
                        joinModel.setStdStatus(stdNull.getStdStatus());
                    } else {
                        result.setData(false);
                        result.setMsg(result.getMsg() + ";;标准号为【" + joinModel.getStdNo() + "】的数据导入失败,库中不存在该标准号。");
                        hasError = true;
                        break;
                    }
                } else {
                    joinModel.setStdName(std.getStdOrgName());
                    joinModel.setStdId(std.getId());
                    joinModel.setStdStatus(std.getStdStatus());
                }
                var joinResult = joinMapper.selectOne(new LambdaQueryWrapper<StdClassJoinStandard>().eq(StdClassJoinStandard::getStdNo, joinModel.getStdNo()).eq(StdClassJoinStandard::getClassCodePath, joinModel.getClassCodePath()).eq(StdClassJoinStandard::getSystemId, postSystem.getId()).last("limit 1"));
                if (joinResult == null || com.baomidou.mybatisplus.core.toolkit.StringUtils.isBlank(joinResult.getId())) {
                    joinMapper.insert(joinModel);
                }
                process.setDealed(process.getDealed() + 1);
            }
            result.setMsg(String.format("已完成导入1个岗位、%d条标准明细。",joinList.size()));
            if (!hasError) {
                //更新标准数量
                var stdCount = joinMapper.selectCount(new LambdaQueryWrapper<StdClassJoinStandard>().eq(StdClassJoinStandard::getSystemId, postSystem.getId()));
                postSystem.setStandardNumber(Integer.parseInt(stdCount.toString()));
                updateById(postSystem);
            } else {
                result.setSuccess(false);
                result.setMsg(result.getMsg() + ";;导入失败！");
                result.setData(false);
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        } catch (Exception ex) {
            result.setSuccess(false);
            result.setMsg(result.getMsg() + ";;导入失败,堆栈跟踪：" + ex.getMessage());
            System.out.println(ex.getStackTrace());
            result.setData(false);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return result;
    }

    /**
     * 通过excel导入体系
     *
     * @param inputStream
     * @return void
     * @throws
     */
    @Override
    public Result<Boolean> uploadSystem(String systemType, String customSessionId,InputStream inputStream) throws IOException, ParseException {
        var opResult=new CResult<Boolean>();
        opResult.setSuccess(true);
        opResult.setResult(true);
        var stdSystem=new StdSystem();
        var classList=new ArrayList<StdClassification>();
        var joinList=new ArrayList<StdClassJoinStandard>();
        //创建
        XSSFWorkbook book = new XSSFWorkbook(inputStream);
        //体系(只取第一行）
        XSSFSheet sheet = book.getSheetAt(0);
        XSSFRow row = sheet.getRow( 1); // 表头不算
        stdSystem.setName(Tool.getCellStringValue(row.getCell(1)));
        stdSystem.setLevel(Tool.getCellStringValue(row.getCell(2)));
        stdSystem.setDescription(Tool.getCellStringValue(row.getCell(3)));
        stdSystem.setIsPublish("0");//编制中
        stdSystem.setLayerNumber(0);
        stdSystem.setStandardNumber(0);
        stdSystem.setStatus("1");
        stdSystem.setSystemType(systemType);
        //取分类-----------------------------------------------------
        sheet = book.getSheetAt(1);
        var positionRow=0;
        var positionCol=0;
        int cols;
        for (int i = positionRow; i < sheet.getLastRowNum(); i++) {
            row = sheet.getRow(i + 1); // 表头不算
            cols = positionCol;
            var model = new StdClassification();
            model.setClassName(Tool.getCellStringValue(row.getCell(1)));
            model.setClassCodePath(Tool.getCellStringValue(row.getCell(2)));
            if(model.getClassCodePath().length()>1) {
                model.setClassCode(model.getClassCodePath().substring(model.getClassCodePath().length() - 1));
                model.setParentFullCode(model.getClassCodePath().substring(0, model.getClassCodePath().length() - 1));
            }
            else
            {
                model.setClassCode(model.getClassCodePath());
                model.setParentFullCode("-1");
            }
            model.setStatus("1");
            //一行数据都是空，则进入下一行
            if(StringUtils.isBlank(model.getClassName()) &&
                    StringUtils.isBlank(model.getClassCodePath()))
            {
                continue;
            }
            classList.add(model);
        }
        //取标准与分类的关联-----------------------------------------------------
        sheet = book.getSheetAt(2);
        positionRow=0;
        positionCol=0;
        for (int i = positionRow; i < sheet.getLastRowNum(); i++) {
            row = sheet.getRow(i + 1); // 表头不算
            cols = positionCol;
            var model = new StdClassJoinStandard();
            model.setClassCodePath(Tool.getCellStringValue(row.getCell(1)));
            model.setStdNo(Tool.getCellStringValue(row.getCell(2)));
            //一行数据都是空，则进入下一行
            if(StringUtils.isBlank(model.getClassCodePath()) &&
                    StringUtils.isBlank(model.getStdNo()))
            {
                continue;
            }
            joinList.add(model);
        }
        book.close();
        var result=ImportData(stdSystem,classList,joinList);
        return result;
    }

    @Override
    public Result<StdSystem> getOrCreateSystem(String systemType, String userId,String userName) {
         var result=new Result<StdSystem>();
         StdSystem sys=null;
         if(SystemTypeEnum.PersonalCollect.equals(systemType)) {
              sys = modelMapper.selectOne(new LambdaQueryWrapper<StdSystem>().eq(StdSystem::getSystemType, SystemTypeEnum.PersonalCollect).eq(StdSystem::getOwnerId, userId).last("limit 1"));
         }
         else if(SystemTypeEnum.PostSubject.equals(systemType))
         {
              sys = modelMapper.selectOne(new LambdaQueryWrapper<StdSystem>().eq(StdSystem::getSystemType, SystemTypeEnum.PostSubject).last("limit 1"));
         }
        if(sys==null || org.apache.commons.lang.StringUtils.isBlank(sys.getId()))
        {
            //个人收藏
            if(SystemTypeEnum.PersonalCollect.equals(systemType)) {
                sys=StdSystem.buildSystem(systemType,userName+"的个人标准收藏",userId,"", StatusEnum.Normal,"1");
                modelMapper.insert(sys);
                //在创建一个顶级分类，用来绑定数据
                var stdClass=new StdClassification();
                stdClass.setClassName(userName+"的个人标准收藏");
                stdClass.setClassCode("A");
                stdClass.setClassCodePath("A");
                stdClass.setClassType(ClassTypeEnum.Leaves);
                stdClass.setParentFullCode("-1");
                stdClass.setParentId("-1");
                stdClass.setSortCondition(0);
                stdClass.setSystemId(sys.getId());
                stdClass.setParentName("");
                stdClass.setStatus(StatusEnum.Normal);
                classMapper.insert(stdClass);
            }
            //单位的岗位专题
            else if(SystemTypeEnum.PostSubject.equals(systemType))
            {
                sys=StdSystem.buildSystem(systemType,"岗位专题","","", StatusEnum.Normal,"1");
                modelMapper.insert(sys);
            }
        }
        result.setData(sys);
        result.setSuccess(true);
        return result;
    }

    @Override
    public StdClassification getPersonalCollectDefaultClass() {
        var user= UserContext.currentUser();
        StdClassification classify=null;
        var res=getOrCreateSystem(SystemTypeEnum.PersonalCollect,user.getUserId(),user.getRealName());
        var sys=res.getData();
        if(sys!=null && StringUtils.isNotBlank(sys.getId()))
        {
            classify=classMapper.selectOne(new LambdaQueryWrapper<StdClassification>().eq(StdClassification::getSystemId,sys.getId()).eq(StdClassification::getStatus,StatusEnum.Normal).last("limit 1"));
        }
        return classify;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public Result<Boolean> ImportData(StdSystem system, ArrayList<StdClassification> classList, ArrayList<StdClassJoinStandard> joinList) {
        var total = 1 + classList.size() + joinList.size();
        process.setTotal(total);
        process.setDealed(0);
        process.setTitle(system.getSystemType().equals("1") ? "标准体系" : "岗位专题");
        Result<Boolean> result = new Result<>();
        result.setSuccess(true);
        result.setMsg("");
        var hasError=false;
        try {
            var systemId = "";
            var orgSys = modelMapper.selectOne(new LambdaQueryWrapper<StdSystem>().eq(StdSystem::getName, system.getName()).last("limit 1"));
            if (orgSys != null && StringUtils.isNotBlank(orgSys.getId())) {
                modelMapper.update(orgSys, new LambdaUpdateWrapper<StdSystem>().set(StdSystem::getName, system.getName()).set(StdSystem::getLevel, system.getLevel()).set(StdSystem::getDescription, system.getDescription()).eq(StdSystem::getId, orgSys.getId()));
                systemId = orgSys.getId();
            } else {
                modelMapper.insert(system);
                systemId = system.getId();
                orgSys = system;
            }
            process.setDealed(process.getDealed() + 1);
            //分类---------------------------------------
            //先处理父分类
            var rootList = classList.stream().filter(d -> d.getClassCodePath().length() == 1).collect(Collectors.toList());
            for (var i = 0; i < rootList.size(); i++) {
                var classify = rootList.get(i);
                classify.setSystemId(systemId);
                var org = classMapper.selectOne(new LambdaQueryWrapper<StdClassification>().eq(StdClassification::getClassCodePath, classify.getClassCodePath()).eq(StdClassification::getSystemId, systemId).last("limit 1"));
                if (org == null || StringUtils.isBlank(org.getId())) {
                    classMapper.insert(classify);
                }
                process.setDealed(process.getDealed() + 1);
            }
            //2 处理子分类,一级一级的处理，防止因excel中顺序错误找不到父分类的情况
            process.setTitle(system.getSystemType().equals("1") ? "标准体系结构" : "岗位专题结构");
            for (var idx = 2; idx < 20; idx++) {
                int finalIdx = idx;
                var subList = classList.stream().filter(d -> d.getClassCodePath().length() == finalIdx).collect(Collectors.toList());
                if (subList == null || subList.size() == 0) {
                    break;
                }
                for (var i = 0; i < subList.size(); i++) {
                    var classify = subList.get(i);
                    //1处理父分类
                    var pCode = classify.getParentFullCode();
                    var parent = classMapper.selectOne(new LambdaQueryWrapper<StdClassification>().eq(StdClassification::getClassCodePath, pCode).eq(StdClassification::getSystemId, systemId).last("limit 1"));
                    if (parent != null) {
                        classify.setParentId(parent.getId());
                        classify.setParentName(parent.getClassName());
                        classify.setParentClassType(parent.getClassType());
                        classify.setSystemId(systemId);
                    }
                    //处理当前分类
                    var org = classMapper.selectOne(new LambdaQueryWrapper<StdClassification>().eq(StdClassification::getClassCodePath, classify.getClassCodePath()).eq(StdClassification::getSystemId, systemId).last("limit 1"));
                    if (org == null || StringUtils.isBlank(org.getId())) {
                        classify.setSystemId(systemId);
                        classMapper.insert(classify);
                    }
                }
                process.setDealed(process.getDealed() + 1);
            }
            //处理分类与标准的关联关系-------------------------------------
            process.setTitle("标准");
            for (var i = 0; i < joinList.size(); i++) {
                var joinModel = joinList.get(i);
                joinModel.setSystemId(systemId);
                joinModel.setSystemName(system.getName());
                var classify = classMapper.selectOne(new LambdaQueryWrapper<StdClassification>().eq(StdClassification::getClassCodePath, joinModel.getClassCodePath()).eq(StdClassification::getSystemId, systemId).last("limit 1"));
                if (classify == null || StringUtils.isBlank(classify.getId())) {
                    result.setData(false);
                    result.setMsg(result.getMsg() + ";;标准号为【" + joinModel.getStdNo() + "】的数据导入失败，位置标识【" + joinModel.getClassCodePath() + "】在体系结构中不存在");
                    hasError=true;
                    break;
                } else {
                    joinModel.setClassCode(classify.getClassCode());
                    joinModel.setClassName(classify.getClassName());
                }
                var std = stdInfoMapper.selectOne(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdNo, joinModel.getStdNo()).last("limit 1"));
                if (std == null || StringUtils.isBlank(std.getId())) {
                    var stdNull = stdInfoNullMapper.selectOne(new LambdaQueryWrapper<StdInfoNull>().eq(StdInfoNull::getStdNo, joinModel.getStdNo()).last("limit 1"));
                    if (stdNull == null || StringUtils.isBlank(stdNull.getId())) {
                        stdNull = new StdInfoNull();
                        stdNull.setStdNo(joinModel.getStdNo());
                        stdNull.setStdOrgName("");
                        stdNull.setStdEnglishName("");
                        stdNull.setStdChineseName("");
                        stdNull.setStdStatus("1");
                        var res=stdInfoNullMapper.insert(stdNull);
                        if(res==0)
                        {
                            stdNull=null;
                        }
                     }
                    if (stdNull != null && StringUtils.isNotBlank(stdNull.getId())) {
                        joinModel.setStdName("");
                        joinModel.setStdId(stdNull.getId());
                        joinModel.setStdStatus(stdNull.getStdStatus());
                    }
                    else
                    {
                        result.setData(false);
                        result.setMsg(result.getMsg() + ";;标准号为【" + joinModel.getStdNo() + "】的数据导入失败,库中不存在该标准号。");
                        hasError=true;
                        break;
                    }

                } else {

                    joinModel.setStdName(std.getStdOrgName());
                    joinModel.setStdId(std.getId());
                    joinModel.setStdStatus(std.getStdStatus());
                }
                var joinResult = joinMapper.selectOne(new LambdaQueryWrapper<StdClassJoinStandard>().eq(StdClassJoinStandard::getStdNo, joinModel.getStdNo()).eq(StdClassJoinStandard::getClassCodePath, joinModel.getClassCodePath()).eq(StdClassJoinStandard::getSystemId, systemId).last("limit 1"));
                if (joinResult == null || StringUtils.isBlank(joinResult.getId())) {
                    joinMapper.insert(joinModel);
                }
                process.setDealed(process.getDealed() + 1);
            }

            result.setMsg(String.format("已完成导入1个标准体系、%d项框架节点、%d条标准明细。",classList.size(),joinList.size()));
            process.setDealed(process.getTotal());
            if(!hasError) {
                //更新层级及标准数量
                var minClass = classMapper.selectOne(new QueryWrapper<StdClassification>().eq("system_id", systemId).orderByDesc("length(class_code_path)").last("limit 1"));
                if (minClass != null) {
                    orgSys.setLayerNumber(minClass.getClassCodePath().length());
                }
                var stdCount = joinMapper.selectCount(new LambdaQueryWrapper<StdClassJoinStandard>().eq(StdClassJoinStandard::getSystemId, systemId));
                orgSys.setStandardNumber(Integer.parseInt(stdCount.toString()));
                modelMapper.updateById(orgSys);
            }
            else
            {
                result.setSuccess(false);
                result.setMsg(result.getMsg() + ";;导入失败！");
                result.setData(false);
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        } catch (Exception ex) {
            result.setSuccess(false);
            result.setMsg(result.getMsg() + ";;导入失败,堆栈跟踪：" + ex.getMessage());
            System.out.println(ex.getStackTrace());
            result.setData(false);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return result;
    }
}
