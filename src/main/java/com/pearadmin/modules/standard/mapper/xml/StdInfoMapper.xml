<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pearadmin.modules.standard.mapper.StdInfoMapper">
    <select id="pageBySystemInfo" resultType="com.pearadmin.modules.standard.domain.StdInfoOfSystem">
        select * from (
                          select aa.id,
                                 aa.std_no,
                                 aa.std_org_name,
                                 aa.std_chinese_name,
                                 aa.std_english_name,
                                 std_langugage,
                                 std_identification,
                                 security_class,
                                 catetory_no,
                                 std_ics,
                                 pub_dept,
                                 pub_date,
                                 implementation_date,
                                 advance_dept,
                                 drafting_unit,
                                 drafter,
                                 primary_coverage,
                                 page_count,
                                 alternate_std_no,
                                 superseded_std_no,
                                 std_status,
                                 std_ocr,
                                 pdf_file_name,
                                 pdf_is_exists,
                                 std_type,
                                 std_class,
                                 sort_identifaction,
                                 sort_serial_no,
                                 sort_char,
                                 sort_year,
                                 is_real,
                                 bb.system_id,
                                 bb.class_code_path,
                                 bb.class_name,
                                 bb.id AS join_id,
                                 aa.remark,
                                 aa.create_time,
                                 aa.create_by,
                                 aa.update_time,
                                 aa.update_by,
                                 bb.update_time AS join_update_time,
                                 bb.create_time AS join_create_time
                                from std_info aa INNER JOIN std_class_join_standard bb ON aa.id = bb.std_id
                                 ${ew.customSqlSegment}
                      )aaa
        union
        select *from
            (
                select aa.id,
                       aa.std_no,
                       aa.std_org_name,
                       aa.std_chinese_name,
                       aa.std_english_name,
                       std_langugage,
                       std_identification,
                       security_class,
                       catetory_no,
                       std_ics,
                       pub_dept,
                       pub_date,
                       implementation_date,
                       advance_dept,
                       drafting_unit,
                       drafter,
                       primary_coverage,
                       page_count,
                       alternate_std_no,
                       superseded_std_no,
                       std_status,
                       std_ocr,
                       pdf_file_name,
                       pdf_is_exists,
                       std_type,
                       std_class,
                       sort_identifaction,
                       sort_serial_no,
                       sort_char,
                       sort_year,
                       is_real,
                       bb.system_id,
                       bb.class_code_path,
                       bb.class_name,
                       bb.id AS join_id,
                       aa.remark,
                       aa.create_time,
                       aa.create_by,
                       aa.update_time,
                       aa.update_by,
                    bb.update_time AS join_update_time,
                    bb.create_time AS join_create_time
                    from std_info_null aa INNER JOIN std_class_join_standard bb ON aa.id = bb.std_id
                    ${ew.customSqlSegment}
            ) bbb
            ${orderSqlSegment}
    </select>
    <select id="getPdfExistCount" resultType="java.lang.Integer">
         select count(0)  from (
        select distinct id from (
        select  aa.id,class_code_path,sort_year,sort_serial_no,sort_char
          from  std_info aa
            INNER JOIN std_class_join_standard bb ON aa.id = bb.std_id
            ${ew.customSqlSegment}
            ) dd
         ) cc
    </select>
   <!-- 取被评价过的标准列表-->
    <select id="pageByOpinion" resultType="com.pearadmin.modules.standard.domain.StdInfo">
        select * from ( select info.*,op.create_time as opinion_create_time  from std_info info inner join (select  std_id,max(create_time) as create_time  from  std_opinion group by std_id) op on info.id=op.std_id
                       union
                       select info.*,op.create_time as opinion_create_time  from std_info_null info inner join (select  std_id,max(create_time) as create_time  from  std_opinion group by std_id) op on info.id=op.std_id
                      ) aa ${ew.customSqlSegment}
    </select>
    <select id="pageByFullTextSearch" resultType="com.pearadmin.modules.standard.domain.StdInfo">
        select * from std_info  where std_type=#{stdType}
        <if test="stdNo != null and stdNo!=''">
            and MATCH(std_no) AGAINST (#{stdNo, jdbcType=VARCHAR} IN BOOLEAN MODE)
        </if>
        <if test="stdOrgName!= null and stdOrgName!=''">
            and MATCH(std_org_name) AGAINST (#{stdOrgName, jdbcType=VARCHAR} IN BOOLEAN MODE)
        </if>
        <if test="stdChineseName!= null and stdChineseName!=''">
            and MATCH(std_chinese_name) AGAINST (#{stdChineseName, jdbcType=VARCHAR} IN BOOLEAN MODE)
        </if>
        <if test="stdEnglishName!= null and stdEnglishName!=''">
            and MATCH(std_english_name) AGAINST (#{stdEnglishName, jdbcType=VARCHAR} IN BOOLEAN MODE)
        </if>
        <if test=" primaryCoverage!= null and primaryCoverage!=''">
            and MATCH(primary_coverage) AGAINST (#{primaryCoverage, jdbcType=VARCHAR} IN BOOLEAN MODE)
        </if>
        <if test="pubDept!= null and pubDept!=''">
            and MATCH(pub_dept) AGAINST (#{pubDept, jdbcType=VARCHAR} IN BOOLEAN MODE)
        </if>
        <if test="advanceDept!= null and advanceDept!=''">
            and MATCH(advance_dept) AGAINST (#{advanceDept, jdbcType=VARCHAR} IN BOOLEAN MODE)
        </if>
        <if test="draftingUnit!= null and draftingUnit!=''">
            and MATCH(drafting_unit) AGAINST (#{draftingUnit, jdbcType=VARCHAR} IN BOOLEAN MODE)
        </if>
        <if test="drafter!= null and drafter!=''">
            and MATCH(drafter) AGAINST (#{drafter, jdbcType=VARCHAR} IN BOOLEAN MODE)
        </if>
        <if test="sqlSegment!= null and sqlSegment!=''">
        and  ${sqlSegment}
        </if>
        ${ew.customSqlSegment}
    </select>
</mapper>
