package com.pearadmin.modules.standard.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.domain.StdInfoNull;
import com.pearadmin.modules.standard.domain.StdInfoOfSystem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Describe: 用户接口层
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
@Mapper
public interface StdInfoNullMapper extends BaseMapper<StdInfoNull> {
}
