<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pearadmin.modules.standard.mapper.StdOpinionMapper">
   <select id="pageByStdInfo" resultType="com.pearadmin.modules.standard.domain.StdOpinion">
       select aa.*,aa.create_time as opinion_time from std_opinion aa inner join (select id from std_info  ${ew.customSqlSegment}) bb on aa.std_id=bb.id  order by aa.create_time desc
   </select>
</mapper>
