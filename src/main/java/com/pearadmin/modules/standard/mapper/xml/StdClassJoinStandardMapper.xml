<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pearadmin.modules.standard.mapper.StdClassJoinStandardMapper">
   <select id="getStandardNumberOfSystem" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*) from (
            select distinct std_id from  std_class_join_standard where system_id=#{systemId}
        ) aa
   </select>
</mapper>
