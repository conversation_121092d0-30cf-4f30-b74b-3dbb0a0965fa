package com.pearadmin.modules.standard.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.domain.StdInfoOfSystem;
import com.pearadmin.modules.sys.domain.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Describe: 用户接口层
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
@Mapper
public interface StdInfoMapper extends BaseMapper<StdInfo> {
    <E extends IPage<StdInfoOfSystem>> E pageBySystemInfo(E page, @Param(Constants.WRAPPER) Wrapper<StdInfoOfSystem> queryWrapper,@Param("orderSqlSegment") String orderSqlSegment);
    Integer getPdfExistCount(@Param(Constants.WRAPPER) Wrapper<StdInfoOfSystem> queryWrapper);
    <E extends IPage<StdInfo>> E pageByOpinion(E page, @Param(Constants.WRAPPER) Wrapper<StdInfo> queryWrapper);
    <E extends IPage<StdInfo>> E pageByFullTextSearch(E page, @Param(Constants.WRAPPER) Wrapper<StdInfo> queryWrapper, @Param("sqlSegment") String sqlSegment, @Param("stdType") String stdType, @Param("stdNo") String stdNo, @Param("stdOrgName") String stdOrgName, @Param("stdChineseName") String stdChineseName, @Param("stdEnglishName") String stdEnglishName, @Param("primaryCoverage") String primaryCoverage, @Param("pubDept") String pubDept, @Param("advanceDept") String advanceDept, @Param("draftingUnit") String draftingUnit, @Param("drafter") String drafter);
}
