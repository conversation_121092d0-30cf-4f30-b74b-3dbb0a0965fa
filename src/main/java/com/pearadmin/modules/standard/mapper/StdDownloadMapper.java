package com.pearadmin.modules.standard.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.pearadmin.modules.standard.domain.StdDownload;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.domain.StdInfoOfSystem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * Describe: 用户接口层
 * Author: 崇义波
 * CreateTime: 2025/4/15
 */
@Mapper
public interface StdDownloadMapper extends BaseMapper<StdDownload> {
        ///获取当前用户当前的下载量
        Integer getDownloadCount(@Param("userName") String userName);

        ///获取当前用户当前的预览量
        Integer getPreviewCount(@Param("userName") String userName);
}
