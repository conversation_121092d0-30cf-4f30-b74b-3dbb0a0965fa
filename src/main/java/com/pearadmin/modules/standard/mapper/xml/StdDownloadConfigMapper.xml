<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pearadmin.modules.standard.mapper.StdDownloadConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.pearadmin.modules.standard.domain.StdDownloadConfig">
        <id column="id" property="id" />
        <result column="user_name" property="userName" />
        <result column="download_limit" property="downloadLimit" />
        <result column="preview_limit" property="previewLimit" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_name, download_limit, preview_limit, status, remark, create_time, update_time, create_by, update_by
    </sql>

    <!-- 根据用户名获取最新的下载配置记录 -->
    <select id="getLatestByUserName" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM std_download_config
        WHERE user_name = #{userName}
        AND status = 1
        ORDER BY create_time DESC
        LIMIT 1
    </select>

</mapper>
