package com.pearadmin.modules.standard.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pearadmin.modules.standard.domain.StdDownloadConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 标准下载配置 Mapper 接口
 *
 * <AUTHOR> Code
 * @date 2023/5/28
 */
@Mapper
public interface StdDownloadConfigMapper extends BaseMapper<StdDownloadConfig> {

    /**
     * 根据用户名获取最新的下载配置记录
     *
     * @param userName 用户名
     * @return 最新的下载配置记录
     */
    StdDownloadConfig getLatestByUserName(@Param("userName") String userName);
}
