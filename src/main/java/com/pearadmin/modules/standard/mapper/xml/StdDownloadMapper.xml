<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pearadmin.modules.standard.mapper.StdDownloadMapper">
        <select id="getDownloadCount" resultType="java.lang.Integer">
            select count(1) from std_download where user_id=#{userName} and create_time &gt;=Curdate() and create_time &lt; DATE_ADD(CurDate(),INTERVAL 1 DAY) and (op_type = 1 or op_type is null)
        </select>

        <select id="getPreviewCount" resultType="java.lang.Integer">
            select count(1) from std_download where user_id=#{userName} and create_time &gt;=Curdate() and create_time &lt; DATE_ADD(CurDate(),INTERVAL 1 DAY) and op_type = 2
        </select>

        <select id="selectAdminUserList" resultType="java.util.Map">
            SELECT
                u.user_id as userId,
                u.username as username,
                u.real_name as realName,
                u.email as email,
                COUNT(d.id) as downloadCount,
                MAX(d.create_time) as lastDownloadTime
            FROM sys_user u
            LEFT JOIN std_download d ON u.user_id COLLATE utf8mb4_0900_ai_ci = d.user_id
            WHERE (d.op_type = 1 OR d.op_type IS NULL)
            <if test="searchKey != null and searchKey != ''">
                AND (u.real_name LIKE CONCAT('%', #{searchKey}, '%') OR u.email LIKE CONCAT('%', #{searchKey}, '%'))
            </if>
            GROUP BY u.user_id, u.username, u.real_name, u.email
            ORDER BY MAX(d.create_time) DESC
        </select>
</mapper>
