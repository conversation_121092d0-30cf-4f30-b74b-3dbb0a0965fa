<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pearadmin.modules.standard.mapper.StdLogMapper">
   <select id="getPageByLogType" resultType="com.pearadmin.modules.standard.domain.StdInfoStatictics">
      select * from (select max(item_id) as item_id,max(item_name) as item_name,count(1) as counts from std_log where log_type=#{logType} group by item_id) a  ${ew.customSqlSegment}
   </select>
   <select id="getCountsByLogType" resultType="java.lang.Integer">
      select count(1) from (select max(item_id) as item_id,max(item_name) as item_name,count(1) as counts from std_log where log_type=#{logType} group by item_id) a  ${ew.customSqlSegment}
   </select>
   <select id="getPageofReferedBySystem" resultType="com.pearadmin.modules.standard.domain.StdInfoStatictics">
      select * from (
         select max(std_no) as item_id,max(std_org_name) as item_name,max(std_exist) as std_exist,count(1) as counts from
         (select a.std_no,a.std_org_name,a.std_exist from
         std_class_join_standard a  inner join std_system b on a.system_id=b.id
         where b.system_type=#{systemType}
         ) d
         group by std_no order by counts desc
     ) e ${ew.customSqlSegment}
   </select>
   <select id="getCountofReferedBySystem"  resultType="java.lang.Integer">
      select count(1) from (
         select max(std_no) as item_id,max(std_org_name) as item_name,count(1) as counts from
         (select a.std_no,a.std_org_name from
         std_class_join_standard a  inner join std_system b on a.system_id=b.id
         where b.system_type=#{systemType}
         ) d
         group by std_no order by counts desc
     ) e ${ew.customSqlSegment}
   </select>
   <select id="getPageOfOpinion" resultType="com.pearadmin.modules.standard.domain.StdInfoStatictics">
      select * from (
         select max(std_no) as item_id,max(std_org_name) as item_name ,count(1) as counts from  std_opinion group by std_no
         ) a ${ew.customSqlSegment}
   </select>
   <select id="getCountsOfOpinion"  resultType="java.lang.Integer">
      select count(1) from (
         select max(std_no) as item_id,max(std_org_name) as item_name ,count(1) as counts from  std_opinion group by std_no
         ) a ${ew.customSqlSegment}
   </select>
</mapper>
