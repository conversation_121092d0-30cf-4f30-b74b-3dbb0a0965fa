package com.pearadmin.modules.standard.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.pearadmin.modules.standard.domain.StdInfoOfSystem;
import com.pearadmin.modules.standard.domain.StdInfoStatictics;
import com.pearadmin.modules.standard.domain.StdLog;
import com.pearadmin.modules.standard.domain.StdOpinion;
import io.swagger.v3.oas.annotations.Parameter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Describe: 用户接口层
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
@Mapper
public interface StdLogMapper extends BaseMapper<StdLog> {
    <E extends IPage<StdInfoStatictics>> E getPageByLogType(E page, @Param("logType") String logType, @Param(Constants.WRAPPER) Wrapper<StdInfoStatictics> queryWrapper);
    Integer getCountsByLogType(@Param("logType") String logType, @Param(Constants.WRAPPER) Wrapper<StdInfoStatictics> queryWrapper);
    <E extends IPage<StdInfoStatictics>> E getPageofReferedBySystem(E page,@Param("systemType") String systemType, @Param(Constants.WRAPPER) Wrapper<StdInfoStatictics> queryWrapper);
    Integer getCountofReferedBySystem(@Param("systemType") String systemType,@Param(Constants.WRAPPER) Wrapper<StdInfoStatictics> queryWrapper);
    <E extends IPage<StdInfoStatictics>> E getPageOfOpinion(E page, @Param(Constants.WRAPPER) Wrapper<StdInfoStatictics> queryWrapper);
    Integer getCountsOfOpinion(@Param(Constants.WRAPPER) Wrapper<StdInfoStatictics> queryWrapper);

}
