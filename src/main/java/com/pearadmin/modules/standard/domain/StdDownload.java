package com.pearadmin.modules.standard.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.pearadmin.common.web.base.BaseDomain;
import lombok.Data;

import java.time.LocalDate;

/**
 * Describe: 标准下载明细表 接 口 实 体
 * Author: chongyb
 * CreateTime: 2025/4/15
 */
@Data
public class StdDownload extends BaseDomain {
    /**
     * 编号
     */
    @TableId
    private String id;
    /**
     * 标准号
     */
    public String stdNo;

    /**
     * 标准名称（原文）
     */
    public String stdOrgName;
    /**
     * 标准名称（中文）
     */
    public String stdChineseName;
    /**
     * 标准类型(china usa foreigin)
     */
    public String stdType;
    /**
     * 用户编号
     */
    public String userId;
    /**
     * 标准表ID
     */
    public String stdInfoId;
    /**
     * 标准文件名
     */
    public String pdfFileName;
    /**
     * 操作类型：1-下载记录，2-预览记录
     */
    public Integer opType;
    @TableField(exist = false)
    public String simpleSearchKey;
    /*
    * 是否收藏（0-未收藏，1-已收藏）
    * */
    @TableField(exist = false)
    public String isCollected;

}
