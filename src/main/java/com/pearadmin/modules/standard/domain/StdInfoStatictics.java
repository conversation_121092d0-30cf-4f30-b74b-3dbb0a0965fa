package com.pearadmin.modules.standard.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.pearadmin.common.web.base.BaseDomain;
import lombok.Data;

/**
 * Describe: 标准统计 接 口 实 体
 * Author: chongyb
 * CreateTime: 2022/12/2
 */
@Data
public class StdInfoStatictics {

    /**
     *  标准号
     */
    private String itemId;

    /**
     * 标准名称
     */
    private String itemName;

    /**
     *  数量
     */
    private long counts;

    @TableField(exist = false)
    private String isCollected;

    /**
     * 标准是否存在
     * 1：std_info存在该记录；0：std_info中不存在，std_info_null中存在一条占位符
     */
    private Integer stdExist;

}
