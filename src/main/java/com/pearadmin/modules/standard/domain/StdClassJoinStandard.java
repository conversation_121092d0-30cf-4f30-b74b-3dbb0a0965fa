package com.pearadmin.modules.standard.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.pearadmin.common.web.base.BaseDomain;
import lombok.Data;

/**
 * Describe: 标准与分类的关联表（多对多） 接 口 实 体
 * Author: chongyb
 * CreateTime: 2023/1/26
 */
@Data
public class StdClassJoinStandard extends BaseDomain {

        /**
         * 编号
         */
        @TableId
        private String id;

        /**
         * 标准自增编号
         */
        private String stdId;

        /**
         * 标准号
         */
        private String stdNo;
        /**
         * 标准名称(原文）
         */
        private String stdOrgName;

        /**
         * 所属体系
         */
        private String systemId;

        /**
         * 体系名称
         */
        private String systemName;

        /**
         * 分类标识
         */
        private String classCode;

        /**
         * 分类名称
         */
        private String className;

        /**
         * 分类标识路径
         */
        private String classCodePath;

        /**
         * 备注
         */
        private String remark;
        /**
         * 标准是否存在
         * 1：std_info存在该记录；0：std_info中不存在，std_info_null中存在一条占位符
         */
        private Integer stdExist;

        /**
         * 标准名称
         */
        @TableField(exist = false)
        private String stdName;
        /**
         * 标准状态
         */
        @TableField(exist = false)
        private String stdStatus;
        @TableField(exist = false)
        private String simpleSearchKey;
        @TableField(exist = false)
        private String advSearchKey;
        //中文标准名称，导入时的冗余
        @TableField(exist = false)
        private String stdChinaName;

}

