package com.pearadmin.modules.standard.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.pearadmin.common.web.base.BaseDomain;
import lombok.Data;

/**
 * Describe: 标准信息 接 口 实 体
 * Author: chongyb
 * CreateTime: 2022/12/2
 */
@Data
public class StdUploadProcess extends BaseDomain {

    /**
     * 编号
     */
    @TableId
    private String id;

    private String session;

    private String customType;

    /**
     * 总记录数
     */
    private Integer totalCount;
    /**
     * 已处理记录数
     */
    private Integer dealedCount;

    /**
     *  剩余记录数
     */
    private Integer restCount;

    /**
     * 文本语言
     */
    private String err;

}
