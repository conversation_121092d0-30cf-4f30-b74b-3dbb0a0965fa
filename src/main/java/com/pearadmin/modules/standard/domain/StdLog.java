package com.pearadmin.modules.standard.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.pearadmin.common.web.base.BaseDomain;
import lombok.Data;

/**
 * Describe: 统计日志  接 口 实 体
 * Author: chongyb
 * CreateTime: 2023/5/17
 */
@Data
public class StdLog extends BaseDomain {

        /**
         * 编号
         */
        @TableId
        private String id;

        /**
         * 操作类型
         */
        private String logType;
        /**
         * 操作名称
         */
        private String logTypeName;
        /**
         * 对象编号
         */
        private String itemId;
        /**
         * 对象名称
         */
        private String itemName;

        /**
         * 栏目编号
         */
        private String columnId;

        /**
         * 栏目名称
         */
        private String columnName;

        /**
         * 操作人编号
         */
        private String userId;
        /**
         * 操作人姓名
         */
        private String userName;
        /**
         * 用户Ip地址
         */
        private String ip;
        /**
         * 参数（如查询的参数等）
         */
        private String param;
        @TableField(exist = false)
        private String systemType;

        @TableField(exist = false)
        private String simpleSearchKey;


        public static StdLog build(String _logType,String _logTypeName,String _userName, String _itemId,String _itemName,String _columnId,String _columnName,String _param, String _ip)
        {
                StdLog model=new StdLog();
                model.setUserName(_userName);
                model.setLogType(_logType);
                model.setLogTypeName(_logTypeName);
                model.setColumnId(_columnId);
                model.setColumnName(_columnName);
                model.setItemId(_itemId);
                model.setItemName(_itemName);
                model.setParam(_param);
                model.setIp(_ip);
                return model;
        }


    }

