package com.pearadmin.modules.standard.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.pearadmin.common.web.base.BaseDomain;
import lombok.Data;

/**
 * Describe: 标准分类/部门分类/岗位 接 口 实 体
 * Author: chongyb
 * CreateTime: 2023/1/26
 */
@Data
public class StdClassification extends BaseDomain {

        /**
         * 编号
         */
        @TableId
        private String id;

        /**
         * 分类名称
         */
        private String className;

        /**
         * 分类标识
         */
        private String classCode;

        /**
         * 所属体系
         */
        private String systemId;

        /**
         * 分类标识路径
         */
        private String classCodePath;

        /**
         * 节点树类型（0 树枝节点 包含有子节点，不能添加标准；1 叶子节点，只能添加标准，不能包含子节点）
         *   在标准体系中表示标准结构  0 树枝  1 树干
         *   在岗位专题中：0 部门    1 岗位
         */
        private String classType;

        /**
         * 父分类标识路径
         */
        private String parentFullCode;

        /**
         * 父分类名称
         */
        private String parentName;

        /**
         * 同一级别下排序（从小到大）
         */
        private int sortCondition;

        /**
         * 备注
         */
        private String remark;

        /**
         * 状态（1 正常 ，0 或其他值 异常）
         */
        private String status;
        /**
         * 父分类编号
         */
        private String parentId;

        /**
         * 父分类类型：-1 体系 0 树枝  1 树干
         */
        @TableField(exist = false)
        private String parentClassType;

        /**
         * 标准数量
         */
        private String standardNumber;

        /**
         * 排序字段(从大到小排列）
         */
        private Integer sortNumber;

    }

