package com.pearadmin.modules.standard.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.pearadmin.common.web.base.BaseDomain;
import lombok.Data;

/**
 * Describe: 岗位 （废弃模型，未使用）
 * Author: chongyb
 * CreateTime: 2023/4/26
 */
@Data
public class StdPost extends BaseDomain {

        /**
         * 编号
         */
        @TableId
        private String id;

        /**
         * 岗位名称
         */
        private String name;

        /**
         * 所属部门
         */
        private String deptId;

        /**
         *  岗位描述
         */
        private String desc;

        /**
         * 同一级别下排序（从小到大）
         */
        private Integer sort_condition;

        private String remark;

        /**
         * 父分类标识路径
         */
        private String parentFullCode;

        /**
         * 父分类名称
         */
        private String parentName;

        /**
         * 同一级别下排序（从小到大）
         */
        private int sortCondition;


        /**
         * 状态（1 正常 ，0 或其他值 异常）
         */
        private String status;
        /**
         * 父分类编号
         */
        private String parentId;

        /**
         * 父分类类型：-1 体系 0 树枝  1 树干
         */
        @TableField(exist = false)
        private String parentClassType;

        /**
         * 标准数量
         */
        private String standardNumber;

    }

