package com.pearadmin.modules.standard.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.pearadmin.common.web.base.BaseDomain;
import lombok.Data;
import lombok.var;

/**
 * Describe: 标准信息 接 口 实 体
 * Author: chongyb
 * CreateTime: 2022/12/2
 */
@Data
@TableName(value="std_info_of_system_view")
public class StdInfoOfSystem extends BaseDomain {

    /**
     * 编号
     */
    @TableId
    private String id;

    /**
     * 标准号
     */
    private String stdNo;

    /**
     * 标准名称（原文）
     */
    private String stdOrgName;

    /**
     * 标准中文名称
     */
    private String stdChineseName;

    /**
     * 标准英文名称
     */
    private String stdEnglishName;

    /**
     * 文本语言
     */
    private String stdLangugage;

    /**
     * 标准标识
     */
    private String stdIdentification;

    /**
     * 标准类别
     */
    private String stdClass;

    /**
     * 密级
     */
    private String securityClass;

    /**
     * 分类号
     */
    private String catetoryNo;

    /**
     * ICS号
     */
    private String stdIcs;

    /**
     * 发布部门
     */
    private String pubDept;

    /**
     * 发布日期
     */
    private String pubDate;

    /**
     * 实施日期
     */
    private String implementationDate;

    /**
     * 提出单位
     */
    private String advanceDept;
    /**
     * 起草单位
     */
    private String draftingUnit;
    /**
     * 起草人
     */
    private String drafter;
    /**
     * 主要内容
     */
    private String primaryCoverage;
    /**
     * 页数
     */
    private String pageCount;

    /**
     * 标准状态（现行、被替代）
     */
    private String stdStatus;

    /**
     * 代替标准号(老标准）
     */
    private String alternateStdNo;

    /**
     * 被替代的标准号（新标准）
     */
    private String supersededStdNo;

    /**
     * 标准年代
     */
    private String stdOcr;

    /**
     * 标准来源类型（1 国内标准 2 美国标准 3 国外标准）
     */
    private String stdType;

    /**
     * pdf文件名称
     */
    private String pdfFileName;

    /**
     * pdf对应的文件是否存在
     */
    private String pdfIsExists;

    /**
     * 备注
     */
    private String remark;
    /**
     * 使用标准类型排序
     * 1)GJB
     * 2)WJ
     * 3)Q/CNG
     * 4)GB
     * 5)其他标准
     */
    private Integer sortIdentifaction;
    /**
     * 使用标准号中的顺序号排序
     */
    private float sortSerialNo;
    /**
     * 使用标准号中顺序号后边的的字母排序
     */
    private String sortChar;
    /**
     * 使用标准号中顺序号后边的年份排序
     */
    private Integer sortYear;
    /**
     * 体系编号
     *
     * @param null
     * @return
     * @throws
     */
    private String systemId;
    /**
     * 分类标识路径
     *
     * @param null
     * @return
     * @throws
     */
    private String classCodePath;
    /**
     * 分类名称
     *
     * @param null
     * @return
     * @throws
     */
    private String className;
    /*
     * 标准是否存在 std_info ：1 std_info_null:0
     * */
    private Integer isReal;
    /**
     * std_class_join_standard 表的ID
     *
     * @param null
     * @return
     * @throws
     */
    private String joinId;
    @TableField(exist = false)
    private String simpleSearchKey;
    @TableField(exist = false)
    private String advSearchKey;


    public StdInfo convertToStdInfo()
    {
        var stdInfo=new StdInfo();
        stdInfo.setId(this.getId());
        stdInfo.setPdfIsExists(this.getPdfIsExists());
        stdInfo.setStdNo(this.getStdNo());
        stdInfo.setSortYear(this.getSortYear());
        stdInfo.setSortChar(this.getSortChar());
        stdInfo.setSortSerialNo(this.getSortSerialNo());
        stdInfo.setSortIdentifaction(this.getSortIdentifaction());
        stdInfo.setStdType(this.getStdType());
        stdInfo.setStdOcr(this.getStdOcr());
        stdInfo.setStdStatus(this.getStdStatus());
        stdInfo.setSupersededStdNo(this.getSupersededStdNo());
        stdInfo.setImplementationDate(this.getImplementationDate());
        stdInfo.setPubDept(this.getPubDept());
        stdInfo.setCatetoryNo(this.getCatetoryNo());
        stdInfo.setSecurityClass(this.getSecurityClass());
        stdInfo.setStdChineseName(this.getStdChineseName());
        stdInfo.setStdEnglishName(this.getStdEnglishName());
        stdInfo.setStdOrgName(this.getStdOrgName());
        stdInfo.setStdLangugage(this.getStdLangugage());
        stdInfo.setSystemId(this.getSystemId());
        stdInfo.setPubDate(this.getPubDate());
        stdInfo.setClassCodePath(this.getClassCodePath());
        stdInfo.setAdvanceDept(this.getAdvanceDept());
        stdInfo.setDraftingUnit(this.getDraftingUnit());
        stdInfo.setDrafter(this.getDrafter());
        stdInfo.setPrimaryCoverage(this.getPrimaryCoverage());
        stdInfo.setPageCount(this.getPageCount());
        stdInfo.setPdfFileName(this.getPdfFileName());
        stdInfo.setAlternateStdNo(this.getAlternateStdNo());
        stdInfo.setStdIdentification(this.getStdIdentification());
        stdInfo.setStdClass(this.getStdClass());
        stdInfo.setClassName(this.getClassName());
        stdInfo.setIsReal(this.getIsReal());
        return stdInfo;
    }


}
