package com.pearadmin.modules.standard.domain;
import com.baomidou.mybatisplus.annotation.TableId;
import com.pearadmin.common.web.base.BaseDomain;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Describe: 标准信息统计实体类
 * Author: chongyibo
 * CreateTime: 2025/7/8
 */
@Data
public class StdClassStatistics extends BaseDomain {
    /**
     * 编号
     */
    @TableId
    private String id;
    /**
     * 分类名称
     */
    private String stdClass;
    /**
     * 类型（china usa foreigin）
     */
    private String stdType;

    /** *
     * 数量统计
     */
    private Integer counts;


    public StdClassStatistics(String className,String typeName, Integer counts) {
        this.stdClass = className;
        this.stdType = typeName;
        this.counts = counts;
    }

    public StdClassStatistics() {

    }
}
