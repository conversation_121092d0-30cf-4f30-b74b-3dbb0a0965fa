package com.pearadmin.modules.standard.domain;

import com.pearadmin.common.tools.CResult;

import java.util.List;
/**
 *文件上传结果
 *
 * @param null
 * @return
 * @throws
 */
public class FileUploadResult<T> {
   private CResult<Boolean> result;
   private List<T> list;

    public CResult<Boolean> getResult() {
        return result;
    }

    public void setResult(CResult<Boolean> result) {
        this.result = result;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }
}
