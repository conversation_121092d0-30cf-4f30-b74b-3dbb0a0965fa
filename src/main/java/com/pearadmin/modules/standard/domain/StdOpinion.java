package com.pearadmin.modules.standard.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.pearadmin.common.web.base.BaseDomain;
import lombok.Data;

/**
 * Describe: 对标准的意见反馈  接 口 实 体
 * Author: chongyb
 * CreateTime: 2023/5/17
 */
@Data
public class StdOpinion extends BaseDomain {

        /**
         * 编号
         */
        @TableId
        private String id;

        /**
         * 标准ID
         */
        private String stdId;
        /**
         * 标准号
         */
        private String stdNo;
        /**
         * 标准名称
         */
        private String stdOrgName;
        /**
         * 章节号
         */
        private String chapterNo;

        /**
         * 意见或建议
         */
        private String suggestion;

        /**
         * 理由
         */
        private String reasion;

        /**
         * 提出人
         */
        private String questioner;

        @TableField(exist = false)
        private String simpleSearchKey;

    }

