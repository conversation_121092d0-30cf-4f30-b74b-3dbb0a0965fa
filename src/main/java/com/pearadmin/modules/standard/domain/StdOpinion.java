package com.pearadmin.modules.standard.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.pearadmin.common.web.base.BaseDomain;
import lombok.Data;

/**
 * Describe: 对标准的意见反馈  接 口 实 体
 * Author: chongyb
 * CreateTime: 2023/5/17
 */
@Data
public class StdOpinion extends BaseDomain {

        /**
         * 编号
         */
        @TableId
        public String id;

        /**
         * 标准ID
         */
        public String stdId;
        /**
         * 标准号
         */
        public String stdNo;
        /**
         * 标准名称
         */
        public String stdOrgName;
        /**
         * 章节号
         */
        public String chapterNo;

        /**
         * 意见或建议
         */
        public String suggestion;

        /**
         * 理由
         */
        public String reasion;

        /**
         * 提出人
         */
        public String questioner;

        @TableField(exist = false)
        public String simpleSearchKey;

        @TableField(exist = false)
        public String opinionTime;
    }

