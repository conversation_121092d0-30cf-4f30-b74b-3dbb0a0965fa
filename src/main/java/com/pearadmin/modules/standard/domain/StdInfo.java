package com.pearadmin.modules.standard.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.pearadmin.common.web.base.BaseDomain;
import lombok.Data;

import java.time.LocalDate;

/**
 * Describe: 标准信息 接 口 实 体
 * Author: chongyb
 * CreateTime: 2022/12/2
 */
@Data
public class StdInfo extends BaseDomain {

    /**
     * 编号
     */
    @TableId
    private String id;

    /**
     * 标准号
     */
    public String stdNo;

    /**
     * 标准名称（原文）
     */
    public String stdOrgName;

    /**
     * 标准中文名称
     */
    public String stdChineseName;

    /**
     * 标准英文名称
     */
    public String stdEnglishName;

    /**
     * 文本语言
     */
    public String stdLangugage;

    /**
     * 标准标识
     */
    public String stdIdentification;

    /**
     * 标准类别
     */
    public String stdClass;

    /**
     * 密级
     */
    public String securityClass;

    /**
     * 分类号
     */
    public String catetoryNo;

    /**
     * ICS号
     */
    public String stdIcs;

    /**
     * 发布部门
     */
    public String pubDept;

    /**
     * 发布日期
     */
    public String pubDate;

    /**
     * 实施日期
     */
    public String implementationDate;
    /**
     * 提出单位
     */
    public String advanceDept;
    /**
     * 起草单位
     */
    public String draftingUnit;
    /**
     * 起草人
     */
    public String drafter;
    /**
     * 主要内容
     */
    public String primaryCoverage;
    /**
     * 页数
     */
    public String pageCount;

    /**
     * 标准状态（现行、被替代）
     */
    public String stdStatus;

    /**
     * 代替标准号(老标准）
     */
    public String alternateStdNo;

    /**
     * 被替代的标准号（新标准）
     */
    public String supersededStdNo;

    /**
     * 标准年代
     */
    public String stdOcr;

    /**
     * 标准来源类型（1 国内标准 2 美国标准 3 国外标准）
     */
    public String stdType;

    /**
     * pdf文件名称
     */
    public String pdfFileName;

    /**
     * pdf对应的文件是否存在
     */
    public String pdfIsExists;

    /**
     * 备注
     */
    public String remark;
    /**
     * 使用标准类型排序
     * 1)GJB
     * 2)WJ
     * 3)Q/CNG
     * 4)GB
     * 5)其他标准
     */
    public Integer sortIdentifaction;
    /**
     * 使用标准号中的顺序号排序
     */
    public float sortSerialNo;
    /**
     * 使用标准号中顺序号后边的的字母排序
     */
    public String sortChar;
    /**
     * 排序数字序号,位置：标准号.排序数字序号
     */
    public Integer sortNumber;
    /**
     * 使用标准号中顺序号后边的年份排序
     */
    public Integer sortYear;
    @TableField(exist = false)
    private String simpleSearchKey;
    @TableField(exist = false)
    private String advSearchKey;
    @TableField(exist = false)
    private String systemId;
    @TableField(exist = false)
    private String classCodePath;
    @TableField(exist = false)
    private String className;
    @TableField(exist = false)
    private String isCollected;
    /**
     * 判断要下载哪些字段（一个字符序列，18个字段中要下载的为1，不下载的为0
     *
     * @param null
     * @return
     * @throws
     */
    @TableField(exist = false)
    private String chooseCol;
    /*
     * 评价时间
     * 根据是否评价取标准列表时用来对标准进行排序
     * 按照最新评价时间来获取数据
     *
     * @param null
     * @return
     * @throws
     */
    @TableField(exist = false)
    private String opinionCreateTime;
    /*
     * 简单查询用的identification条件
     */
    @TableField(exist = false)
    private String queryIdenti;
    /**
     *是否已经有了solr索引
     *
     * @param null
     * @return
     * @throws
     */
    public Integer hasSolrIndex;
}
