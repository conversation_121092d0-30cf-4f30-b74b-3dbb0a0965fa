package com.pearadmin.modules.standard.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.pearadmin.common.web.base.BaseDomain;
import lombok.Data;

/**
 * Describe: 标准体系的意见反馈  接 口 实 体
 * Author: chongyb
 * CreateTime: 2023/1/26
 */
@Data
public class StdSystemOpinion extends BaseDomain {

        /**
         * 编号
         */
        @TableId
        private String id;

        /**
         * 体系编号
         */
        private String systemId;

        /**
         * 体系名称
         */
        private String systemName;

        /**
         * 体系位置
         */
        private String classCodePath;
        /**
         * 节点名称
         */
        private String className;

        /**
         * 意见或建议
         */
        private String suggestion;

        /**
         * 理由
         */
        private String reasion;

        /**
         * 提出人
         */
        private String questioner;

    }

