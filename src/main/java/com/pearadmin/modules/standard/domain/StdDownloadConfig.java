package com.pearadmin.modules.standard.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 标准下载配置实体类
 *
 * <AUTHOR> Code
 * @date 2023/5/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("std_download_config")
public class StdDownloadConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置类型常量
     */
    public static final String CONFIG_TYPE_DOWNLOAD = "1"; // 下载配置
    public static final String CONFIG_TYPE_PREVIEW = "2";  // 预览配置

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 配置类型 (download_limit, preview_limit等)
     */
    @TableField("config_type")
    private String configType;

    /**
     * 配置值
     */
    @TableField("config_value")
    private String configValue;

    /**
     * 已下载次数
     */
    @TableField("download_count")
    private Integer downloadCount;

    /**
     * 开始时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 配置状态 (0-禁用, 1-启用)
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 下载次数（用户在该配置时间段内的下载次数）
     */
    @TableField("download_count")
    private Integer downloadCount;

    /**
     * 简单搜索关键字（用于前端搜索）
     */
    @TableField(exist = false)
    private String simpleSearchKey;

    /**
     * 下载限制数量（虚拟字段，用于前端显示）
     */
    @TableField(exist = false)
    private Integer downloadLimit;

    /**
     * 预览限制数量（虚拟字段，用于前端显示）
     */
    @TableField(exist = false)
    private Integer previewLimit;
}
