package com.pearadmin.modules.standard.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.tools.*;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.*;
import com.pearadmin.modules.standard.mapper.*;
import com.pearadmin.modules.standard.service.StdClassificationService;
import com.pearadmin.modules.standard.service.StdSystemService;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import io.swagger.annotations.Api;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.io.InputStream;
import java.io.OutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Describe:  岗位 控制器（数据库使用std_classification模型）
 * Author: 就 眠 仪 式
 * CreateTime: 2022/10/23
 */
@RestController
@Api(tags = {"岗位"})
@RequestMapping(ControllerConstant.API_POST_PREFIX)
public class StdPostController extends BaseController {
    private final String MODULE_PATH = "stdpost/";
    private final String MODULE_CLASS_TYPE="1";//1表示岗位 0 表示部门

    @Resource
    private StdClassificationService stdClassificationService;
    @Resource
    private StdSystemService stdSystemService;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private StringEncryptor stringEncryptor;
    //可使用总小时数
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;
    @Autowired
    private com.pearadmin.common.tools.UploadProcess process;
    @Resource
    private com.pearadmin.common.tools.Tool tool;
    /**
     * 检查使用许可
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    public Boolean security() throws IOException {
        Boolean isSecurity=false;
        if(checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        }
        else
        {
            isSecurity=true;
        }
        return isSecurity;
    }

    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("main")
    public ModelAndView main() {
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("data")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable data(StdClassification model, PageDomain pageDomain) throws ParseException, IOException {
        if (!security()) {
            return null;
        }
        var query = new LambdaQueryWrapper<StdClassification>();
        var systemModel = stdSystemService.getOne(new LambdaQueryWrapper<StdSystem>().eq(StdSystem::getSystemType, SystemTypeEnum.PostSubject).last("limit 1"));
        if (StringUtils.isNotBlank(model.getClassName()))
        {
            query.like(StdClassification::getClassName, model.getClassName());
        }
        query.eq(StdClassification::getStatus, StatusEnum.Normal);
        query.eq(StdClassification::getSystemId,systemModel.getId());
        query.eq(StdClassification::getClassType, ClassTypeEnum.Post);
        query.orderByDesc(StdClassification::getSortNumber);
        var page= stdClassificationService.page(new Page<StdClassification>(pageDomain.getPage(),pageDomain.getLimit()),query);
        return pageTable(page.getRecords(), page.getTotal());
    }

   /* @GetMapping("departments")
    public JSONArray departments()
    {
        var data=stdSystemService.getOne(new LambdaQueryWrapper<StdSystem>().eq(StdSystem::getSystemType, SystemTypeEnum.PostSubject).last("limit 1"));
        var systemId=data.getId();
        var result= new  JSONArray();
        JSONObject sys=new JSONObject();
        //sys.put("title",data.getName());
        sys.put("title","本单位");
        sys.put("classType","-1");
        sys.put("classPath","-1");
        sys.put("id",systemId);
        sys.put("spread",true);
        result.add(sys);
        var classNode= new  JSONArray();
       // sys.put("children",classes);
        var query=new LambdaQueryWrapper<StdClassification>();
        query.eq(StdClassification::getSystemId,systemId).eq(StdClassification::getStatus,StatusEnum.Normal).eq(StdClassification::getClassType,ClassTypeEnum.Department);
        var list=stdClassificationService.list(query);
        var tops=list.stream().filter(d->d.getClassCodePath().length()==1 && d.getParentFullCode().equals("-1")).collect(Collectors.toList());
        tops.sort(Comparator.comparing(StdClassification::getClassCode));
        for(var i=0;i<tops.size();i++)
        {
            var obj=new JSONObject();
            obj.put("title",tops.get(i).getClassName());
            obj.put("id",tops.get(i).getId());
            obj.put("classType",tops.get(i).getClassType());
            obj.put("classPath",tops.get(i).getClassCodePath());
            var subs=getByParentCode(tops.get(i).getClassCodePath(),list);
            obj.put("children",subs);
            obj.put("spread",true);
            classNode.add(obj);
        }
        sys.put("children",classNode);
        return result;
    }

    public JSONArray getByParentCode(String parentFullCode,List<StdClassification> all)
    {
        var result=new JSONArray();
        var list=all.stream().filter(d->d.getParentFullCode().equals(parentFullCode)).collect(Collectors.toList());
        for(var i=0;i<list.size();i++)
        {
            var classify=list.get(i);
            var obj=new JSONObject();
            obj.put("title",classify.getClassName());
            obj.put("id",classify.getId());
            obj.put("classType",classify.getClassType());
            obj.put("classPath",classify.getClassCodePath());
            var subs=getByParentCode(classify.getClassCodePath(),all);
            obj.put("children",subs);
            obj.put("spread",true);
            result.add(obj);
        }
        return result;
    }*/

    @GetMapping("list")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public List<StdClassification> list(StdClassification model, PageDomain pageDomain) throws IOException {
        if(!security())
        {
            return null;
        }
        var systemModel=stdSystemService.getOne(new LambdaQueryWrapper<StdSystem>().eq(StdSystem::getSystemType, SystemTypeEnum.PostSubject).last("limit 1"));
        var query=new LambdaQueryWrapper<StdClassification>();
        query.eq(StdClassification::getStatus, StatusEnum.Normal);
        query.eq(StdClassification::getSystemId,systemModel.getId());
        query.eq(StdClassification::getClassType, ClassTypeEnum.Post);  List<StdClassification> list = stdClassificationService.list(query);
        return list;
    }

    /**
     * Describe: 数据字典类型新增视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("add")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public ModelAndView add(Model model) {
        var data=stdSystemService.getOne(new LambdaQueryWrapper<StdSystem>().eq(StdSystem::getSystemType, SystemTypeEnum.PostSubject).last("limit 1"));
        model.addAttribute("system", data);
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("save")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result save(@RequestBody StdClassification model) throws IOException {
        var systemModel=stdSystemService.getOne(new LambdaQueryWrapper<StdSystem>().eq(StdSystem::getSystemType, SystemTypeEnum.PostSubject).last("limit 1"));
        model.setSystemId(systemModel.getId());
        model.setStatus(StatusEnum.Normal);
        fillClassCode(model);
        boolean result = stdClassificationService.save(model);
        stdClassificationService.statisticsLayerNumber(model.getSystemId());
        return decide(result);
    }

    /**
     * Describe: 修改分类名称接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("updateName")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result updateName(@RequestBody StdClassification model) throws IOException {
        var orgModel=stdClassificationService.getById(model.getId());
        boolean result = stdClassificationService.update(null, new LambdaUpdateWrapper<StdClassification>().set(StdClassification::getClassName,model.getClassName()).eq(StdClassification::getId,model.getId()));
        return decide(result);
    }

    public void fillClassCode(StdClassification model)
    {
        //无父节点,说明是体系下边的一节子节点
        if(model.getParentFullCode().equals("-1"))
        {
            //找兄弟节点
            var maxBrother = stdClassificationService.getOne(new LambdaQueryWrapper<StdClassification>().eq(StdClassification::getSystemId,model.getSystemId()).eq(StdClassification::getParentFullCode, -1).eq(StdClassification::getStatus, "1").orderByDesc(StdClassification::getClassCode).last("limit 1"));
            var brotherInt=(int)'A';
            char newCode = 'A';
            if(maxBrother!=null && StringUtils.isNotBlank(maxBrother.getId())) {
                brotherInt = (int) (maxBrother.getClassCode().charAt(0));
                newCode = (char) (brotherInt + 1);
            }
            model.setClassCode(String.valueOf(newCode));
            model.setClassCodePath(String.valueOf(newCode));
        }
        else
        {
            var query = new LambdaQueryWrapper<StdClassification>();
            query.eq(StdClassification::getSystemId,model.getSystemId());
            query.eq(StdClassification::getClassCodePath, model.getParentFullCode()).last("limit 1");
            var parent = stdClassificationService.getOne(query);
            var maxBrother = stdClassificationService.getOne(new LambdaQueryWrapper<StdClassification>().eq(StdClassification::getSystemId,model.getSystemId()).eq(StdClassification::getParentFullCode, model.getParentFullCode()).eq(StdClassification::getStatus, "1").orderByDesc(StdClassification::getClassCode).last("limit 1"));
            var brotherInt=(int)'A';
            char newCode = 'A';
            if(maxBrother!=null && StringUtils.isNotBlank(maxBrother.getId())) {
                brotherInt = (int) (maxBrother.getClassCode().charAt(0));
                newCode = (char) (brotherInt + 1);
            }
            model.setClassCode(String.valueOf(newCode));
            model.setClassCodePath(parent.getClassCodePath() + String.valueOf(newCode));
            model.setParentId(parent.getId());
        }
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("index")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView index(Model model) {
        return jumpPage(MODULE_PATH + "index");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("editofsys")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView editofsys(Model model) {
        var data=stdSystemService.getOne(new LambdaQueryWrapper<StdSystem>().eq(StdSystem::getSystemType, SystemTypeEnum.PostSubject).last("limit 1"));
        model.addAttribute("system", data);
       // var classes=treeOfSystem(data.getId());
        return jumpPage(MODULE_PATH + "editofsys");
    }

    @GetMapping("detailofsys")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detailofsys(Model model) {
        var data=stdSystemService.getOne(new LambdaQueryWrapper<StdSystem>().eq(StdSystem::getSystemType, SystemTypeEnum.PostSubject).last("limit 1"));
        model.addAttribute("system", data);
        // var classes=treeOfSystem(data.getId());
        return jumpPage(MODULE_PATH + "detailofsys");
    }


    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("edit")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView edit(Model model,String id) {
        var data=stdClassificationService.getById(id);
        model.addAttribute("model", data);
        return jumpPage(MODULE_PATH + "edit");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detail")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detail(Model model,String id) {
        var data=stdClassificationService.getById(id);
        model.addAttribute("model",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PutMapping("update")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result update(@RequestBody StdClassification model) throws IOException {
        var org=stdClassificationService.getById(model.getId());
        if(!org.getParentFullCode().equals(model.getParentFullCode()))
        {
            fillClassCode(model);
        }
        boolean result = stdClassificationService.updateById(model);
        return decide(result);
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("delete")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result delete(@RequestBody StdClassification model) throws IOException {
        Boolean result = stdClassificationService.removeById(model.getId());
        stdClassificationService.statisticsLayerNumber(model.getSystemId());
        return decide(result);
    }

    /**
     * Describe: 删除岗位（status位置0）
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("softDelete")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result softDelete(@RequestParam(name="id",required = true) String id) throws IOException {
        var res=true;
        var data=stdClassificationService.getById(id);
        if(data!=null)
        {
            data.setStatus(StatusEnum.Delete);
            res= stdClassificationService.updateById(data);
        }
        return decide(res);
    }



    /**
     * Describe: 数据字典删除
     * Param: sysDictType
     * Return: ModelAndView
     */
    @DeleteMapping("remove/{id}")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result remove(@PathVariable("id") String id) throws IOException {
        var model=stdClassificationService.getById(id);
        Boolean result = stdClassificationService.removeById(id);
        stdClassificationService.statisticsLayerNumber(model.getSystemId());
        return decide(result);
    }

    /**
     * Describe: 岗位标准检索
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("stdofpost")
    public ModelAndView stdofpost(Model model) {
        var systemModel=stdSystemService.getOne(new LambdaQueryWrapper<StdSystem>().eq(StdSystem::getSystemType, SystemTypeEnum.PostSubject).last("limit 1"));
        model.addAttribute("system", systemModel);
        return jumpPage(MODULE_PATH + "index");
    }

    /**
     * 导入岗位Excel数据
     */
    @PostMapping("file-upload")
    public CResult<?> importExcel(HttpServletRequest req, HttpServletResponse response) throws Exception {
        var result=new CResult<Boolean>();
        result.setResult(true);
        result.setSuccess(true);
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) req;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        var customSessionId=req.getParameter("customSessionId");
        try {
            var uploadResult= stdSystemService.uploadPost(file.getInputStream(), customSessionId);
            if(!uploadResult.isSuccess())
            {
                result.setResult(false);
                result.setSuccess(false);
                var fileName=tool.savelErrToExcel(uploadResult.getMsg());
                result.setMessage(fileName);
            }
            else
            {
                result.setMessage(uploadResult.getMsg());
            }
        }
        catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
            result.setResult(false);
            result.setSuccess(false);
            result.setMessage("上传失败，"+ex.getMessage());
            return result;
        }
        return result;
    }
}
