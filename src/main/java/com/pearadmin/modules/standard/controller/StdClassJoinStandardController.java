package com.pearadmin.modules.standard.controller;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mysql.cj.ServerPreparedQueryTestcaseGenerator;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.*;
import com.pearadmin.modules.standard.service.*;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import io.swagger.annotations.Api;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.jasypt.encryption.StringEncryptor;
import org.quartz.SimpleTrigger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.parameters.P;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Describe:  标准与分类的关联关系 控制器
 * Author: 就 眠 仪 式
 * CreateTime: 2022/10/23
 */
@RestController
@Api(tags = {"标准与分类的关联关系"})
@RequestMapping(ControllerConstant.API_CLASS_JOIN_STANDARD_PREFIX)
public class StdClassJoinStandardController extends BaseController {
    private final String MODULE_PATH = "stdsysstd/";
    @Resource
    private StdClassJoinStandardService stdClassJoinStandardService;
    @Resource
    private StdSystemService stdSystemService;
    @Resource
    private StdClassificationService stdClassificationService;
    @Resource
    private StdInfoService stdInfoService;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private StringEncryptor stringEncryptor;
    //可使用总小时数
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;

    /**
     * 检查使用许可
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    public Boolean security() throws IOException {
        Boolean isSecurity=false;
        if(checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        }
        else
        {
            isSecurity=true;
        }
        return isSecurity;
    }

    /**
     * Describe: 明细表
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("main")
    public ModelAndView main() {
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 维护体系的明细表
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("mainofsys")
    public ModelAndView mainofsys(Model model,String sysId) {
        var data=stdSystemService.getById(sysId);
        model.addAttribute("system", data);
        return jumpPage(MODULE_PATH + "sysmain");
    }

    /**
     * Describe: 维护岗位的明细表
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("mainofpost")
    public ModelAndView mainofpost(Model model,String sysId,String postId) {
        var data=stdSystemService.getById(sysId);
        model.addAttribute("system", data);
        if(StringUtils.isNotBlank(postId)) {
            var post = stdClassificationService.getById(postId);
            model.addAttribute("post", post);
        }
        else
        {
            model.addAttribute("post", new StdPost());
        }
        return jumpPage(MODULE_PATH + "postmain");
    }

    /**
     * Describe: 查看体系的明细表
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("detailofsys")
    public ModelAndView detailofsys(Model model,String sysId) {
        var data=stdSystemService.getById(sysId);
        model.addAttribute("system", data);
        return jumpPage(MODULE_PATH + "sysdetail");
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("data")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable data(StdClassJoinStandard model, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
       {
           return null;
       }
        //拼凑标准检索的条件
        var query=new QueryWrapper<StdInfoOfSystem>();
        query.eq(StringUtils.isNotBlank(model.getSystemId()),"system_id",model.getSystemId());
        query.likeRight(StringUtils.isNotBlank(model.getClassCodePath()),"class_code_path",model.getClassCodePath());
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getSimpleSearchKey()))
        {
            var keys=model.getSimpleSearchKey();
            while(keys.contains("  "))
            {
                keys=keys.replaceFirst("  "," ");
            }
            var keyArr=keys.split(" ");
            for(String key : keyArr)
            {
                query.and(foo->foo.like("aa.std_no",key).or().like("aa.std_org_Name",key));
            }
        }
        else if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getAdvSearchKey()))
        {
            var jsonArray= (JSONArray)JSONArray.parse(model.getAdvSearchKey());
            for(Object item :jsonArray)
            {
                var obj=(com.alibaba.fastjson.JSONObject)item;
                var logic=obj.getString("logic");
                var field=obj.getString("field");
                var val=obj.getString("val");
                var type=obj.getString("type");
                if("AND".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.eq("aa."+field, val);
                    }
                    else {
                        //模糊查询
                        query.like("aa."+field,val);
                    }
                }
                else if("OR".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.or(qr->qr.eq("aa."+field, val));
                    }
                    else {
                        //模糊查询
                        query.or(qr->qr.like("aa."+field, val));
                    }
                }
                else if("NOT".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.ne("aa."+field, val);
                    }
                    else {
                        //模糊查询
                        query.notLike("aa."+field,val);
                    }
                }
            }
        }
        query.orderByAsc("class_code_path","sort_serial_no");
        var page1= stdInfoService.pageBySystemInfo(new Page<StdInfoOfSystem>(pageDomain.getPage(),pageDomain.getLimit()),query);
        var result=new ArrayList<StdInfo>();
        if(page1.getRecords().size()>0) {
            var modelList = page1.getRecords();
            for (var i = 0; i < modelList.size(); i++) {
                result.add(modelList.get(i).convertToStdInfo());
            }
        }
        var pg= pageTable(stdInfoService.fillIsCollected(result), page1.getTotal());
        return pg;
/*
        var stdWhereSql=query.getCustomSqlSegment();
        var suffix="\\#\\{"+query.getParamAlias()+".paramNameValuePairs";
        var paramMap=query.getParamNameValuePairs();
        for (String key:paramMap.keySet()
             ) {
            var val=paramMap.get(key);
            stdWhereSql=stdWhereSql.replaceAll(suffix+"."+key+"\\}","'"+val+"'");
        }
        var infoFullSql="";
        if(StringUtils.isNotBlank(stdWhereSql))
        {
            infoFullSql="select id from std_info "+stdWhereSql;
        }
        System.out.println("stdInfoSql:"+infoFullSql);

        var joinQuery=new LambdaQueryWrapper<StdClassJoinStandard>();
        if(StringUtils.isNotBlank(model.getSystemId()))
        {
            joinQuery.eq(StdClassJoinStandard::getSystemId,model.getSystemId());
        }
        if(StringUtils.isNotBlank(infoFullSql))
        {
            joinQuery.inSql(StdClassJoinStandard::getStdId,infoFullSql);
        }
        var page= stdClassJoinStandardService.page(new Page<StdClassJoinStandard>(pageDomain.getPage(),pageDomain.getLimit()),joinQuery);
        if(page.getRecords().size()>0) {
           var stdIdList = page.getRecords().parallelStream().map(b ->b.getStdId()).collect(Collectors.toList());
           var stdList = stdInfoService.list(new LambdaQueryWrapper<StdInfo>().in(StdInfo::getId, stdIdList));
           var joinList=page.getRecords();
           for (var i = 0; i < stdList.size(); i++) {
               for(var j=0;j<joinList.size();j++)
               {
                   if(joinList.get(j).getStdId().equals(stdList.get(i).getId()))
                   {
                       joinList.get(j).setStdName(stdList.get(i).getStdOrgName());
                       joinList.get(j).setStdStatus(stdList.get(i).getStdStatus());
                   }
               }
           }
           page.setRecords(joinList);
       }
        var pg= pageTable(page.getRecords(), page.getTotal());
        return pg;
        */
    }

    @GetMapping("list")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public List<StdClassJoinStandard> list(StdClassJoinStandard model, PageDomain pageDomain) throws IOException {
        if(!security())
        {
            return null;
        }
        var query=new LambdaQueryWrapper<StdClassJoinStandard>();
        List<StdClassJoinStandard> list = stdClassJoinStandardService.list(query);
        return list;
    }

    /**
     * Describe: 数据字典类型新增视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("add")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public ModelAndView add(Model model,String sysId) {
        var sysModel=stdSystemService.getById(sysId);
        model.addAttribute("system",sysModel);
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * Describe: 数据字典类型新增视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("addPostStd")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public ModelAndView addPostStd(Model model,String sysId,String postId) {
        var sysModel=stdSystemService.getById(sysId);
        var post=stdClassificationService.getById(postId);
        model.addAttribute("system",sysModel);
        model.addAttribute("post",post);
        return jumpPage(MODULE_PATH + "addPostStd");
    }


    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("save")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result save(@RequestBody StdClassJoinStandard model) throws IOException {
        boolean result = stdClassJoinStandardService.save(model);
        //统计标准数量
        var total=stdClassJoinStandardService.getStandardNumberOfSystem(model.getSystemId());
        stdSystemService.update(new LambdaUpdateWrapper<StdSystem>().in(StdSystem::getId,model.getSystemId()).set(StdSystem::getStandardNumber,total));
        return decide(result);
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("addOrUpdate")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result addOrUpdate(@RequestBody Map<String,Object> params) throws IOException {
        var classId=params.get("classId").toString();
        var stdIds=params.get("stdIds").toString();
        var stdIdArr=stdIds.split(",");
        if(stdIdArr.length>0 && StringUtils.isNotBlank(classId)) {
            var classify = stdClassificationService.getById(classId);
            var system=stdSystemService.getById(classify.getSystemId());
            var stdInfoList = stdInfoService.list(new LambdaQueryWrapper<StdInfo>().in(StdInfo::getId, stdIdArr));
            var orgStdInClassList=stdClassJoinStandardService.list(new LambdaQueryWrapper<StdClassJoinStandard>().eq(StdClassJoinStandard::getSystemId,classify.getSystemId()).eq(StdClassJoinStandard::getClassCodePath,classify.getClassCodePath()));
            for (StdInfo info:stdInfoList
                 ) {
                if(orgStdInClassList.parallelStream().filter(d->d.getStdId().equals(info.getId())).collect(Collectors.toList()).size()==0)
                {
                    var model=new StdClassJoinStandard();
                    model.setStdStatus(info.getStdStatus());
                    model.setStdName(info.getStdOrgName());
                    model.setClassCode(classify.getClassCode());
                    model.setClassCodePath(classify.getClassCodePath());
                    model.setClassName(classify.getClassName());
                    model.setStdId(info.getId());
                    model.setStdNo(info.getStdNo());
                    model.setSystemId(classify.getSystemId());
                    model.setSystemName(system.getName());
                    stdClassJoinStandardService.save(model);
                }
            }
            //统计标准数量
            var total=stdClassJoinStandardService.getStandardNumberOfSystem(system.getId());
            stdSystemService.update(new LambdaUpdateWrapper<StdSystem>().in(StdSystem::getId,system.getId()).set(StdSystem::getStandardNumber,total));
        }
        return decide(true);
    }
    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("index")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView index(Model model) {
        return jumpPage(MODULE_PATH + "index");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("edit")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView edit(Model model,String id) {
        var data=stdClassJoinStandardService.getById(id);
        model.addAttribute("model", data);
        return jumpPage(MODULE_PATH + "edit");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detail")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detail(Model model,String id) {
        var data=stdClassJoinStandardService.getById(id);
        model.addAttribute("model",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PutMapping("update")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result update(@RequestBody StdClassJoinStandard model) throws IOException {
        boolean result = stdClassJoinStandardService.updateById(model);
        return decide(result);
    }

    /**
     * Describe: 数据字典删除
     * Param: sysDictType
     * Return: ModelAndView
     */
    @DeleteMapping("remove/{id}")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result remove(@PathVariable("id") String id) throws IOException {
        //更新体系下标准数量
        var join=stdClassJoinStandardService.getById(id);
        var systemId=join.getSystemId();
        Boolean result = stdClassJoinStandardService.removeById(id);
        var total=stdClassJoinStandardService.getStandardNumberOfSystem(systemId);
        stdSystemService.update(new LambdaUpdateWrapper<StdSystem>().in(StdSystem::getId,systemId).set(StdSystem::getStandardNumber,total));
        return decide(result);
    }

    /**
     * Describe: 数据字典删除
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("batchRemove")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result batchRemove(@RequestBody Map<String,String> data) throws IOException {
        if(data.containsKey("ids")  && data.containsKey("systemId"))
        {
            var systemId=data.get("systemId").toString();
            //标准编号
            var arr=data.get("ids").toString().split(",");
            if(arr.length>0)
            {
                var query=new LambdaQueryWrapper<StdClassJoinStandard>();
                if(data.containsKey("classId") && StringUtils.isNotBlank(data.get("classId")))
                {
                    var classId=data.get("classId").toString();
                    var classify=stdClassificationService.getById(classId);
                    query.eq(StdClassJoinStandard::getClassCodePath,classify.getClassCodePath());
                }
                else
                {
                    query.eq(StdClassJoinStandard::getSystemId,systemId);
                }
                query.in(StdClassJoinStandard::getStdId,Arrays.stream(arr).collect(Collectors.toList()));
                stdClassJoinStandardService.remove(query);
            }
            //更新体系下标准数量
            var total=stdClassJoinStandardService.getStandardNumberOfSystem(systemId);
            stdSystemService.update(new LambdaUpdateWrapper<StdSystem>().in(StdSystem::getId,systemId).set(StdSystem::getStandardNumber,total));
        }
        return decide(true);
    }

    /**
     * Describe: 获取分类下的标准列表
     * Param: classId
     * Return: Result
     */
    @GetMapping("getStandardsByClassId")
    public Result getStandardsByClassId(@RequestParam String classId) {
        try {
            if (StringUtils.isBlank(classId)) {
                return Result.failure("分类ID不能为空");
            }

            // 获取分类信息
            StdClassification classification = stdClassificationService.getById(classId);
            if (classification == null) {
                return Result.failure("分类不存在");
            }

            // 查询该分类下的所有标准
            LambdaQueryWrapper<StdClassJoinStandard> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StdClassJoinStandard::getClassCodePath, classification.getClassCodePath())
                       .eq(StdClassJoinStandard::getSystemId, classification.getSystemId());

            List<StdClassJoinStandard> joinList = stdClassJoinStandardService.list(queryWrapper);

            // 提取标准ID列表
            List<String> stdIds = joinList.stream()
                    .map(StdClassJoinStandard::getStdId)
                    .collect(Collectors.toList());

            if (stdIds.isEmpty()) {
                return Result.success(new ArrayList<>());
            }

            // 获取标准详细信息
            LambdaQueryWrapper<StdInfo> stdQueryWrapper = new LambdaQueryWrapper<>();
            stdQueryWrapper.in(StdInfo::getId, stdIds);
            List<StdInfo> standards = stdInfoService.list(stdQueryWrapper);

            return Result.success(standards);
        } catch (Exception e) {
            System.err.println("获取分类下标准列表失败: " + e.getMessage());
            e.printStackTrace();
            return Result.failure("获取分类下标准列表失败: " + e.getMessage());
        }
    }

}
