package com.pearadmin.modules.standard.controller;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mysql.cj.ServerPreparedQueryTestcaseGenerator;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.*;
import com.pearadmin.modules.standard.service.*;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import io.swagger.annotations.Api;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.jasypt.encryption.StringEncryptor;
import org.quartz.SimpleTrigger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.parameters.P;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Describe:  标准与分类的关联关系 控制器
 * Author: 就 眠 仪 式
 * CreateTime: 2022/10/23
 */
@RestController
@Api(tags = {"标准与分类的关联关系"})
@RequestMapping(ControllerConstant.API_CLASS_JOIN_STANDARD_PREFIX)
public class StdClassJoinStandardController extends BaseController {
    private final String MODULE_PATH = "stdsysstd/";
    @Resource
    private StdClassJoinStandardService stdClassJoinStandardService;
    @Resource
    private StdSystemService stdSystemService;
    @Resource
    private StdClassificationService stdClassificationService;
    @Resource
    private StdInfoService stdInfoService;
    @Resource
    private StdInfoNullService  stdInfoNullService;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private StringEncryptor stringEncryptor;
    //可使用总小时数
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;


    /**
     * 检查使用许可
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    public Boolean security() throws IOException {
        Boolean isSecurity=false;
        if(checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        }
        else
        {
            isSecurity=true;
        }
        return isSecurity;
    }

    /**
     * Describe: 明细表
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("main")
    public ModelAndView main() {
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 维护体系的明细表
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("mainofsys")
    public ModelAndView mainofsys(Model model,String sysId) {
        var data=stdSystemService.getById(sysId);
        model.addAttribute("system", data);
        return jumpPage(MODULE_PATH + "sysmain");
    }

    /**
     * Describe: 维护岗位的明细表
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("mainofpost")
    public ModelAndView mainofpost(Model model,String sysId,String postId) {
        var data=stdSystemService.getById(sysId);
        model.addAttribute("system", data);
        if(StringUtils.isNotBlank(postId)) {
            var post = stdClassificationService.getById(postId);
            model.addAttribute("post", post);
        }
        else
        {
            model.addAttribute("post", new StdPost());
        }
        return jumpPage(MODULE_PATH + "postmain");
    }

    /**
     * Describe: 查看体系的明细表
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("detailofsys")
    public ModelAndView detailofsys(Model model,String sysId) {
        var data=stdSystemService.getById(sysId);
        model.addAttribute("system", data);
        return jumpPage(MODULE_PATH + "sysdetail");
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("data")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable data(StdClassJoinStandard model, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
       {
           return null;
       }
        //拼凑标准检索的条件
        var query=new QueryWrapper<StdInfoOfSystem>();
        query.eq(StringUtils.isNotBlank(model.getSystemId()),"system_id",model.getSystemId());
        query.likeRight(StringUtils.isNotBlank(model.getClassCodePath()),"class_code_path",model.getClassCodePath());
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getSimpleSearchKey()))
        {
            var keys=model.getSimpleSearchKey();
            while(keys.contains("  "))
            {
                keys=keys.replaceFirst("  "," ");
            }
            var keyArr=keys.split(" ");
            for(String key : keyArr)
            {
                query.and(foo->foo.like("aa.std_no",key).or().like("aa.std_org_Name",key));
            }
        }
        else if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getAdvSearchKey()))
        {
            var jsonArray= (JSONArray)JSONArray.parse(model.getAdvSearchKey());
            for(Object item :jsonArray)
            {
                var obj=(com.alibaba.fastjson.JSONObject)item;
                var logic=obj.getString("logic");
                var field=obj.getString("field");
                var val=obj.getString("val");
                var type=obj.getString("type");
                if("AND".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.eq("aa."+field, val);
                    }
                    else {
                        //模糊查询
                        query.like("aa."+field,val);
                    }
                }
                else if("OR".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.or(qr->qr.eq("aa."+field, val));
                    }
                    else {
                        //模糊查询
                        query.or(qr->qr.like("aa."+field, val));
                    }
                }
                else if("NOT".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.ne("aa."+field, val);
                    }
                    else {
                        //模糊查询
                        query.notLike("aa."+field,val);
                    }
                }
            }
        }
        //query.orderByAsc("class_code_path","sort_serial_no");
        var page1= stdInfoService.pageBySystemInfo(new Page<StdInfoOfSystem>(pageDomain.getPage(),pageDomain.getLimit()),query,"order by class_code_path asc,class_code_path asc");
        var result=new ArrayList<StdInfo>();
        if(page1.getRecords().size()>0) {
            var modelList = page1.getRecords();
            for (var i = 0; i < modelList.size(); i++) {
                result.add(modelList.get(i).convertToStdInfo());
            }
        }
        var pg= pageTable(stdInfoService.fillIsCollected(result), page1.getTotal());
        return pg;
    }

    /**
     * Describe: 岗位标准列表（排序方式：按添加到岗位的日期时间倒序排列）
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("dataOfPost")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable dataOfPost(StdClassJoinStandard model, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return null;
        }
        //拼凑标准检索的条件
        var query=new QueryWrapper<StdInfoOfSystem>();
        query.eq(StringUtils.isNotBlank(model.getSystemId()),"system_id",model.getSystemId());
        query.likeRight(StringUtils.isNotBlank(model.getClassCodePath()),"class_code_path",model.getClassCodePath());
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getSimpleSearchKey()))
        {
            var keys=model.getSimpleSearchKey();
            while(keys.contains("  "))
            {
                keys=keys.replaceFirst("  "," ");
            }
            var keyArr=keys.split(" ");
            for(String key : keyArr)
            {
                query.and(foo->foo.like("aa.std_no",key).or().like("aa.std_org_Name",key));
            }
        }
        else if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getAdvSearchKey()))
        {
            var jsonArray= (JSONArray)JSONArray.parse(model.getAdvSearchKey());
            for(Object item :jsonArray)
            {
                var obj=(com.alibaba.fastjson.JSONObject)item;
                var logic=obj.getString("logic");
                var field=obj.getString("field");
                var val=obj.getString("val");
                var type=obj.getString("type");
                if("AND".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.eq("aa."+field, val);
                    }
                    else {
                        //模糊查询
                        query.like("aa."+field,val);
                    }
                }
                else if("OR".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.or(qr->qr.eq("aa."+field, val));
                    }
                    else {
                        //模糊查询
                        query.or(qr->qr.like("aa."+field, val));
                    }
                }
                else if("NOT".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.ne("aa."+field, val);
                    }
                    else {
                        //模糊查询
                        query.notLike("aa."+field,val);
                    }
                }
            }
        }
        //query.orderByDesc("bb.create_time");
        var page1= stdInfoService.pageBySystemInfo(new Page<StdInfoOfSystem>(pageDomain.getPage(),pageDomain.getLimit()),query,"order by join_create_time desc");
        var result=new ArrayList<StdInfo>();
        if(page1.getRecords().size()>0) {
            var modelList = page1.getRecords();
            for (var i = 0; i < modelList.size(); i++) {
                result.add(modelList.get(i).convertToStdInfo());
            }
        }
        var pg= pageTable(stdInfoService.fillIsCollected(result), page1.getTotal());
        return pg;
    }

    /**
     * Describe: 获取个人收藏标准列表
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("dataOfCollected")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable dataOfCollected(StdClassJoinStandard model, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return null;
        }
        //拼凑标准检索的条件
        var query=new QueryWrapper<StdInfoOfSystem>();
        query.eq(StringUtils.isNotBlank(model.getSystemId()),"system_id",model.getSystemId());
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getSimpleSearchKey()))
        {
            var keys=model.getSimpleSearchKey();
            while(keys.contains("  "))
            {
                keys=keys.replaceFirst("  "," ");
            }
            var keyArr=keys.split(" ");
            for(String key : keyArr)
            {
                query.and(foo->foo.like("aa.std_no",key).or().like("aa.std_org_Name",key));
            }
        }
        //query.orderByDesc("bb.create_time");
        var page1= stdInfoService.pageBySystemInfo(new Page<StdInfoOfSystem>(pageDomain.getPage(),pageDomain.getLimit()),query,"order by join_create_time desc");
        var result=new ArrayList<StdInfo>();
        if(page1.getRecords().size()>0) {
            var modelList = page1.getRecords();
            for (var i = 0; i < modelList.size(); i++) {
                result.add(modelList.get(i).convertToStdInfo());
            }
        }
        var pg= pageTable(stdInfoService.fillIsCollected(result), page1.getTotal());
        return pg;
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("dataformanage")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable dataformanage(StdClassJoinStandard model, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return null;
        }
        //拼凑标准检索的条件
        var query=new QueryWrapper<StdInfoOfSystem>();
        query.eq(StringUtils.isNotBlank(model.getSystemId()),"system_id",model.getSystemId());
        query.likeRight(StringUtils.isNotBlank(model.getClassCodePath()),"class_code_path",model.getClassCodePath());
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getSimpleSearchKey()))
        {
            var keys=model.getSimpleSearchKey();
            while(keys.contains("  "))
            {
                keys=keys.replaceFirst("  "," ");
            }
            var keyArr=keys.split(" ");
            for(String key : keyArr)
            {
                query.and(foo->foo.like("aa.std_no",key).or().like("aa.std_org_Name",key));
            }
        }
        else if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getAdvSearchKey()))
        {
            var jsonArray= (JSONArray)JSONArray.parse(model.getAdvSearchKey());
            for(Object item :jsonArray)
            {
                var obj=(com.alibaba.fastjson.JSONObject)item;
                var logic=obj.getString("logic");
                var field=obj.getString("field");
                var val=obj.getString("val");
                var type=obj.getString("type");
                if("AND".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.eq("aa."+field, val);
                    }
                    else {
                        //模糊查询
                        query.like("aa."+field,val);
                    }
                }
                else if("OR".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.or(qr->qr.eq("aa."+field, val));
                    }
                    else {
                        //模糊查询
                        query.or(qr->qr.like("aa."+field, val));
                    }
                }
                else if("NOT".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.ne("aa."+field, val);
                    }
                    else {
                        //模糊查询
                        query.notLike("aa."+field,val);
                    }
                }
            }
        }
        //query.orderByDesc("bb.update_time","bb.create_time");
        var page1= stdInfoService.pageBySystemInfo(new Page<StdInfoOfSystem>(pageDomain.getPage(),pageDomain.getLimit()),query," order by join_update_time desc, join_create_time desc");
        var result=new ArrayList<StdInfo>();
        if(page1.getRecords().size()>0) {
            var modelList = page1.getRecords();
            for (var i = 0; i < modelList.size(); i++) {
                result.add(modelList.get(i).convertToStdInfo());
            }
        }
        return pageTable(stdInfoService.fillIsCollected(result), page1.getTotal());
/*
        var stdWhereSql=query.getCustomSqlSegment();
        var suffix="\\#\\{"+query.getParamAlias()+".paramNameValuePairs";
        var paramMap=query.getParamNameValuePairs();
        for (String key:paramMap.keySet()
             ) {
            var val=paramMap.get(key);
            stdWhereSql=stdWhereSql.replaceAll(suffix+"."+key+"\\}","'"+val+"'");
        }
        var infoFullSql="";
        if(StringUtils.isNotBlank(stdWhereSql))
        {
            infoFullSql="select id from std_info "+stdWhereSql;
        }
        System.out.println("stdInfoSql:"+infoFullSql);

        var joinQuery=new LambdaQueryWrapper<StdClassJoinStandard>();
        if(StringUtils.isNotBlank(model.getSystemId()))
        {
            joinQuery.eq(StdClassJoinStandard::getSystemId,model.getSystemId());
        }
        if(StringUtils.isNotBlank(infoFullSql))
        {
            joinQuery.inSql(StdClassJoinStandard::getStdId,infoFullSql);
        }
        var page= stdClassJoinStandardService.page(new Page<StdClassJoinStandard>(pageDomain.getPage(),pageDomain.getLimit()),joinQuery);
        if(page.getRecords().size()>0) {
           var stdIdList = page.getRecords().parallelStream().map(b ->b.getStdId()).collect(Collectors.toList());
           var stdList = stdInfoService.list(new LambdaQueryWrapper<StdInfo>().in(StdInfo::getId, stdIdList));
           var joinList=page.getRecords();
           for (var i = 0; i < stdList.size(); i++) {
               for(var j=0;j<joinList.size();j++)
               {
                   if(joinList.get(j).getStdId().equals(stdList.get(i).getId()))
                   {
                       joinList.get(j).setStdName(stdList.get(i).getStdOrgName());
                       joinList.get(j).setStdStatus(stdList.get(i).getStdStatus());
                   }
               }
           }
           page.setRecords(joinList);
       }
        var pg= pageTable(page.getRecords(), page.getTotal());
        return pg;
        */
    }


    @GetMapping("list")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public List<StdClassJoinStandard> list(StdClassJoinStandard model, PageDomain pageDomain) throws IOException {
        if(!security())
        {
            return null;
        }
        var query=new LambdaQueryWrapper<StdClassJoinStandard>();
        List<StdClassJoinStandard> list = stdClassJoinStandardService.list(query);
        return list;
    }

    /**
     * Describe: 数据字典类型新增视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("add")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public ModelAndView add(Model model,String sysId) {
        var sysModel=stdSystemService.getById(sysId);
        model.addAttribute("system",sysModel);
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * Describe: 岗位标准添加页面
     * Param: sysId, postId
     * Return: ModelAndView
     */
    @GetMapping("addPostStd")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public ModelAndView addPostStd(Model model, String sysId, String postId) {
        try {
            // 获取体系信息
            StdSystem sysModel = null;
            if (StringUtils.isNotBlank(sysId)) {
                sysModel = stdSystemService.getById(sysId);
            }

            // 如果没有找到指定的体系，尝试获取默认的岗位体系
            if (sysModel == null) {
                sysModel = stdSystemService.getOne(
                    new LambdaQueryWrapper<StdSystem>()
                        .eq(StdSystem::getSystemType, "2") // 岗位标准化管理
                        .last("limit 1")
                );
            }

            // 如果还是没有找到，创建一个默认对象
            if (sysModel == null) {
                sysModel = new StdSystem();
                sysModel.setId("");
                sysModel.setName("默认岗位体系");
            }

            // 获取岗位信息
            StdClassification post = null;
            if (StringUtils.isNotBlank(postId)) {
                post = stdClassificationService.getById(postId);
            }

            // 如果没有找到岗位，创建一个默认对象
            if (post == null) {
                post = new StdClassification();
                post.setId("");
                post.setClassName("请选择岗位");
            }

            model.addAttribute("system", sysModel);
            model.addAttribute("post", post);

            return jumpPage(MODULE_PATH + "addPostStd");
        } catch (Exception e) {
            System.err.println("加载岗位标准添加页面失败: " + e.getMessage());
            e.printStackTrace();

            // 创建默认对象避免模板错误
            StdSystem defaultSystem = new StdSystem();
            defaultSystem.setId("");
            defaultSystem.setName("默认体系");

            StdClassification defaultPost = new StdClassification();
            defaultPost.setId("");
            defaultPost.setClassName("默认岗位");

            model.addAttribute("system", defaultSystem);
            model.addAttribute("post", defaultPost);

            return jumpPage(MODULE_PATH + "addPostStd");
        }
    }


    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("save")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result save(@RequestBody StdClassJoinStandard model) throws IOException {
        if(StringUtils.isBlank(model.getStdOrgName())) {
            model.setStdOrgName(getStdOrgName(model.getStdId()));
        }
        boolean result = stdClassJoinStandardService.save(model);
        //统计标准数量
        var total=stdClassJoinStandardService.getStandardNumberOfSystem(model.getSystemId());
        stdSystemService.update(new LambdaUpdateWrapper<StdSystem>().in(StdSystem::getId,model.getSystemId()).set(StdSystem::getStandardNumber,total));
        return decide(result);
    }

    /**
     * Describe: 从stdInfo 或 stdInfoNUll中获取标准名称（stdOrgName放到model的stdOrgName字段中
     * Param: stdInfoId
     * Return: String
     */
    private String getStdOrgName(String stdInfoId)
    {
        String stdOrgName="";
            var stdInfo=stdInfoService.getById(stdInfoId);
            if(stdInfo!=null)
            {
                stdOrgName=stdInfo.getStdOrgName();
            }
            else
            {
                //从stdInfoNull表中尝试获取标准名称
                var stdInfoNull=stdInfoNullService.getById(stdInfoId);
                if(stdInfoNull!=null)
                {
                    stdOrgName=stdInfoNull.getStdOrgName();
                }
            }
            return stdOrgName;
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("addOrUpdate")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result addOrUpdate(@RequestBody Map<String,Object> params) throws IOException {
        var classId=params.get("classId").toString();
        var stdIds=params.get("stdIds").toString();
        var stdIdArr=stdIds.split(",");
        if(stdIdArr.length>0 && StringUtils.isNotBlank(classId)) {
            var classify = stdClassificationService.getById(classId);
            var system=stdSystemService.getById(classify.getSystemId());
            var stdInfoList = stdInfoService.list(new LambdaQueryWrapper<StdInfo>().in(StdInfo::getId, stdIdArr));
            var orgStdInClassList=stdClassJoinStandardService.list(new LambdaQueryWrapper<StdClassJoinStandard>().eq(StdClassJoinStandard::getSystemId,classify.getSystemId()).eq(StdClassJoinStandard::getClassCodePath,classify.getClassCodePath()));
            for (StdInfo info:stdInfoList
                 ) {
                if(orgStdInClassList.parallelStream().filter(d->d.getStdId().equals(info.getId())).collect(Collectors.toList()).size()==0)
                {
                    var model=new StdClassJoinStandard();
                    model.setStdStatus(info.getStdStatus());
                    model.setStdOrgName(info.getStdOrgName());
                    model.setClassCode(classify.getClassCode());
                    model.setClassCodePath(classify.getClassCodePath());
                    model.setClassName(classify.getClassName());
                    model.setStdId(info.getId());
                    model.setStdNo(info.getStdNo());
                    model.setSystemId(classify.getSystemId());
                    model.setSystemName(system.getName());
                    stdClassJoinStandardService.save(model);
                }
            }
            //统计标准数量
            var total=stdClassJoinStandardService.getStandardNumberOfSystem(system.getId());
            stdSystemService.update(new LambdaUpdateWrapper<StdSystem>().in(StdSystem::getId,system.getId()).set(StdSystem::getStandardNumber,total));
        }
        return decide(true);
    }

    /**
     * Describe: 向体系中添加单个标准(StdInfoNull表)
     * Param: Map<String,Object>
     * Return: Result
     */
    @PostMapping("addSingleStandardToSystem")
    public Result addSingleStandardToSystem(@RequestBody Map<String,Object> params) throws IOException {
        try {

            String classId = params.get("classId").toString();
            String systemId = params.get("systemId").toString();
            String stdNo = params.get("stdNo").toString();
            String stdName = params.get("stdName").toString();

            // 创建新的标准记录
            StdInfoNull model = new StdInfoNull();
            model.setStdNo(stdNo);
            model.setStdOrgName(stdName);
            model.setStdStatus("空"); // 设置默认状态
            stdInfoNullService.save(model);

            // 获取分类信息
            StdClassification classify = stdClassificationService.getById(classId);
            if (classify == null) {
                return Result.failure("分类不存在");
            }

            // 获取系统信息
            StdSystem system = stdSystemService.getById(systemId);
            if (system == null) {
                return Result.failure("系统不存在");
            }

            // 创建关联记录
            StdClassJoinStandard joinStandard = new StdClassJoinStandard();
            joinStandard.setStdId(model.getId());
            joinStandard.setStdNo(stdNo);
            joinStandard.setStdOrgName(stdName);
            joinStandard.setStdStatus("");
            joinStandard.setClassCode(classify.getClassCode());
            joinStandard.setClassCodePath(classify.getClassCodePath());
            joinStandard.setClassName(classify.getClassName());
            joinStandard.setSystemId(systemId);
            joinStandard.setSystemName(system.getName());
            joinStandard.setStdExist(0);
            stdClassJoinStandardService.save(joinStandard);

            // 更新系统标准数量
            int total = stdClassJoinStandardService.getStandardNumberOfSystem(systemId);
            stdSystemService.update(new LambdaUpdateWrapper<StdSystem>()
                    .eq(StdSystem::getId, systemId)
                    .set(StdSystem::getStandardNumber, total));

            return Result.success("添加成功");
        } catch (Exception e) {
            return Result.failure("添加失败：" + e.getMessage());
        }
    }

    /**
     * Describe: 编辑体系下单个标准(StdInfoNull表)
     * Param: Map<String,Object>
     * Return: Result
     */
    @PostMapping("editSingleStandardOfSystem")
    public Result editSingleStandardOfSystem(@RequestBody Map<String,Object> params) throws IOException {
        try {
            String id = params.get("id").toString();
            String classId = params.get("classId").toString();
            String systemId = params.get("systemId").toString();
            String stdNo = params.get("stdNo").toString();
            String stdName = params.get("stdName").toString();
            String orgClassPath=params.get("orgClassPath").toString();
            // 获取分类信息
            StdClassification classify = stdClassificationService.getById(classId);
            if (classify == null) {
                return Result.failure("分类不存在");
            }

            // 获取系统信息
            StdSystem system = stdSystemService.getById(systemId);
            if (system == null) {
                return Result.failure("系统不存在");
            }

            // 更新标准记录
            StdInfoNull model = stdInfoNullService.getById(id);
            if (model == null) {
                return Result.failure("标准不存在");
            }
            //更新标准名称

            model.setStdNo(stdNo);
            model.setStdOrgName(stdName);
            stdInfoNullService.updateById(model);

            // 更新关联记录
            var joinQuqery=new LambdaQueryWrapper<StdClassJoinStandard>().eq(StdClassJoinStandard::getStdId,id).eq(StdClassJoinStandard::getSystemId,systemId).eq(StdClassJoinStandard::getClassCodePath,orgClassPath).last("limit 1");
            StdClassJoinStandard joinStandard = stdClassJoinStandardService.getOne(joinQuqery);
            if (joinStandard == null) {
                return Result.failure("标准关联记录不存在");
            }
            joinStandard.setStdNo(stdNo);
            joinStandard.setStdName(stdName);
            joinStandard.setClassCode(classify.getClassCode());
            joinStandard.setClassCodePath(classify.getClassCodePath());
            joinStandard.setClassName(classify.getClassName());
            joinStandard.setSystemId(systemId);
            joinStandard.setSystemName(system.getName());
            joinStandard.setStdExist(0);
            stdClassJoinStandardService.updateById(joinStandard);

            return Result.success("编辑成功");
        } catch (Exception e) {
            return Result.failure("编辑失败：" + e.getMessage());
        }
    }

    /**
     * Describe: 向岗位中添加单个标准(StdInfoNull表)
     * Param: Map<String,Object>
     * Return: Result
     */
    @PostMapping("addSingleStandardToPost")
    public Result addSingleStandardToPost(@RequestBody Map<String,Object> params) throws IOException {
        try {
            String classId = params.get("classId").toString();
            String systemId = params.get("systemId").toString();
            String stdNo = params.get("stdNo").toString();
            String stdName = params.get("stdName").toString();

            // 创建新的标准记录
            StdInfoNull model = new StdInfoNull();
            model.setStdNo(stdNo);
            model.setStdOrgName(stdName);
            model.setStdStatus("空"); // 设置默认状态
            stdInfoNullService.save(model);

            // 获取岗位信息
            StdClassification classify = stdClassificationService.getById(classId);
            if (classify == null) {
                return Result.failure("岗位不存在");
            }

            // 获取系统信息
            StdSystem system = stdSystemService.getById(systemId);
            if (system == null) {
                return Result.failure("系统信息异常");
            }

            // 创建关联记录
            StdClassJoinStandard joinStandard = new StdClassJoinStandard();
            joinStandard.setStdId(model.getId());
            joinStandard.setStdNo(stdNo);
            joinStandard.setStdName(stdName);
            joinStandard.setStdStatus("");
            joinStandard.setClassCode(classify.getClassCode());
            joinStandard.setClassCodePath(classify.getClassCodePath());
            joinStandard.setClassName(classify.getClassName());
            joinStandard.setSystemId(systemId);
            joinStandard.setSystemName(system.getName());
            joinStandard.setStdExist(0);
            stdClassJoinStandardService.save(joinStandard);

            // 更新岗位标准数量
//            int total = stdClassJoinStandardService.getStandardNumberOfSystem(systemId);
//            stdSystemService.update(new LambdaUpdateWrapper<StdSystem>()
//                    .eq(StdSystem::getId, systemId)
//                    .set(StdSystem::getStandardNumber, total));

            return Result.success("添加成功");
        } catch (Exception e) {
            return Result.failure("添加失败：" + e.getMessage());
        }
    }

    /**
     * Describe: 编辑岗位下标准(StdInfoNull表)
     * Param: Map<String,Object>
     * Return: Result
     */
    @PostMapping("editSingleStandardOfPost")
    public Result editSingleStandardOfPost(@RequestBody Map<String,Object> params) throws IOException {
        try {
            String id = params.get("id").toString();
            String classId = params.get("classId").toString();
            String systemId = params.get("systemId").toString();
            String stdNo = params.get("stdNo").toString();
            String stdName = params.get("stdName").toString();
            String orgClassPath=params.get("orgClassPath").toString();
            // 获取岗位信息
            StdClassification classify = stdClassificationService.getById(classId);
            if (classify == null) {
                return Result.failure("岗位不存在");
            }

            // 获取系统信息
            StdSystem system = stdSystemService.getById(systemId);
            if (system == null) {
                return Result.failure("系统信息异常");
            }

            // 更新标准记录
            StdInfoNull model = stdInfoNullService.getById(id);
            if (model == null) {
                return Result.failure("标准不存在");
            }
            model.setStdNo(stdNo);
            model.setStdOrgName(stdName);
            stdInfoNullService.updateById(model);

            // 更新关联记录
            var joinQuqery=new LambdaQueryWrapper<StdClassJoinStandard>().eq(StdClassJoinStandard::getStdId,id).eq(StdClassJoinStandard::getSystemId,systemId).eq(StdClassJoinStandard::getClassCodePath,orgClassPath).last("limit 1");
            StdClassJoinStandard joinStandard = stdClassJoinStandardService.getOne(joinQuqery);
            if (joinStandard == null) {
                return Result.failure("标准关联记录不存在");
            }
            joinStandard.setStdNo(stdNo);
            joinStandard.setStdName(stdName);
            joinStandard.setClassCode(classify.getClassCode());
            joinStandard.setClassCodePath(classify.getClassCodePath());
            joinStandard.setClassName(classify.getClassName());
            joinStandard.setSystemId(systemId);
            joinStandard.setSystemName(system.getName());
            joinStandard.setStdExist(0);
            stdClassJoinStandardService.updateById(joinStandard);

            return Result.success("编辑成功");
        } catch (Exception e) {
            return Result.failure("编辑失败：" + e.getMessage());
        }
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("index")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView index(Model model) {
        return jumpPage(MODULE_PATH + "index");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("edit")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView edit(Model model,String id) {
        var data=stdClassJoinStandardService.getById(id);
        model.addAttribute("model", data);
        return jumpPage(MODULE_PATH + "edit");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detail")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detail(Model model,String id) {
        var data=stdClassJoinStandardService.getById(id);
        model.addAttribute("model",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PutMapping("update")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result update(@RequestBody StdClassJoinStandard model) throws IOException {
        boolean result = stdClassJoinStandardService.updateById(model);
        return decide(result);
    }

    /**
     * Describe: 数据字典删除
     * Param: sysDictType
     * Return: ModelAndView
     */
    @DeleteMapping("remove/{id}")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result remove(@PathVariable("id") String id) throws IOException {
        //更新体系下标准数量
        var join=stdClassJoinStandardService.getById(id);
        var systemId=join.getSystemId();
        Boolean result = stdClassJoinStandardService.removeById(id);
        var total=stdClassJoinStandardService.getStandardNumberOfSystem(systemId);
        stdSystemService.update(new LambdaUpdateWrapper<StdSystem>().in(StdSystem::getId,systemId).set(StdSystem::getStandardNumber,total));
        return decide(result);
    }

    /**
     * Describe: 数据字典删除
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("batchRemove")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result batchRemove(@RequestBody Map<String,String> data) throws IOException {
        if(data.containsKey("ids")  && data.containsKey("systemId"))
        {
            var systemId=data.get("systemId").toString();
            //标准编号
            var arr=data.get("ids").toString().split(",");
            if(arr.length>0)
            {
                var query=new LambdaQueryWrapper<StdClassJoinStandard>();
                if(data.containsKey("classId") && StringUtils.isNotBlank(data.get("classId")) && !data.get("classId").equals("-1"))
                {
                    var classId=data.get("classId").toString();
                    var classify=stdClassificationService.getById(classId);
                    query.eq(StdClassJoinStandard::getClassCodePath,classify.getClassCodePath());
                }
                else
                {
                    query.eq(StdClassJoinStandard::getSystemId,systemId);
                }
                query.in(StdClassJoinStandard::getStdId,Arrays.stream(arr).collect(Collectors.toList()));
                stdClassJoinStandardService.remove(query);
            }
            //更新体系下标准数量
            var total=stdClassJoinStandardService.getStandardNumberOfSystem(systemId);
            stdSystemService.update(new LambdaUpdateWrapper<StdSystem>().in(StdSystem::getId,systemId).set(StdSystem::getStandardNumber,total));
        }
        return decide(true);
    }

    /**
     * Describe: 获取分类下的标准列表
     * Param: classId
     * Return: Result
     */
    @GetMapping("getStandardsByClassId")
    public Result getStandardsByClassId(@RequestParam String classId) {
        try {
            if (StringUtils.isBlank(classId)) {
                return Result.failure("分类ID不能为空");
            }

            // 获取分类信息
            StdClassification classification = stdClassificationService.getById(classId);
            if (classification == null) {
                return Result.failure("分类不存在");
            }

            // 查询该分类下的所有标准
            LambdaQueryWrapper<StdClassJoinStandard> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StdClassJoinStandard::getClassCodePath, classification.getClassCodePath())
                       .eq(StdClassJoinStandard::getSystemId, classification.getSystemId());

            List<StdClassJoinStandard> joinList = stdClassJoinStandardService.list(queryWrapper);

            // 提取标准ID列表
            List<String> stdIds = joinList.stream()
                    .map(StdClassJoinStandard::getStdId)
                    .collect(Collectors.toList());

            if (stdIds.isEmpty()) {
                return Result.success(new ArrayList<>());
            }

            // 获取标准详细信息
            LambdaQueryWrapper<StdInfo> stdQueryWrapper = new LambdaQueryWrapper<>();
            stdQueryWrapper.in(StdInfo::getId, stdIds);
            List<StdInfo> standards = stdInfoService.list(stdQueryWrapper);

            return Result.success(standards);
        } catch (Exception e) {
            System.err.println("获取分类下标准列表失败: " + e.getMessage());
            e.printStackTrace();
            return Result.failure("获取分类下标准列表失败: " + e.getMessage());
        }
    }

    /**
     * Describe: 获取岗位下的标准列表
     * Param: postId
     * Return: Result
     */
    @GetMapping("getStandardsByPostId")
    public Result getStandardsByPostId(@RequestParam String postId) {
        try {
            if (StringUtils.isBlank(postId)) {
                return Result.failure("岗位ID不能为空");
            }

            // 获取岗位信息
            StdClassification post = stdClassificationService.getById(postId);
            if (post == null) {
                return Result.failure("岗位不存在");
            }

            // 查询该岗位下的所有标准
            LambdaQueryWrapper<StdClassJoinStandard> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StdClassJoinStandard::getClassCodePath, post.getClassCodePath())
                       .eq(StdClassJoinStandard::getSystemId, post.getSystemId());

            List<StdClassJoinStandard> joinList = stdClassJoinStandardService.list(queryWrapper);

            // 提取标准ID列表
            List<String> stdIds = joinList.stream()
                    .map(StdClassJoinStandard::getStdId)
                    .collect(Collectors.toList());

            if (stdIds.isEmpty()) {
                return Result.success(new ArrayList<>());
            }

            // 获取标准详细信息
            LambdaQueryWrapper<StdInfo> stdQueryWrapper = new LambdaQueryWrapper<>();
            stdQueryWrapper.in(StdInfo::getId, stdIds);
            List<StdInfo> standards = stdInfoService.list(stdQueryWrapper);

            return Result.success(standards);
        } catch (Exception e) {
            System.err.println("获取岗位下标准列表失败: " + e.getMessage());
            e.printStackTrace();
            return Result.failure("获取岗位下标准列表失败: " + e.getMessage());
        }
    }

}
