package com.pearadmin.modules.standard.controller;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pearadmin.common.aop.annotation.Log;
import com.pearadmin.common.aop.enums.BusinessType;
import com.pearadmin.common.constant.ServletConstant;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.tools.*;
import com.pearadmin.common.tools.string.StringUtil;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.*;
import com.pearadmin.modules.standard.mapper.StdClassStatisticsMapper;
import com.pearadmin.modules.standard.mapper.StdInfoMapper;
import com.pearadmin.modules.standard.service.ChongQueryWrapper;
import com.pearadmin.modules.standard.service.StdInfoService;
import com.pearadmin.modules.standard.service.StdLogService;
import com.pearadmin.modules.sys.domain.SysPower;
import com.pearadmin.modules.sys.domain.SysUser;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import com.pearadmin.modules.sys.mapper.SysPowerMapper;
import com.pearadmin.modules.sys.service.SysPowerService;
import io.swagger.annotations.ApiOperation;
import lombok.var;
import org.apache.commons.io.FileUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.HttpSolrClient;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.client.solrj.response.UpdateResponse;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.SolrDocumentList;
import org.apache.solr.common.SolrInputDocument;
import org.apache.solr.common.params.DisMaxParams;
import org.apache.solr.common.util.NamedList;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

import org.apache.commons.io.FileUtils;

@RestController
@RequestMapping("/chinastd")
public class ChinaStdController  extends BaseController {
    private final String MODULE_PATH = "stdchina/";
    @Value(value="${pdf.source.china-path}")
    private String pdfPath;
    @Value(value="${pdf.encryptSource.path}")
    private String encryptPdfPath;
    @Autowired
    private StdInfoService stdInfoService;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private StringEncryptor stringEncryptor;
    //可使用总小时数
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;
    @Resource
    private StdInfoMapper stdInfoMapper;
    @Autowired
    private com.pearadmin.common.tools.UploadProcess process;
    @Autowired
    private StdLogService stdLogService;
    @Resource
    private SysPowerMapper sysPowerMapper;
    @Resource
    private StdClassStatisticsMapper stdClassStatisticsMapper;
    /**
     * 检查使用许可
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    public Boolean security() throws IOException {
        Boolean isSecurity=false;
//        var a=stringEncryptor.decrypt("x3r8Ujzo3T3SlHm/Yg5ytnBzFOevZdLAFeggqWaIV6NITctUoiYeЗOhgRL0E7QAH");
//        System.out.println(a);
        if(checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (org.apache.commons.lang.StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        }
        else
        {
            isSecurity=true;
        }
        return isSecurity;
    }
    @GetMapping("menuData")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    @Log(title = "著录项检索菜单", describe = "获取著录项检索菜单列表", type = BusinessType.STD_Data)
    public ResultTable getMenuList() throws ParseException, IOException {
        if (!security()) {
            return new ResultTable();
        }
        var result=new ResultTable();
        var query = new LambdaQueryWrapper<SysPower>();
        query.like(SysPower::getPowerUrl, "%myIndex");
        var list= sysPowerMapper.selectList(query);
        result.setData(list);
        result.setCode(0);
        return result;
    }


    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("main")
    public ModelAndView main() {
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("myIndex")
    public ModelAndView index() {
        return jumpPage(MODULE_PATH + "index");
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("data")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    @Log(title = "国内标准", describe = "获取数据列表", type = BusinessType.STD_Data)
    public ResultTable simpleQuery(StdInfo stdInfo, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return new ResultTable();
        }
        try{
        //存放非全文的查询条件
        var query=new ChongQueryWrapper<StdInfo>();
        //存放排序条件
        var sortQuery=new QueryWrapper<StdInfo>();
       // query.eq("std_type", StandardTypeEnum.China);
        StringBuilder stdNo= new StringBuilder();
        StringBuilder stdOrgName= new StringBuilder();
        String stdChineseName=null;
        String stdEnglishName=null;
        String primaryCoverage=null;
        String pubDept=null;
        String advanceDept=null;
        String draftingUnit=null;
        String drafter=null;
        String sortSql="";

        if(StringUtils.isNotBlank(stdInfo.getAdvSearchKey()))
        {
            query.eq("std_type", StandardTypeEnum.China);
            var jsonArray= (JSONArray)JSONArray.parse(stdInfo.getAdvSearchKey());
            for(Object item :jsonArray) {
                var obj = (com.alibaba.fastjson.JSONObject) item;
                var logic = obj.getString("logic");
                var field = obj.getString("field");
                var valOrg = obj.getString("val");
                var val=SqlUtils.handleUserInput(valOrg);
                var type = obj.getString("type");
                if(StringUtils.isNotBlank(val))
                {
                if ("AND".equals(logic)) {
                    //精确查找
                    if ("exact".equals(type)) {
                        query.eq(field, val);
                    } else {
                        //模糊查询
                        query.like(field, val);
                    }
                } else if ("OR".equals(logic)) {
                    //精确查找
                    if ("exact".equals(type)) {
                        query.or(qr -> qr.eq(field, val));
                    } else {
                        //模糊查询
                        query.or(qr -> qr.like(field, val));
                    }
                } else if ("NOT".equals(logic)) {
                    //精确查找
                    if ("exact".equals(type)) {
                        query.ne(field, val);
                    } else {
                        //模糊查询
                        query.notLike(field, val);
                    }
                }
                //排序条件------------------------------
                //精确查找
                if ("exact".equals(type)) {
                    if (field.trim().equals("std_no")) {
                        sortSql += " when std_no = '" + val + "' then 10 ";
                    }
                } else {
                    //模糊查询
                    if (field.trim().equals("std_no")) {
                        if (StringUtil.isNumeric(val)) {
                            sortSql += " when sort_serial_no='" + val + "' then 10 when sort_serial_no like '" + val + "%' then 5 ";
                        }
//                        else {
//                            if(!identityList.contains(val.toUpperCase())) {
//                                sortSql += " when std_no='"+ val+"' then 2 when std_no like '" + val + "%' then 1 ";
//                            }
//                          //  sortSql += " when std_no='"+ val+"' then 2 when std_no like '" + val + "%' then 1 ";
//                        }
                    }
                }
            }
            }
            DealIdnetity(stdInfo,query);
            if(StringUtils.isNotBlank(sortSql)) {
                sortQuery.orderBy(true,false, "CASE "+sortSql+" ELSE 0 END");
            }else
            {
                sortQuery.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_year");
            }
            query.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_year");
            var page= stdInfoMapper.selectPage(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
            //获取可以下载的标准的数量
            query.eq("pdf_is_exists","1");
            var pdfCanDownloads=stdInfoService.count(query);
            var pg= pageTable(stdInfoService.fillIsCollected(page.getRecords()), page.getTotal());
            pg.setExtData(pdfCanDownloads);
            return pg;
        }
        else {
            StringBuilder fulltextSql = new StringBuilder();
            if (StringUtils.isNotBlank(stdInfo.getSimpleSearchKey())) {
                var keys = stdInfo.getSimpleSearchKey();
                while (keys.contains("  ")) {
                    keys = keys.replaceFirst("  ", " ");
                }
                var keyArr = keys.split(" ");
                //拼凑stdNo的全文检索条件，每个关键字后要带上通配符*，可以匹配到更多标准编号
                for (String item : keyArr) {
                    String key = SqlUtils.handleUserInput(item);
                    if (StringUtils.isNotBlank(key)) {
//                        if(key.equals("/") || key.equals("-") || key.equals("."))
//                        {
//                            continue;
//                        }
                        //用"或"的方式在标准号和标准名称中进行全文检索
                        //  fulltextSql.append((fulltextSql.length()>0?" AND ":"")+ String.format(" (MATCH(std_no) AGAINST ('%s*' IN BOOLEAN MODE) or MATCH(std_org_name) AGAINST ('%s' IN BOOLEAN MODE))",key,key));
                      /*  stdOrgName.append(" ").append(key);
                        stdNo.append(" ").append(key).append("*");*/
                        //如果key包含中文内容，则赋值给stdOrgName
                        //1.先判断是否以@符号开始，如果是则直接搜索中文名称字段
                        if (item.startsWith("@")) {
                            if (SqlUtils.needLike(key)) {
                                fulltextSql.append(fulltextSql.length() > 0 ? " AND " : "").append(String.format(" std_org_name like '%%%s%%'", key.substring(1)));
                            } else {
                                fulltextSql.append(fulltextSql.length() > 0 ? " AND " : "").append(String.format(" (MATCH(std_org_name) AGAINST ('%s' IN BOOLEAN MODE))", key.substring(1)));
                            }
                        }
                        //2.如果key以#号开始，则直接搜索标准号字段
                        else if (item.startsWith("#")) {
                            //fulltextSql.append((fulltextSql.length()>0?" AND ":"")+ String.format(" (MATCH(std_no) AGAINST ('%s*' IN BOOLEAN MODE))",key.substring(1)));
                            if (SqlUtils.needLike(key)) {
                                fulltextSql.append(fulltextSql.length() > 0 ? " AND " : "").append(String.format(" std_no like '%%%s%%'", key.substring(1)));
                            } else {
                                fulltextSql.append(fulltextSql.length() > 0 ? " AND " : "").append(String.format(" (MATCH(std_no) AGAINST ('%s*' IN BOOLEAN MODE))", key.substring(1)));
                            }
                        } else {
                            // 如果key包含中文字符，则走标准名称
                            if (StringUtil.containsChinese(key)) {
                                if (SqlUtils.needLike(key)) {
                                    fulltextSql.append(fulltextSql.length() > 0 ? " AND " : "").append(String.format(" std_org_name like '%%%s%%'", key));
                                } else {
                                    fulltextSql.append(fulltextSql.length() > 0 ? " AND " : "").append(String.format(" (MATCH(std_org_name) AGAINST ('%s' IN BOOLEAN MODE))", key));
                                }
                                //fulltextSql.append((fulltextSql.length()>0?" AND ":"")+ String.format(" (MATCH(std_org_name) AGAINST ('%s' IN BOOLEAN MODE))",key));
                            } else {
                                if (SqlUtils.needLike(key)) {
                                    fulltextSql.append(fulltextSql.length() > 0 ? " AND " : "").append(String.format(" std_no like '%%%s%%'", key));
                                } else {
                                    fulltextSql.append(fulltextSql.length() > 0 ? " AND " : "").append(String.format(" (MATCH(std_no) AGAINST ('%s*' IN BOOLEAN MODE))", key));
                                }
                                //fulltextSql.append((fulltextSql.length()>0?" AND ":"")+ String.format(" (MATCH(std_no) AGAINST ('%s*' IN BOOLEAN MODE))",key));
                            }
                        }
//                        if (StringUtil.containsChinese(key)) {
//                            stdOrgName.append(" ").append(key);
//                        }
//                        else
//                        {
//                            stdNo.append(" ").append(key).append("*");
//                        }
//                         query.and(foo -> foo.like("std_no", key).or().like("std_org_Name", key));
                        if (StringUtil.isNumeric(key)) {
                            sortSql += " when sort_serial_no='" + key + "' then 10 when sort_serial_no like '" + key + "%' then 5 ";
                        }
                    }
                }
            }
            DealIdnetity(stdInfo, query);
//            if(StringUtils.isNotBlank(sortSql)) {
//                sortQuery.orderBy(true,false, "CASE "+sortSql+" ELSE 0 END");
//            }
            //
            if (fulltextSql.toString().length() > 0) {
                sortQuery.orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_year").orderByAsc("sort_identifaction");
            } else {
                sortQuery.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_year");
            }
            String customSqlSegment = SqlUtils.getFullSql(query, StdInfo.class);
            customSqlSegment += ((StringUtils.isNotBlank(customSqlSegment) && fulltextSql.length() > 0) ? " and " : "") + fulltextSql.toString();
            var page = stdInfoService.pageByFullTextSearch(new Page<StdInfo>(pageDomain.getPage(), pageDomain.getLimit()), sortQuery, customSqlSegment, StandardTypeEnum.China, stdNo.toString().trim(), stdOrgName.toString().trim(), stdChineseName, stdEnglishName, primaryCoverage, pubDept, advanceDept, draftingUnit, drafter);
            //  var page= stdInfoMapper.selectPage(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
            //获取可以下载的标准的数量
            // query.eq("pdf_is_exists","1");
            //var pdfCanDownloads=stdInfoService.count(query);
            var pg = pageTable(stdInfoService.fillIsCollected(page.getRecords()), page.getTotal());
            pg.setExtData(page.getTotal());
            return pg;
        }
        }
        catch (Exception ex)
        {
            return  pageTable(new ArrayList<StdInfo>(),0);
        }
    }

    //处理std_class查询条件
    public void DealIdnetity(StdInfo stdInfo ,QueryWrapper<StdInfo> query)
    {
        if(StringUtils.isNotBlank(stdInfo.getQueryIdenti())) {
            var arr = stdInfo.getQueryIdenti().split(",");
            var list = (List<String>) Arrays.asList(arr);
            //检索全部Identification
            if (list.contains("0")) {
                //什么都不做,取所有标准
            } else if(list.size()>0) {
                query.and(qw-> {
//                if (list.contains("GJB")) {
//                    // qw.or(q -> {q.eq("std_class", "国家军用标准");});
//                    qw.or(q -> {q.eq("std_class", 1001);});
//                }
//                if (list.contains("WJ")) {
//                    // qw.or(q -> {q.eq("std_class", "兵器行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1002);});
//                }
//                if (list.contains("Q/CNG")) {
//                    // qw.or(q -> {q.eq("std_class", "集团公司标准");});
//                    qw.or(q -> {q.eq("std_class", 1003);});
//                }
//                if (list.contains("GB")) {
//                    // qw.or(q -> {q.eq("std_class", "国家标准");});
//                    qw.or(q -> {q.eq("std_class", 1004);});
//                }
//                if (list.contains("HB")) {
//                    // qw.or(q -> {q.eq("std_class", "航空行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1005);});
//                }
//                if (list.contains("HG")) {
//                    // qw.or(q -> {q.eq("std_class", "化工行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1006);});
//                }
//                if (list.contains("JB")) {
//                    //qw.or(q -> {q.eq("std_class", "机械行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1007);});
//                }
//                if (list.contains("JJF")) {
//                   // qw.or(q -> {q.eq("std_class", "计量技术规范");});
//                    qw.or(q -> {q.eq("std_class", 1008);});
//                }
//                if (list.contains("JJG")) {
//                    //qw.or(q -> {q.eq("std_class", "计量检定规程");});
//                    qw.or(q -> {q.eq("std_class", 1009);});
//                }
//                if (list.contains("QC")) {
//                    // qw.or(q -> {q.eq("std_class", "汽车行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1010);});
//                }
//                if (list.contains("QJ")) {
//                    //qw.or(q -> {q.eq("std_class", "航天行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1011);});
//                }
//                if (list.contains("SJ")) {
//                    //qw.or(q -> {q.eq("std_class", "电子行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1012);});
//                }
//                if (list.contains("SY")) {
//                    //qw.or(q -> {q.eq("std_class", "石油天然气行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1013);});
//                }
//                if (list.contains("TB")) {
//                    //qw.or(q -> {q.eq("std_class", "铁道行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1014);});
//                }
//                if (list.contains("YD")) {
//                    //qw.or(q -> {q.eq("std_class", "通信行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1015);});
//                }
//                if (list.contains("CB")) {
//                    //qw.or(q -> {q.eq("std_class", "船舶行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1016);});
//                }
//                if (list.contains("DL")) {
//                    //qw.or(q -> {q.eq("std_class", "电力行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1017);});
//                }
//                if (list.contains("AQ")) {
//                    //qw.or(q -> {q.eq("std_class", "安全生产行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1018);});
//                }
//                if (list.contains("DB11")) {
//                    //qw.or(q -> {q.eq("std_class", "北京市地方标准");});
//                    qw.or(q -> {q.eq("std_class", 1019);});
//                }
//                if (list.contains("DB13")) {
//                    // qw.or(q -> {q.eq("std_class", "河北省地方标准");});
//                    qw.or(q -> {q.eq("std_class", 1020);});
//                }
//                if (list.contains("DB31")) {
//                    qw.or(q -> {q.eq("std_class", 1021);});
//                }
//                if (list.contains("DB37")) {
//                    // qw.or(q -> {q.eq("std_class", "山东省地方标准");});
//                    qw.or(q -> {q.eq("std_class", 1022);});
//                }
//                if (list.contains("DB50")) {
//                    //qw.or(q -> {q.eq("std_class", "重庆市地方标准");});
//                    qw.or(q -> {q.eq("std_class", 1023);});
//                }
//                if (list.contains("DB61")) {
//                    //qw.or(q -> {q.eq("std_class", "陕西省地方标准");});
//                    qw.or(q -> {q.eq("std_class", 1024);});
//                }
//                if (list.contains("HJ")) {
//                    //qw.or(q -> {q.eq("std_class", "生态环境标准");});
//                    qw.or(q -> {q.eq("std_class", 1025);});
//                }
//                if (list.contains("JR")) {
//                    //qw.or(q -> {q.eq("std_class", "金融行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1026);});
//                }
//                if (list.contains("KA")) {
//                    //qw.or(q -> {q.eq("std_class", "矿山安全行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1027);});
//                }
//                if (list.contains("MT")) {
//                    //qw.or(q -> {q.eq("std_class", "煤炭行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1028);});
//                }
//                if (list.contains("QX")) {
//                    //qw.or(q -> {q.eq("std_class", "气象行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1029);});
//                }
//                if (list.contains("WS")) {
//                    //qw.or(q -> {q.eq("std_class", "卫生行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1030);});
//                }
//                if (list.contains("YZ")) {
//                    //qw.or(q -> {q.eq("std_class", "邮政行业标准");});
//                    qw.or(q -> {q.eq("std_class", 1031);});
//                }
                    //界面上默认组合了GJB和WJ 及 Q/CNG，所以这里要判断是否包含这三个，如果包含，则查询std_class_int为1的情况（组合条件）
                    if(list.size()==3 && list.contains("GJB") && list.contains("WJ") && list.contains("Q/CNG")) {
                        qw.or(q -> {q.eq("std_class_int", 1);});
                    }
                    else {
                        if (list.contains("GJB")) {
                            qw.or(q -> {
                                q.eq("std_class", "国家军用标准");
                            });
                        }
                        if (list.contains("WJ")) {
                            qw.or(q -> {
                                q.eq("std_class", "兵器行业标准");
                            });
                        }
                        if (list.contains("Q/CNG")) {
                            qw.or(q -> {
                                q.eq("std_class", "集团公司标准");
                            });
                        }

                        if (list.contains("GB")) {
                            qw.or(q -> {
                                q.eq("std_class", "国家标准");
                            });
                        }
                        if (list.contains("HB")) {
                            qw.or(q -> {
                                q.eq("std_class", "航空行业标准");
                            });
                        }
                        if (list.contains("HG")) {
                            qw.or(q -> {
                                q.eq("std_class", "化工行业标准");
                            });
                        }
                        if (list.contains("JB")) {
                            qw.or(q -> {
                                q.eq("std_class", "机械行业标准");
                            });
                        }
                        if (list.contains("JJF")) {
                            qw.or(q -> {
                                q.eq("std_class", "计量技术规范");
                            });
                        }
                        if (list.contains("JJG")) {
                            qw.or(q -> {
                                q.eq("std_class", "计量检定规程");
                            });
                        }
                        if (list.contains("QC")) {
                            qw.or(q -> {
                                q.eq("std_class", "汽车行业标准");
                            });
                        }
                        if (list.contains("QJ")) {
                            qw.or(q -> {
                                q.eq("std_class", "航天行业标准");
                            });
                        }
                        if (list.contains("SJ")) {
                            qw.or(q -> {
                                q.eq("std_class", "电子行业标准");
                            });
                        }
                        if (list.contains("SY")) {
                            qw.or(q -> {
                                q.eq("std_class", "石油天然气行业标准");
                            });
                        }
                        if (list.contains("TB")) {
                            qw.or(q -> {
                                q.eq("std_class", "铁道行业标准");
                            });
                        }
                        if (list.contains("YD")) {
                            qw.or(q -> {
                                q.eq("std_class", "通信行业标准");
                            });
                        }
                        if (list.contains("CB")) {
                            qw.or(q -> {
                                q.eq("std_class", "船舶行业标准");
                            });
                        }
                        if (list.contains("DL")) {
                            qw.or(q -> {
                                q.eq("std_class", "电力行业标准");
                            });
                        }
                        if (list.contains("AQ")) {
                            qw.or(q -> {
                                q.eq("std_class", "安全生产行业标准");
                            });
                        }
                        if (list.contains("DB11")) {
                            qw.or(q -> {
                                q.eq("std_class", "北京市地方标准");
                            });
                        }
                        if (list.contains("DB13")) {
                            qw.or(q -> {
                                q.eq("std_class", "河北省地方标准");
                            });
                        }
                        if (list.contains("DB31")) {
                            qw.or(q -> {
                                q.eq("std_class", "上海市地方标准");
                            });
                        }
                        if (list.contains("DB37")) {
                            qw.or(q -> {
                                q.eq("std_class", "山东省地方标准");
                            });
                        }
                        if (list.contains("DB50")) {
                            qw.or(q -> {
                                q.eq("std_class", "重庆市地方标准");
                            });
                        }
                        if (list.contains("DB61")) {
                            qw.or(q -> {
                                q.eq("std_class", "陕西省地方标准");
                            });
                        }
                        if (list.contains("HJ")) {
                            qw.or(q -> {
                                q.eq("std_class", "生态环境标准");
                            });
                        }
                        if (list.contains("JR")) {
                            qw.or(q -> {
                                q.eq("std_class", "金融行业标准");
                            });
                        }
                        if (list.contains("KA")) {
                            qw.or(q -> {
                                q.eq("std_class", "矿山安全行业标准");
                            });
                        }
                        if (list.contains("MT")) {
                            qw.or(q -> {
                                q.eq("std_class", "煤炭行业标准");
                            });
                        }
                        if (list.contains("QX")) {
                            qw.or(q -> {
                                q.eq("std_class", "气象行业标准");
                            });
                        }
                        if (list.contains("WS")) {
                            qw.or(q -> {
                                q.eq("std_class", "卫生行业标准");
                            });
                        }
                        if (list.contains("YZ")) {
                            qw.or(q -> {
                                q.eq("std_class", "邮政行业标准");
                            });
                        }
                    }
                });
            }
        } else {
            query.and(wq->{wq.eq("1","2");});
        }
    }

    @GetMapping("dataofsystem")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable dataofsystem(StdInfo stdInfo, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return new ResultTable();
        }
        var query=new QueryWrapper<StdInfoOfSystem>();
        var sortSegment=new StringBuilder();
        if(StringUtils.isNotBlank(stdInfo.getSystemId())) {
            query.eq(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getSystemId()),"system_id",stdInfo.getSystemId());
            query.likeRight(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getClassCodePath()),"class_code_path",stdInfo.getClassCodePath());
            if (StringUtils.isNotBlank(stdInfo.getSimpleSearchKey())) {
                var keys = stdInfo.getSimpleSearchKey();
                while (keys.contains("  ")) {
                    keys = keys.replaceFirst("  ", " ");
                }
                var keyArr = keys.split(" ");
                for (String key : keyArr) {
                    query.and(foo -> foo.like("aa.std_no", key).or().like("aa.std_org_Name", key));
                }
            } else if (StringUtils.isNotBlank(stdInfo.getAdvSearchKey())) {
                var jsonArray = (JSONArray) JSONArray.parse(stdInfo.getAdvSearchKey());
                for (Object item : jsonArray) {
                    var obj = (com.alibaba.fastjson.JSONObject) item;
                    var logic = obj.getString("logic");
                    var field = obj.getString("field");
                    var val = obj.getString("val");
                    var type = obj.getString("type");
                    if(StringUtils.isNotBlank(val)) {
                        if ("AND".equals(logic)) {
                            //精确查找
                            if ("exact".equals(type)) {
                                query.eq("aa." + field, val);
                            } else {
                                //模糊查询
                                query.like("aa." + field, val);
                            }
                        } else if ("OR".equals(logic)) {
                            //精确查找
                            if ("exact".equals(type)) {
                                query.or(qr -> qr.eq("aa." + field, val));
                            } else {
                                //模糊查询
                                query.or(qr -> qr.like("aa." + field, val));
                            }
                        } else if ("NOT".equals(logic)) {
                            //精确查找
                            if ("exact".equals(type)) {
                                query.ne("aa." + field, val);
                            } else {
                                //模糊查询
                                query.notLike("aa." + field, val);
                            }
                        }
                    }
                }
            }
           // query.orderByAsc("class_code_path","sort_serial_no");
            sortSegment.append("order by class_code_path asc,sort_identifaction asc,sort_serial_no asc,sort_year asc,sort_char asc,sort_char asc");
           // query.orderByAsc("class_code_path").orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_year").orderByAsc("sort_char");
            //获取可以下载的标准的数量
            var page1= stdInfoService.pageBySystemInfo(new Page<StdInfoOfSystem>(pageDomain.getPage(),pageDomain.getLimit()),query,sortSegment.toString());
            var result=new ArrayList<StdInfo>();
            if(page1.getRecords().size()>0) {
                var modelList = page1.getRecords();
                for (var i = 0; i < modelList.size(); i++) {
                    result.add(modelList.get(i).convertToStdInfo());
                }
            }
            //query.eq("pdf_is_exists", "1");
            //var pdfCanDownloads = stdInfoService.getPdfExistCount(query);
            var pg= pageTable(stdInfoService.fillIsCollected(result), page1.getTotal());
            //pg.setExtData((long)pdfCanDownloads);
            pg.setExtData(page1.getTotal());
            return pg;
        }
        else {
            return pageTable(null, 0);
        }
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("adv")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable advQuery(StdInfo stdInfo, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return new ResultTable();
        }
        var query=new QueryWrapper<StdInfo>();
        query.eq("std_type", StandardTypeEnum.China);
        if(StringUtils.isNotBlank(stdInfo.getAdvSearchKey()))
        {
            var jsonArray= (JSONArray)JSONArray.parse(stdInfo.getAdvSearchKey());
            for(Object item :jsonArray)
            {
                var obj=(JSONObject)item;
                var logic=obj.getStr("logic");
                var field=obj.getStr("field");
                var val=obj.getStr("val");
                var type=obj.getStr("type");
                if("AND".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.eq(field, val);
                    }
                    else {
                        //模糊查询
                        query.like(field,val);
                    }
                }
                else if("OR".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.or().eq(field, val);
                    }
                    else {
                        //模糊查询
                        query.or().like(field,val);
                    }
                }
                else if("NOT".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.ne(field, val);
                    }
                    else {
                        //模糊查询
                        query.notLike(field,val);
                    }
                }
            }
        }
        query.orderByAsc("sort_identifaction").orderByDesc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_char").orderByAsc("sort_year");
        var page= stdInfoMapper.selectPage(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
        // var page =stdInfoService.page(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
        //获取可以下载的标准的数量
        query.eq("pdf_is_exists","1");
        var pdfCanDownloads=stdInfoService.count(query);
        var pg= pageTable(page.getRecords(), page.getTotal());
        pg.setExtData(pdfCanDownloads);
        return pg;
    }

    @GetMapping("list")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable list(StdInfo stdInfo, PageDomain pageDomain) {
        var query=new LambdaQueryWrapper<StdInfo>();
        query.eq(StdInfo::getStdType, StandardTypeEnum.China);
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getStdNo()))
        {
            query.like(StdInfo::getStdNo, stdInfo.getStdNo());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getStdOrgName()))
        {
            query.like(StdInfo::getStdOrgName,stdInfo.getStdOrgName());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getDraftingUnit()))
        {
            query.like(StdInfo::getDraftingUnit, stdInfo.getDraftingUnit());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getDrafter()))
        {
            query.like(StdInfo::getDrafter, stdInfo.getDrafter());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getPrimaryCoverage()))
        {
            query.like(StdInfo::getPrimaryCoverage, stdInfo.getPrimaryCoverage());
        }
        query.orderByAsc(StdInfo::getSortIdentifaction).orderByDesc(StdInfo::getSortSerialNo).orderByAsc(StdInfo::getSortNumber).orderByAsc(StdInfo::getSortChar).orderByAsc(StdInfo::getSortYear);
        var page =stdInfoService.page(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
        //获取可以下载的标准的数量
        query.eq(StdInfo::getPdfIsExists,"1");
        var pdfCanDownloads=stdInfoService.count(query);
        var pg= pageTable(page.getRecords(), page.getTotal());
        pg.setExtData(pdfCanDownloads);
        return pg;
    }

    /**
     *统计可下载的pdf文件数量
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @GetMapping("updatePysicFileState")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result updatePysicFileState(HttpServletResponse resp, @RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "10") Integer pageSize) throws IOException
    {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).orderByAsc(StdInfo::getStdClass).last("limit "+(pageNum-1)*pageSize+","+pageSize));
        encryptPdfPath=mergePath(encryptPdfPath);
        try {
            for (var i = 0; i < list.size(); i++) {
                var exists = false;
                var data = list.get(i);
                if (StringUtils.isNotBlank(data.getPdfFileName())) {
                    File file = new File(encryptPdfPath + "\\" + data.getId());
                    if (file.exists()) {
                        exists = true;
                    }
                }
                data.setPdfIsExists(exists ? "1" : "0");
                stdInfoService.updateById(data);
            }
        }catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
        }
        return decide(true);
    }

    @GetMapping("getBigFileEncryptCmd")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result updatePysicFileState() throws IOException {
        File f=new File("E:\\bigfile");
        if(f.exists())
        {
            var files=f.listFiles();
            for (File ff:files) {
                if(ff.isFile()) {
                    var stdInfo = stdInfoService.getOne(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getPdfFileName, ff.getName()).last("limit 1"));
                    if (stdInfo != null) {
                        System.out.println("C:\\StdSys\\FileEncrypt.exe encrypt 20221103 \"E:\\bigfile\\" + ff.getName() + "\" " + "\"E:\\EncryptSources\\" + stdInfo.getId() +"\"");
                    }
                }
            }
        }
        return Result.success(true);
    }


    /**
     * 将未加密的文件收集起来放在指定文件夹内（D:\notEncryptFile)
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @GetMapping("collectNotEncryptFile")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result collectNotEncryptFile(HttpServletResponse resp) throws IOException
    {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getPdfIsExists,0));
        //pdfPath=mergePath(pdfPath);
        var pdfOrgPath="E:\\allPDF\\pdfs";
        try {
            for (var i = 0; i < list.size(); i++) {
                var exists = false;
                var data = list.get(i);
                if (StringUtils.isNotBlank(data.getPdfFileName())) {
                    findPdfFile(pdfOrgPath,data.getPdfFileName());
                }
            }
        }catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
        }
        return decide(true);
    }


    /**
     *  收集多出来的加密文件（与数据库内编号对应补上的）
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @GetMapping("collectBadFile")
    public boolean collectBadFile() throws IOException {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China));
        File root=new File("D:\\EncryptSources");
        File aimDic=new File("D:\\NotExistInDB");
        aimDic.mkdir();
        if(root.exists() && root.isDirectory())
        {
            var files=root.listFiles();
            for (File f:files
            ) {
                var inDbModel=list.stream().filter(d->d.getId().equals(f.getName())).findFirst();
                if(!inDbModel.isPresent())
                {
                    FileInputStream fis=new FileInputStream(f);
                    FileOutputStream fos=new FileOutputStream(new File("D:\\NotExistInDB\\"+f.getName()));
                    byte b[]=new byte[1024];
                    int len=0;
                    while((len=fis.read(b))!=-1)
                    {
                        fos.write(b);
                    }
                }
            }
        }
        return false;
    }

    /**
     *  收集多出来的加密文件（与数据库内编号对应补上的）
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @GetMapping("moveWJtoOtherDir")
    public boolean moveWJtoOtherDir(@RequestParam(name = "l",defaultValue = "20000") Integer limit) throws IOException {
        var query=new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).in(StdInfo::getStdClass,"国家军用标准","兵器行业标准").last("limit "+limit);
        var list=stdInfoService.list(query);
        File root=new File("D:\\EncryptSources");
        File aimDic=new File("D:\\GJBWJ");
        aimDic.mkdir();
        if(root.exists() && root.isDirectory())
        {
            for (var info:list
            ) {
                var inFile=new File("D:\\EncryptSources\\"+info.getId());
                if(inFile.exists())
                {
                    File saveFile=new File("D:\\GJBWJ\\"+info.getId());
                    FileUtils.moveFileToDirectory(inFile, aimDic,true);
                }
            }
        }
        return true;
    }

    /**
     * 将未加密的文件收集起来放在指定文件夹内（D:\notEncryptFile)
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @GetMapping("collectNotSolrFile")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result collectNotSolrFile(HttpServletResponse resp) throws IOException
    {
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).in(StdInfo::getStdClass,"国家军用标准","兵器行业标准","集团公司标准"));
        //pdfPath=mergePath(pdfPath);
        var pdfOrgPath="E:\\allPDF\\pdfs";
        try {
            for (var i = 0; i < list.size(); i++) {
                var exists = false;
                var data = list.get(i);
                SolrQuery solrQuery = new SolrQuery();
                solrQuery.set("df", "id");
                solrQuery.setQuery("id:"+data.getId());
                QueryResponse response;
                try {
                    response = client.query(solrQuery);
                     var total = response.getResults().getNumFound();
                     if(total==0)
                     {
                         if (StringUtils.isNotBlank(data.getPdfFileName())) {
                             findPdfFile(pdfOrgPath,data.getPdfFileName());
                         }
                         data.setHasSolrIndex(0);
                         stdInfoService.updateById(data);
                     }
                     else
                     {
                         data.setHasSolrIndex(1);
                         stdInfoService.updateById(data);
                     }
                } catch (SolrServerException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
        }
        client.close();
        return decide(true);
    }

    public boolean findPdfFile(String pdfPath,String fileName) throws IOException {
        File root=new File(pdfPath);
        if(root.exists() && root.isDirectory())
        {
            var files=root.listFiles();
            for (File f:files
                 ) {
                if(f.isDirectory())
                {
                    File ff=new File(f.getAbsolutePath()+"\\"+fileName);
                    if(ff.exists() && ff.isFile())
                    {
                        FileInputStream fis=new FileInputStream(ff);
                        FileOutputStream fos=new FileOutputStream(new File("F:\\UnEncrpytFile\\"+fileName));
                        byte b[]=new byte[1024];
                        int len=0;
                        while((len=fis.read(b))!=-1)
                        {
                            fos.write(b);
                        }
                        return true;
                    }
                }
            }
        }
        return false;
    }


    /**
     * 从多个盘符对应的文件中找到加密文件的存放位置
     *
     * @param filePaths
     * @param id
     * @return java.lang.String
     * @throws
     */
    public String getCorrespondingPath(String filePaths,String id)
    {
        String path=filePaths;
        if(filePaths.contains(",")) {
            var paths = filePaths.split(",");
            path=paths[0];
            for(var i=0;i<paths.length;i++)
            {
                File file=new File(paths[i]+"\\"+id);
                if(file.exists())
                {
                    path=paths[i];
                    break;
                }
            }
        }
        return path;
    }

    /**
     *统计可下载的pdf文件数量
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @PostMapping("testpdfstatus")
    public Result statisticsPdfCount()
    {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China));
        encryptPdfPath=mergePath(encryptPdfPath);
        try {
            for (var i = 0; i < list.size(); i++) {
                var exists = false;
                var data = list.get(i);
                if (StringUtils.isNotBlank(data.getPdfFileName())) {
                    File file = new File(getCorrespondingPath(encryptPdfPath,data.getId()) + "\\" + data.getId());
                    if (file.exists()) {
                        exists = true;
                    }
                }
                data.setPdfIsExists(exists ? "1" : "0");
                stdInfoService.updateById(data);
            }
        }catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
        }
        return decide(true);
    }

    /**
     * Describe: 数据字典类型新增视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("add")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public ModelAndView add() {
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("save")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result save(@RequestBody StdInfo stdInfo) throws IOException, InterruptedException {
        stdInfo.setStdType(StandardTypeEnum.China);
        stdInfo=stdInfoService.AnalysisSortCondition(stdInfo);
        if(StringUtils.isBlank(stdInfo.getId()))
        {
            if(!StringUtils.isNotBlank(stdInfo.getPdfFileName()))
            {
                stdInfo.setPdfIsExists("1");
            }
            else
            {
                stdInfo.setPdfIsExists("0");
            }
        }
        if(StringUtils.isNotBlank(stdInfo.getPdfFileName())) {
            File f = new File(getCorrespondingPath(mergePath(pdfPath),stdInfo.getPdfFileName()) + "\\" + stdInfo.getPdfFileName());
            File encryptFile = new File(getCorrespondingPath(mergePath(encryptPdfPath),stdInfo.getId()) + "\\" + stdInfo.getId());
            var pdfIsExists = false;
            if (!encryptFile.exists() && f.exists()) {
                //大于100M的文件单独处理
                if (f.length() > 104857600) {
                    encryptPdfPath = getCorrespondingPath(mergePath(encryptPdfPath),stdInfo.getId());
                    Tool.encryptByExe(getCorrespondingPath(mergePath(pdfPath),stdInfo.getPdfFileName())+ "\\" + stdInfo.getPdfFileName(), encryptPdfPath + "\\" + stdInfo.getId(), "20221103");
                    Thread.sleep(Tool.estimatedEncryptTime(f.length()));
                } else {
                    var data = Tool.getBinaryData(f);
                    saveEncryptPdf(stdInfo.getId(), data);
                }
                pdfIsExists = true;
            } else if (encryptFile.exists()) {
                pdfIsExists = true;
            }
            stdInfo.setPdfIsExists(pdfIsExists ? "1" : "0");
        }
        boolean result = stdInfoService.save(stdInfo);
        return decide(result);
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("edit")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView edit(Model model,String stdID) {
        var data=stdInfoService.getById(stdID);
        model.addAttribute("stdInfo",data);
        return jumpPage(MODULE_PATH + "edit");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detail")
    @Log(title = "国内标准", describe = "信息预览", type = BusinessType.Standard_OverView)
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detail(Model model,String stdID) {
        var data=stdInfoService.getById(stdID);
        model.addAttribute("stdInfo",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    @GetMapping("getByNo")
    public Result<StdInfo> getByNo(@RequestParam(name="stdNo") String stdNo)
    {
        var data=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdNo,stdNo).last("limit 1"));
        var newList= stdInfoService.fillIsCollected(data);
        var result=new Result<StdInfo>();
        if(newList.size()>0) {
            result.setData(newList.get(0));
        }
        result.setSuccess(true);
        return result;
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detailbystdno")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detailbystdno(Model model,String stdNo) {
        var data=stdInfoService.getOne(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdNo,stdNo).last("limit 1"));
        model.addAttribute("stdInfo",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PutMapping("update")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result update(@RequestBody StdInfo stdInfo) throws IOException, InterruptedException {
//        if(StringUtils.isNotBlank(stdInfo.getPdfFileName())) {
//            File f = new File(getCorrespondingPath(mergePath(pdfPath),stdInfo.getPdfFileName()) + "\\" + stdInfo.getPdfFileName());
//            File encryptFile = new File(getCorrespondingPath(mergePath(encryptPdfPath),stdInfo.getId()) + "\\" + stdInfo.getId());
//            var pdfIsExists = false;
//            if (!encryptFile.exists() && f.exists()) {
//                //大于100M的文件单独处理
//                if (f.length() > 104857600) {
//                    encryptPdfPath = getCorrespondingPath(mergePath(encryptPdfPath),stdInfo.getId());
//                    Tool.encryptByExe(getCorrespondingPath(mergePath(pdfPath),stdInfo.getPdfFileName()) + "\\" + stdInfo.getPdfFileName(), encryptPdfPath + "\\" + stdInfo.getId(), "20221103");
//                    Thread.sleep(Tool.estimatedEncryptTime(f.length()));
//                } else {
//                    var data = Tool.getBinaryData(f);
//                    saveEncryptPdf(stdInfo.getId(), data);
//                }
//                pdfIsExists = true;
//            } else if (encryptFile.exists()) {
//                pdfIsExists = true;
//            }
//            stdInfo.setPdfIsExists(pdfIsExists ? "1" : "0");
//        }
        stdInfo.setStdType(StandardTypeEnum.China);
        stdInfo=stdInfoService.AnalysisSortCondition(stdInfo);
        boolean result = stdInfoService.updateById(stdInfo);
        return decide(result);
    }

    public void traverseFolder(File folder) throws InterruptedException, IOException {
        File[] files = folder.listFiles();
        var i=0;
        for (File f : files) {
            i++;
            if (f.isFile()) {
                var query = new LambdaQueryWrapper<StdInfo>();
                query.eq(StdInfo::getPdfFileName, f.getName());
                var model = stdInfoService.getOne(query);
                if (model != null) {
                    System.out.println("---------------------------------------------");
                    System.out.println(i);
                    System.out.println(model.getId());
                    System.out.println(f.getName());
                    System.out.println("---------------------------------------------");
                    File encryptFile = new File(getCorrespondingPath(mergePath(encryptPdfPath),model.getId()) + "\\" + model.getId());
                    var pdfIsExists = false;
                    //if (!encryptFile.exists() && f.exists()) {
                    if (f.exists()) {
                        encryptPdfPath = getCorrespondingPath(mergePath(encryptPdfPath),model.getId());
                        //大于100M的文件单独处理
                        if (f.length() > 104857600) {
                            Tool.encryptByExe(getCorrespondingPath(mergePath(pdfPath),model.getPdfFileName()) + "\\" + model.getPdfFileName(), encryptPdfPath + "\\" + model.getId(), "20221103");
                            Thread.sleep(Tool.estimatedEncryptTime(f.length()));
                        } else {
                            var data = Tool.getBinaryData(f);
                            saveEncryptPdf1(encryptPdfPath + "\\" + model.getId(), data);
                        }
                        pdfIsExists = true;
                    } else if (encryptFile.exists()) {
                        pdfIsExists = true;
                    }
                    model.setPdfIsExists(pdfIsExists ? "1" : "0");
                    stdInfoService.updateById(model);
                }
            } else if (f.isDirectory()) {
                traverseFolder(f); // 递归调用
            }
        }
    }


    /**
     * Describe: 批量生成加密文件
     * Param: sysDictType
     * Return: ModelAndView
     * 大文件生成命令的sql：
     * select 'C:\\StdSys\\FileEncrypt.exe','encrypt',20221103, concat('"','D:\\allPDF\\1\\',pdf_file_name,'"'),concat('"','C:\\EncryptSources\\',id,'"') from std_info where id in('2a3cf9c3733311ee805')
     */
    @PostMapping("batchUpdateDoc")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result batchUpdateDoc() throws IOException, InterruptedException {
        File dir = new File(pdfPath);
        System.out.println("---------------------------------------------");
        System.out.println(dir.getAbsolutePath());
        try {
            traverseFolder(dir);
        }
        catch (Exception ex)
        {
            System.out.println(ex.getStackTrace().toString());
            return Result.failure(ex.getMessage());
        }
        return decide(true);
    }
    /**
     * Describe: 批量生成加密文件
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("batchUpdateDoc_last")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result batchUpdateDoc_last() throws IOException, InterruptedException {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China));
        pdfPath=mergePath(pdfPath);
        for(var i=0;i<list.size();i++)
        {
            var model =list.get(i);
            if(StringUtils.isNotBlank(model.getPdfFileName())) {
                File f = new File(getCorrespondingPath(pdfPath,model.getPdfFileName()) + "\\" + model.getPdfFileName());
                File encryptFile = new File(getCorrespondingPath(encryptPdfPath,model.getId()) + "\\" + model.getId());
                var pdfIsExists = false;
                if (!encryptFile.exists() && f.exists()) {
                    //大于100M的文件单独处理
                    if (f.length() > 104857600) {
                        encryptPdfPath = getCorrespondingPath(mergePath(encryptPdfPath),model.getId());
                        Tool.encryptByExe(getCorrespondingPath(pdfPath,model.getPdfFileName()) + "\\" + model.getPdfFileName(), encryptPdfPath + "\\" + model.getId(), "20221103");
                        Thread.sleep(Tool.estimatedEncryptTime(f.length()));
                    } else {
                        var data = Tool.getBinaryData(f);
                        saveEncryptPdf(model.getId(), data);
                    }
                    pdfIsExists = true;
                } else if (encryptFile.exists()) {
                    pdfIsExists = true;
                }
                model.setPdfIsExists(pdfIsExists ? "1" : "0");
                stdInfoService.updateById(model);
            }
        }
        return decide(true);
    }

    /**
     * Describe: 批量生成加密文件
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("batchUpdateDoc1")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result batchUpdateDoc1(HttpServletResponse resp, @RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "10") Integer pageSize) throws IOException {
        var query=new LambdaQueryWrapper<StdInfo>();
        query.orderByDesc(StdInfo::getCreateTime);
        query.eq(StdInfo::getPdfIsExists,"0");
        var page =stdInfoService.page(new Page<StdInfo>(pageNum,pageSize),query);
        var pdfPath1=mergePath(pdfPath);
        StdInfo model=null;
        int i=0;
        var errList=new ArrayList<String>();
            for (i = 0; i < page.getRecords().size(); i++) {
                model=page.getRecords().get(i);
                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(page.getRecords().get(i).getPdfFileName())) {
                  //  File file = new File(getCorrespondingPath(pdfPath1,page.getRecords().get(i).getPdfFileName()) + "\\" + page.getRecords().get(i).getPdfFileName());
                    File encryptFile=new File("D:\\EncryptSources\\" + page.getRecords().get(i).getId());
                    File file = new File( "E:\\allPDF\\usaPdfs\\" + page.getRecords().get(i).getPdfFileName());
                    if (!encryptFile.exists() && file.exists()) {
                    /*byte[] buffer = new byte[1024];
                    var os=resp.getOutputStream();
                    var fis = new FileInputStream(file);
                    var bis = new BufferedInputStream(fis);
                    int ii = bis.read(buffer);
                    while(ii != -1){
                        os.write(buffer);
                        i = bis.read(buffer);
                    }*/
                        System.out.println("id:"+model.getId());
                        try {
                        var byteData = Tool.getBinaryData(file);
                        saveEncryptPdf(page.getRecords().get(i).getId(), byteData);
                        stdInfoService.update(null,new LambdaUpdateWrapper<StdInfo>().set(StdInfo::getPdfIsExists,1).eq(StdInfo::getId,model.getId()));
                        }
                        catch (Exception ex)
                        {
                            errList.add("ID:"+model.getId()+";Index:"+Integer.toString(i));
                            System.out.println("--------------------------------------------------");
                            System.out.println("ERROR ID:"+model.getId()+";Index:"+Integer.toString(i));
                        }
                    }
                    else if(encryptFile.exists())
                    {
                        stdInfoService.update(null,new LambdaUpdateWrapper<StdInfo>().set(StdInfo::getPdfIsExists,1).eq(StdInfo::getId,model.getId()));
                    }
                }
            }
            if(errList.size()>0)
            {

                savelErrToExcel(errList);
                var result=new Result<String>();
                result.setSuccess(false);
                result.setMsg("error");
                return  result;
            }
            return decide(true);

    }

    /**
     * Describe: 批量生成加密文件
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("fetchEncryptFile")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result fetchEncryptFile(HttpServletResponse resp) throws IOException {
        var query=new LambdaQueryWrapper<StdInfo>();
        query.orderByDesc(StdInfo::getCreateTime);
        query.eq(StdInfo::getPdfIsExists,"0");
        var page =stdInfoService.page(new Page<StdInfo>(1,10000),query);
        StdInfo model=null;
        int i=0;
        var errList=new ArrayList<String>();
        for (i = 0; i < page.getRecords().size(); i++) {
            model=page.getRecords().get(i);
            if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(page.getRecords().get(i).getPdfFileName())) {
                //  File file = new File(getCorrespondingPath(pdfPath1,page.getRecords().get(i).getPdfFileName()) + "\\" + page.getRecords().get(i).getPdfFileName());
                FileUtils.copyFile(new File("E:\\allPDF\\usaPdfs\\" + page.getRecords().get(i).getPdfFileName()), new File("d:\\pdf\\"+page.getRecords().get(i).getPdfFileName()));
            }
        }
        if(errList.size()>0)
        {

            savelErrToExcel(errList);
            var result=new Result<String>();
            result.setSuccess(false);
            result.setMsg("error");
            return  result;
        }
        return decide(true);
    }

    @GetMapping("pdfText")
    public Result pdfText() throws IOException {
        PDDocument doc = PDDocument.load(new FileInputStream("D://learn/电子书/Programming Rust.pdf"));

        PDFTextStripper text = new PDFTextStripper();
        //获取全文件的所有文本
        String FinalText = text.getText(doc);
        String[] arr=FinalText.split("\n");
        for(var i=0;i<arr.length;i++)
        {
            System.out.println("段落"+(i+1));
            System.out.println(arr[i]);
        }

        //关闭
        doc.close();
        return null;
    }

    /**
     * Describe: 数据字典删除
     * Param: sysDictType
     * Return: ModelAndView
     */
    @DeleteMapping("remove/{id}")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result remove(@PathVariable("id") String id) throws IOException {
        //var model=stdInfoService.getById(id);
        Boolean result = stdInfoService.removeById(id);
        if(result) {
            //删除磁盘上的加密文件
            Files.deleteIfExists(Paths.get(encryptPdfPath+"\\"+id));
            //Files.deleteIfExists(Paths.get(pdfPath+"\\"+model.getPdfFileName()));
            //删除solr对应对象
            HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
            try {
                client.deleteById(id);
                System.out.print(id + " 在solr索引中删除成功");
            } catch (SolrServerException e) {
                e.printStackTrace();
                return decide(false);
            } catch (IOException e) {
                e.printStackTrace();
                try {
                    client.rollback();
                    return decide(false);
                } catch (SolrServerException ex) {
                    ex.printStackTrace();
                    return decide(false);
                } catch (IOException ex) {
                    ex.printStackTrace();
                    return decide(false);
                }
            }
        }
        return decide(result);
    }
    @GetMapping("testBigFile")
    public Result testBigF()
    {
        try (RandomAccessFile file = new RandomAccessFile("E:\\learn\\电子书\\c++Primer Plus第6版中文版\\C++ Primer Plus  第6版  中文版.pdf", "r")) {
            FileChannel channel = file.getChannel();
            MappedByteBuffer buffer = channel.map(FileChannel.MapMode.READ_ONLY, 0, channel.size());
            buffer.load();

            // 处理读取的数据
            int idx=0;
            while (buffer.hasRemaining()) {
                byte[] arr=new byte[1024];
                buffer.get(arr,idx,1024);
                System.out.print(arr);
            }

            buffer.clear();
            channel.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Result.success();
    }

    /**
     * Describe: 个人资料
     * Param: null
     * Return: ModelAndView
     */
    @PostMapping("createsolrindex")
    @ApiOperation(value = "为所有标准创建或更新solr索引")
    public CResult<Boolean> createSolorIndex(@RequestParam String id) throws IOException {
        var data=stdInfoService.getById(id);
        if(data==null)
        {
            System.out.println("无效的编号");
            var result=new CResult<Boolean>();
            result.setSuccess(false);
            result.setMessage("无效的编号");
            return result;
        }
        SolrInputDocument doc = new SolrInputDocument();
        var fileExists=false;
        if(StringUtils.isNotBlank(data.getPdfFileName()))
        {
            pdfPath=mergePath(pdfPath);
            File file=new File(getCorrespondingPath(pdfPath,data.getPdfFileName())+"\\"+data.getPdfFileName());
            if(file.exists())
            {
                fileExists=true;
                var content=getText(file);
                doc.addField("content", content.getContent());
            }
        }
        if(!fileExists)
        {
            System.out.println("pdf文件不存在！");
            var result=new CResult<Boolean>();
            result.setSuccess(false);
            result.setMessage("pdf文件不存在");
            return result;
        }
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
        //新增或更新。新增文档类型都是SolrInputDocument
        doc.addField("id",data.getId());
        doc.addField("stdNo",data.getStdNo());
        doc.addField("stdOrgName",data.getStdOrgName());
        doc.addField("stdIdentification",data.getStdIdentification());
        doc.addField("stdLangugage",data.getStdLangugage());
        doc.addField("catetoryNo",data.getCatetoryNo());
        doc.addField("stdIcs",data.getStdIcs());
        doc.addField("implementationDate",data.getImplementationDate());
        doc.addField("stdStatus",data.getStdStatus());
        doc.addField("supersededStdNo",data.getSupersededStdNo());
        doc.addField("stdOcr",data.getStdOcr());
        doc.addField("stdChineseName",data.getStdChineseName());
        doc.addField("stdEnglishName",data.getStdEnglishName());
        doc.addField("securityClass",data.getSecurityClass());
        doc.addField("pubDept",data.getPubDept());
        doc.addField("advanceDept",data.getAdvanceDept());
        doc.addField("draftingUnit",data.getDraftingUnit());
        doc.addField("drafter",data.getDrafter());
        doc.addField("primaryCoverage",data.getPrimaryCoverage());
        doc.addField("alternateStdNo",data.getAlternateStdNo());
        doc.addField("pdfFileName", data.getPdfFileName());
        doc.addField("stdClass", data.getStdClass());
        try {
            UpdateResponse response = client.add(doc);
            System.out.println(String.format("solor数据更新：status = %s ; QTime = %s",response.getStatus(),response.getQTime()));
        } catch (Exception e) {
            e.printStackTrace();
            try {
                client.rollback();
            } catch (SolrServerException ex) {
                ex.printStackTrace();
                var result=new CResult<Boolean>();
                result.setSuccess(false);
                return result;
            } catch (IOException ex) {
                ex.printStackTrace();
                var result=new CResult<Boolean>();
                result.setSuccess(false);
                return result;
            }
        }
        //在Solr服务中，数据的写操作也是有事务的，WEB管理平台默认一次操作一次提交。
        try {
            client.commit();
            client.close();
        } catch (SolrServerException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return CResult.OK(true);
    }

    /**
     * 给所有的标准解析排序条件（从标准号中）
     *
     * @param start
     * @param rows
     * @return com.pearadmin.common.tools.CResult<?>
     * @throws
     */
    @GetMapping("updatesortcondition")
    @ApiOperation(value = "updatesortcondition")
    public CResult<?> updatesortcondition(
            @RequestParam(name = "start", defaultValue = "0",required = true) Integer start,
            @RequestParam(name = "rows", defaultValue = "15",required = true) Integer rows) throws ParseException, IOException, SolrServerException {
        var list = stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType, StandardTypeEnum.China).last("limit " + start + "," + rows));
        var result = new CResult<Boolean>();
        StdInfo stdInfo = null;
        try {
            for (var i = 0; i < list.size(); i++) {
                stdInfo = stdInfoService.AnalysisSortCondition(list.get(i));
                stdInfoService.updateById(stdInfo);
            }
            result.setSuccess(true);
            result.setResult(true);
            return result;
        } catch (Exception ex)
        {
            result.setSuccess(false);
            result.setResult(false);
            result.setMessage(ex.getStackTrace().toString());
            return result;
        }
    }

    /**
     * 给所有的标准解析排序条件（从标准号中）
     *
     * @param start
     * @param rows
     * @return com.pearadmin.common.tools.CResult<?>
     * @throws
     */
    @GetMapping("updatesortconditionifnull")
    @ApiOperation(value = "updatesortconditionifnull")
    public CResult<?> updatesortconditionifnull(
            @RequestParam(name = "start", defaultValue = "0",required = true) Integer start,
            @RequestParam(name = "rows", defaultValue = "15",required = true) Integer rows) throws ParseException, IOException, SolrServerException {
        var list = stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType, StandardTypeEnum.China).eq(StdInfo::getSortSerialNo,0).last("limit " + start + "," + rows));
        var result = new CResult<Boolean>();
        StdInfo stdInfo = null;
        try {
            for (var i = 0; i < list.size(); i++) {
                stdInfo = stdInfoService.AnalysisSortCondition(list.get(i));
                stdInfoService.updateById(stdInfo);
            }
            result.setSuccess(true);
            result.setResult(true);
            return result;
        } catch (Exception ex)
        {
            result.setSuccess(false);
            result.setResult(false);
            result.setMessage(ex.getStackTrace().toString());
            return result;
        }
    }

    /**
     * 自定义页码页容量生成solr索引
     *
     * @param start
     * @param rows
     * @return com.pearadmin.common.tools.CResult<?>
     * @throws
     */
    @GetMapping("customupdate")
    @ApiOperation(value = "customupdate")
    public CResult<?> customupdate(
                                 @RequestParam(name = "start", defaultValue = "0",required = true) Integer start,
                                 @RequestParam(name = "rows", defaultValue = "15",required = true) Integer rows) throws ParseException, IOException, SolrServerException {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).in(StdInfo::getStdClass,"国家军用标准","兵器行业标准","集团公司标准").last("limit "+start+","+rows));
        //先删除所有索引，不然更新不彻底
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
//        client.deleteByQuery("*:*");
//        client.commit();
        pdfPath=mergePath(pdfPath);
        for (StdInfo data:list)
        {
            //新增或更新。新增文档类型都是SolrInputDocument
            SolrInputDocument doc = new SolrInputDocument();
            var fileExists=false;
            if(StringUtils.isNotBlank(data.getPdfFileName()))
            {
                File file=new File(getCorrespondingPath(pdfPath,data.getPdfFileName())+"\\"+data.getPdfFileName());
                if(file.exists())
                {
                    fileExists=true;
                    var content=getText(file);
                    doc.addField("content", content.getContent());
                }
            }
            if(!fileExists)
            {
                continue;
            }
            doc.addField("id", data.getId());
            doc.addField("stdNo",data.getStdNo());
            doc.addField("stdOrgName",data.getStdOrgName());
            doc.addField("stdIdentification",data.getStdIdentification());
            doc.addField("stdLangugage",data.getStdLangugage());
            doc.addField("catetoryNo",data.getCatetoryNo());
            doc.addField("stdIcs",data.getStdIcs());
            doc.addField("implementationDate",data.getImplementationDate());
            doc.addField("stdStatus",data.getStdStatus());
            doc.addField("supersededStdNo",data.getSupersededStdNo());
            doc.addField("stdOcr",data.getStdOcr());
            doc.addField("stdChineseName",data.getStdChineseName());
            doc.addField("stdEnglishName",data.getStdEnglishName());
            doc.addField("securityClass",data.getSecurityClass());
            doc.addField("pubDept",data.getPubDept());
            doc.addField("advanceDept",data.getAdvanceDept());
            doc.addField("draftingUnit",data.getDraftingUnit());
            doc.addField("drafter",data.getDrafter());
            doc.addField("primaryCoverage",data.getPrimaryCoverage());
            doc.addField("alternateStdNo",data.getAlternateStdNo());
            doc.addField("pdfFileName", data.getPdfFileName());
            doc.addField("stdClass", data.getStdClass());
            try {
                UpdateResponse response = client.add(doc);
                System.out.println(String.format("solor数据更新：status = %s ; QTime = %s", response.getStatus(), response.getQTime()));
            } catch (Exception e) {
                e.printStackTrace();
                try {
                    client.rollback();
                } catch (SolrServerException ex) {
                    ex.printStackTrace();
                    var result = new CResult<Boolean>();
                    result.setSuccess(false);
                    return result;
                } catch (IOException ex) {
                    ex.printStackTrace();
                    var result = new CResult<Boolean>();
                    result.setSuccess(false);
                    return result;
                }
            }
            //在Solr服务中，数据的写操作也是有事务的，WEB管理平台默认一次操作一次提交。
            try {
                client.commit();
            } catch (SolrServerException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }        }
        client.close();
        return CResult.OK(true);
    }

        /**
         * Describe: 为所有标准创建或更新solr索引
         * Param: null
         */
    @GetMapping("updateallsolrindex")
    @ApiOperation(value = "为所有标准创建或更新solr索引")
    public CResult<Boolean> updateAllSolorIndex() throws IOException, SolrServerException {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).in(StdInfo::getStdClass,"国家军用标准","兵器行业标准","集团公司标准"));
        //先删除所有索引，不然更新不彻底
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
        client.deleteByQuery("*:*");
        client.commit();
        pdfPath=mergePath(pdfPath);
        for (StdInfo data:list)
        {
            //新增或更新。新增文档类型都是SolrInputDocument
            SolrInputDocument doc = new SolrInputDocument();
            var fileExists=false;
            if(StringUtils.isNotBlank(data.getPdfFileName()))
            {
                File file=new File(getCorrespondingPath(pdfPath,data.getPdfFileName())+"\\"+data.getPdfFileName());
                if(file.exists())
                {
                    fileExists=true;
                    var content=getText(file);
                    doc.addField("content", content.getContent());
                }
            }
            if(!fileExists)
            {
                continue;
            }
            doc.addField("id", data.getId());
            doc.addField("stdNo",data.getStdNo());
            doc.addField("stdOrgName",data.getStdOrgName());
            doc.addField("stdIdentification",data.getStdIdentification());
            doc.addField("stdLangugage",data.getStdLangugage());
            doc.addField("catetoryNo",data.getCatetoryNo());
            doc.addField("stdIcs",data.getStdIcs());
            doc.addField("implementationDate",data.getImplementationDate());
            doc.addField("stdStatus",data.getStdStatus());
            doc.addField("supersededStdNo",data.getSupersededStdNo());
            doc.addField("stdOcr",data.getStdOcr());
            doc.addField("stdChineseName",data.getStdChineseName());
            doc.addField("stdEnglishName",data.getStdEnglishName());
            doc.addField("securityClass",data.getSecurityClass());
            doc.addField("pubDept",data.getPubDept());
            doc.addField("advanceDept",data.getAdvanceDept());
            doc.addField("draftingUnit",data.getDraftingUnit());
            doc.addField("drafter",data.getDrafter());
            doc.addField("primaryCoverage",data.getPrimaryCoverage());
            doc.addField("alternateStdNo",data.getAlternateStdNo());
            doc.addField("pdfFileName", data.getPdfFileName());
            doc.addField("stdClass", data.getStdClass());
            try {
                UpdateResponse response = client.add(doc);
                System.out.println(String.format("solor数据更新：status = %s ; QTime = %s", response.getStatus(), response.getQTime()));
            } catch (Exception e) {
                e.printStackTrace();
                try {
                    client.rollback();
                } catch (SolrServerException ex) {
                    ex.printStackTrace();
                    var result = new CResult<Boolean>();
                    result.setSuccess(false);
                    return result;
                } catch (IOException ex) {
                    ex.printStackTrace();
                    var result = new CResult<Boolean>();
                    result.setSuccess(false);
                    return result;
                }
            }
            //在Solr服务中，数据的写操作也是有事务的，WEB管理平台默认一次操作一次提交。
            try {
                client.commit();
            } catch (SolrServerException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
           }        }
        client.close();
        return CResult.OK(true);
    }

    /**
     * Describe: 为所有标准创建或更新solr索引
     * updateallsolrindex相比，本方法是真实的update
     * Param: null
     */
    @PostMapping("updateallsolrindex_stdOrgName")
    @ApiOperation(value = "为所有标准创建或更新solr索引")
    public CResult<Boolean> updateallsolrindex_stdOrgName() throws IOException, SolrServerException {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China));
        //先删除所有索引，不然更新不彻底
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
        SolrQuery solrQuery = new SolrQuery();
        //设置默认搜索域
        solrQuery.set("df", "content");
        solrQuery.setQuery("*:*");
        solrQuery.set("q.op", "AND");
        solrQuery.set("defType","edismax");
        solrQuery.set("mm","90%");
        solrQuery.setFacetLimit(-1);
        solrQuery.setStart(0);
        solrQuery.setRows(5000);
        QueryResponse response;
        try {
            response = client.query(solrQuery);
            SolrDocumentList documentList = response.getResults();
            var total=documentList.getNumFound();
            for(SolrDocument d:documentList)
            {
                try {
                    SolrInputDocument doc = new SolrInputDocument();
                    doc.addField("id", d.getFieldValue("id"));
                    doc.addField("stdNo",d.getFieldValue("stdNo"));
                    doc.addField("stdOrgName",d.getFieldValue("stdOrgName"));
                    doc.addField("stdIdentification",d.getFieldValue("stdIdentification"));
                    doc.addField("stdLangugage",d.getFieldValue("stdLangugage"));
                    doc.addField("catetoryNo",d.getFieldValue("catetoryNo"));
                    doc.addField("stdIcs",d.getFieldValue("stdIcs"));
                    doc.addField("implementationDate",d.getFieldValue("implementationDate"));
                    doc.addField("stdStatus",d.getFieldValue("stdStatus"));
                    doc.addField("supersededStdNo",d.getFieldValue("supersededStdNo"));
                    doc.addField("stdOcr",d.getFieldValue("stdOcr"));
                    doc.addField("stdChineseName",d.getFieldValue("stdChineseName"));
                    doc.addField("stdEnglishName",d.getFieldValue("stdEnglishName"));
                    doc.addField("securityClass",d.getFieldValue("securityClass"));
                    doc.addField("pubDept",d.getFieldValue("pubDept"));
                    doc.addField("advanceDept",d.getFieldValue("advanceDept"));
                    doc.addField("draftingUnit",d.getFieldValue("draftingUnit"));
                    doc.addField("drafter",d.getFieldValue("drafter"));
                    doc.addField("primaryCoverage",d.getFieldValue("primaryCoverage"));
                    doc.addField("alternateStdNo",d.getFieldValue("alternateStdNo"));
                    doc.addField("pdfFileName", d.getFieldValue("pdfFileName"));
                    doc.addField("stdClass",d.getFieldValue("stdClass"));
                    doc.addField("content",d.getFieldValue("content").toString());
                    client.add( doc);
                    client.commit();
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                    try {
                        client.rollback();
                    } catch (SolrServerException ex) {
                        ex.printStackTrace();
                        var result = new CResult<Boolean>();
                        result.setSuccess(false);
                        return result;
                    } catch (IOException ex) {
                        ex.printStackTrace();
                        var result = new CResult<Boolean>();
                        result.setSuccess(false);
                        return result;
                    }
                }
            }
        }
        catch (Exception e) {
        }
        client.close();
       return CResult.OK(true);
    }


    /**
     * Describe: 为明文存放路径下的标准文件创建或更新solr索引
     * updateallsolrindex相比，本方法是真实的update
     * Param: null
     */
    @PostMapping("updateallsolrindexByRealFile")
    @ApiOperation(value = "为所有标准创建或更新solr索引")
    public CResult<Boolean> updateallsolrindexByRealFile() throws IOException, SolrServerException {
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
        //pdfPath = mergePath(pdfPath);
        //var rootPath="D:\\allPDF\\pdfs";
        var rootPath=pdfPath;
        File root = new File(rootPath);
        System.out.println("---------------------------------------------");
        System.out.println(root.getAbsolutePath());
        File[] directores = root.listFiles();
        try {
            if (directores != null) {
                for (File dir : directores) {
                    //判断这个File是否是一个文件，是：
                    if (dir.isDirectory() && (dir.getName().startsWith("GJB") || dir.getName().startsWith("WJ") || dir.getName().startsWith("QCNG"))) {
                        File[] files = dir.listFiles();
                        if (files != null && files.length > 0) {
                            var idx = 0;
                            System.out.println("--------------------------------------------------");
                            System.out.println("files length:" + files.length);
                            System.out.println("--------------------------------------------------");
                            for (File f : files) {
                                idx++;
                                System.out.println("----------------------------检查索引:" + f.getName() + "-------------------------");
                                System.out.println("----------------------------" + idx + "-------------------------");
                                var query = new LambdaQueryWrapper<StdInfo>();
                                System.out.println(f.getName());
                                query.eq(StdInfo::getPdfFileName, f.getName());
                                var data = stdInfoService.getOne(query);
                                if (data != null) {
                                    //先删除索引（指定id），不然更新不彻底
                                    try {
                                        client.deleteById(data.getId());
                                        client.commit();
                                    } catch (SolrServerException e) {
                                        e.printStackTrace();
                                    } catch (IOException e) {
                                        e.printStackTrace();
                                    }
//                                        SolrQuery solrQuery = new SolrQuery();
//                                        //设置默认搜索域
//                                        solrQuery.set("df", "stdOrgName");
//                                        solrQuery.setQuery("stdOrgName:" + data.getStdOrgName());
//                                        QueryResponse response;
//                                           try {
//                                            response = client.query(solrQuery);
//                                            System.out.println(response.getResults());
//                                            //响应头
//                                            SolrDocumentList documentList = response.getResults();
//                                            if (documentList.getNumFound() == 0) {
                                     System.out.println("----------------------------创建索引-------------------------");
                                    //更新数据
                                    //新增或更新。新增文档类型都是SolrInputDocument
                                    SolrInputDocument doc = new SolrInputDocument();
                                    var content = getText(f);
                                    doc.addField("content", content.getContent());
                                    doc.addField("id", data.getId());
                                    doc.addField("stdNo", data.getStdNo());
                                    doc.addField("stdOrgName", data.getStdOrgName());
                                    doc.addField("stdIdentification", data.getStdIdentification());
                                    doc.addField("stdLangugage", data.getStdLangugage());
                                    doc.addField("catetoryNo", data.getCatetoryNo());
                                    doc.addField("stdIcs", data.getStdIcs());
                                    doc.addField("implementationDate", data.getImplementationDate());
                                    doc.addField("stdStatus", data.getStdStatus());
                                    doc.addField("supersededStdNo", data.getSupersededStdNo());
                                    doc.addField("stdOcr", data.getStdOcr());
                                    doc.addField("stdChineseName", data.getStdChineseName());
                                    doc.addField("stdEnglishName", data.getStdEnglishName());
                                    doc.addField("securityClass", data.getSecurityClass());
                                    doc.addField("pubDept", data.getPubDept());
                                    doc.addField("advanceDept", data.getAdvanceDept());
                                    doc.addField("draftingUnit", data.getDraftingUnit());
                                    doc.addField("drafter", data.getDrafter());
                                    doc.addField("primaryCoverage", data.getPrimaryCoverage());
                                    doc.addField("alternateStdNo", data.getAlternateStdNo());
                                    doc.addField("pdfFileName", data.getPdfFileName());
                                    doc.addField("stdClass", data.getStdClass());
                                    try {
                                        UpdateResponse udpResponse = client.add(doc);
                                        System.out.println(String.format("solor数据更新：status = %s ; QTime = %s", udpResponse.getStatus(), udpResponse.getQTime()));
                                        data.setHasSolrIndex(1);
                                        stdInfoService.updateById(data);
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        try {
                                            client.rollback();
                                            data.setHasSolrIndex(0);
                                            stdInfoService.updateById(data);
                                        } catch (SolrServerException ex) {
                                            ex.printStackTrace();
                                            var result = new CResult<Boolean>();
                                            result.setSuccess(false);
                                            return result;
                                        } catch (IOException ex) {
                                            ex.printStackTrace();
                                            var result = new CResult<Boolean>();
                                            result.setSuccess(false);
                                            return result;
                                        }
                                    }
                                    //在Solr服务中，数据的写操作也是有事务的，WEB管理平台默认一次操作一次提交。
                                    try {
                                        client.commit();
                                    } catch (SolrServerException e) {
                                        data.setHasSolrIndex(0);
                                        stdInfoService.updateById(data);
                                        e.printStackTrace();
                                    } catch (IOException e) {
                                        data.setHasSolrIndex(0);
                                        stdInfoService.updateById(data);
                                        e.printStackTrace();
                                    }
                                }
                            }
                            //先删除索引（指定id），不然更新不彻底
                            // client.deleteById(data.getId());
                            // client.commit();
                        }
                        // return CResult.OK(true);
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        client.close();
        return CResult.OK(true);
    }

//
//    @PostMapping("updatesolrindex")
//    @ApiOperation(value = "更新指定编号的记录")
//    //包括新增和更新。 主键一致-更新。主键不存在-新增
//    public  CResult<Boolean> update(@RequestParam(name="ids", required = true) String strIds) throws IOException, SolrServerException {
//        if(StringUtils.isBlank(strIds))
//        {
//            var result = new CResult<Boolean>();
//            result.setSuccess(false);
//            return result;
//        }
//        var idArr=strIds.split(",");
//        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().in(StdInfo::getId,idArr));
//        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
//        //先删除索引（指定id），不然更新不彻底
//        client.deleteById(list.stream().map(StdInfo::getId).collect(Collectors.toList()));
//        client.commit();
//        //更新数据
//        pdfPath=mergePath(pdfPath);
//        for (StdInfo data:list)
//        {
//            //新增或更新。新增文档类型都是SolrInputDocument
//            SolrInputDocument doc = new SolrInputDocument();
//            var fileExists=false;
//            if(StringUtils.isNotBlank(data.getPdfFileName()))
//            {
//                File file=new File(pdfPath+"\\"+data.getPdfFileName());
//                if(file.exists())
//                {
//                    fileExists=true;
//                    var content=getText(file);
//                    doc.addField("content", content.getContent());
//                }
//            }
//            if(!fileExists)
//            {
//                continue;
//            }
//            doc.addField("id", data.getId());
//            doc.addField("stdNo",data.getStdNo());
//            doc.addField("stdOrgName",data.getStdOrgName());
//            doc.addField("stdIdentification",data.getStdIdentification());
//            doc.addField("stdLangugage",data.getStdLangugage());
//            doc.addField("catetoryNo",data.getCatetoryNo());
//            doc.addField("stdIcs",data.getStdIcs());
//            doc.addField("implementationDate",data.getImplementationDate());
//            doc.addField("stdStatus",data.getStdStatus());
//            doc.addField("supersededStdNo",data.getSupersededStdNo());
//            doc.addField("stdOcr",data.getStdOcr());
//            doc.addField("stdChineseName",data.getStdChineseName());
//            doc.addField("stdEnglishName",data.getStdEnglishName());
//            doc.addField("securityClass",data.getSecurityClass());
//            doc.addField("pubDept",data.getPubDept());
//            doc.addField("advanceDept",data.getAdvanceDept());
//            doc.addField("draftingUnit",data.getDraftingUnit());
//            doc.addField("drafter",data.getDrafter());
//            doc.addField("primaryCoverage",data.getPrimaryCoverage());
//            doc.addField("alternateStdNo",data.getAlternateStdNo());
//            doc.addField("pdfFileName", data.getPdfFileName());
//            doc.addField("stdClass", data.getStdClass());
//            try {
//                UpdateResponse response = client.add(doc);
//                System.out.println(String.format("solor数据更新：status = %s ; QTime = %s", response.getStatus(), response.getQTime()));
//            } catch (Exception e) {
//                e.printStackTrace();
//                try {
//                    client.rollback();
//                } catch (SolrServerException ex) {
//                    ex.printStackTrace();
//                    var result = new CResult<Boolean>();
//                    result.setSuccess(false);
//                    return result;
//                } catch (IOException ex) {
//                    ex.printStackTrace();
//                    var result = new CResult<Boolean>();
//                    result.setSuccess(false);
//                    return result;
//                }
//            }
//            //在Solr服务中，数据的写操作也是有事务的，WEB管理平台默认一次操作一次提交。
//            try {
//                client.commit();
//            } catch (SolrServerException e) {
//                e.printStackTrace();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//
//        }
//        client.close();
//        return CResult.OK(true);
//    }

    /**
     * Describe: solrsearch
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("solrsearch")
    @ApiOperation(value = "solr搜索")
    public CResult<?> solrsearch(@RequestParam String key,
                                 @RequestParam String value,
                                 @RequestParam(name = "start", defaultValue = "0",required = true) Integer start,
                                 @RequestParam(name = "rows", defaultValue = "15",required = true) Integer rows) throws ParseException, IOException {
        if(!security())
        {
            return CResult.error("");
        }
        var result=new CResult<>();
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
        SolrQuery solrQuery = new SolrQuery();
       //设置默认搜索域
        solrQuery.setQuery(value);
        solrQuery.set("defType","edismax");
        // 设置搜索字段（根据实际字段名修改）
        //solrQuery.set("qf", "title^2 content"); // 标题权重更高
        solrQuery.set("qf", "content");
        // 关键配置：提升短语匹配优先级
        solrQuery.set(DisMaxParams.PF, "content");// 对content字段进行短语匹配
        solrQuery.set(DisMaxParams.PS, 0); // 要求零位置容差（精确匹配）
        // 显式添加高权重精确匹配（优先级最高）
        String exactPhrase = "\"" + value.replace("\"", "\\\"") + "\"";
        solrQuery.add("bq", "content:" + exactPhrase + "^100"); // 权重提升100倍

//        solrQuery.set("df", key);
//        solrQuery.setQuery(key+":"+value);
//        solrQuery.set("q.op", "AND");
//        solrQuery.set("mm","90%");
        solrQuery.setHighlight(true);
        solrQuery.addHighlightField("content");
        solrQuery.setHighlightSnippets(10000);
        solrQuery.setFacetLimit(-1);
        solrQuery.setStart(start);
        solrQuery.setRows(rows);
//        solrQuery.set("df", key);
//        solrQuery.setQuery(key+":"+value);
//        solrQuery.set("q.op", "AND");
//        solrQuery.set("defType","edismax");
//        solrQuery.set("mm","90%");
//        solrQuery.setHighlight(true);
//        solrQuery.addHighlightField("content");
//        solrQuery.setHighlightSnippets(10000);
//        solrQuery.setFacetLimit(-1);
//        solrQuery.setStart(start);
//        solrQuery.setRows(rows);
        QueryResponse response;
        try {
            response = client.query(solrQuery);
            System.out.println(response.getResults());
            //响应头
//            NamedList<Object> namedList = response.getHeader();
//            Iterator iterator = namedList.iterator();
//            while(iterator.hasNext()){
//                System.out.println(iterator.next());
//            }
            SolrDocumentList documentList = response.getResults();
            var total=documentList.getNumFound();
            System.out.println("总计数据行数"+documentList.getNumFound());
            ArrayList<String> ids=new ArrayList<>();
            for(SolrDocument solrDocument:documentList){
                System.out.print("id = "+solrDocument.get("id"));
                ids.add(solrDocument.get("id").toString());
                // System.out.println("; primaryCoverage = "+solrDocument.get("primaryCoverage"));
            }
            if(ids.size()>0)
            {
               HashMap<String,Integer> isCollectedMap= (HashMap<String, Integer>) stdInfoService.IsCollected(ids);
               if(isCollectedMap.size()>0)
               {
                   for(SolrDocument solrDocument:documentList){
                       String id=solrDocument.get("id").toString();
                       if(isCollectedMap.containsKey(id))
                       {
                           solrDocument.addField("isCollected",isCollectedMap.get(id));
                       }
                   }
               }
            }
            var highliting=response.getHighlighting();
            var obj=new com.alibaba.fastjson.JSONObject();
            obj.put("datalist",documentList);
            obj.put("highlight",highliting);
            obj.put("total",total);
            result.setResult(obj);
        } catch (SolrServerException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }


    /**
     * Describe: 全文检索--检索标题版
     * Param: start 不是当前页码，是记录的起始数，如 第一页=0， 第二页=30，第三页=60 ...
     * Return: ModelAndView
     */
    @GetMapping("solrtitlesearch")
    @ApiOperation(value = "solr搜索标题")
    public CResult<?> solrtitlesearch(@RequestParam String key,
                                 @RequestParam String value,
                                 @RequestParam(name = "start", defaultValue = "0",required = true) Integer start,
                                 @RequestParam(name = "rows", defaultValue = "15",required = true) Integer rows) throws ParseException, IOException {
        if (!security()) {
            return CResult.error("");
        }
        //先从数据库中检索标题配置的记录
        var query = new ChongQueryWrapper<StdInfo>();
        query.eq("std_type", StandardTypeEnum.China);
        String sortSql = "";
        if (StringUtils.isNotBlank(value)) {
            var keys = value;
            while (keys.contains("  ")) {
                keys = keys.replaceFirst("  ", " ");
            }
            var keyArr = keys.split(" ");
            for (String itemKey : keyArr) {
                if (StringUtils.isNotBlank(itemKey)) {
                    query.and(foo -> foo.like("std_org_Name", itemKey));
                }
            }
        }
        //query.and(q -> {q.eq("std_class", "国家军用标准").or().eq("std_class", "兵器行业标准").or().eq("std_class", "集团公司标准");});
        query.eq("std_class_int",1);
        query.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_char").orderByAsc("sort_year");
        //只返回id，并且是不重复的
        query.select("DISTINCT id,sort_identifaction,sort_serial_no,sort_number,sort_char,sort_year");
        var page = stdInfoMapper.selectPage(new Page<StdInfo>((start / rows + 1), rows), query);
        var total=page.getTotal();
        var stdInfoList=page.getRecords();
        var rowCount=stdInfoList.size();
        var condition="";
        var idx=stdInfoList.size()*5+10;
        var ids2="";
        for(var i=0;i<stdInfoList.size();i++)
        {
            condition+="|| id:"+stdInfoList.get(i).getId()+"^"+idx;
            ids2+=","+stdInfoList.get(i).getId();
            idx-=5;
        }
        if(StringUtils.isNotBlank(condition) && condition.startsWith("||"))
        {
            condition=condition.substring(2);
        }
        condition+="|| content:"+value;
        System.out.println("curr page is:"+condition);
        var result=new CResult<>();
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
        SolrQuery solrQuery = new SolrQuery();
        //设置默认搜索域
        solrQuery.set("df", "content");
        solrQuery.setQuery(condition);//id 的权重是9，高于content的1
        //solrQuery.set("q.op", "AND");
        //solrQuery.set("defType","edismax");
        //solrQuery.set("mm","90%");
        solrQuery.setHighlight(true);
        solrQuery.addHighlightField("content");
        solrQuery.setHighlightSnippets(10000);
        solrQuery.setFacetLimit(-1);
        solrQuery.setStart(0);
        solrQuery.setRows(rowCount);
        QueryResponse response;
        try {
            response = client.query(solrQuery);
            System.out.println(response.getResults());
            //响应头
//            NamedList<Object> namedList = response.getHeader();
//            Iterator iterator = namedList.iterator();
//            while(iterator.hasNext()){
//                System.out.println(iterator.next());
//            }
            SolrDocumentList documentList = response.getResults();
            //var total=documentList.getNumFound();
            System.out.println("总计数据行数"+documentList.getNumFound());
            ArrayList<String> ids=new ArrayList<>();
            for(SolrDocument solrDocument:documentList){
                System.out.print("id = "+solrDocument.get("id"));
                ids.add(solrDocument.get("id").toString());
                // System.out.println("; primaryCoverage = "+solrDocument.get("primaryCoverage"));
            }
            if(ids.size()>0)
            {
                HashMap<String,Integer> isCollectedMap= (HashMap<String, Integer>) stdInfoService.IsCollected(ids);
                if(isCollectedMap.size()>0)
                {
                    for(SolrDocument solrDocument:documentList){
                        String id=solrDocument.get("id").toString();
                        if(isCollectedMap.containsKey(id))
                        {
                            solrDocument.addField("isCollected",isCollectedMap.get(id));
                        }
                    }
                }
            }
            var highliting=response.getHighlighting();
            var obj=new com.alibaba.fastjson.JSONObject();
            obj.put("datalist",documentList);
            obj.put("highlight",highliting);
            obj.put("total",total);
            result.setResult(obj);
        } catch (SolrServerException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    @PostMapping("deleteRecordAndIndex")
    @ApiOperation(value = "删除")
    public CResult<Boolean> deleteRecordAndIndex(@RequestParam(name="data",required = true) String data){
        var result=new CResult<Boolean>();
        var idArr= Arrays.asList(data.split(","));
        if(idArr.size()>0) {
            HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
            try {
                client.deleteById(idArr);
                System.out.print(data + " 在solr索引中删除成功");
            } catch (SolrServerException e) {
                e.printStackTrace();
                result.setSuccess(false);
                result.setResult(false);
                result.setMessage(e.getMessage());
                return result;
            } catch (IOException e) {
                e.printStackTrace();
                try {
                    client.rollback();
                    result.setSuccess(false);
                    result.setResult(false);
                    result.setMessage(e.getMessage());
                    return result;
                } catch (SolrServerException ex) {
                    ex.printStackTrace();
                    result.setSuccess(false);
                    result.setResult(false);
                    result.setMessage(e.getMessage());
                    return result;
                } catch (IOException ex) {
                    ex.printStackTrace();
                    result.setSuccess(false);
                    result.setResult(false);
                    result.setMessage(e.getMessage());
                    return result;
                }
            }
            try {
                client.commit();
                stdInfoService.removeByIds(idArr);
                for(var i=0;i<idArr.size();i++)
                {
                    deleteEncryptFile(idArr.get(i));
                }
                result.setSuccess(true);
                result.setResult(true);
                result.setMessage("");
            } catch (SolrServerException e) {
                e.printStackTrace();
                result.setSuccess(false);
                result.setResult(false);
                result.setMessage(e.getMessage());
                return result;
            } catch (IOException e) {
                e.printStackTrace();
                result.setSuccess(false);
                result.setResult(false);
                result.setMessage(e.getMessage());
                return result;
            }
        }
        else
        {
            result.setSuccess(false);
            result.setResult(false);
            result.setMessage("请输入要删除的记录编号！");
        }
        return result;
    }

    @PostMapping("deleteAll")
    @ApiOperation(value = "删除所有标准")
    public CResult<Boolean> deleteAll(){
        var result=new CResult<Boolean>();
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China));
        var idArr=list.stream().map(StdInfo::getId).collect(Collectors.toList());
        if(idArr.size()>0) {
            HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
            try {
                client.deleteById(idArr);
                System.out.print(idArr + " 在solr索引中删除成功");
            } catch (SolrServerException e) {
                e.printStackTrace();
                result.setSuccess(false);
                result.setResult(false);
                result.setMessage(e.getMessage());
                return result;
            } catch (IOException e) {
                e.printStackTrace();
                try {
                    client.rollback();
                    result.setSuccess(false);
                    result.setResult(false);
                    result.setMessage(e.getMessage());
                    return result;
                } catch (SolrServerException ex) {
                    ex.printStackTrace();
                    result.setSuccess(false);
                    result.setResult(false);
                    result.setMessage(e.getMessage());
                    return result;
                } catch (IOException ex) {
                    ex.printStackTrace();
                    result.setSuccess(false);
                    result.setResult(false);
                    result.setMessage(e.getMessage());
                    return result;
                }
            }
            try {
                client.commit();
                stdInfoService.removeByIds(idArr);
                for(var i=0;i<idArr.size();i++)
                {
                    deleteEncryptFile(idArr.get(i));
                }
                result.setSuccess(true);
                result.setResult(true);
                result.setMessage("");
            } catch (SolrServerException e) {
                e.printStackTrace();
                result.setSuccess(false);
                result.setResult(false);
                result.setMessage(e.getMessage());
                return result;
            } catch (IOException e) {
                e.printStackTrace();
                result.setSuccess(false);
                result.setResult(false);
                result.setMessage(e.getMessage());
                return result;
            }
        }
        else
        {
            result.setSuccess(false);
            result.setResult(false);
            result.setMessage("请输入要删除的记录编号！");
        }
        return result;
    }

    @GetMapping("solrdelete")
    @ApiOperation(value = "删除")
    public void delete(@RequestParam String id){
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
        try {
            client.deleteById(id);
            System.out.print(id+" 删除成功");
        } catch (SolrServerException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
            try {
                client.rollback();
            } catch (SolrServerException ex) {
                ex.printStackTrace();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        try {
            client.commit();
        } catch (SolrServerException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @PostMapping("updatesolrindex")
    @ApiOperation(value = "更新指定编号的记录")
    //包括新增和更新。 主键一致-更新。主键不存在-新增
    public  CResult<Boolean> update(@RequestParam(name="ids", required = true) String strIds) throws IOException, SolrServerException {
        if(StringUtils.isBlank(strIds))
        {
            var result = new CResult<Boolean>();
            result.setSuccess(false);
            return result;
        }
        var idArr=strIds.split(",");
        //只把没有进行solr索引的标准进行索引创建
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().in(StdInfo::getId,idArr).eq(StdInfo::getHasSolrIndex,0));
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_china").build();
        //先删除索引（指定id），不然更新不彻底
        client.deleteById(list.stream().map(StdInfo::getId).collect(Collectors.toList()));
        client.commit();
        //更新数据
        pdfPath=mergePath(pdfPath);
        for (StdInfo data:list)
        {
            //新增或更新。新增文档类型都是SolrInputDocument
            SolrInputDocument doc = new SolrInputDocument();
            var fileExists=false;
            if(StringUtils.isNotBlank(data.getPdfFileName()))
            {
                File file=new File(getCorrespondingPath(pdfPath,data.getPdfFileName())+"\\"+data.getPdfFileName());
                if(file.exists())
                {
                    fileExists=true;
                    var content=getText(file);
                    doc.addField("content", content.getContent());
                }
            }
            if(!fileExists)
            {
                continue;
            }
            doc.addField("id", data.getId());
            doc.addField("stdNo",data.getStdNo());
            doc.addField("stdOrgName",data.getStdOrgName());
            doc.addField("stdIdentification",data.getStdIdentification());
            doc.addField("stdLangugage",data.getStdLangugage());
            doc.addField("catetoryNo",data.getCatetoryNo());
            doc.addField("stdIcs",data.getStdIcs());
            doc.addField("implementationDate",data.getImplementationDate());
            doc.addField("stdStatus",data.getStdStatus());
            doc.addField("supersededStdNo",data.getSupersededStdNo());
            doc.addField("stdOcr",data.getStdOcr());
            doc.addField("stdChineseName",data.getStdChineseName());
            doc.addField("stdEnglishName",data.getStdEnglishName());
            doc.addField("securityClass",data.getSecurityClass());
            doc.addField("pubDept",data.getPubDept());
            doc.addField("advanceDept",data.getAdvanceDept());
            doc.addField("draftingUnit",data.getDraftingUnit());
            doc.addField("drafter",data.getDrafter());
            doc.addField("primaryCoverage",data.getPrimaryCoverage());
            doc.addField("alternateStdNo",data.getAlternateStdNo());
            doc.addField("pdfFileName", data.getPdfFileName());
            doc.addField("stdClass", data.getStdClass());
            try {
                UpdateResponse response = client.add(doc);
                System.out.println(String.format("solor数据更新：status = %s ; QTime = %s", response.getStatus(), response.getQTime()));
            } catch (Exception e) {
                e.printStackTrace();
                try {
                    client.rollback();
                } catch (SolrServerException ex) {
                    ex.printStackTrace();
                    var result = new CResult<Boolean>();
                    result.setSuccess(false);
                    return result;
                } catch (IOException ex) {
                    ex.printStackTrace();
                    var result = new CResult<Boolean>();
                    result.setSuccess(false);
                    return result;
                }
            }
            //在Solr服务中，数据的写操作也是有事务的，WEB管理平台默认一次操作一次提交。
            try {
                client.commit();
            } catch (SolrServerException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
        client.close();
        return CResult.OK(true);
    }

    /**
     * Describe: 个人资料
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("index1")
    @ApiOperation(value = "搜索首页")
    public ModelAndView index1(Model model) {
        SysUser sysUser = UserContext.currentUser();
        //model.addAttribute("logs", sysLogService.selectTopLoginLog(sysUser.getUsername()));
        return jumpPage(MODULE_PATH+ "searchSolr");
    }

    /**
     * Describe: 个人资料
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("index")
    @ApiOperation(value = "搜索首页")
    public ModelAndView center(Model model) {
        SysUser sysUser = UserContext.currentUser();
        //model.addAttribute("logs", sysLogService.selectTopLoginLog(sysUser.getUsername()));
        return jumpPage(MODULE_PATH+ "main");
    }

    /**
     * Describe: 个人资料
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("db")
    @ApiOperation(value = "搜索首页")
    public ModelAndView dbIndex(Model model) throws ParseException {
        return jumpPage(MODULE_PATH+ "searchDB");
    }

    /**
     * 获取文本提取
     *
     * @param document
     * @param writer
     * @throws IOException
     */
    public void getTextStripper(PDDocument document, Writer writer)
            throws IOException {
        PDFTextStripper textStripper = new PDFTextStripper();
        textStripper.writeText(document, writer);
    }

    /**
     * 提取文本内容
     * @param  file 加载文档的路径
     * @return FileContent
     * @throws IOException
     */
    public FileContent getText(File file) throws IOException {
        String textString = "";
        PDDocument document = PDDocument.load(file);
        //将提取出来的字节流转换为字符流进行显示
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(out);
        getTextStripper(document, writer);
        document.close();
        out.close();
        writer.close();
        byte[] con = out.toByteArray();
        textString = new String(con);
        var fileContent=new FileContent();
        fileContent.setContent(textString);
        fileContent.setBinContent(con);
        return fileContent;
    }

    /**
     *  excel 判断哪些上传失败的
     */
    @PostMapping("/filemark")
    @ApiOperation(value = "excel文件上传")
    public CResult<?> excelMark(HttpServletRequest req) throws Exception {
        var result=new CResult<Boolean>();
        result.setResult(true);
        result.setSuccess(true);
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) req;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        try {
            //创建
            XSSFWorkbook book = new XSSFWorkbook(file.getInputStream());
            XSSFSheet sheet = book.getSheetAt(0);
            // add some validation here
            // parse data
            var positionRow=0;
            var positionCol=0;
            int cols;
            for (int i = positionRow; i < sheet.getLastRowNum(); i++) {
                //Thread.sleep(2000);
                XSSFRow row = sheet.getRow(i + 1); // 表头不算
                var stdNo = Tool.getCellStringValue(row.getCell(0));
                System.out.println("sdtNo:" + stdNo);
                var hasPdf = Tool.getCellStringValue(row.getCell(3));
                if (hasPdf.trim().equals("有全文"))
                {
                    continue;
                }
                stdNo= stdNo.trim();
                var model= stdInfoService.getOne(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdNo,stdNo).last("limit 1"));
                if(model!=null)
                {
                    var exists=false;
                    if(model.getPdfIsExists().equals("1"))
                    {
                        exists=true;
                        System.out.println("sdtNo:" + stdNo+";有pdf文件");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
            result.setResult(false);
            result.setSuccess(false);
            result.setMessage("发生错误，"+ex.getMessage());
            return result;
        }
        return result;
    }


    /**
     *  excel文件上传
     */
    @PostMapping("/file-upload")
    @ApiOperation(value = "excel文件上传")
    public CResult<?> fileUpload(HttpServletRequest req) throws Exception,IOException {
        var result=new CResult<Boolean>();
        result.setResult(true);
        result.setSuccess(true);
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) req;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        var errList=new ArrayList<String>();
        try {
            var customSessionId=req.getParameter("customSessionId");
            var uploadResult= stdInfoService.upload(customSessionId,StandardTypeEnum.China, file.getInputStream(),errList);
            //更新二进制文件存储
            if(uploadResult.getResult().getResult() && uploadResult.getList().size()>0)
            {
                process.setTotal(uploadResult.getList().size());
                process.setDealed(0);
                process.setTitle("加密标准文件");
                process.setHasNextProcess(0);
                var list=uploadResult.getList();
                pdfPath=mergePath(pdfPath);
                encryptPdfPath = mergePath(encryptPdfPath);
                for(var i=0;i<list.size();i++)
                {
                    var model =list.get(i);
                    if(StringUtils.isNotBlank(model.getPdfFileName())) {
                        File f = new File(getCorrespondingPath(pdfPath,model.getPdfFileName()) + "\\" + model.getPdfFileName());
                        File encryptFile = new File(getCorrespondingPath(encryptPdfPath,model.getId()) + "\\" + model.getId());
                        var pdfIsExists = false;
                        if (!encryptFile.exists() && f.exists()) {
                            //大于100M的文件单独处理
                            if (f.length() > 104857600) {
                                Tool.encryptByExe(getCorrespondingPath(pdfPath,model.getPdfFileName()) + "\\" + model.getPdfFileName(), getCorrespondingPath(encryptPdfPath,model.getId()) + "\\" + model.getId(), "20221103");
                                Thread.sleep(Tool.estimatedEncryptTime(f.length()));
                            } else {
                                var data = Tool.getBinaryData(f);
                                saveEncryptPdf(model.getId(), data);
                            }
                            pdfIsExists = true;
                        } else if (encryptFile.exists()) {
                            pdfIsExists = true;
                        }
                        model.setPdfIsExists(pdfIsExists ? "1" : "0");
                        stdInfoService.updateById(model);
                    }
                    process.setDealed(process.getDealed()+1);
                }
                process.setDealed(process.getTotal());
            }
            else if(errList.size()>0) {
               var fileName= savelErrToExcel(errList);
                //返回一个excel文件名称
                result.setMessage(fileName);
                result.setSuccess(false);
                result.setResult(false);
                return result;
            }
        }
        catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
            result.setResult(false);
            result.setSuccess(false);
            result.setMessage("上传失败，"+ex.getMessage());
            return result;
        }
        return result;
    }

    /**
     * 获取jar包所在文件路径
     *
     * @param
     * @return java.lang.String
     * @throws
     */
    public String getJarFilePath() {
        ApplicationHome home = new ApplicationHome(getClass());
        File jarFile = home.getSource();
        return jarFile.getParentFile().toString();
    }

    public String savelErrToExcel(List<String> errArray) throws IOException {
        // create a new Workbook
        var sdf=new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss");
        var fileName="system_upload_err_"+sdf.format(new Date())+".xlsx";
        String fileFullPath=getJarFilePath()+"\\"+fileName;
        Workbook workbook = new XSSFWorkbook();
        // create a new sheet
        XSSFSheet sheet = (XSSFSheet) workbook.createSheet("Sheet1");
        sheet.setColumnWidth(0, 1000);// 设置第二列的宽度
        sheet.setColumnWidth(1, 10000);// 设置第二列的宽度
        // create some data rows
        Row row = sheet.createRow(0);
        row.createCell(0).setCellValue("序号");
        row.createCell(1).setCellValue("错误描述");
        Integer rowIndex=1;
        for(Integer i=0;i<errArray.size();i++)
        {
            if(org.apache.commons.lang.StringUtils.isNotBlank(errArray.get(i))) {
                row = sheet.createRow(rowIndex);
                row.createCell(0).setCellValue(rowIndex);
                row.createCell(1).setCellValue(errArray.get(i));
                rowIndex++;
            }
        }
        // write the Workbook to a ByteArrayOutputStream
        var cal= Calendar.getInstance();
        var outputStream=new FileOutputStream(fileFullPath);
        // set the headers for downloading the file
        try {
            workbook.write(outputStream);
            return fileName;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

//    fetchEncryptFile

    /**
     * 数据加密后将pdf保存为二进制数据
     *
     * @param data
     * @return void
     * @throws
     */
    public void saveEncryptPdf(String id,byte[] data) throws IOException {
        //原始程序
//        encryptPdfPath=mergePath(encryptPdfPath);
//        var encryptData= DESUtil.getEncryptBytes(data);
//        File file=new File(getCorrespondingPath(encryptPdfPath,id)+"\\"+id);
        //直接写死到D盘EncryptSources
        var encryptData= DESUtil.getEncryptBytes(data);
        File file=new File("d:\\EncryptSources\\"+id);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(encryptData);
        fos.flush();
        fos.close();
    }

    /**
     * 数据加密后将pdf保存为二进制数据
     *
     * @param data
     * @return void
     * @throws
     */
    public void saveEncryptPdf1(String path,byte[] data) throws IOException {
        var encryptData= DESUtil.getEncryptBytes(data);
        File file=new File(path);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(encryptData);
        fos.flush();
        fos.close();
    }

    /**
     * 网站文件数量统计（废弃）
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @PostMapping("totalstatistics")
    public Result getTotalStatistics()
    {
        var total=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().in(StdInfo::getStdType,StandardTypeEnum.China,StandardTypeEnum.USA));
        var c1=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"国家军用标准"));
        var c2=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"兵器行业标准"));
        var c3=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"集团公司标准"));
        var c4=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"国家标准"));
        var c5=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"航空行业标准"));
        var c6=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"航天行业标准"));
        var c7=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"电子行业标准"));
        var c8=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"船舶行业标准"));
        var c9=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"计量技术规范"));
        var c10=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"计量检定规程"));
        var c11=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"机械行业标准"));
        var c12=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"化工行业标准"));
        var c13=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"汽车行业标准"));
        var c14=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"铁道行业标准"));
        var c15=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"石油天然气行业标准"));
        var c16=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"电力行业标准"));
        var c17=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"通信行业标准"));
        var c18=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"北约标准"));
        var c19=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).ne(StdInfo::getStdClass,"北约标准"));

        var c20=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"安全生产行业标准"));
        var c21=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"北京市地方标准"));
        var c22=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"河北省地方标准"));
        var c23=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"上海市地方标准"));
        var c24=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"山东省地方标准"));
        var c25=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"重庆市地方标准"));
        var c26=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"陕西省地方标准"));
        var c27=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"生态环境标准"));
        var c28=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"金融行业标准"));
        var c29=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"矿山安全行业标准"));
        var c30=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"煤炭行业标准"));
        var c31=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"气象行业标准"));
        var c32=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"卫生行业标准"));
        var c33=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"邮政行业标准"));

        var obj=new JSONObject();
        obj.set("total",total);
        obj.set("c1",c1);
        obj.set("c2",c2);
        obj.set("c3",c3);
        obj.set("c4",c4);
        obj.set("c5",c5);
        obj.set("c6",c6);
        obj.set("c7",c7);
        obj.set("c8",c8);
        obj.set("c9",c9);
        obj.set("c10",c10);
        obj.set("c11",c11);
        obj.set("c12",c12);
        obj.set("c13",c13);
        obj.set("c14",c14);
        obj.set("c15",c15);
        obj.set("c16",c16);
        obj.set("c17",c17);
        obj.set("c18",c18);
        obj.set("c19",c19);

        obj.set("c20",c20);
        obj.set("c21",c21);
        obj.set("c22",c22);
        obj.set("c23",c23);
        obj.set("c24",c24);
        obj.set("c25",c25);
        obj.set("c26",c26);
        obj.set("c27",c27);
        obj.set("c28",c28);
        obj.set("c29",c29);
        obj.set("c30",c30);
        obj.set("c31",c31);
        obj.set("c32",c32);
        obj.set("c33",c33);

        var result=new Result();
        result.setData(obj);
        result.setSuccess(true);
        return result;
    }

    /**
     *  按照分类获取标准数量统计
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @PostMapping("statisticsList")
    public Result getTotalStatisticsList()
    {
        Result result=new Result();
        QueryWrapper<StdClassStatistics> queryWrapper = new QueryWrapper<StdClassStatistics>();
        var list = stdClassStatisticsMapper.selectList(queryWrapper);
        result.setData(list);
        result.setSuccess(true);
        return result;
    }

    /**
     * 网站文件数量统计
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @PostMapping("totalstatistics_org")
    public Result getTotalStatisticsOrg()
    {
        var chinaTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China));
        var gjbTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"国家军用标准"));
        var wjTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"兵器行业标准"));
        var otherTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).and(c->c.ne(StdInfo::getStdClass,"兵器行业标准").ne(StdInfo::getStdClass,"国家军用标准")));


        var chinaPDFTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getPdfIsExists,"1"));
        var gjbPDFTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"国家军用标准").eq(StdInfo::getPdfIsExists,"1"));
        var wjPDFTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).eq(StdInfo::getStdClass,"兵器行业标准").eq(StdInfo::getPdfIsExists,"1"));
        var otherPDFTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.China).and(c->c.ne(StdInfo::getStdClass,"兵器行业标准").ne(StdInfo::getStdClass,"国家军用标准")).eq(StdInfo::getPdfIsExists,"1"));
        var obj=new JSONObject();
        obj.set("total",chinaTotal);
        obj.set("totalPDF",chinaPDFTotal);

        obj.set("gjbTotal",gjbTotal);
        obj.set("wjTotal",wjTotal);
        obj.set("otherTotal",otherTotal);

        obj.set("gjbPDFTotal",gjbPDFTotal);
        obj.set("wjPDFTotal",wjPDFTotal);
        obj.set("otherPDFTotal",otherPDFTotal);
        var result=new Result();
        result.setData(obj);
        result.setSuccess(true);
        return result;
    }

    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("statistics")
    public ModelAndView statistics() {
        return jumpPage(MODULE_PATH + "statistics");
    }

    /**
     * Describe: 数据统计列表
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("sIndex")
    public ModelAndView sIndex() {
        return jumpPage(MODULE_PATH + "statisticsIndex");
    }

/**
 * Describe: 批量生成解密文件
 * Param: sysDictType
 * Return: ModelAndView
 */
    @PostMapping("batchDecryptDoc")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result batchDecryptDoc() throws IOException {
        encryptPdfPath=mergePath(encryptPdfPath);
        var list=stdInfoService.list();
        for(var i=0;i<list.size();i++)
        {
            var stdInfo=list.get(i);
            var data=getDecyptData(getCorrespondingPath(encryptPdfPath,stdInfo.getId()),stdInfo.getId());
            saveFile(stdInfo.getPdfFileName(),data);
        }
        return decide(true);
    }
    /**
     *从pdf加密后的数据中解密出真实的二进制数据
     *
     * @param id
     * @return byte[]
     * @throws
     */
    public byte[] getDecyptData(String filePath, String id) throws IOException {
        var convertData=new byte[0];
        File file=new File(filePath+"\\"+id);
        var byteData= Tool.getBinaryData(file);
        convertData= DESUtil.getDecryptBytes(byteData);
        return convertData;
    }
    /**
     * 数据加密后将pdf保存为二进制数据
     *
     * @param data
     * @return void
     * @throws
     */
    public void saveFile(String fileName,byte[] data) throws IOException {
        pdfPath=mergePath(pdfPath);
        File file=new File(getCorrespondingPath(pdfPath,fileName)+"\\"+fileName);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data);
        fos.flush();
        fos.close();
    }

    /**
     * 删除加密文件
     *
     * @param fileName
     * @return void
     * @throws
     */
    public void deleteEncryptFile(String fileName){
        encryptPdfPath=mergePath(encryptPdfPath);
        try {
            File file = new File(getCorrespondingPath(encryptPdfPath,fileName) + "\\" + fileName);
            if (file.exists()) {
                file.delete();
            }
        }catch (Exception ex)
        {

        }
    }

    /**
     *根据用户配置的文件存放盘符及软件默认的文件盘符合并起来作为最终的文件盘符
     *
     * @param basePath
     * @return java.lang.String
     * @throws
     */
    public String mergePath(String basePath) {
        var path = "";
        var diskNo = "";
        var config = sysConfigMapper.selectByCode("security_file_path");
        if (config != null && com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(config.getConfigValue())) {
            diskNo = config.getConfigValue();
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(diskNo)) {
//            if(diskNo.contains(":"))
//            {
//                diskNo=diskNo.split(":")[0];
//            }
            var baseArr = basePath.split(":");
            // baseArr[0]=diskNo;
            if (diskNo.contains(",")) {
                var arr = diskNo.split(",");
                for (var i = 0; i < arr.length; i++) {
                    if (StringUtils.isNotBlank(arr[i])) {
                        var p = arr[i] + ":" + baseArr[1];
                        path += "," + p;
                    }
                }
                if (path.length() > 1) {
                    path = path.substring(1);
                }
            } else {
                path = diskNo + ":" + baseArr[1];
            }
        }
        //增加扩展文件夹，如系统配置可在两个盘符下存放密文（C，D），则扩展为8个位置，搜索顺序为：  C:\EncryptSources,D:\EncryptSources,C:\EncryptSources0,D:\EncryptSources0,C:\EncryptSources1,D:\EncryptSources1,C:\EncryptSources2,D:\EncryptSources2
        //多个盘符依次类推
        var pathArr = path.split(",");
        var list = new ArrayList<String>();
        for (var i = 0; i < pathArr.length; i++) {
            list.add(pathArr[i]);
        }
        for (var i = 0; i < pathArr.length; i++) {
            list.add(pathArr[i] + "0");
        }
        for (var i = 0; i < pathArr.length; i++) {
            list.add(pathArr[i] + "1");
        }
        for (var i = 0; i < pathArr.length; i++) {
            list.add(pathArr[i] + "2");
        }
        path = String.join(",", list);
        System.out.println("系统配置的文件路径为：" + path);
        return path;
    }

    /**
     * Describe: 获取上传进度
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("uploadProcess")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result uploadProcess(@RequestParam(defaultValue = "") String customSessionId) throws IOException {
        var result=new Result<StdUploadProcess>();
        var pro= new StdUploadProcess();
        pro.setTotalCount(process.getTotal());
        pro.setDealedCount(process.getDealed());
        pro.setRestCount(process.getTotal()-process.getDealed());
        pro.setErr(process.getErr());
        result.setSuccess(!StringUtils.isNotBlank(pro.getErr()));
        result.setData(pro);

        /*var data=uploadProcessService.getOne(new LambdaQueryWrapper<StdUploadProcess>().eq(StdUploadProcess::getSession,customSessionId).last("limit 1"));
        if(data!=null)
        {
            process.setTotalCount(data.getTotalCount());
            process.setRestCount(data.getRestCount());
            process.setDealedCount(data.getDealedCount());
            process.setErr(data.getErr());
            if(StringUtils.isNotBlank(process.getErr()))
            {
                result.setSuccess(false);
            }
            result.setData(process);
        }*/
        return result;
    }
}
