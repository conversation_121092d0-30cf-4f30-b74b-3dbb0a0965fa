package com.pearadmin.modules.standard.controller;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pearadmin.common.aop.annotation.Log;
import com.pearadmin.common.aop.enums.BusinessType;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.tools.*;
import com.pearadmin.common.tools.string.StringUtil;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.FileContent;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.domain.StdInfoOfSystem;
import com.pearadmin.modules.standard.domain.StdUploadProcess;
import com.pearadmin.modules.standard.mapper.StdInfoMapper;
import com.pearadmin.modules.standard.service.ChongQueryWrapper;
import com.pearadmin.modules.standard.service.StdInfoService;
import com.pearadmin.modules.standard.service.StdLogService;
import com.pearadmin.modules.sys.domain.SysUser;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import io.swagger.annotations.ApiOperation;
import lombok.var;
import org.apache.commons.io.FileUtils;
import org.apache.lucene.store.Directory;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.HttpSolrClient;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.client.solrj.response.UpdateResponse;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.SolrDocumentList;
import org.apache.solr.common.SolrInputDocument;
import org.apache.solr.common.util.NamedList;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.FileSystem;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
/**
 * 美国标准
 */
@RestController
@RequestMapping("/usastd")
public class USAStdController extends BaseController {
    private final String MODULE_PATH = "stdusa/";
    @Value(value="${pdf.source.usa-path}")
    private String pdfPath;
    @Value(value="${pdf.encryptSource.path}")
    private String encryptPdfPath;
    @Autowired
    private StdInfoService stdInfoService;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private StringEncryptor stringEncryptor;
    //可使用总小时数
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;
    @Resource
    private StdInfoMapper stdInfoMapper;
    @Autowired
    private com.pearadmin.common.tools.UploadProcess process;
    @Autowired
    private StdLogService stdLogService;
    /**
     * 检查使用许可
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    public Boolean security() throws IOException {
        Boolean isSecurity=false;
//        var a=stringEncryptor.decrypt("x3r8Ujzo3T3SlHm/Yg5ytnBzFOevZdLAFeggqWaIV6NITctUoiYeЗOhgRL0E7QAH");
//        System.out.println(a);
        if(checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (org.apache.commons.lang.StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        }
        else
        {
            isSecurity=true;
        }
        return isSecurity;
    }
    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("main")
    public ModelAndView main() {
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("myIndex")
    public ModelAndView index() {
        return jumpPage(MODULE_PATH + "index");
    }

    /**
     * Describe: 给std_info创建排序条件
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("makesorting")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    @Log(title = "给std_info创建排序条件", describe = "给std_info创建排序条件", type = BusinessType.STD_Data)
    public boolean makesorting(StdInfo stdInfo, PageDomain pageDomain) throws ParseException, IOException {
        var query=new ChongQueryWrapper<StdInfo>();
        query.eq("std_type", StandardTypeEnum.USA);
        var page= stdInfoMapper.selectPage(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
        for(var i=0;i<page.getSize();i++)
        {
            var model=page.getRecords().get(i);
            model= stdInfoService.AnalysisSortConditionForUsa(model);
            stdInfoService.updateById(model);
        }
        return true;
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("data")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    @Log(title = "国内标准", describe = "获取数据列表", type = BusinessType.STD_Data)
    public ResultTable simpleQuery(StdInfo stdInfo, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return new ResultTable();
        }
        //存放非全文的查询条件
        var query=new ChongQueryWrapper<StdInfo>();
        //存放排序条件
        var sortQuery=new QueryWrapper<StdInfo>();
        // query.eq("std_type", StandardTypeEnum.China);
        StringBuilder stdNo= new StringBuilder();
        StringBuilder stdOrgName= new StringBuilder();
        String stdChineseName=null;
        String stdEnglishName=null;
        String primaryCoverage=null;
        String pubDept=null;
        String advanceDept=null;
        String draftingUnit=null;
        String drafter=null;
        String sortSql="";

        if(StringUtils.isNotBlank(stdInfo.getAdvSearchKey()))
        {
            query.eq("std_type", StandardTypeEnum.USA);
            var jsonArray= (JSONArray)JSONArray.parse(stdInfo.getAdvSearchKey());
            for(Object item :jsonArray) {
                var obj = (com.alibaba.fastjson.JSONObject) item;
                var logic = obj.getString("logic");
                var field = obj.getString("field");
                var val = obj.getString("val");
                var type = obj.getString("type");
                if(StringUtils.isNotBlank(val))
                {
                    if ("AND".equals(logic)) {
                        //精确查找
                        if ("exact".equals(type)) {
                            query.eq(field, val);
                        } else {
                            //模糊查询
                            query.like(field, val);
                        }
                    } else if ("OR".equals(logic)) {
                        //精确查找
                        if ("exact".equals(type)) {
                            query.or(qr -> qr.eq(field, val));
                        } else {
                            //模糊查询
                            query.or(qr -> qr.like(field, val));
                        }
                    } else if ("NOT".equals(logic)) {
                        //精确查找
                        if ("exact".equals(type)) {
                            query.ne(field, val);
                        } else {
                            //模糊查询
                            query.notLike(field, val);
                        }
                    }
                    //排序条件------------------------------
                    //精确查找
                    if ("exact".equals(type)) {
                        if (field.trim().equals("std_no")) {
                            sortSql += " when std_no = '" + val + "' then 10 ";
                        }
                    } else {
                        //模糊查询
                        if (field.trim().equals("std_no")) {
                            if (StringUtil.isNumeric(val)) {
                                sortSql += " when sort_serial_no='" + val + "' then 10 when sort_serial_no like '" + val + "%' then 5 ";
                            }
//                        else {
//                            if(!identityList.contains(val.toUpperCase())) {
//                                sortSql += " when std_no='"+ val+"' then 2 when std_no like '" + val + "%' then 1 ";
//                            }
//                          //  sortSql += " when std_no='"+ val+"' then 2 when std_no like '" + val + "%' then 1 ";
//                        }
                        }
                    }
                }
            }
            DealIdnetity(stdInfo,query);
            if(StringUtils.isNotBlank(sortSql)) {
                sortQuery.orderBy(true,false, "CASE "+sortSql+" ELSE 0 END");
            }else
            {
                sortQuery.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_year");
            }
            query.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_year");
            var page= stdInfoMapper.selectPage(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
            //获取可以下载的标准的数量
            query.eq("pdf_is_exists","1");
            var pdfCanDownloads=stdInfoService.count(query);
            var pg= pageTable(stdInfoService.fillIsCollected(page.getRecords()), page.getTotal());
            pg.setExtData(pdfCanDownloads);
            return pg;
        }
        else
        {
            StringBuilder fulltextSql=new StringBuilder();
            if(StringUtils.isNotBlank(stdInfo.getSimpleSearchKey()))
            {
                var keys=stdInfo.getSimpleSearchKey();
                while(keys.contains("  "))
                {
                    keys=keys.replaceFirst("  "," ");
                }
                var keyArr=keys.split(" ");
                //拼凑stdNo的全文检索条件，每个关键字后要带上通配符*，可以匹配到更多标准编号
                for(String key : keyArr)
                {
                    if(StringUtils.isNotBlank(key)) {
                        fulltextSql.append((fulltextSql.length()>0?" AND ":"")+ String.format(" (MATCH(std_no) AGAINST ('%s*' IN BOOLEAN MODE) or MATCH(std_english_name) AGAINST ('%s' IN BOOLEAN MODE))",key,key));
                        //  stdOrgName.append(" ").append(key);
                        //  stdNo.append(" ").append(key).append("*");
                        //如果key包含中文内容，则赋值给stdOrgName
                      /*  if (StringUtil.containsChinese(key)) {
                            stdOrgName.append(" ").append(key);
                        }
                        else
                        {
                            stdNo.append(" ").append(key).append("*");
                        }*/
                        // query.and(foo -> foo.like("std_no", key).or().like("std_org_Name", key));
                        if (StringUtil.isNumeric(key)) {
                            sortSql  += " when sort_serial_no='" + key + "' then 10 when sort_serial_no like '" + key + "%' then 5 ";
                        }
                    }
                }
            }
            DealIdnetity(stdInfo,query);
//            if(StringUtils.isNotBlank(sortSql)) {
//                sortQuery.orderBy(true,false, "CASE "+sortSql+" ELSE 0 END");
//            }
            //
            sortQuery.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_year");
            String customSqlSegment = SqlUtils.getFullSql(query, StdInfo.class);
            customSqlSegment+= ((StringUtils.isNotBlank(customSqlSegment) && fulltextSql.length()>0) ?" and ":"")+fulltextSql.toString();
            var page= stdInfoService.pageByFullTextSearch(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),sortQuery,customSqlSegment,StandardTypeEnum.USA, stdNo.toString().trim(), stdOrgName.toString().trim(),stdChineseName,stdEnglishName,primaryCoverage,pubDept,advanceDept,draftingUnit,drafter);
            //  var page= stdInfoMapper.selectPage(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
            //获取可以下载的标准的数量
            // query.eq("pdf_is_exists","1");
            //var pdfCanDownloads=stdInfoService.count(query);
            var pg= pageTable(stdInfoService.fillIsCollected(page.getRecords()), page.getTotal());
            pg.setExtData(page.getTotal());
            return pg;
        }
    }

    //处理std_class查询条件
    public void DealIdnetity(StdInfo stdInfo ,QueryWrapper<StdInfo> query)
    {
        if(StringUtils.isNotBlank(stdInfo.getQueryIdenti())) {
            var arr = stdInfo.getQueryIdenti().split(",");
            var list = (List<String>) Arrays.asList(arr);
            //检索全部Identification
            if (list.contains("0")) {
                //什么都不做,取所有标准
            } else if(!list.isEmpty()) {
                query.and(qw-> {
                    //界面上默认组合了GJB和WJ 及 Q/CNG，所以这里要判断是否包含这三个，如果包含，则查询std_class_int为1的情况（组合
                    if(list.size()==2 && list.contains("MIL") && list.contains("NATO") ) {
                        qw.eq("std_class_int", 2);
                    }
                    else {
                        if (list.contains("AA")) {
                            qw.or(q -> q.eq("std_class", "美国铝协会标准"));
                        }
                        if (list.contains("AASHTO")) {
                            qw.or(q -> q.eq("std_class", "美国国家公路与运输协会标准"));
                        }
                        if (list.contains("ABS")) {
                            qw.or(q -> q.eq("std_class", "美国船舶局标准"));
                        }
                        if (list.contains("AIA")) {
                            qw.or(q -> q.eq("std_class", "美国航空航天工业联合会标准"));
                        }
                        if (list.contains("AIAA")) {
                            qw.or(q -> q.eq("std_class", "美国航空与航天协会标准"));
                        }
                        if (list.contains("AIIM")) {
                            qw.or(q -> q.eq("std_class", "美国信息与图像管理协会标准"));
                        }
                        if (list.contains("ARINC")) {
                            qw.or(q -> q.eq("std_class", "美国航空无线电通信公司标准"));
                        }
                        if (list.contains("ASCE")) {
                            qw.or(q -> q.eq("std_class", "美国土木工程师协会标准"));
                        }
                        if (list.contains("ASME")) {
                            qw.or(q -> q.eq("std_class", "美国机械工程师协会标准"));
                        }
                        if (list.contains("ASQ")) {
                            qw.or(q -> q.eq("std_class", "美国质量协会标准"));
                        }
                        if (list.contains("ASTM")) {
                            qw.or(q -> q.eq("std_class", "美国材料与试验协会标准"));
                        }
                        if (list.contains("ATIS")) {
                            qw.or(q -> q.eq("std_class", "美国信息技术协会标准"));
                        }
                        if (list.contains("AWS")) {
                            qw.or(q -> q.eq("std_class", "美国焊接协会标准"));
                        }
                        if (list.contains("EIA")) {
                            qw.or(q -> q.eq("std_class", "美国电子工业协会标准"));
                        }
                        if (list.contains("FM")) {
                            qw.or(q -> q.eq("std_class", "美国陆军野战手册"));
                        }
                        if (list.contains("GPA")) {
                            qw.or(q -> q.eq("std_class", "美国气体处理协会标准"));
                        }
                        if (list.contains("GSA")) {
                            qw.or(q -> q.eq("std_class", "美国总务管理局标准"));
                        }
                        if (list.contains("IEEE")) {
                            qw.or(q -> q.eq("std_class", "美国电气与电子工程师协会标准"));
                        }
                        if (list.contains("ISA")) {
                            qw.or(q -> q.eq("std_class", "美国仪器、系统与自动化协会标准"));
                        }
                        if (list.contains("MIL")) {
                            qw.or(q -> q.eq("std_class", "美国军用标准"));
                        }
                        if (list.contains("MSS")) {
                            qw.or(q -> q.eq("std_class", "美国阀门及配件工业制造商标准化协会标准"));
                        }
                        if (list.contains("NASA")) {
                            qw.or(q -> q.eq("std_class", "美国宇航局标准"));
                        }
                        if (list.contains("NATO")) {
                            qw.or(q -> q.eq("std_class", "北约标准"));
                        }
                        if (list.contains("NEMA")) {
                            qw.or(q -> q.eq("std_class", "美国电气制造商协会标准"));
                        }
                        if (list.contains("NISO")) {
                            qw.or(q -> q.eq("std_class", "美国国家信息标准协会标准"));
                        }
                        if (list.contains("RTCA")) {
                            qw.or(q -> q.eq("std_class", "美国航空无线电技术委员会标准"));
                        }
                        if (list.contains("RWMA")) {
                            qw.or(q -> q.eq("std_class", "美国电阻焊接机制造商协会标准"));
                        }
                        if (list.contains("SAE")) {
                            qw.or(q -> q.eq("std_class", "美国汽车工程师学会标准"));
                        }
                        if (list.contains("SSPC")) {
                            qw.or(q -> q.eq("std_class", "美国钢结构油漆委员会标准"));
                        }
                        if (list.contains("NACE")) {
                            qw.or(q -> q.eq("std_class", "美国全国腐蚀工程师协会标准"));
                        }
                        if (list.contains("TIA")) {
                            qw.or(q -> q.eq("std_class", "美国通信工业协会标准"));
                        }
                        if (list.contains("TM")) {
                            qw.or(q -> q.eq("std_class", "美国陆军技术手册"));
                        }
                        if (list.contains("TOP")) {
                            qw.or(q -> q.eq("std_class", "美国试验操作规则"));
                        }
                    }
                });
            }
        } else {
            query.and(wq->{wq.eq("1","2");});
        }
    }

//    /**
//     * Describe: 数据字典列表数据
//     * Param: sysDictType
//     * Return: ResuTable
//     */
//    @GetMapping("data")
//    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
//    @Log(title = "美国标准", describe = "获取数据列表", type = BusinessType.STD_Data)
//    public ResultTable simpleQuery(StdInfo stdInfo, PageDomain pageDomain) throws ParseException, IOException {
//        if(!security())
//        {
//            return new ResultTable();
//        }
//        var query=new ChongQueryWrapper<StdInfo>();
//        query.eq("std_type", StandardTypeEnum.USA);
//        String sortSql="";
//        if(StringUtils.isNotBlank(stdInfo.getSimpleSearchKey()))
//        {
//            var keys=stdInfo.getSimpleSearchKey();
//            while(keys.contains("  "))
//            {
//                keys=keys.replaceFirst("  "," ");
//            }
//            var keyArr=keys.split(" ");
//            for(String key : keyArr)
//            {
//                if(StringUtils.isNotBlank(key)) {
//                    query.and(foo -> foo.like("std_no", key).or().like("std_org_Name", key).or().like("std_chinese_Name", key));
//                    if (StringUtil.isNumeric(key)) {
//                        sortSql  += " when sort_serial_no='" + key + "' then 10 when sort_serial_no like '" + key + "%' then 5 ";
//                    }
//                }
//            }
//        }
//        else if(StringUtils.isNotBlank(stdInfo.getAdvSearchKey()))
//        {
//            var jsonArray= (JSONArray)JSONArray.parse(stdInfo.getAdvSearchKey());
//            for(Object item :jsonArray)
//            {
//                var obj=(com.alibaba.fastjson.JSONObject)item;
//                var logic=obj.getString("logic");
//                var field=obj.getString("field");
//                var val=obj.getString("val");
//                var type=obj.getString("type");
//                if("AND".equals(logic))
//                {
//                    //精确查找
//                    if("exact".equals(type)) {
//                        query.eq(field, val);
//                    }
//                    else {
//                        //模糊查询
//                        query.like(field,val);
//                    }
//                }
//                else if("OR".equals(logic))
//                {
//                    //精确查找
//                    if("exact".equals(type)) {
//                        query.or(qr->qr.eq(field, val));
//                    }
//                    else {
//                        //模糊查询
//                        query.or(qr->qr.like(field, val));
//                    }
//                }
//                else if("NOT".equals(logic))
//                {
//                    //精确查找
//                    if("exact".equals(type)) {
//                        query.ne(field, val);
//                    }
//                    else {
//                        //模糊查询
//                        query.notLike(field,val);
//                    }
//                }
//                //排序条件------------------------------
//                //精确查找
//                if("exact".equals(type)) {
//                    if(field.trim().equals("std_no"))
//                    {
//                        sortSql += " when std_no = '" + val + "' then 10 ";
//                    }
//                }
//                else {
//                    //模糊查询
//                    if(field.trim().equals("std_no"))
//                    {
//                        if(StringUtil.isNumeric(val))
//                        {
//                            sortSql+=" when sort_serial_no='"+val+"' then 10 when sort_serial_no like '"+val+"%' then 5 ";
//                        }
////                        else {
////                            if(!identityList.contains(val.toUpperCase())) {
////                                sortSql += " when std_no='"+ val+"' then 2 when std_no like '" + val + "%' then 1 ";
////                            }
////                          //  sortSql += " when std_no='"+ val+"' then 2 when std_no like '" + val + "%' then 1 ";
////                        }
//                    }
//                }
//            }
//        }
//        if(StringUtils.isNotBlank(stdInfo.getQueryIdenti())) {
//            var arr = stdInfo.getQueryIdenti().split(",");
//            var list = (List<String>) Arrays.asList(arr);
//            //检索全部Identification
//            if (list.contains("0")) {
//                //什么都不做,取所有标准
//            } else if (list.size() > 0) {
//                query.and(qw -> {
//                    for (var i = 0; i < list.size(); i++) {
//                        switch (list.get(i)) {
//                            case "AA":
//                                qw.or(q -> q.eq("std_class", "美国铝协会标准"));
//                                break;
//                            case "AASHTO":
//                                qw.or(q -> q.eq("std_class", "美国国家公路与运输协会标准"));
//                                break;
//                            case "ABS":
//                                qw.or(q -> q.eq("std_class", "美国船舶局标准"));
//                                break;
//                            case "AIA":
//                                qw.or(q -> q.eq("std_class", "美国航空航天工业联合会标准"));
//                                break;
//                            case "AIAA":
//                                qw.or(q -> q.eq("std_class", "美国航空与航天协会标准"));
//                                break;
//                            case "AIIM":
//                                qw.or(q -> q.eq("std_class", "美国信息与图像管理协会标准"));
//                                break;
//                            case "ARINC":
//                                qw.or(q -> q.eq("std_class", "美国航空无线电通信公司标准"));
//                                break;
//                            case "ASCE":
//                                qw.or(q -> q.eq("std_class", "美国土木工程师协会标准"));
//                                break;
//                            case "ASME":
//                                qw.or(q -> q.eq("std_class", "美国机械工程师协会标准"));
//                                break;
//                            case "ASQ":
//                                qw.or(q -> q.eq("std_class", "美国质量协会标准"));
//                                break;
//                            case "ASTM":
//                                qw.or(q -> q.eq("std_class", "美国材料与试验协会标准"));
//                                break;
//                            case "ATIS":
//                                qw.or(q -> q.eq("std_class", "美国信息技术协会标准"));
//                                break;
//                            case "AWS":
//                                qw.or(q -> q.eq("std_class", "美国焊接协会标准"));
//                                break;
//                            case "EIA":
//                                qw.or(q -> q.eq("std_class", "美国电子工业协会标准"));
//                                break;
//                            case "FM":
//                                qw.or(q -> q.eq("std_class", "美国陆军野战手册"));
//                                break;
//                            case "GPA":
//                                qw.or(q -> q.eq("std_class", "美国气体处理协会标准"));
//                                break;
//                            case "GSA":
//                                qw.or(q -> q.eq("std_class", "美国总务管理局标准"));
//                                break;
//                            case "IEEE":
//                                qw.or(q -> q.eq("std_class", "美国电气与电子工程师协会标准"));
//                                break;
//                            case "ISA":
//                                qw.or(q -> q.eq("std_class", "美国仪器、系统与自动化协会标准"));
//                                break;
//                            case "MIL":
//                                qw.or(q -> q.eq("std_class", "美国军用标准"));
//                                break;
//                            case "MSS":
//                                qw.or(q -> q.eq("std_class", "美国阀门及配件工业制造商标准化协会标准"));
//                                break;
//                            case "NASA":
//                                qw.or(q -> q.eq("std_class", "美国宇航局标准"));
//                                break;
//                            case "NATO":
//                                qw.or(q -> q.eq("std_class", "北约标准"));
//                                break;
//                            case "NEMA":
//                                qw.or(q -> q.eq("std_class", "美国电气制造商协会标准"));
//                                break;
//                            case "NISO":
//                                qw.or(q -> q.eq("std_class", "美国国家信息标准协会标准"));
//                                break;
//                            case "RTCA":
//                                qw.or(q -> q.eq("std_class", "美国航空无线电技术委员会标准"));
//                                break;
//                            case "RWMA":
//                                qw.or(q -> q.eq("std_class", "美国电阻焊接机制造商协会标准"));
//                                break;
//                            case "SAE":
//                                qw.or(q -> q.eq("std_class", "美国汽车工程师学会标准"));
//                                break;
//                            case "SSPC":
//                                qw.or(q -> q.eq("std_class", "美国钢结构油漆委员会标准"));
//                                break;
//                            case "NACE":
//                                qw.or(q -> q.eq("std_class", "美国全国腐蚀工程师协会标准"));
//                                break;
//                            case "TIA":
//                                qw.or(q -> q.eq("std_class", "美国通信工业协会标准"));
//                                break;
//                            case "TM":
//                                qw.or(q -> q.eq("std_class", "美国陆军技术手册"));
//                                break;
//                            case "TOP":
//                                qw.or(q -> q.eq("std_class", "美国试验操作规则"));
//                                break;
//                        }
//                    }
//                });
//            } else {
//                query.and(wq -> {
//                    wq.eq("1", "2");
//                });
//            }
//        }
//        if(StringUtils.isNotBlank(sortSql)) {
//            query.orderBy(true,false, "CASE "+sortSql+" ELSE 0 END");
//        }
//        //query.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_char").orderByAsc("sort_year");
//        query.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_year");
//        var page= stdInfoMapper.selectPage(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
//        //获取可以下载的标准的数量
//        query.eq("pdf_is_exists","1");
//        var pdfCanDownloads=stdInfoService.count(query);
//        var pg= pageTable(stdInfoService.fillIsCollected(page.getRecords()), page.getTotal());
//        pg.setExtData(pdfCanDownloads);
//        return pg;
//    }

    @GetMapping("dataofsystem")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable dataofsystem(StdInfo stdInfo, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return new ResultTable();
        }
        var query=new QueryWrapper<StdInfoOfSystem>();
        if(StringUtils.isNotBlank(stdInfo.getSystemId())) {
            query.eq(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getSystemId()),"system_id",stdInfo.getSystemId());
            query.likeRight(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getClassCodePath()),"class_code_path",stdInfo.getClassCodePath());
            if (StringUtils.isNotBlank(stdInfo.getSimpleSearchKey())) {
                var keys = stdInfo.getSimpleSearchKey();
                while (keys.contains("  ")) {
                    keys = keys.replaceFirst("  ", " ");
                }
                var keyArr = keys.split(" ");
                for (String key : keyArr) {
                    query.and(foo -> foo.like("aa.std_no", key).or().like("aa.std_org_Name", key));
                }
            } else if (StringUtils.isNotBlank(stdInfo.getAdvSearchKey())) {
                var jsonArray = (JSONArray) JSONArray.parse(stdInfo.getAdvSearchKey());
                for (Object item : jsonArray) {
                    var obj = (com.alibaba.fastjson.JSONObject) item;
                    var logic = obj.getString("logic");
                    var field = obj.getString("field");
                    var val = obj.getString("val");
                    var type = obj.getString("type");
                    if ("AND".equals(logic)) {
                        //精确查找
                        if ("exact".equals(type)) {
                            query.eq("aa."+field, val);
                        } else {
                            //模糊查询
                            query.like("aa."+field, val);
                        }
                    } else if ("OR".equals(logic)) {
                        //精确查找
                        if ("exact".equals(type)) {
                            query.or(qr -> qr.eq("aa."+field, val));
                        } else {
                            //模糊查询
                            query.or(qr -> qr.like("aa."+field, val));
                        }
                    } else if ("NOT".equals(logic)) {
                        //精确查找
                        if ("exact".equals(type)) {
                            query.ne("aa."+field, val);
                        } else {
                            //模糊查询
                            query.notLike("aa."+field, val);
                        }
                    }
                }
            }
            // query.orderByAsc("class_code_path","sort_serial_no");
            //query.orderByAsc("class_code_path").orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_year").orderByAsc("sort_char");
            //获取可以下载的标准的数量
            var page1= stdInfoService.pageBySystemInfo(new Page<StdInfoOfSystem>(pageDomain.getPage(),pageDomain.getLimit()),query,"order by class_code_path asc,sort_identifaction asc,sort_serial_no asc,sort_year asc,sort_char asc,sort_char asc");
            var result=new ArrayList<StdInfo>();
            if(page1.getRecords().size()>0) {
                var modelList = page1.getRecords();
                for (var i = 0; i < modelList.size(); i++) {
                    result.add(modelList.get(i).convertToStdInfo());
                }
            }
            query.eq("pdf_is_exists", "1");
            var pdfCanDownloads = stdInfoService.getPdfExistCount(query);
            var pg= pageTable(stdInfoService.fillIsCollected(result), page1.getTotal());
            pg.setExtData((long)pdfCanDownloads);
            return pg;
        }
        else {
            return pageTable(null, 0);
        }
    }



    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("adv")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable advQuery(StdInfo stdInfo, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return new ResultTable();
        }
        var query=new QueryWrapper<StdInfo>();
        query.eq("std_type", StandardTypeEnum.USA);
        if(StringUtils.isNotBlank(stdInfo.getAdvSearchKey()))
        {
            var jsonArray= (JSONArray)JSONArray.parse(stdInfo.getAdvSearchKey());
            for(Object item :jsonArray)
            {
                var obj=(JSONObject)item;
                var logic=obj.getStr("logic");
                var field=obj.getStr("field");
                var val=obj.getStr("val");
                var type=obj.getStr("type");
                if("AND".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.eq(field, val);
                    }
                    else {
                        //模糊查询
                        query.like(field,val);
                    }
                }
                else if("OR".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.or().eq(field, val);
                    }
                    else {
                        //模糊查询
                        query.or().like(field,val);
                    }
                }
                else if("NOT".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.ne(field, val);
                    }
                    else {
                        //模糊查询
                        query.notLike(field,val);
                    }
                }
            }
        }
        query.orderByAsc("sort_identifaction").orderByDesc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_char").orderByAsc("sort_year");
        var page= stdInfoMapper.selectPage(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
        // var page =stdInfoService.page(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
        //获取可以下载的标准的数量
        query.eq("pdf_is_exists","1");
        var pdfCanDownloads=stdInfoService.count(query);
        var pg= pageTable(page.getRecords(), page.getTotal());
        pg.setExtData(pdfCanDownloads);
        return pg;
    }

    @GetMapping("list")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable list(StdInfo stdInfo, PageDomain pageDomain) {
        var query=new LambdaQueryWrapper<StdInfo>();
        query.eq(StdInfo::getStdType, StandardTypeEnum.USA);
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getStdNo()))
        {
            query.like(StdInfo::getStdNo, stdInfo.getStdNo());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getStdOrgName()))
        {
            query.like(StdInfo::getStdOrgName,stdInfo.getStdOrgName());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getDraftingUnit()))
        {
            query.like(StdInfo::getDraftingUnit, stdInfo.getDraftingUnit());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getDrafter()))
        {
            query.like(StdInfo::getDrafter, stdInfo.getDrafter());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getPrimaryCoverage()))
        {
            query.like(StdInfo::getPrimaryCoverage, stdInfo.getPrimaryCoverage());
        }
        query.orderByAsc(StdInfo::getSortIdentifaction).orderByDesc(StdInfo::getSortSerialNo).orderByAsc(StdInfo::getSortNumber).orderByAsc(StdInfo::getSortChar).orderByAsc(StdInfo::getSortYear);
        var page =stdInfoService.page(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
        //获取可以下载的标准的数量
        query.eq(StdInfo::getPdfIsExists,"1");
        var pdfCanDownloads=stdInfoService.count(query);
        var pg= pageTable(page.getRecords(), page.getTotal());
        pg.setExtData(pdfCanDownloads);
        return pg;
    }

    /**
     *统计可下载的pdf文件数量
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @GetMapping("updatePysicFileState")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result updatePysicFileState(HttpServletResponse resp, @RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "10") Integer pageSize) throws IOException
    {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).orderByAsc(StdInfo::getStdClass).last("limit "+(pageNum-1)*pageSize+","+pageSize));
        encryptPdfPath=mergePath(encryptPdfPath);
        try {
            for (var i = 0; i < list.size(); i++) {
                var exists = false;
                var data = list.get(i);
                if (StringUtils.isNotBlank(data.getPdfFileName())) {
                    File file = new File(encryptPdfPath + "\\" + data.getId());
                    if (file.exists()) {
                        exists = true;
                    }
                }
                data.setPdfIsExists(exists ? "1" : "0");
                stdInfoService.updateById(data);
            }
        }catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
        }
        return decide(true);
    }

    @GetMapping("getBigFileEncryptCmd")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result updatePysicFileState() throws IOException {
        File f=new File("E:\\bigfile");
        if(f.exists())
        {
            var files=f.listFiles();
            for (File ff:files) {
                if(ff.isFile()) {
                    var stdInfo = stdInfoService.getOne(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getPdfFileName, ff.getName()).last("limit 1"));
                    if (stdInfo != null) {
                        System.out.println("C:\\StdSys\\FileEncrypt.exe encrypt 20221103 \"E:\\bigfile\\" + ff.getName() + "\" " + "\"E:\\EncryptSources\\" + stdInfo.getId() +"\"");
                    }
                }
            }
        }
        return Result.success(true);
    }

    /**
     * 将未加密的文件收集起来放在指定文件夹内（D:\notEncryptFile)
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @GetMapping("collectNotEncryptFile")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result collectNotEncryptFile(HttpServletResponse resp) throws IOException
    {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getPdfIsExists,0));
        //pdfPath=mergePath(pdfPath);
        var pdfOrgPath="E:\\allPDF\\pdfs";
        try {
            for (var i = 0; i < list.size(); i++) {
                var exists = false;
                var data = list.get(i);
                if (StringUtils.isNotBlank(data.getPdfFileName())) {
                    findPdfFile(pdfOrgPath,data.getPdfFileName());
                }
            }
        }catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
        }
        return decide(true);
    }


    /**
     *  收集多出来的加密文件（与数据库内编号对应补上的）
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @GetMapping("collectBadFile")
    public boolean collectBadFile() throws IOException {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA));
        File root=new File("D:\\EncryptSources");
        File aimDic=new File("D:\\NotExistInDB");
        aimDic.mkdir();
        if(root.exists() && root.isDirectory())
        {
            var files=root.listFiles();
            for (File f:files
            ) {
                var inDbModel=list.stream().filter(d->d.getId().equals(f.getName())).findFirst();
                if(!inDbModel.isPresent())
                {
                    FileInputStream fis=new FileInputStream(f);
                    FileOutputStream fos=new FileOutputStream(new File("D:\\NotExistInDB\\"+f.getName()));
                    byte b[]=new byte[1024];
                    int len=0;
                    while((len=fis.read(b))!=-1)
                    {
                        fos.write(b);
                    }
                }
            }
        }
        return false;
    }


    public boolean findPdfFile(String pdfPath,String fileName) throws IOException {
        File root=new File(pdfPath);
        if(root.exists() && root.isDirectory())
        {
            var files=root.listFiles();
            for (File f:files
            ) {
                if(f.isDirectory())
                {
                    File ff=new File(f.getAbsolutePath()+"\\"+fileName);
                    if(ff.exists() && ff.isFile())
                    {
                        FileInputStream fis=new FileInputStream(ff);
                        FileOutputStream fos=new FileOutputStream(new File("F:\\UnEncrpytFile\\"+fileName));
                        byte b[]=new byte[1024];
                        int len=0;
                        while((len=fis.read(b))!=-1)
                        {
                            fos.write(b);
                        }
                        return true;
                    }
                }
            }
        }
        return false;
    }


    /**
     * 从多个盘符对应的文件中找到加密文件的存放位置
     *
     * @param filePaths
     * @param id
     * @return java.lang.String
     * @throws
     */
    public String getCorrespondingPath(String filePaths,String id)
    {
        String path=filePaths;
        if(filePaths.contains(",")) {
            var paths = filePaths.split(",");
            path=paths[0];
            for(var i=0;i<paths.length;i++)
            {
                File file=new File(paths[i]+"\\"+id);
                if(file.exists())
                {
                    path=paths[i];
                    break;
                }
            }
        }
        return path;
    }

    /**
     *统计可下载的pdf文件数量
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @PostMapping("testpdfstatus")
    public Result statisticsPdfCount()
    {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA));
        encryptPdfPath=mergePath(encryptPdfPath);
        try {
            for (var i = 0; i < list.size(); i++) {
                var exists = false;
                var data = list.get(i);
                if (StringUtils.isNotBlank(data.getPdfFileName())) {
                    File file = new File(getCorrespondingPath(encryptPdfPath,data.getId()) + "\\" + data.getId());
                    if (file.exists()) {
                        exists = true;
                    }
                }
                data.setPdfIsExists(exists ? "1" : "0");
                stdInfoService.updateById(data);
            }
        }catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
        }
        return decide(true);
    }

    /**
     * Describe: 数据字典类型新增视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("add")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public ModelAndView add() {
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("save")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result save(@RequestBody StdInfo stdInfo) throws IOException, InterruptedException {
        stdInfo.setStdType(StandardTypeEnum.USA);
        stdInfo=stdInfoService.AnalysisSortCondition(stdInfo);
        if(StringUtils.isBlank(stdInfo.getId()))
        {
            if(!StringUtils.isNotBlank(stdInfo.getPdfFileName()))
            {
                stdInfo.setPdfIsExists("1");
            }
            else
            {
                stdInfo.setPdfIsExists("0");
            }
        }
        if(StringUtils.isNotBlank(stdInfo.getPdfFileName())) {
            File f = new File(getCorrespondingPath(mergePath(pdfPath),stdInfo.getPdfFileName()) + "\\" + stdInfo.getPdfFileName());
            File encryptFile = new File(getCorrespondingPath(mergePath(encryptPdfPath),stdInfo.getId()) + "\\" + stdInfo.getId());
            var pdfIsExists = false;
            if (!encryptFile.exists() && f.exists()) {
                //大于100M的文件单独处理
                if (f.length() > 104857600) {
                    encryptPdfPath = getCorrespondingPath(mergePath(encryptPdfPath),stdInfo.getId());
                    Tool.encryptByExe(getCorrespondingPath(mergePath(pdfPath),stdInfo.getPdfFileName())+ "\\" + stdInfo.getPdfFileName(), encryptPdfPath + "\\" + stdInfo.getId(), "20221103");
                    Thread.sleep(Tool.estimatedEncryptTime(f.length()));
                } else {
                    var data = Tool.getBinaryData(f);
                    saveEncryptPdf(stdInfo.getId(), data);
                }
                pdfIsExists = true;
            } else if (encryptFile.exists()) {
                pdfIsExists = true;
            }
            stdInfo.setPdfIsExists(pdfIsExists ? "1" : "0");
        }
        boolean result = stdInfoService.save(stdInfo);
        return decide(result);
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("edit")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView edit(Model model,String stdID) {
        var data=stdInfoService.getById(stdID);
        model.addAttribute("stdInfo",data);
        return jumpPage(MODULE_PATH + "edit");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detail")
    @Log(title = "美国标准", describe = "信息预览", type = BusinessType.Standard_OverView)
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detail(Model model,String stdID) {
        var data=stdInfoService.getById(stdID);
        model.addAttribute("stdInfo",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    @GetMapping("getByNo")
    public Result<StdInfo> getByNo(@RequestParam(name="stdNo") String stdNo)
    {
        var data=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdNo,stdNo).last("limit 1"));
        var newList= stdInfoService.fillIsCollected(data);
        var result=new Result<StdInfo>();
        if(newList.size()>0) {
            result.setData(newList.get(0));
        }
        result.setSuccess(true);
        return result;
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detailbystdno")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detailbystdno(Model model,String stdNo) {
        var data=stdInfoService.getOne(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdNo,stdNo).last("limit 1"));
        model.addAttribute("stdInfo",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PutMapping("update")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result update(@RequestBody StdInfo stdInfo) throws IOException, InterruptedException {
//        if(StringUtils.isNotBlank(stdInfo.getPdfFileName())) {
//            File f = new File(getCorrespondingPath(mergePath(pdfPath),stdInfo.getPdfFileName()) + "\\" + stdInfo.getPdfFileName());
//            File encryptFile = new File(getCorrespondingPath(mergePath(encryptPdfPath),stdInfo.getId()) + "\\" + stdInfo.getId());
//            var pdfIsExists = false;
//            if (!encryptFile.exists() && f.exists()) {
//                //大于100M的文件单独处理
//                if (f.length() > 104857600) {
//                    encryptPdfPath = getCorrespondingPath(mergePath(encryptPdfPath),stdInfo.getId());
//                    Tool.encryptByExe(getCorrespondingPath(mergePath(pdfPath),stdInfo.getPdfFileName()) + "\\" + stdInfo.getPdfFileName(), encryptPdfPath + "\\" + stdInfo.getId(), "20221103");
//                    Thread.sleep(Tool.estimatedEncryptTime(f.length()));
//                } else {
//                    var data = Tool.getBinaryData(f);
//                    saveEncryptPdf(stdInfo.getId(), data);
//                }
//                pdfIsExists = true;
//            } else if (encryptFile.exists()) {
//                pdfIsExists = true;
//            }
//            stdInfo.setPdfIsExists(pdfIsExists ? "1" : "0");
//        }
        boolean result = stdInfoService.updateById(stdInfo);
        return decide(result);
    }

    public void traverseFolder(File folder) throws InterruptedException, IOException {
        File[] files = folder.listFiles();
        var i=0;
        for (File f : files) {
            i++;
            if (f.isFile()) {
                var query = new LambdaQueryWrapper<StdInfo>();
                query.eq(StdInfo::getPdfFileName, f.getName());
                var model = stdInfoService.getOne(query);
                if (model != null) {
                    System.out.println("---------------------------------------------");
                    System.out.println(i);
                    System.out.println(model.getId());
                    System.out.println(f.getName());
                    System.out.println("---------------------------------------------");
                    File encryptFile = new File(getCorrespondingPath(mergePath(encryptPdfPath),model.getId()) + "\\" + model.getId());
                    var pdfIsExists = false;
                    //if (!encryptFile.exists() && f.exists()) {
                    if (f.exists()) {
                        encryptPdfPath = getCorrespondingPath(mergePath(encryptPdfPath),model.getId());
                        //大于100M的文件单独处理
                        if (f.length() > 104857600) {
                            Tool.encryptByExe(getCorrespondingPath(mergePath(pdfPath),model.getPdfFileName()) + "\\" + model.getPdfFileName(), encryptPdfPath + "\\" + model.getId(), "20221103");
                            Thread.sleep(Tool.estimatedEncryptTime(f.length()));
                        } else {
                            var data = Tool.getBinaryData(f);
                            saveEncryptPdf1(encryptPdfPath + "\\" + model.getId(), data);
                        }
                        pdfIsExists = true;
                    } else if (encryptFile.exists()) {
                        pdfIsExists = true;
                    }
                    model.setPdfIsExists(pdfIsExists ? "1" : "0");
                    stdInfoService.updateById(model);
                }
            } else if (f.isDirectory()) {
                traverseFolder(f); // 递归调用
            }
        }
    }


    /**
     * Describe: 批量生成加密文件
     * Param: sysDictType
     * Return: ModelAndView
     * 大文件生成命令的sql：
     * select 'C:\\StdSys\\FileEncrypt.exe','encrypt',20221103, concat('"','D:\\allPDF\\1\\',pdf_file_name,'"'),concat('"','C:\\EncryptSources\\',id,'"') from std_info where id in('2a3cf9c3733311ee805')
     */
    @PostMapping("batchUpdateDoc")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result batchUpdateDoc() throws IOException, InterruptedException {
        File dir = new File(pdfPath);
        System.out.println("---------------------------------------------");
        System.out.println(dir.getAbsolutePath());
        try {
            traverseFolder(dir);
        }
        catch (Exception ex)
        {
            System.out.println(ex.getStackTrace().toString());
            return Result.failure(ex.getMessage());
        }
        return decide(true);
    }
    /**
     * Describe: 批量生成加密文件
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("batchUpdateDoc_last")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result batchUpdateDoc_last() throws IOException, InterruptedException {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA));
        pdfPath=mergePath(pdfPath);
        for(var i=0;i<list.size();i++)
        {
            var model =list.get(i);
            if(StringUtils.isNotBlank(model.getPdfFileName())) {
                File f = new File(getCorrespondingPath(pdfPath,model.getPdfFileName()) + "\\" + model.getPdfFileName());
                File encryptFile = new File(getCorrespondingPath(encryptPdfPath,model.getId()) + "\\" + model.getId());
                var pdfIsExists = false;
                if (!encryptFile.exists() && f.exists()) {
                    //大于100M的文件单独处理
                    if (f.length() > 104857600) {
                        encryptPdfPath = getCorrespondingPath(mergePath(encryptPdfPath),model.getId());
                        Tool.encryptByExe(getCorrespondingPath(pdfPath,model.getPdfFileName()) + "\\" + model.getPdfFileName(), encryptPdfPath + "\\" + model.getId(), "20221103");
                        Thread.sleep(Tool.estimatedEncryptTime(f.length()));
                    } else {
                        var data = Tool.getBinaryData(f);
                        saveEncryptPdf(model.getId(), data);
                    }
                    pdfIsExists = true;
                } else if (encryptFile.exists()) {
                    pdfIsExists = true;
                }
                model.setPdfIsExists(pdfIsExists ? "1" : "0");
                stdInfoService.updateById(model);
            }
        }
        return decide(true);
    }

    /**
     * Describe: 批量生成加密文件
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("batchUpdateDoc1")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result batchUpdateDoc1(HttpServletResponse resp, @RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "10") Integer pageSize) throws IOException {
        var query=new LambdaQueryWrapper<StdInfo>();
        query.orderByDesc(StdInfo::getCreateTime);
        query.eq(StdInfo::getPdfIsExists,"0");
        var page =stdInfoService.page(new Page<StdInfo>(pageNum,pageSize),query);
        var pdfPath1=mergePath(pdfPath);
        try {
            for (var i = 0; i < page.getRecords().size(); i++) {
                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(page.getRecords().get(i).getPdfFileName())) {
                    File file = new File(getCorrespondingPath(pdfPath1,page.getRecords().get(i).getPdfFileName()) + "\\" + page.getRecords().get(i).getPdfFileName());
                    if (file.exists()) {
                    /*byte[] buffer = new byte[1024];
                    var os=resp.getOutputStream();
                    var fis = new FileInputStream(file);
                    var bis = new BufferedInputStream(fis);
                    int ii = bis.read(buffer);
                    while(ii != -1){
                        os.write(buffer);
                        i = bis.read(buffer);
                    }*/

                        var byteData = Tool.getBinaryData(file);
                        saveEncryptPdf(page.getRecords().get(i).getId(), byteData);
                    }
                }
            }
            return decide(true);
        }
        catch (Exception ex)
        {
            var result=new Result<String>();
            result.setSuccess(false);
            result.setMsg(ex.getStackTrace().toString());
            return  result;
        }
    }

    /**
     * Describe: 数据字典删除
     * Param: sysDictType
     * Return: ModelAndView
     */
    @DeleteMapping("remove/{id}")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result remove(@PathVariable("id") String id) throws IOException {
        //var model=stdInfoService.getById(id);
        Boolean result = stdInfoService.removeById(id);
        if(result) {
            //删除磁盘上的加密文件
            Files.deleteIfExists(Paths.get(encryptPdfPath+"\\"+id));
            //Files.deleteIfExists(Paths.get(pdfPath+"\\"+model.getPdfFileName()));
        }
        return decide(result);
    }



    /**
     * 给所有的标准解析排序条件（从标准号中）
     *
     * @param start
     * @param rows
     * @return com.pearadmin.common.tools.CResult<?>
     * @throws
     */
    @GetMapping("updatesortcondition")
    @ApiOperation(value = "updatesortcondition")
    public CResult<?> updatesortcondition(
            @RequestParam(name = "start", defaultValue = "0",required = true) Integer start,
            @RequestParam(name = "rows", defaultValue = "15",required = true) Integer rows) throws ParseException, IOException, SolrServerException {
        var list = stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType, StandardTypeEnum.USA).last("limit " + start + "," + rows));
        var result = new CResult<Boolean>();
        StdInfo stdInfo = null;
        try {
            for (var i = 0; i < list.size(); i++) {
                stdInfo = stdInfoService.AnalysisSortConditionForUsa(list.get(i));
                stdInfoService.updateById(stdInfo);
            }
            result.setSuccess(true);
            result.setResult(true);
            return result;
        } catch (Exception ex)
        {
            result.setSuccess(false);
            result.setResult(false);
            result.setMessage(ex.getStackTrace().toString());
            return result;
        }
    }

    @PostMapping("deleteAll")
    @ApiOperation(value = "删除所有标准")
    public CResult<Boolean> deleteAll(){
        var result=new CResult<Boolean>();
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA));
        var idArr=list.stream().map(StdInfo::getId).collect(Collectors.toList());
        if(idArr.size()>0) {
            stdInfoService.removeByIds(idArr);
            for(var i=0;i<idArr.size();i++)
            {
                deleteEncryptFile(idArr.get(i));
            }
            result.setSuccess(true);
            result.setResult(true);
            result.setMessage("");
        }
        else
        {
            result.setSuccess(false);
            result.setResult(false);
            result.setMessage("请输入要删除的记录编号！");
        }
        return result;
    }

    /**
     * Describe: 个人资料
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("index")
    @ApiOperation(value = "搜索首页")
    public ModelAndView center(Model model) {
        SysUser sysUser = UserContext.currentUser();
        //model.addAttribute("logs", sysLogService.selectTopLoginLog(sysUser.getUsername()));
        return jumpPage(MODULE_PATH+ "main");
    }

    /**
     * Describe: 个人资料
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("db")
    @ApiOperation(value = "搜索首页")
    public ModelAndView dbIndex(Model model) throws ParseException {
        return jumpPage(MODULE_PATH+ "searchDB");
    }

    /**
     * 获取文本提取
     *
     * @param document
     * @param writer
     * @throws IOException
     */
    public void getTextStripper(PDDocument document, Writer writer)
            throws IOException {
        PDFTextStripper textStripper = new PDFTextStripper();
        textStripper.writeText(document, writer);
    }

    /**
     * 提取文本内容
     * @param  file 加载文档的路径
     * @return FileContent
     * @throws IOException
     */
    public FileContent getText(File file) throws IOException {
        String textString = "";
        PDDocument document = PDDocument.load(file);
        //将提取出来的字节流转换为字符流进行显示
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(out);
        getTextStripper(document, writer);
        document.close();
        out.close();
        writer.close();
        byte[] con = out.toByteArray();
        textString = new String(con);
        var fileContent=new FileContent();
        fileContent.setContent(textString);
        fileContent.setBinContent(con);
        return fileContent;
    }

    /**
     *  excel 判断哪些上传失败的
     */
    @PostMapping("/filemark")
    @ApiOperation(value = "excel文件上传")
    public CResult<?> excelMark(HttpServletRequest req) throws Exception {
        var result=new CResult<Boolean>();
        result.setResult(true);
        result.setSuccess(true);
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) req;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        try {
            //创建
            XSSFWorkbook book = new XSSFWorkbook(file.getInputStream());
            XSSFSheet sheet = book.getSheetAt(0);
            // add some validation here
            // parse data
            var positionRow=0;
            var positionCol=0;
            int cols;
            for (int i = positionRow; i < sheet.getLastRowNum(); i++) {
                //Thread.sleep(2000);
                XSSFRow row = sheet.getRow(i + 1); // 表头不算
                var stdNo = Tool.getCellStringValue(row.getCell(0));
                System.out.println("sdtNo:" + stdNo);
                var hasPdf = Tool.getCellStringValue(row.getCell(3));
                if (hasPdf.trim().equals("有全文"))
                {
                    continue;
                }
                stdNo= stdNo.trim();
                var model= stdInfoService.getOne(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdNo,stdNo).last("limit 1"));
                if(model!=null)
                {
                    var exists=false;
                    if(model.getPdfIsExists().equals("1"))
                    {
                        exists=true;
                        System.out.println("sdtNo:" + stdNo+";有pdf文件");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
            result.setResult(false);
            result.setSuccess(false);
            result.setMessage("发生错误，"+ex.getMessage());
            return result;
        }
        return result;
    }


//    /**
//     *  excel文件上传
//     */
//    @PostMapping("/file-upload")
//    @ApiOperation(value = "excel文件上传")
//    public CResult<?> fileUpload(HttpServletRequest req) throws Exception {
//        var result=new CResult<Boolean>();
//        result.setResult(true);
//        result.setSuccess(true);
//        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) req;
//        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
//        var errList=new ArrayList<String>();
//        try {
//            var customSessionId=req.getParameter("customSessionId");
//            var uploadResult= stdInfoService.upload(customSessionId,StandardTypeEnum.USA, file.getInputStream(),errList);
//            //更新二进制文件存储
//            if(uploadResult.getResult().getResult() && uploadResult.getList().size()>0)
//            {
//                process.setTotal(uploadResult.getList().size());
//                process.setDealed(0);
//                process.setTitle("加密标准文件");
//                process.setHasNextProcess(0);
//                var list=uploadResult.getList();
//                pdfPath=mergePath(pdfPath);
//                encryptPdfPath = mergePath(encryptPdfPath);
//                for(var i=0;i<list.size();i++)
//                {
//                    var model =list.get(i);
//                    if(StringUtils.isNotBlank(model.getPdfFileName())) {
//                        File f = new File(getCorrespondingPath(pdfPath,model.getPdfFileName()) + "\\" + model.getPdfFileName());
//                        File encryptFile = new File(getCorrespondingPath(encryptPdfPath,model.getId()) + "\\" + model.getId());
//                        var pdfIsExists = false;
//                        if (!encryptFile.exists() && f.exists()) {
//                            //大于100M的文件单独处理
//                            if (f.length() > 104857600) {
//
//                                Tool.encryptByExe(getCorrespondingPath(pdfPath,model.getPdfFileName()) + "\\" + model.getPdfFileName(), getCorrespondingPath(encryptPdfPath,model.getId()) + "\\" + model.getId(), "20221103");
//                                Thread.sleep(Tool.estimatedEncryptTime(f.length()));
//                            } else {
//                                var data = Tool.getBinaryData(f);
//                                saveEncryptPdf(model.getId(), data);
//                            }
//                            pdfIsExists = true;
//                        } else if (encryptFile.exists()) {
//                            pdfIsExists = true;
//                        }
//                        model.setPdfIsExists(pdfIsExists ? "1" : "0");
//                        stdInfoService.updateById(model);
//                    }
//                    process.setDealed(process.getDealed()+1);
//                }
//                process.setDealed(process.getTotal());
//            }
//            else if(errList.size()>0) {
//                var fileName= savelErrToExcel(errList);
//                //返回一个excel文件名称
//                result.setMessage(fileName);
//                result.setSuccess(false);
//                result.setResult(false);
//                return result;
//            }
//        }
//        catch (Exception ex)
//        {
//            System.out.println(ex.getStackTrace());
//            result.setResult(false);
//            result.setSuccess(false);
//            result.setMessage("上传失败，"+ex.getMessage());
//            return result;
//        }
//        return result;
//    }

    @PostMapping("/file-upload")
    @ApiOperation(value = "excel文件上传")
    public CResult<?> fileUpload(HttpServletRequest req) throws Exception {
        var result = new CResult<Boolean>();
        result.setResult(true);
        result.setSuccess(true);

        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) req;
        MultipartFile file = multipartRequest.getFile("file");
        var errList = new ArrayList<String>();

        try {
            var customSessionId = req.getParameter("customSessionId");
            var uploadResult = stdInfoService.upload(customSessionId, StandardTypeEnum.USA,
                    file.getInputStream(), errList);

            if (uploadResult.getResult().getResult() && uploadResult.getList().size() > 0) {
                process.setTotal(uploadResult.getList().size());
                process.setDealed(0);
                process.setTitle("加密标准文件");
                process.setHasNextProcess(0);

                var list = uploadResult.getList();
                pdfPath = mergePath(pdfPath);
                encryptPdfPath = mergePath(encryptPdfPath);

                // 创建并行处理线程池（CPU核心数+IO等待补偿）
                int coreCount = Runtime.getRuntime().availableProcessors();
                ExecutorService executor = Executors.newFixedThreadPool(coreCount * 2);

                // 使用线程安全的进度计数器
                AtomicInteger processedCount = new AtomicInteger(0);
                List<CompletableFuture<Void>> futures = new ArrayList<>();

                // 并行提交加密任务
                for (var model : list) {
                    if (StringUtils.isNotBlank(model.getPdfFileName())) {
                        futures.add(CompletableFuture.runAsync(() -> {
                            try {
                                processPdfFile(model, processedCount);
                            } catch (Exception e) {
                                synchronized (errList) {
                                    errList.add("文件处理异常: " + e.getMessage());
                                }
                            }
                        }, executor));
                    }
                }

                // 等待所有任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
                executor.shutdownNow();

                process.setDealed(process.getTotal());
            } else if (!errList.isEmpty()) {
                return handleErrorResult(errList, result);
            }
        } catch (Exception ex) {
            return handleExceptionResult(ex, result);
        }
        return result;
    }

    // 封装文件处理方法
    private void processPdfFile(StdInfo model, AtomicInteger counter) throws Exception, IOException {
        File f = new File(getCorrespondingPath(pdfPath, model.getPdfFileName())
                + File.separator + model.getPdfFileName());
        File encryptFile = new File(getCorrespondingPath(encryptPdfPath, model.getId())
                + File.separator + model.getId());

        boolean pdfExists = false;
        if (!encryptFile.exists() && f.exists()) {
            if (f.length() > 104_857_600) {
                // 大文件使用外部进程加密
                Tool.encryptByExe(
                        getCorrespondingPath(pdfPath, model.getPdfFileName())
                                + File.separator + model.getPdfFileName(),
                        getCorrespondingPath(encryptPdfPath, model.getId())
                                + File.separator + model.getId(),
                        "20221103"
                );
                Thread.sleep(Tool.estimatedEncryptTime(f.length()));
            } else {
                // 小文件直接内存操作
                byte[] data = Tool.getBinaryData(f);
                saveEncryptPdf(model.getId(), data);
            }
            pdfExists = true;
        } else if (encryptFile.exists()) {
            pdfExists = true;
        }

        model.setPdfIsExists(pdfExists ? "1" : "0");
        stdInfoService.updateById(model);

        // 原子操作更新进度
        counter.incrementAndGet();
        process.setDealed(counter.get());
    }

    // 错误处理统一封装
    private CResult<?> handleErrorResult(ArrayList<String> errList, CResult<Boolean> result) throws IOException {
        var fileName= savelErrToExcel(errList);
        //返回一个excel文件名称
        result.setMessage(fileName);
        result.setSuccess(false);
        result.setResult(false);
        return result;
    }

    private CResult<?> handleExceptionResult(Exception ex, CResult<Boolean> result) {
        ex.printStackTrace();
        result.setResult(false);
        result.setSuccess(false);
        result.setMessage("上传失败，" + ex.getMessage());
        return result;
    }


    /**
     * 获取jar包所在文件路径
     *
     * @param
     * @return java.lang.String
     * @throws
     */
    public String getJarFilePath() {
        ApplicationHome home = new ApplicationHome(getClass());
        File jarFile = home.getSource();
        return jarFile.getParentFile().toString();
    }

    public String savelErrToExcel(List<String> errArray) throws IOException {
        // create a new Workbook
        var sdf=new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss");
        var fileName="system_upload_err_"+sdf.format(new Date())+".xlsx";
        String fileFullPath=getJarFilePath()+"\\"+fileName;
        Workbook workbook = new XSSFWorkbook();
        // create a new sheet
        XSSFSheet sheet = (XSSFSheet) workbook.createSheet("Sheet1");
        sheet.setColumnWidth(0, 1000);// 设置第二列的宽度
        sheet.setColumnWidth(1, 10000);// 设置第二列的宽度
        // create some data rows
        Row row = sheet.createRow(0);
        row.createCell(0).setCellValue("序号");
        row.createCell(1).setCellValue("错误描述");
        Integer rowIndex=1;
        for(Integer i=0;i<errArray.size();i++)
        {
            if(org.apache.commons.lang.StringUtils.isNotBlank(errArray.get(i))) {
                row = sheet.createRow(rowIndex);
                row.createCell(0).setCellValue(rowIndex);
                row.createCell(1).setCellValue(errArray.get(i));
                rowIndex++;
            }
        }
        // write the Workbook to a ByteArrayOutputStream
        var cal= Calendar.getInstance();
        var outputStream=new FileOutputStream(fileFullPath);
        // set the headers for downloading the file
        try {
            workbook.write(outputStream);
            return fileName;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    /**
     * 数据加密后将pdf保存为二进制数据
     *
     * @param data
     * @return void
     * @throws
     */
    public void saveEncryptPdf(String id,byte[] data) throws IOException {
//        encryptPdfPath=mergePath(encryptPdfPath);
//        var encryptData= DESUtil.getEncryptBytes(data);
//        File file=new File(getCorrespondingPath(encryptPdfPath,id)+"\\"+id);
//        FileOutputStream fos = new FileOutputStream(file);
//        fos.write(encryptData);
//        fos.flush();
//        fos.close();
        var encryptData= DESUtil.getEncryptBytes(data);
        File file=new File("d:\\EncryptSources\\"+id);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(encryptData);
        fos.flush();
        fos.close();
    }

    /**
     * 数据加密后将pdf保存为二进制数据
     *
     * @param data
     * @return void
     * @throws
     */
    public void saveEncryptPdf1(String path,byte[] data) throws IOException {
        var encryptData= DESUtil.getEncryptBytes(data);
        File file=new File(path);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(encryptData);
        fos.flush();
        fos.close();
    }

    /**
     * 网站文件数量统计
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @PostMapping("totalstatistics")
    public Result getTotalStatistics()
    {
        var total=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA));
        var c1=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"国家军用标准"));
        var c2=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"兵器行业标准"));
        var c3=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"集团公司标准"));
        var c4=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"国家标准"));
        var c5=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"航空行业标准"));
        var c6=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"航天行业标准"));
        var c7=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"电子行业标准"));
        var c8=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"船舶行业标准"));
        var c9=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"计量技术规范"));
        var c10=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"计量检定规程"));
        var c11=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"机械行业标准"));
        var c12=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"化工行业标准"));
        var c13=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"汽车行业标准"));
        var c14=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"铁道行业标准"));
        var c15=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"石油天然气行业标准"));
        var c16=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"电力行业标准"));
        var c17=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"通信行业标准"));

        var obj=new JSONObject();
        obj.set("total",total);
        obj.set("c1",c1);
        obj.set("c2",c2);
        obj.set("c3",c3);
        obj.set("c4",c4);
        obj.set("c5",c5);
        obj.set("c6",c6);
        obj.set("c7",c7);
        obj.set("c8",c8);
        obj.set("c9",c9);
        obj.set("c10",c10);
        obj.set("c11",c11);
        obj.set("c12",c12);
        obj.set("c13",c13);
        obj.set("c14",c14);
        obj.set("c15",c15);
        obj.set("c16",c16);
        obj.set("c17",c17);
        var result=new Result();
        result.setData(obj);
        result.setSuccess(true);
        return result;
    }

    /**
     * 网站文件数量统计
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @PostMapping("totalstatistics_org")
    public Result getTotalStatisticsOrg()
    {
        var chinaTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA));
        var gjbTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"国家军用标准"));
        var wjTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"兵器行业标准"));
        var otherTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).and(c->c.ne(StdInfo::getStdClass,"兵器行业标准").ne(StdInfo::getStdClass,"国家军用标准")));


        var chinaPDFTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getPdfIsExists,"1"));
        var gjbPDFTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"国家军用标准").eq(StdInfo::getPdfIsExists,"1"));
        var wjPDFTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).eq(StdInfo::getStdClass,"兵器行业标准").eq(StdInfo::getPdfIsExists,"1"));
        var otherPDFTotal=stdInfoService.count(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.USA).and(c->c.ne(StdInfo::getStdClass,"兵器行业标准").ne(StdInfo::getStdClass,"国家军用标准")).eq(StdInfo::getPdfIsExists,"1"));
        var obj=new JSONObject();
        obj.set("total",chinaTotal);
        obj.set("totalPDF",chinaPDFTotal);

        obj.set("gjbTotal",gjbTotal);
        obj.set("wjTotal",wjTotal);
        obj.set("otherTotal",otherTotal);

        obj.set("gjbPDFTotal",gjbPDFTotal);
        obj.set("wjPDFTotal",wjPDFTotal);
        obj.set("otherPDFTotal",otherPDFTotal);
        var result=new Result();
        result.setData(obj);
        result.setSuccess(true);
        return result;
    }

    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("statistics")
    public ModelAndView statistics() {
        return jumpPage(MODULE_PATH + "statistics");
    }

    /**
     * Describe: 批量生成解密文件
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("batchDecryptDoc")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result batchDecryptDoc() throws IOException {
        encryptPdfPath=mergePath(encryptPdfPath);
        var list=stdInfoService.list();
        for(var i=0;i<list.size();i++)
        {
            var stdInfo=list.get(i);
            var data=getDecyptData(getCorrespondingPath(encryptPdfPath,stdInfo.getId()),stdInfo.getId());
            saveFile(stdInfo.getPdfFileName(),data);
        }
        return decide(true);
    }
    /**
     *从pdf加密后的数据中解密出真实的二进制数据
     *
     * @param id
     * @return byte[]
     * @throws
     */
    public byte[] getDecyptData(String filePath, String id) throws IOException {
        var convertData=new byte[0];
        File file=new File(filePath+"\\"+id);
        var byteData= Tool.getBinaryData(file);
        convertData= DESUtil.getDecryptBytes(byteData);
        return convertData;
    }
    /**
     * 数据加密后将pdf保存为二进制数据
     *
     * @param data
     * @return void
     * @throws
     */
    public void saveFile(String fileName,byte[] data) throws IOException {
        pdfPath=mergePath(pdfPath);
        File file=new File(getCorrespondingPath(pdfPath,fileName)+"\\"+fileName);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data);
        fos.flush();
        fos.close();
    }

    /**
     * 删除加密文件
     *
     * @param fileName
     * @return void
     * @throws
     */
    public void deleteEncryptFile(String fileName){
        encryptPdfPath=mergePath(encryptPdfPath);
        try {
            File file = new File(getCorrespondingPath(encryptPdfPath,fileName) + "\\" + fileName);
            if (file.exists()) {
                file.delete();
            }
        }catch (Exception ex)
        {

        }
    }

    /**
     *根据用户配置的文件存放盘符及软件默认的文件盘符合并起来作为最终的文件盘符
     *
     * @param basePath
     * @return java.lang.String
     * @throws
     */
    public String mergePath(String basePath) {
        var path = "";
        var diskNo = "";
        var config = sysConfigMapper.selectByCode("security_file_path");
        if (config != null && com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(config.getConfigValue())) {
            diskNo = config.getConfigValue();
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(diskNo)) {
//            if(diskNo.contains(":"))
//            {
//                diskNo=diskNo.split(":")[0];
//            }
            var baseArr = basePath.split(":");
            // baseArr[0]=diskNo;
            if (diskNo.contains(",")) {
                var arr = diskNo.split(",");
                for (var i = 0; i < arr.length; i++) {
                    if (StringUtils.isNotBlank(arr[i])) {
                        var p = arr[i] + ":" + baseArr[1];
                        path += "," + p;
                    }
                }
                if (path.length() > 1) {
                    path = path.substring(1);
                }
            } else {
                path = diskNo + ":" + baseArr[1];
            }
        }
        //增加扩展文件夹，如系统配置可在两个盘符下存放密文（C，D），则扩展为8个位置，搜索顺序为：  C:\EncryptSources,D:\EncryptSources,C:\EncryptSources0,D:\EncryptSources0,C:\EncryptSources1,D:\EncryptSources1,C:\EncryptSources2,D:\EncryptSources2
        //多个盘符依次类推
        var pathArr = path.split(",");
        var list = new ArrayList<String>();
        for (var i = 0; i < pathArr.length; i++) {
            list.add(pathArr[i]);
        }
        for (var i = 0; i < pathArr.length; i++) {
            list.add(pathArr[i] + "0");
        }
        for (var i = 0; i < pathArr.length; i++) {
            list.add(pathArr[i] + "1");
        }
        for (var i = 0; i < pathArr.length; i++) {
            list.add(pathArr[i] + "2");
        }
        path = String.join(",", list);
        System.out.println("系统配置的文件路径为：" + path);
        return path;
    }

    /**
     * Describe: 获取上传进度
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("uploadProcess")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result uploadProcess(@RequestParam(defaultValue = "") String customSessionId) throws IOException {
        var result=new Result<StdUploadProcess>();
        var pro= new StdUploadProcess();
        pro.setTotalCount(process.getTotal());
        pro.setDealedCount(process.getDealed());
        pro.setRestCount(process.getTotal()-process.getDealed());
        pro.setErr(process.getErr());
        result.setSuccess(!StringUtils.isNotBlank(pro.getErr()));
        result.setData(pro);
        return result;
    }

    /**
     *  将指定文件夹下的原始文件加密并存储到指定位置
     */
    @GetMapping("/encrypt")
    @ApiOperation(value = "将指定文件夹下的原始文件加密并存储到指定位置")
    public CResult<?> encryptFile(HttpServletRequest req) throws Exception, IOException {
        var result=new CResult<Boolean>();
        result.setResult(true);
        result.setSuccess(true);
        File directory = new File("E:\\_ADecr");
        File[] allFiles=directory.listFiles();
        var errList=new ArrayList<String>();
        String fileName="";
        try {
            //更新二进制文件存储
            if(allFiles.length>0)
            {
                encryptPdfPath = mergePath(encryptPdfPath);
                for (File file : allFiles) {
                    if (!file.isDirectory()) {
                        fileName=file.getName();
                        var query=new LambdaQueryWrapper<StdInfo>();
                        query.eq(StdInfo::getPdfFileName,fileName);
                        var res=stdInfoService.list(query);
                        if(res.size()>0)
                        {
                            var model=res.get(0);
                            var orgFile=new File(getCorrespondingPath(encryptPdfPath,model.getId())+"\\"+model.getId());
                            if(orgFile.exists())
                            {
                                orgFile.delete();
                            }
                            var data = Tool.getBinaryData(file);
                            saveEncryptPdf(model.getId(), data);
                            System.out.println("--------------------------------------------------------------");
                            System.out.println("fileName:"+fileName);
                            System.out.println("状态:完成");
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.out.println("--------------------------------------------------------------");
            System.out.println("fileName:"+fileName);
            System.out.println(ex.getStackTrace());
            result.setResult(false);
            result.setSuccess(false);
            result.setMessage("上传失败，fileName:"+fileName+";"+ex.getMessage());
            return result;
        }
        return result;
    }

    /**
     *  删除北约标准的数据
     */
    @GetMapping("/deletebeiyue")
    @ApiOperation(value = "删除北约标准的数据")
    public boolean deletebeiyue() throws Exception {
        var result=new CResult<Boolean>();
        result.setResult(true);
        result.setSuccess(true);
        var query=new LambdaQueryWrapper<StdInfo>();
        query.eq(StdInfo::getStdClass,"北约标准");
        var res=stdInfoService.list(query);
        encryptPdfPath = mergePath(encryptPdfPath);
        if(res.size()>0) {
            for (var i = 0; i < res.size(); i++) {
                System.out.println("encryptPdfPath:"+encryptPdfPath);
                var path=getCorrespondingPath(encryptPdfPath, res.get(i).getId()) + "\\" + res.get(i).getId();
                System.out.println("filePath:"+path);
                var file = new File(path);
                file.delete();
            }
        }
        return result.getResult();
    }
}
