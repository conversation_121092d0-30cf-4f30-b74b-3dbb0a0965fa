package com.pearadmin.modules.standard.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.StdDownloadConfig;
import com.pearadmin.modules.standard.service.StdDownloadConfigService;
import com.pearadmin.modules.sys.domain.SysUser;
import com.pearadmin.modules.sys.service.SysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;

/**
 * 标准下载配置 Controller
 *
 * <AUTHOR> Code
 * @date 2023/5/28
 */
@RestController
@Api(tags = {"标准下载配置"})
@RequestMapping(ControllerConstant.API_SYSTEM_PREFIX + "stdDownloadConfig")
public class StdDownloadConfigController extends BaseController {

    private String MODULE_PATH = "stdDownloadConfig/";

    @Resource
    private StdDownloadConfigService stdDownloadConfigService;

    @Resource
    private SysUserService sysUserService;

    /**
     * 返回下载配置管理主页
     *
     * @return 主页视图
     */
    @GetMapping("index")
    @ApiOperation(value = "下载配置管理主页")
    @PreAuthorize("hasPermission('/system/stdDownloadConfig/index','sys:stdDownloadConfig:index')")
    public ModelAndView index() {
        return jumpPage(MODULE_PATH + "index");
    }

    /**
     * 返回用户下载配置管理页面
     *
     * @return 用户配置页面
     */
    @GetMapping("userConfig")
    @ApiOperation(value = "用户下载配置管理页面")
    @PreAuthorize("hasPermission('/system/stdDownloadConfig/userConfig','sys:stdDownloadConfig:userConfig')")
    public ModelAndView userConfig() {
        return jumpPage(MODULE_PATH + "userConfig");
    }

    /**
     * 返回快速配置页面
     *
     * @return 快速配置页面
     */
    @GetMapping("quickConfig")
    @ApiOperation(value = "快速配置页面")
    @PreAuthorize("hasPermission('/system/stdDownloadConfig/quickConfig','sys:stdDownloadConfig:quickConfig')")
    public ModelAndView quickConfig() {
        return jumpPage(MODULE_PATH + "quickConfig");
    }

    /**
     * 返回下载配置新增页面
     *
     * @return 新增页面
     */
    @GetMapping("add")
    @ApiOperation(value = "下载配置新增页面")
    @PreAuthorize("hasPermission('/system/stdDownloadConfig/add','sys:stdDownloadConfig:add')")
    public ModelAndView add() {
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * 返回下载配置编辑页面
     *
     * @param id 配置ID
     * @return 编辑页面
     */
    @GetMapping("edit")
    @ApiOperation(value = "下载配置编辑页面")
    @PreAuthorize("hasPermission('/system/stdDownloadConfig/edit','sys:stdDownloadConfig:edit')")
    public ModelAndView edit(@RequestParam("id") String id) {
        ModelAndView mav = jumpPage(MODULE_PATH + "edit");
        mav.addObject("stdDownloadConfig", stdDownloadConfigService.getById(id));
        return mav;
    }

    /**
     * 获取下载配置列表数据
     *
     * @param stdDownloadConfig 查询条件
     * @param pageDomain 分页参数
     * @return 配置列表
     */
    @GetMapping("data")
    @ApiOperation(value = "获取下载配置列表")
    @PreAuthorize("hasPermission('/system/stdDownloadConfig/data','sys:stdDownloadConfig:data')")
    public ResultTable data(StdDownloadConfig stdDownloadConfig, PageDomain pageDomain) {
        LambdaQueryWrapper<StdDownloadConfig> queryWrapper = new LambdaQueryWrapper<>();

        // 添加搜索条件
        if (stdDownloadConfig.getSimpleSearchKey() != null && !stdDownloadConfig.getSimpleSearchKey().trim().isEmpty()) {
            String searchKey = stdDownloadConfig.getSimpleSearchKey().trim();

            // 检查是否包含用户名过滤条件
            if (searchKey.startsWith("userName:")) {
                String[] parts = searchKey.split(" ", 2);
                String userName = parts[0].substring("userName:".length());

                // 添加用户名精确匹配条件
                queryWrapper.eq(StdDownloadConfig::getUserName, userName);

                // 如果还有其他搜索条件，添加到查询中
                if (parts.length > 1 && !parts[1].trim().isEmpty()) {
                    String otherSearchKey = parts[1].trim();
                    queryWrapper.and(wrapper -> wrapper
                        .like(StdDownloadConfig::getConfigType, otherSearchKey)
                        .or().like(StdDownloadConfig::getConfigValue, otherSearchKey)
                        .or().like(StdDownloadConfig::getRemark, otherSearchKey)
                    );
                }
            } else {
                // 普通搜索逻辑
                queryWrapper.and(wrapper -> wrapper
                    .like(StdDownloadConfig::getUserName, searchKey)
                    .or().like(StdDownloadConfig::getConfigType, searchKey)
                    .or().like(StdDownloadConfig::getConfigValue, searchKey)
                    .or().like(StdDownloadConfig::getRemark, searchKey)
                );
            }
        }

        // 只查询启用状态的配置
        queryWrapper.eq(StdDownloadConfig::getStatus, 1);

        // 按创建时间降序排序
        queryWrapper.orderByDesc(StdDownloadConfig::getCreateTime);

        // 分页查询
        Page<StdDownloadConfig> page = stdDownloadConfigService.page(
            new Page<>(pageDomain.getPage(), pageDomain.getLimit()),
            queryWrapper
        );

        return pageTable(page.getRecords(), page.getTotal());
    }

    /**
     * 保存下载配置
     *
     * @param stdDownloadConfig 配置信息
     * @return 操作结果
     */
    @PostMapping("save")
    @ApiOperation(value = "保存下载配置")
    @PreAuthorize("hasPermission('/system/stdDownloadConfig/save','sys:stdDownloadConfig:save')")
    public Result<String> save(@RequestBody StdDownloadConfig stdDownloadConfig) {
        try {
            // 设置创建时间和更新时间
            stdDownloadConfig.setCreateTime(LocalDateTime.now());
            stdDownloadConfig.setUpdateTime(LocalDateTime.now());
            stdDownloadConfig.setStatus(1); // 默认启用

            boolean result = stdDownloadConfigService.save(stdDownloadConfig);
            return result ? Result.success("保存成功") : Result.failure("保存失败");
        } catch (Exception e) {
            return Result.failure("保存失败：" + e.getMessage());
        }
    }

    /**
     * 更新下载配置
     *
     * @param stdDownloadConfig 配置信息
     * @return 操作结果
     */
    @PutMapping("update")
    @ApiOperation(value = "更新下载配置")
    @PreAuthorize("hasPermission('/system/stdDownloadConfig/update','sys:stdDownloadConfig:update')")
    public Result<String> update(@RequestBody StdDownloadConfig stdDownloadConfig) {
        try {
            // 设置更新时间
            stdDownloadConfig.setUpdateTime(LocalDateTime.now());

            boolean result = stdDownloadConfigService.updateById(stdDownloadConfig);
            return result ? Result.success("更新成功") : Result.failure("更新失败");
        } catch (Exception e) {
            return Result.failure("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除下载配置（逻辑删除，设置状态为0）
     *
     * @param ids 配置ID数组
     * @return 操作结果
     */
    @DeleteMapping("remove")
    @ApiOperation(value = "删除下载配置")
    @PreAuthorize("hasPermission('/system/stdDownloadConfig/remove','sys:stdDownloadConfig:remove')")
    public Result<String> remove(@RequestParam("ids") String ids) {
        try {
            String[] idArray = ids.split(",");
            for (String id : idArray) {
                StdDownloadConfig config = stdDownloadConfigService.getById(id);
                if (config != null) {
                    config.setStatus(0); // 逻辑删除
                    config.setUpdateTime(LocalDateTime.now());
                    stdDownloadConfigService.updateById(config);
                }
            }
            return Result.success("删除成功");
        } catch (Exception e) {
            return Result.failure("删除失败：" + e.getMessage());
        }
    }

    /**
     * 根据用户名获取最新配置
     *
     * @param userName 用户名
     * @return 配置信息
     */
    @GetMapping("getByUserName")
    @ApiOperation(value = "根据用户名获取最新配置")
    @PreAuthorize("hasPermission('/system/stdDownloadConfig/getByUserName','sys:stdDownloadConfig:getByUserName')")
    public Result<StdDownloadConfig> getByUserName(@RequestParam("userName") String userName) {
        try {
            StdDownloadConfig config = stdDownloadConfigService.getLatestByUserName(userName);
            return Result.success("查询成功", config);
        } catch (Exception e) {
            return Result.failure("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户的下载限制
     *
     * @param userName 用户名
     * @return 下载限制数量
     */
    @GetMapping("getDownloadLimit")
    @ApiOperation(value = "获取用户下载限制")
    public Result<Integer> getDownloadLimit(@RequestParam("userName") String userName) {
        try {
            Integer limit = stdDownloadConfigService.getDownloadLimit(userName);
            return Result.success("查询成功", limit);
        } catch (Exception e) {
            return Result.failure("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取用户的预览限制
     *
     * @param userName 用户名
     * @return 预览限制数量
     */
    @GetMapping("getPreviewLimit")
    @ApiOperation(value = "获取用户预览限制")
    public Result<Integer> getPreviewLimit(@RequestParam("userName") String userName) {
        try {
            Integer limit = stdDownloadConfigService.getPreviewLimit(userName);
            return Result.success("查询成功", limit);
        } catch (Exception e) {
            return Result.failure("查询失败：" + e.getMessage());
        }
    }

    /**
     * 保存或更新用户配置
     *
     * @param userName 用户名
     * @param downloadLimit 下载限制
     * @param previewLimit 预览限制
     * @param remark 备注
     * @return 操作结果
     */
    @PostMapping("saveOrUpdate")
    @ApiOperation(value = "保存或更新用户配置")
    @PreAuthorize("hasPermission('/system/stdDownloadConfig/saveOrUpdate','sys:stdDownloadConfig:saveOrUpdate')")
    public Result<String> saveOrUpdate(@RequestParam("userName") String userName,
                                     @RequestParam("downloadLimit") Integer downloadLimit,
                                     @RequestParam("previewLimit") Integer previewLimit,
                                     @RequestParam(value = "remark", required = false) String remark) {
        try {
            boolean result = stdDownloadConfigService.saveOrUpdateConfig(userName, downloadLimit, previewLimit, remark);
            return result ? Result.success("操作成功") : Result.failure("操作失败");
        } catch (Exception e) {
            return Result.failure("操作失败：" + e.getMessage());
        }
    }

    /**
     * 根据用户名和配置类型获取配置值
     *
     * @param userName 用户名
     * @param configType 配置类型
     * @return 配置值
     */
    @GetMapping("getConfigValue")
    @ApiOperation(value = "根据用户名和配置类型获取配置值")
    public Result<String> getConfigValue(@RequestParam("userName") String userName,
                                       @RequestParam("configType") String configType) {
        try {
            String configValue = stdDownloadConfigService.getConfigValue(userName, configType);
            return Result.success("查询成功", configValue);
        } catch (Exception e) {
            return Result.failure("查询失败：" + e.getMessage());
        }
    }

    /**
     * 保存或更新指定类型的配置
     *
     * @param userName 用户名
     * @param configType 配置类型
     * @param configValue 配置值
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param remark 备注
     * @return 操作结果
     */
    @PostMapping("saveOrUpdateByType")
    @ApiOperation(value = "保存或更新指定类型的配置")
    @PreAuthorize("hasPermission('/system/stdDownloadConfig/saveOrUpdateByType','sys:stdDownloadConfig:saveOrUpdateByType')")
    public Result<String> saveOrUpdateByType(@RequestParam("userName") String userName,
                                           @RequestParam("configType") String configType,
                                           @RequestParam("configValue") String configValue,
                                           @RequestParam(value = "startTime", required = false) String startTimeStr,
                                           @RequestParam(value = "endTime", required = false) String endTimeStr,
                                           @RequestParam(value = "status", required = false, defaultValue = "1") Integer status,
                                           @RequestParam(value = "remark", required = false) String remark) {
        try {
            LocalDateTime startTime = null;
            LocalDateTime endTime = null;

            // 解析时间参数
            if (startTimeStr != null && !startTimeStr.trim().isEmpty()) {
                startTime = LocalDateTime.parse(startTimeStr.replace(" ", "T"));
            }
            if (endTimeStr != null && !endTimeStr.trim().isEmpty()) {
                endTime = LocalDateTime.parse(endTimeStr.replace(" ", "T"));
            }

            boolean result = stdDownloadConfigService.saveOrUpdateConfigByType(
                userName, configType, configValue, startTime, endTime, remark);
            return result ? Result.success("操作成功") : Result.failure("操作失败");
        } catch (Exception e) {
            return Result.failure("操作失败：" + e.getMessage());
        }
    }

    /**
     * 获取系统用户列表（用于配置管理）
     *
     * @param pageDomain 分页参数
     * @param realName 真实姓名搜索条件
     * @param username 用户名搜索条件
     * @return 用户列表
     */
    @GetMapping("users")
    @ApiOperation(value = "获取系统用户列表")
    @PreAuthorize("hasPermission('/system/stdDownloadConfig/users','sys:stdDownloadConfig:users')")
    public ResultTable getUsers(PageDomain pageDomain,
                               @RequestParam(value = "realName", required = false) String realName,
                               @RequestParam(value = "username", required = false) String username) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<SysUser> queryWrapper = new LambdaQueryWrapper<>();

            // 添加搜索条件
            if (realName != null && !realName.trim().isEmpty()) {
                queryWrapper.like(SysUser::getRealName, realName.trim());
            }
            if (username != null && !username.trim().isEmpty()) {
                queryWrapper.like(SysUser::getUsername, username.trim());
            }

            // 只查询启用状态的用户
            queryWrapper.eq(SysUser::getEnable, true);

            // 按创建时间降序排序
            queryWrapper.orderByDesc(SysUser::getCreateTime);

            // 分页查询
            Page<SysUser> page = sysUserService.page(
                new Page<>(pageDomain.getPage(), pageDomain.getLimit()),
                queryWrapper
            );

            return pageTable(page.getRecords(), page.getTotal());
        } catch (Exception e) {
            System.err.println("Error while querying user list: " + e.getMessage());
            e.printStackTrace();
            return pageTable(null, 0);
        }
    }
}
