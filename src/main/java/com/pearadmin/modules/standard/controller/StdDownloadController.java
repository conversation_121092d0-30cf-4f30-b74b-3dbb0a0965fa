package com.pearadmin.modules.standard.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.tools.*;
import com.pearadmin.common.tools.string.StringUtil;
import com.pearadmin.common.tools.velocity.StdUserOperateType;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.StdClassification;
import com.pearadmin.modules.standard.domain.StdDownload;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.domain.StdSystem;
import com.pearadmin.modules.standard.mapper.StdDownloadMapper;
import com.pearadmin.modules.standard.service.*;
import com.pearadmin.modules.sys.domain.SysConfig;
import com.pearadmin.modules.sys.domain.SysUser;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import com.pearadmin.modules.sys.service.SysConfigService;
import io.swagger.annotations.Api;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Describe:  下载 控制器
 * Author: 崇义波
 * CreateTime: 2025/4/15
 */
@RestController
@Api(tags = {"标准下载"})
@RequestMapping(ControllerConstant.API_DOWNLOAD_PREFIX)
public class StdDownloadController extends BaseController {
    private final String MODULE_PATH = "download/";
    @Resource
    private StdDownloadService stdDownloadService;
    @Resource
    private StdDownloadMapper stdDownloadMapper;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private SysConfigService sysConfigService;
    @Resource
    private StringEncryptor stringEncryptor;
    //可使用总小时数
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;

    @Resource
    private StdInfoService stdInfoService;

    /**
     * 检查使用许可
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    public Boolean security() throws IOException {
        Boolean isSecurity=false;
        if(checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        }
        else
        {
            isSecurity=true;
        }
        return isSecurity;
    }

    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("index")
    public ModelAndView index() {
        return jumpPage(MODULE_PATH + "index");
    }

    /**
     * Describe: 管理员下载记录列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("admin")
    public ModelAndView admin() {
        return jumpPage(MODULE_PATH + "admin");
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("data")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable data(StdDownload model, PageDomain pageDomain) throws ParseException, IOException {
        if (!security()) {
            return null;
        }
        var query = new LambdaQueryWrapper<StdDownload>();
        SysUser sysUser = UserContext.currentUser();
        if (sysUser != null) {
            query.eq(StdDownload::getUserId, sysUser.getUserId());
        }
        query.and(wrapper -> wrapper.eq(StdDownload::getOpType, 1).or().isNull(StdDownload::getOpType));
        String sortSql="";
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getSimpleSearchKey()))
        {
            var keys=model.getSimpleSearchKey();
            while(keys.contains("  "))
            {
                keys=keys.replaceFirst("  "," ");
            }
            var keyArr=keys.split(" ");
            for(String key : keyArr)
            {
                if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(key)) {
                    query.and(foo -> foo.like(StdDownload::getStdNo, "%" + key + "%")
                            .or().like(StdDownload::getStdOrgName, "%" + key + "%")
                            .or().like(StdDownload::getStdChineseName, "%" + key + "%")
                            .or().like(StdDownload::getPdfFileName, "%" + key + "%"));
                }
            }
        }
        query.orderByDesc(StdDownload::getCreateTime);
        var page= stdDownloadMapper.selectPage(new Page<StdDownload>(pageDomain.getPage(),pageDomain.getLimit()),query);
        // 根据page中stdInfoId从stdInfo表中查找指定的标准是否被当前用户收藏，并将状态更新到page中对象的isCollect字段中
        // 获取所有记录
        var records = page.getRecords();

        // 如果有记录，查询是否收藏
        if (records != null && !records.isEmpty()) {
            // 提取所有的 stdInfoId
            List<String> stdInfoIds = records.stream()
                    .map(StdDownload::getStdInfoId)
                    .filter(id -> id != null && !id.isEmpty())
                    .collect(Collectors.toList());

            if (!stdInfoIds.isEmpty()) {
                // 查询收藏状态
                Map<String, Integer> collectStatus = stdInfoService.IsCollected(stdInfoIds);

                // 更新收藏状态
                for (StdDownload record : records) {
                    if (record.getStdInfoId() != null && collectStatus.containsKey(record.getStdInfoId())) {
                        record.setIsCollected(collectStatus.get(record.getStdInfoId()).toString());
                    } else {
                        record.setIsCollected("0"); // 默认为未收藏
                    }
                }
            }
        }
        return pageTable(records, page.getTotal());
    }

    @GetMapping("list")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public List<StdDownload> list(StdDownload model, PageDomain pageDomain) throws IOException {
        if (!security()) {
            return null;
        }
        var query = new LambdaQueryWrapper<StdDownload>();
        SysUser sysUser = UserContext.currentUser();
        //model.addAttribute("logs", sysLogService.selectTopLoginLog(sysUser.getUsername()));
        if (sysUser != null) {
            query.eq(StdDownload::getUserId,sysUser.getUserId());
        }
        query.eq(StdDownload::getOpType,model.getOpType());
        query.orderByDesc(StdDownload::getCreateTime);
        return stdDownloadService.list(query);
    }


    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("delete")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result delete(@RequestBody StdDownload model) throws IOException {
        Boolean result = stdDownloadService.removeById(model.getId());
        return decide(result);
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("save")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result save(@RequestBody StdDownload model) throws IOException {
        SysUser sysUser = UserContext.currentUser();
        //检查今天是否已经下载过该pdf
        if(model.getOpType().equals(StdUserOperateType.Download)) {
            StdDownload data = stdDownloadService.getOne(new LambdaQueryWrapper<StdDownload>().eq(StdDownload::getOpType, model.getOpType()).eq(StdDownload::getStdInfoId, model.getStdInfoId()).gt(StdDownload::getCreateTime, LocalDate.now()).lt(StdDownload::getCreateTime, LocalDate.now().plusDays(1)).last("limit 1"));
            if (data == null) {
                model.setUserId(sysUser.getUserId());
                boolean result = stdDownloadService.save(model);
                return decide(result);
            }
        }
        else if(model.getOpType().equals(StdUserOperateType.Preview))
        {
            model.setUserId(sysUser.getUserId());
            boolean result = stdDownloadService.save(model);
            return decide(result);
        }
        var res=new Result<Boolean>();
        res.setSuccess(true);
        return res;
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @GetMapping("canDownload")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result<Boolean> getCount(@RequestParam(name = "stdInfoId", defaultValue = "0",required = true) String stdInfoId) throws IOException {
        var result = new Result<Boolean>();
        SysUser sysUser = UserContext.currentUser();

        if (sysUser == null) {
            result.setData(false);
            result.setSuccess(true);
            result.setMsg("用户未登录");
            return result;
        }

        try {
            // 检查该文件是否已经下载过
            StdDownload fileHasDownload = stdDownloadService.getOne(
                new LambdaQueryWrapper<StdDownload>()
                    .eq(StdDownload::getOpType, StdUserOperateType.Download)
                    .eq(StdDownload::getStdInfoId, stdInfoId)
                    .eq(StdDownload::getUserId, sysUser.getUserId())
                    .last("limit 1")
            );

            // 如果已经下载过，直接允许
            if (fileHasDownload != null && StringUtils.isNotBlank(fileHasDownload.getId())) {
                result.setData(true);
                result.setSuccess(true);
                return result;
            }

            // 检查用户是否有专门的下载配置
            if (stdDownloadConfigService.hasUserDownloadConfig(sysUser.getUserId())) {
                // 使用方案2：针对单独用户的配置
                List<StdDownloadConfig> validConfigs = stdDownloadConfigService.getValidUserDownloadConfigs(sysUser.getUserId());

                if (validConfigs.isEmpty()) {
                    // 有配置记录但没有有效配置，不允许下载
                    result.setData(false);
                    result.setMsg("您的下载配置已过期或被禁用");
                } else {
                    // 检查是否有可用的配置（下载次数未超限）
                    boolean canDownload = false;
                    for (StdDownloadConfig config : validConfigs) {
                        int configValue = Integer.parseInt(config.getConfigValue());
                        int downloadCount = config.getDownloadCount() != null ? config.getDownloadCount() : 0;

                        if (downloadCount < configValue) {
                            canDownload = true;
                            break;
                        }
                    }

                    result.setData(canDownload);
                    if (!canDownload) {
                        result.setMsg("您的下载次数已达到配置上限");
                    }
                }
            } else {
                // 使用方案1：全局配置
                Integer counts = stdDownloadService.getDownloadCount(sysUser.getUserId());
                SysConfig downloadLimitConfig = sysConfigService.getByCode("download_limit");

                if (downloadLimitConfig == null) {
                    result.setData(false);
                    result.setMsg("系统下载配置不存在");
                } else {
                    Integer allowedDownloadCount = Integer.parseInt(downloadLimitConfig.getConfigValue());
                    boolean canDownload = counts < allowedDownloadCount;

                    result.setData(canDownload);
                    if (!canDownload) {
                        result.setMsg("您当日下载量已达上限（" + allowedDownloadCount + "次）");
                    }
                }
            }

            result.setSuccess(true);
        } catch (Exception e) {
            System.err.println("Error in canDownload: " + e.getMessage());
            e.printStackTrace();
            result.setData(false);
            result.setSuccess(false);
            result.setMsg("检查下载权限时发生错误: " + e.getMessage());
        }

        return result;
    }

    /**
     * 检查是否可以预览
     *
     * @param stdInfoId 标准ID
     * @return com.pearadmin.common.web.domain.response.Result<java.lang.Boolean>
     * @throws IOException 异常
     */
    @GetMapping("canPreview")
    public Result<Boolean> canPreview(@RequestParam(name = "stdInfoId", defaultValue = "0",required = true) String stdInfoId) throws IOException {
        System.out.println("Checking if can preview: " + stdInfoId);
        var result = new Result<Boolean>();
        result.setSuccess(true);

        try {
            // 获取当前用户
            SysUser sysUser = UserContext.currentUser();
            if (sysUser == null) {
                System.err.println("Current user is null");
                result.setSuccess(false);
                result.setMsg("用户未登录或会话已过期");
                return result;
            }
            System.out.println("Current user: " + sysUser.getUsername() + ", userId: " + sysUser.getUserId());

            // 获取用户当天的预览次数
            Integer counts = stdDownloadService.getPreviewCount(sysUser.getUserId());
            if (counts == null) {
                counts = 0;
            }
            System.out.println("Today's preview count: " + counts);

            // 获取预览限制配置
            SysConfig previewLimitConfig = sysConfigService.getByCode("preview_limit");
            if (previewLimitConfig == null) {
                // 如果配置不存在，创建默认配置
                System.out.println("Preview limit config not found, creating default");
                previewLimitConfig = new SysConfig();
                previewLimitConfig.setConfigName("预览限制");
                previewLimitConfig.setConfigCode("preview_limit");
                previewLimitConfig.setConfigType("system");
                previewLimitConfig.setConfigValue("10"); // 默认每日预览次数为10次
                sysConfigService.save(previewLimitConfig);
            }

            Integer allowedPreviewCount=0;
            try {
                allowedPreviewCount = Integer.parseInt(previewLimitConfig.getConfigValue());
            } catch (NumberFormatException e) {
                System.err.println("Invalid preview limit value: " + previewLimitConfig.getConfigValue());
                allowedPreviewCount = 10; // 默认值
                // 更新为有效值
                previewLimitConfig.setConfigValue(String.valueOf(allowedPreviewCount));
                sysConfigService.updateById(previewLimitConfig);
            }
            System.out.println("Allowed preview count: " + allowedPreviewCount);

            // 检查用户当日是否已经预览过该文件
//            var fileHasPreview = stdDownloadService.getOne(new LambdaQueryWrapper<StdDownload>()
//                    .eq(StdDownload::getStdInfoId, stdInfoId)
//                    .eq(StdDownload::getUserId, sysUser.getUserId())
//                    .eq(StdDownload::getOpType, 2)
//                    .gt(StdDownload::getCreateTime, LocalDate.now())
//                    .lt(StdDownload::getCreateTime, LocalDate.now().plusDays(1))
//            );

            // 判断是否可以预览
//            if (counts < allowedPreviewCount || (fileHasPreview != null && StringUtils.isNotBlank(fileHasPreview.getId()))) {
            if (counts < allowedPreviewCount){
                System.out.println("User can preview");
                result.setData(true);
            } else {
                System.out.println("User cannot preview, limit exceeded: " + counts + " >= " + allowedPreviewCount);
                result.setData(false);
                result.setMsg("你今日的预览次数已达上限（" + allowedPreviewCount + "次）");
            }
        } catch (Exception e) {
            System.err.println("Error in canPreview: " + e.getMessage());
            e.printStackTrace();
            result.setSuccess(false);
            result.setMsg("检查预览权限时发生错误: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取有下载记录的用户列表（管理员功能）
     * 按最新下载时间排序
     *
     * @param pageDomain 分页参数
     * @param searchKey 搜索关键字（姓名或邮箱）
     * @return 用户列表
     * @throws IOException 异常
     */
    @GetMapping("adminUsers")
    public ResultTable getAdminUsers(PageDomain pageDomain,
                                   @RequestParam(value = "searchKey", required = false) String searchKey) throws IOException {
        if (!security()) {
            return pageTable(null, 0);
        }

        try {
            // 分页参数
            int pageNum = pageDomain != null && pageDomain.getPage() != null ? pageDomain.getPage() : 1;
            int pageSize = pageDomain != null && pageDomain.getLimit() != null ? pageDomain.getLimit() : 10;

            // 使用原生SQL查询用户列表，按最新下载时间排序
            var page = stdDownloadMapper.selectAdminUserList(new Page<>(pageNum, pageSize), searchKey);

            return pageTable(page.getRecords(), page.getTotal());
        } catch (Exception e) {
            System.err.println("Error while querying admin user list: " + e.getMessage());
            e.printStackTrace();
            return pageTable(null, 0);
        }
    }

    /**
     * 获取指定用户的下载记录（管理员功能）
     *
     * @param userId 用户ID
     * @param model 查询条件
     * @param pageDomain 分页参数
     * @return 下载记录列表
     * @throws IOException 异常
     */
    @GetMapping("adminData")
    public ResultTable getAdminData(@RequestParam("userId") String userId, StdDownload model, PageDomain pageDomain) throws IOException {
        if (!security()) {
            return pageTable(null, 0);
        }

        try {
            var query = new LambdaQueryWrapper<StdDownload>();

            // 设置查询条件：指定用户且操作类型为下载(1)或null
            query.eq(StdDownload::getUserId, userId);
            query.and(wrapper -> wrapper.eq(StdDownload::getOpType, 1).or().isNull(StdDownload::getOpType));

            // 添加搜索条件
            if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getSimpleSearchKey())) {
                var keys = model.getSimpleSearchKey();
                while (keys.contains("  ")) {
                    keys = keys.replaceFirst("  ", " ");
                }
                var keyArr = keys.split(" ");
                for (String key : keyArr) {
                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(key)) {
                        query.and(foo -> foo.like(StdDownload::getStdNo, "%" + key + "%")
                                .or().like(StdDownload::getStdOrgName, "%" + key + "%")
                                .or().like(StdDownload::getStdChineseName, "%" + key + "%")
                                .or().like(StdDownload::getPdfFileName, "%" + key + "%"));
                    }
                }
            }

            // 按创建时间降序排序
            query.orderByDesc(StdDownload::getCreateTime);

            // 分页参数
            int pageNum = pageDomain != null && pageDomain.getPage() != null ? pageDomain.getPage() : 1;
            int pageSize = pageDomain != null && pageDomain.getLimit() != null ? pageDomain.getLimit() : 10;

            var page = stdDownloadMapper.selectPage(new Page<StdDownload>(pageNum, pageSize), query);

            // 获取所有记录
            var records = page.getRecords();

            // 如果有记录，查询是否收藏
            if (records != null && !records.isEmpty()) {
                // 提取所有的 stdInfoId
                List<String> stdInfoIds = records.stream()
                        .map(StdDownload::getStdInfoId)
                        .filter(id -> id != null && !id.isEmpty())
                        .collect(Collectors.toList());

                if (!stdInfoIds.isEmpty()) {
                    // 查询收藏状态
                    Map<String, Integer> collectStatus = stdInfoService.IsCollected(stdInfoIds);

                    // 更新收藏状态
                    for (StdDownload record : records) {
                        if (record.getStdInfoId() != null && collectStatus.containsKey(record.getStdInfoId())) {
                            record.setIsCollected(collectStatus.get(record.getStdInfoId()).toString());
                        } else {
                            record.setIsCollected("0"); // 默认为未收藏
                        }
                    }
                }
            }

            return pageTable(records, page.getTotal());
        } catch (Exception e) {
            System.err.println("Error while querying admin download records: " + e.getMessage());
            e.printStackTrace();
            return pageTable(null, 0);
        }
    }
}
