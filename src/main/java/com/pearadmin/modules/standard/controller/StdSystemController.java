package com.pearadmin.modules.standard.controller;

import com.alibaba.druid.pool.PreparedStatementPool;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.BeanProperty;
import com.google.common.primitives.Bytes;
import com.mysql.cj.ServerPreparedQueryTestcaseGenerator;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.tools.CResult;
import com.pearadmin.common.tools.DESUtil;
import com.pearadmin.common.tools.StandardTypeEnum;
import com.pearadmin.common.tools.Tool;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.EquipmentAssessment;
import com.pearadmin.modules.standard.domain.StdClassJoinStandard;
import com.pearadmin.modules.standard.domain.StdSystem;
import com.pearadmin.modules.standard.service.EquipmentAssessmentService;
import com.pearadmin.modules.standard.service.StdSystemService;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.var;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.sl.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.xpath.operations.Mod;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.InetAddress;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Describe:  标准体系 控制器
 * Author: 就 眠 仪 式
 * CreateTime: 2022/10/23
 */
@RestController
@Api(tags = {"标准体系"})
@RequestMapping(ControllerConstant.API_STD_SYSTEM_PREFIX)
public class StdSystemController extends BaseController {
    private final String MODULE_PATH = "stdsystem/";
    private final String MODEULE_SYSTEM_TYPE="1";
    @Resource
    private StdSystemService stdSystemService;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private StringEncryptor stringEncryptor;
    //可使用总小时数
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;

    /**
     * 检查使用许可
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    public Boolean security() throws IOException {
        Boolean isSecurity=false;
        if(checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        }
        else
        {
            isSecurity=true;
        }
        return isSecurity;
    }

    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("main")
    public ModelAndView main() {
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("data")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable data(StdSystem model, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return null;
        }
        var query=new QueryWrapper<StdSystem>();
        query.eq("system_type",MODEULE_SYSTEM_TYPE);
        // 过滤已删除的体系（status != -1）
        query.ne("status", "-1");
        if(StringUtils.isNotBlank(model.getName()))
        {
            query.like("name",model.getName());
        }
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getSimpleSearchKey()))
        {
            var keys=model.getSimpleSearchKey();
            while(keys.contains("  "))
            {
                keys=keys.replaceFirst("  "," ");
            }
            var keyArr=keys.split(" ");
            for(String key : keyArr)
            {
                //var subQuery=new QueryWrapper<StdInfo>();
                //subQuery.like("std_no",key).or().like("std_org_Name",key);
                //单独的一种写法
                // Consumer<QueryWrapper<StdInfo>> c=foo->foo.like("std_no",key).or().like("std_org_Name",key);
                // c.accept(query);
                query.and(foo->foo.like("name",key));
                //query.and((Consumer<QueryWrapper<StdInfo>>) subQuery);
            }
        }
        var page= stdSystemService.page(new Page<StdSystem>(pageDomain.getPage(),pageDomain.getLimit()),query);
        return pageTable(page.getRecords(), page.getTotal());
    }

    /**
     * Describe: 已发布体系列表
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("datapublished")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable datapublished(StdSystem model, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return null;
        }
        var query=new QueryWrapper<StdSystem>();
        query.eq("system_type",MODEULE_SYSTEM_TYPE);
        query.eq("is_publish","1");
        // 过滤已删除的体系（status != -1）
        query.ne("status", "-1");
        if(StringUtils.isNotBlank(model.getName()))
        {
            query.like("name",model.getName());
        }
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getSimpleSearchKey()))
        {
            var keys=model.getSimpleSearchKey();
            while(keys.contains("  "))
            {
                keys=keys.replaceFirst("  "," ");
            }
            var keyArr=keys.split(" ");
            for(String key : keyArr)
            {
                //var subQuery=new QueryWrapper<StdInfo>();
                //subQuery.like("std_no",key).or().like("std_org_Name",key);
                //单独的一种写法
                // Consumer<QueryWrapper<StdInfo>> c=foo->foo.like("std_no",key).or().like("std_org_Name",key);
                // c.accept(query);
                query.and(foo->foo.like("name",key));
                //query.and((Consumer<QueryWrapper<StdInfo>>) subQuery);
            }
        }
        var page= stdSystemService.page(new Page<StdSystem>(pageDomain.getPage(),pageDomain.getLimit()),query);
        return pageTable(page.getRecords(), page.getTotal());
    }

    @GetMapping("list")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public List<StdSystem> list(StdSystem model, PageDomain pageDomain) throws IOException {
        if(!security())
        {
            return null;
        }
        var query=new LambdaQueryWrapper<StdSystem>();
        query.eq(StdSystem::getSystemType,MODEULE_SYSTEM_TYPE);
        // 过滤已删除的体系（status != -1）
        query.ne(StdSystem::getStatus, "-1");
        List<StdSystem> list = stdSystemService.list(query);
        return list;
    }

    /**
     * Describe: 数据字典类型新增视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("add")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public ModelAndView add() {
        return jumpPage(MODULE_PATH + "add");
    }

    @GetMapping("stdofsystem")
    public ModelAndView stdofsystem(Model model,String sysId){
        var system=stdSystemService.getById(sysId);
        model.addAttribute("system",system);
        return jumpPage(MODULE_PATH + "stdlist");
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("save")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result save(@RequestBody StdSystem model) throws IOException {
        model.setIsPublish("0");//编辑中
        model.setSystemType(MODEULE_SYSTEM_TYPE);
        boolean result = stdSystemService.save(model);
        return decide(result);
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("index")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView index(Model model) {
        return jumpPage(MODULE_PATH + "index");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("edit")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView edit(Model model,String id) {
        var data=stdSystemService.getById(id);
        model.addAttribute("model", data);
        return jumpPage(MODULE_PATH + "edit");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detail")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detail(Model model,String id) {
        var data=stdSystemService.getById(id);
        model.addAttribute("model",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PutMapping("update")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result update(@RequestBody StdSystem stdSystem) throws IOException {
        stdSystem.setSystemType(MODEULE_SYSTEM_TYPE);
        boolean result = stdSystemService.updateById(stdSystem);
        return decide(result);
    }

    /**
     * Describe: 删除标准体系（逻辑删除，设置status=-1）
     * Param: id
     * Return: Result
     */
    @DeleteMapping("remove/{id}")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result remove(@PathVariable("id") String id) throws IOException {
        try {
            // 检查是否为标准体系类型
            StdSystem system = stdSystemService.getById(id);
            if (system == null) {
                return Result.failure("体系不存在");
            }

            if (!MODEULE_SYSTEM_TYPE.equals(system.getSystemType())) {
                return Result.failure("只能删除标准体系类型的数据");
            }

            // 逻辑删除：设置status为-1
            boolean result = stdSystemService.update(null,
                new LambdaUpdateWrapper<StdSystem>()
                    .eq(StdSystem::getId, id)
                    .set(StdSystem::getStatus, "-1")
            );

            if (result) {
                return Result.success("删除成功");
            } else {
                return Result.failure("删除失败");
            }
        } catch (Exception e) {
            System.err.println("删除体系时发生错误: " + e.getMessage());
            e.printStackTrace();
            return Result.failure("删除失败：" + e.getMessage());
        }
    }

    /**
     * Describe: 发布
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("publish")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result publish(@RequestBody StdSystem stdSystem) throws IOException {
        boolean result = stdSystemService.update(null,new LambdaUpdateWrapper<StdSystem>().eq(StdSystem::getId,stdSystem.getId()).set(StdSystem::getIsPublish,"1"));
        return decide(result);
    }

    /**
     * Describe: 下架
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("toEditStatus")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result toEditStatus(@RequestBody StdSystem stdSystem) throws IOException {
        boolean result = stdSystemService.update(null,new LambdaUpdateWrapper<StdSystem>().eq(StdSystem::getId,stdSystem.getId()).set(StdSystem::getIsPublish,"0"));
        return decide(result);
    }


    /**
     * Describe: 停止发布
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("stopPublish")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result stopPublish(@RequestBody StdSystem stdSystem) throws IOException {
        boolean result = stdSystemService.update(null,new LambdaUpdateWrapper<StdSystem>().eq(StdSystem::getId,stdSystem.getId()).set(StdSystem::getIsPublish,"-1"));
        return decide(result);
    }


    /**
     *  excel文件上传
     */
    @PostMapping("/file-upload")
    @ApiOperation(value = "excel文件上传")
    public CResult<?> importExcel(HttpServletRequest req,HttpServletResponse response) throws Exception {
      //  public void importExcel(HttpServletRequest req,HttpServletResponse response) throws Exception {
        var result=new CResult<Boolean>();
        result.setResult(true);
        result.setSuccess(true);
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) req;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        var customSessionId=req.getParameter("customSessionId");
        try {
            var uploadResult= stdSystemService.upload(MODEULE_SYSTEM_TYPE,customSessionId,file.getInputStream());
            if(!uploadResult.isSuccess())
            {
                result.setResult(false);
                result.setSuccess(false);
                var fileName= savelErrToExcel(uploadResult.getMsg());
                result.setMessage(fileName);
            }
        }
        catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
            result.setResult(false);
            result.setSuccess(false);
            result.setMessage("上传失败，"+ex.getMessage());
            return result;
        }
        return result;
    }

    /**
     * 获取jar包所在文件路径
     *
     * @param
     * @return java.lang.String
     * @throws
     */
    public String getJarFilePath() {
        ApplicationHome home = new ApplicationHome(getClass());
        File jarFile = home.getSource();
        return jarFile.getParentFile().toString();
    }

    public String savelErrToExcel(String err) throws IOException {
        // create a new Workbook
        var sdf=new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss");
        var fileName="system_upload_err_"+sdf.format(new Date())+".xlsx";
        String fileFullPath=getJarFilePath()+"\\"+fileName;
        Workbook workbook = new XSSFWorkbook();
        // create a new sheet
        XSSFSheet sheet = (XSSFSheet) workbook.createSheet("Sheet1");
        sheet.setColumnWidth(0, 1000);// 设置第二列的宽度
        sheet.setColumnWidth(1, 10000);// 设置第二列的宽度
        // create some data rows
        Row row = sheet.createRow(0);
        row.createCell(0).setCellValue("序号");
        row.createCell(1).setCellValue("错误描述");
        var errArray=err.split(";;");
        Integer rowIndex=1;
        for(Integer i=0;i<errArray.length;i++)
        {
            if(StringUtils.isNotBlank(errArray[i])) {
                row = sheet.createRow(rowIndex);
                row.createCell(0).setCellValue(rowIndex);
                row.createCell(1).setCellValue(errArray[i]);
                rowIndex++;
            }
        }
        // write the Workbook to a ByteArrayOutputStream
        var cal= Calendar.getInstance();
        var outputStream=new FileOutputStream(fileFullPath);
        // set the headers for downloading the file
        try {
            workbook.write(outputStream);
            return fileName;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

}
