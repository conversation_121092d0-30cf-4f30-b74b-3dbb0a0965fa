package com.pearadmin.modules.standard.controller;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.tools.LogTypeEnum;
import com.pearadmin.common.tools.ServletUtil;
import com.pearadmin.common.tools.StandardTypeEnum;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.*;
import com.pearadmin.modules.standard.service.StdInfoService;
import com.pearadmin.modules.standard.service.StdLogService;
import com.pearadmin.modules.standard.service.StdOpinionService;
import com.pearadmin.modules.sys.domain.SysUser;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import io.swagger.annotations.Api;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Describe:  日志 控制器
 * Author: chongyibo
 * CreateTime: 2023/5/23
 */
@RestController
@Api(tags = {"日志"})
@RequestMapping(ControllerConstant.API_STD_LOG_PREFIX)
public class StdLogController extends BaseController {
    private final String MODULE_PATH = "stdlog/";

    @Resource
    private StdLogService stdLogService;

    /**
     * Describe: 标准意见列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("main")
    public ModelAndView main(Model modelstdId) {
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 全文检索统计
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("static_1")
    public ModelAndView static_1() {
        return jumpPage( "stdstatictics/index_1");
    }

    /**
     * Describe: 访问查看统计
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("static_2")
    public ModelAndView static_2() {
        return jumpPage( "stdstatictics/index_2");
    }

    /**
     * Describe: 体系引用统计
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("static_3")
    public ModelAndView static_3() {
        return jumpPage( "stdstatictics/index_3");
    }

    /**
     * Describe: 在线预览统计
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("static_4")
    public ModelAndView static_4() {
        return jumpPage( "stdstatictics/index_4");
    }

    /**
     * Describe: 全文下载统计
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("static_5")
    public ModelAndView static_5() {
        return jumpPage( "stdstatictics/index_5");
    }

    /**
     * Describe: 意见反馈统计
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("static_6")
    public ModelAndView static_6() {
        return jumpPage( "stdstatictics/index_6");
    }

    /**
     * Describe: 用户收藏统计
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("static_7")
    public ModelAndView static_7() {
        return jumpPage( "stdstatictics/index_7");
    }

    /**
     * Describe: 岗位引用统计
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("static_8")
    public ModelAndView static_8() {
        return jumpPage( "stdstatictics/index_8");
    }
    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("log")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result save(@RequestBody StdLog model) throws IOException {
        switch (model.getLogTypeName())
        {
            case "Standard_OverView":
                model.setLogType("1");
                break;
            case "Standard_Online_Read":
                model.setLogType("2");
                break;
            case "Standard_Collect":
                model.setLogType("3");
                break;
            case "Standard_Query":
                model.setLogType("4");
                break;
            case "Standard_Opinion":
                model.setLogType("5");
                break;
            case "Solr_Query":
                model.setLogType("6");
                break;
            case "Solr_Query_More_Result":
                model.setLogType("7");
                break;
            case "StdSystem_OverView":
                model.setLogType("8");
                break;
            case "StdSystem_Struct_Img_OverView":
                model.setLogType("9");
                break;
            case "StdSystem_Standard_OverView":
                model.setLogType("10");
                break;
            case "StdSystem_Standard_Online_Read":
                model.setLogType("11");
                break;
            case "StdSystem_Standard_Collect":
                model.setLogType("12");
                break;
            case "StdSystem_Standard_Query":
                model.setLogType("13");
                break;
            case "StdSystem_Standard_Opinion":
                model.setLogType("14");
                break;
            case "Post_Standard_OverView":
                model.setLogType("15");
                break;
            case "Post_Standard_Online_Read":
                model.setLogType("16");
                break;
            case "Post_Standard_Collect":
                model.setLogType("17");
                break;
            case "Post_Standard_Query":
                model.setLogType("18");
                break;
            case "Post_Standard_Opinion":
                model.setLogType("19");
                break;
            case "Standard_Download":
                model.setLogType("20");
                break;
        }
        SysUser sysUser = UserContext.currentUser();
        model.setIp(ServletUtil.getIpAddress());
        model.setUserId(sysUser.getUserId());
        model.setUserName(sysUser.getUsername());
        var result=stdLogService.save(model);
        return decide(result);
    }
    /**
     * 根据日志类型分页
     * 1.logType=7, 标准-全文检索_查看更多匹配结果
     * 2.logType=1, 访问查看统计
     * 3.logType=20 下载统计
     * 4.logType=2 在线预览统计
     *
     * @param model
     * @param pageDomain
     * @return com.pearadmin.common.web.domain.response.module.ResultTable
     * @throws
     */
    @GetMapping("getPageofLogType")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable dataofsystem(StdLog model, PageDomain pageDomain) throws ParseException, IOException {
        var query=new QueryWrapper<StdInfoStatictics>();
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getLogType())) {
            if(StringUtils.isNotBlank(model.getSimpleSearchKey()))
            {
                var keys=model.getSimpleSearchKey();
                while(keys.contains("  "))
                {
                    keys=keys.replaceFirst("  "," ");
                }
                var keyArr=keys.split(" ");
                for(String key : keyArr)
                {
                    query.and(foo->foo.like("item_id",key).or().like("item_name",key));
                }
            }
            query.orderByDesc("counts");
            String logType="1";
            var strLogType=model.getLogType();
            if(strLogType.equals("Standard_OverView"))
            {
                logType="1";
            }
            else if(strLogType.equals("Standard_Online_Read"))
            {
                logType="2";
            }
            else if(strLogType.equals("Standard_Collect"))
            {
                logType="3";
            }
            else if(strLogType.equals("Standard_Query"))
            {
                logType="4";
            }
            else if(strLogType.equals("Standard_Opinion"))
            {
                logType="5";
            }
            else if(strLogType.equals("Solr_Query"))
            {
                logType="6";
            }
            else if(strLogType.equals("Solr_Query_More_Result"))
            {
                logType="7";
            }
            else if(strLogType.equals("StdSystem_OverView"))
            {
                logType="8";
            }
            else if(strLogType.equals("StdSystem_Struct_Img_OverView"))
            {
                logType="9";
            }
            else if(strLogType.equals("StdSystem_Standard_OverView"))
            {
                logType="10";
            }
            else if(strLogType.equals("StdSystem_Standard_Online_Read"))
            {
                logType="11";
            }  else if(strLogType.equals("StdSystem_Standard_Collect"))
            {
                logType="12";
            }
            else if(strLogType.equals("StdSystem_Standard_Query"))
            {
                logType="13";
            }
            else if(strLogType.equals("StdSystem_Standard_Opinion"))
            {
                logType="14";
            }
            else if(strLogType.equals("Post_Standard_OverView"))
            {
                logType="15";
            }
            else if(strLogType.equals("Post_Standard_Online_Read"))
            {
                logType="16";
            }
            else if(strLogType.equals("Post_Standard_Collect"))
            {
                logType="17";
            }
            else if(strLogType.equals("Post_Standard_Query"))
            {
                logType="18";
            }
            else if(strLogType.equals("Post_Standard_Opinion"))
            {
                logType="19";
            }
            else if(strLogType.equals("Standard_Download"))
            {
                logType="20";
            }
            //获取可以下载的标准的数量
            var page1= stdLogService.getPageByLogType(new Page<StdInfoStatictics>(pageDomain.getPage(),pageDomain.getLimit()),logType,query);
            var total= stdLogService.getCountsByLogType(logType,query);
            var pg= pageTable(stdLogService.fillIsCollected(page1.getRecords()),total);
            return pg;
        }
        else {
            return pageTable(null, 0);
        }
    }

    /**
     * 获取体系统计
     * systemType：1 体系
     *             2 岗位
     *             3 个人收藏
     * @param null
     * @return
     * @throws
     */
    @GetMapping("getPageofReferedBySystem")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable getPageofReferedBySystem(StdLog model, PageDomain pageDomain) throws ParseException, IOException {
        var query=new QueryWrapper<StdInfoStatictics>();
        if(StringUtils.isNotBlank(model.getSimpleSearchKey()))
        {
            var keys=model.getSimpleSearchKey();
            while(keys.contains("  "))
            {
                keys=keys.replaceFirst("  "," ");
            }
            var keyArr=keys.split(" ");
            for(String key : keyArr)
            {
                query.and(foo->foo.like("item_id",key).or().like("item_name",key));
            }
        }
        query.orderByDesc("counts");
        //获取可以下载的标准的数量
        var page1= stdLogService.getPageofReferedBySystem(new Page<StdInfoStatictics>(pageDomain.getPage(),pageDomain.getLimit()),model.getSystemType(),query);
        var total= stdLogService.getCountofReferedBySystem(model.getSystemType(),query);
        var pg= pageTable(stdLogService.fillIsCollected(page1.getRecords()),total);
        return pg;
    }

    /**
     * 分页获取意见反馈统计
     *
     * @param model
     * @param pageDomain
     * @return com.pearadmin.common.web.domain.response.module.ResultTable
     * @throws
     */
    @GetMapping("getPageofOpinion")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable getPageofOpinion(StdLog model, PageDomain pageDomain) throws ParseException, IOException {
        var query=new QueryWrapper<StdInfoStatictics>();
        if(StringUtils.isNotBlank(model.getSimpleSearchKey()))
        {
            var keys=model.getSimpleSearchKey();
            while(keys.contains("  "))
            {
                keys=keys.replaceFirst("  "," ");
            }
            var keyArr=keys.split(" ");
            for(String key : keyArr)
            {
                query.and(foo->foo.like("item_id",key).or().like("item_name",key));
            }
        }
        query.orderByDesc("counts");
        //获取可以下载的标准的数量
        var page1= stdLogService.getPageOfOpinion(new Page<StdInfoStatictics>(pageDomain.getPage(),pageDomain.getLimit()),query);
        var total= stdLogService.getCountsOfOpinion(query);
        var pg= pageTable(stdLogService.fillIsCollected(page1.getRecords()),total);
        return pg;
    }

    /**
     * 1.全文检索统计
     * 2.个人收藏统计
     * 3.岗位引用统计
     * 4.意见反馈统计
     *
     * @param null
     * @return
     * @throws
     */



}
