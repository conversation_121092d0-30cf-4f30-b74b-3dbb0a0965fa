package com.pearadmin.modules.standard.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.google.common.primitives.Bytes;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.tools.CResult;
import com.pearadmin.common.tools.DESUtil;
import com.pearadmin.common.tools.SequenceUtil;
import com.pearadmin.common.tools.Tool;
import com.pearadmin.common.tools.string.StringUtil;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.EquipmentAssessment;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.service.StdInfoService;
import com.pearadmin.modules.sys.domain.SysDict;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import com.pearadmin.modules.sys.service.SysDictService;
import io.swagger.annotations.Api;
import lombok.var;
import org.apache.catalina.core.StandardService;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.HttpSolrClient;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;

/**
 * Describe: 标准控制器
 * Author: 就 眠 仪 式
 * CreateTime: 2022/10/23
 */
@RestController
@Api(tags = {"标准文件"})
@RequestMapping(ControllerConstant.API_STANDARD_PREFIX)
public class StdInfoController extends BaseController {
    private final String MODULE_PATH = "standard/";
    @Value(value="${pdf.source.china-path}")
    private String pdfPath;
    @Value(value="${pdf.encryptSource.path}")
    private String encryptPdfPath;

    @Resource
    private StdInfoService stdInfoService;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private StringEncryptor stringEncryptor;
    //可使用总小时数
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;

    /**
     * 检查使用许可
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    public Boolean security() throws IOException {
        Boolean isSecurity=false;
        if(checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (org.apache.commons.lang.StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        }
        else
        {
            isSecurity=true;
        }
        return isSecurity;
    }
    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("main")
    public ModelAndView main() {
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("myIndex")
    public ModelAndView index() {
        return jumpPage(MODULE_PATH + "index");
    }


    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("data")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable data(StdInfo stdInfo, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return new ResultTable();
        }
        var result=new CResult<>();
      /*  var sdf=new SimpleDateFormat("yyyy-MM-dd");
        var dateLimit=sdf.parse("2023-2-20");
        if(dateLimit.compareTo(new Date())<=0)
        {
            return new ResultTable();
        }*/
        var query=new LambdaQueryWrapper<StdInfo>();
        if(StringUtils.isNotBlank(stdInfo.getStdChineseName()))
        {
            query.like(StdInfo::getStdChineseName,stdInfo.getStdChineseName());
        }
        if(StringUtils.isNotBlank(stdInfo.getPrimaryCoverage()))
        {
            query.like(StdInfo::getPrimaryCoverage,stdInfo.getPrimaryCoverage());
        }
        if(StringUtils.isNotBlank(stdInfo.getDrafter()))
        {
            query.like(StdInfo::getDrafter,stdInfo.getDrafter());
        }
        if(StringUtils.isNotBlank(stdInfo.getDraftingUnit()))
        {
            query.like(StdInfo::getDraftingUnit,stdInfo.getDraftingUnit());
        }
        if(StringUtils.isNotBlank(stdInfo.getStdNo()))
        {
            query.like(StdInfo::getStdNo,stdInfo.getStdNo());
        }
        //query.select(StdInfo.class, info-> !info.getColumn().equals("bin_content"));
        var page =stdInfoService.page(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
        return pageTable(page.getRecords(), page.getTotal());
    }

    @GetMapping("list")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public List<StdInfo> list(StdInfo stdInfo, PageDomain pageDomain) {
        var query=new LambdaQueryWrapper<StdInfo>();
        if(StringUtils.isNotBlank(stdInfo.getStdNo()))
        {
            query.like(StdInfo::getStdNo, stdInfo.getStdNo());
        }
        if(StringUtils.isNotBlank(stdInfo.getStdChineseName()))
        {
            query.like(StdInfo::getStdChineseName, stdInfo.getStdChineseName());
        }
        if(StringUtils.isNotBlank(stdInfo.getDraftingUnit()))
        {
            query.like(StdInfo::getDraftingUnit, stdInfo.getDraftingUnit());
        }
        if(StringUtils.isNotBlank(stdInfo.getDrafter()))
        {
            query.like(StdInfo::getDrafter, stdInfo.getDrafter());
        }
        if(StringUtils.isNotBlank(stdInfo.getPrimaryCoverage()))
        {
            query.like(StdInfo::getPrimaryCoverage, stdInfo.getPrimaryCoverage());
        }
        //query.select(StdInfo.class, info-> !info.getColumn().equals("bin_content"));
        List<StdInfo> list = stdInfoService.list(query);
        return list;
    }

    /**
     * Describe: 数据字典类型新增视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("add")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public ModelAndView add() {
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("save")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result save(@RequestBody StdInfo stdInfo) throws IOException {
        boolean result = stdInfoService.save(stdInfo);
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(stdInfo.getPdfFileName()))
        {
            File file=new File(pdfPath+"\\"+stdInfo.getPdfFileName());
            if(file.exists())
            {
                var byteData=Tool.getBinaryData(file);
                saveEncryptPdf(stdInfo.getId(), byteData);
            }
        }
        return decide(result);
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("edit")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView edit(Model model,String stdID) {
        var data=stdInfoService.getById(stdID);
        model.addAttribute("stdInfo",data);
        return jumpPage(MODULE_PATH + "edit");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detail")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detail(Model model,String stdID) {
        var data=stdInfoService.getById(stdID);
        model.addAttribute("stdInfo",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detailbystdno")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detailbystdno(Model model,String stdNo) {
        var data=stdInfoService.getOne(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdNo,stdNo).last("limit 1"));
        model.addAttribute("stdInfo",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PutMapping("update")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result update(@RequestBody StdInfo stdInfo) throws IOException {
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(stdInfo.getPdfFileName()))
        {
            File file=new File(pdfPath+"\\"+stdInfo.getPdfFileName());
            if(file.exists())
            {
                var byteData= Tool.getBinaryData(file);
                saveEncryptPdf(stdInfo.getId(), byteData);
            }
        }
        boolean result = stdInfoService.updateById(stdInfo);
        return decide(result);
    }

    /**
     * Describe: 批量生成加密文件
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("batchUpdateDoc")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result batchUpdateDoc() throws IOException {
        var list=stdInfoService.list();
        for(var i=0;i<list.size();i++)
        {
            if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(list.get(i).getPdfFileName()))
            {
                File file=new File(pdfPath+"\\"+list.get(i).getPdfFileName());
                if(file.exists())
                {
                    var byteData= Tool.getBinaryData(file);
                    saveEncryptPdf(list.get(i).getId(), byteData);
                }
            }
        }
        return decide(true);
    }

  /*  *//**
     * Describe: 批量生成加密文件
     * Param: sysDictType
     * Return: ModelAndView
     *//*
    @PostMapping("batchDecryptDoc")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result batchDecryptDoc() throws IOException {
        var list=stdInfoService.list();
        for(var i=0;i<list.size();i++)
        {
            var stdInfo=list.get(i);
            var data=getDecyptData(encryptPdfPath,stdInfo.getId());
            saveFile(stdInfo.getPdfFileName(),data);
        }
        return decide(true);
    }*/

    /**
     *从pdf加密后的数据中解密出真实的二进制数据
     *
     * @param id
     * @return byte[]
     * @throws
     */
    public byte[] getDecyptData(String filePath, String id) throws IOException {
        var convertData=new byte[0];
        File file=new File(filePath+"\\"+id);
        var byteData= Tool.getBinaryData(file);
        convertData= DESUtil.getDecryptBytes(byteData);
        return convertData;
    }


    /**
     * Describe: 数据字典删除
     * Param: sysDictType
     * Return: ModelAndView
     */
    @DeleteMapping("remove/{id}")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result remove(@PathVariable("id") String id) throws IOException {
        //var model=stdInfoService.getById(id);
        Boolean result = stdInfoService.removeById(id);
        if(result) {
            //删除磁盘上的文件
            Files.deleteIfExists(Paths.get(encryptPdfPath+"\\"+id));
            //Files.deleteIfExists(Paths.get(pdfPath+"\\"+model.getPdfFileName()));
            //删除solr对应对象
            HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_home").build();
            try {
                client.deleteById(id);
                System.out.print(id + " 在solr索引中删除成功");
            } catch (SolrServerException e) {
                e.printStackTrace();
                return decide(false);
            } catch (IOException e) {
                e.printStackTrace();
                try {
                    client.rollback();
                    return decide(false);
                } catch (SolrServerException ex) {
                    ex.printStackTrace();
                    return decide(false);
                } catch (IOException ex) {
                    ex.printStackTrace();
                    return decide(false);
                }
            }
        }
        return decide(result);
    }

    /**
     * 数据加密后将pdf保存为二进制数据
     *
     * @param data
     * @return void
     * @throws
     */
    public void saveEncryptPdf(String id,byte[] data) throws IOException {
        var encryptData= DESUtil.getEncryptBytes(data);
        File file=new File(encryptPdfPath+"\\"+id);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(encryptData);
        fos.flush();
        fos.close();
    }

    /**
     * 数据加密后将pdf保存为二进制数据
     *
     * @param data
     * @return void
     * @throws
     */
    public void saveFile(String fileName,byte[] data) throws IOException {
        File file=new File(pdfPath+"\\"+fileName);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data);
        fos.flush();
        fos.close();
    }
}
