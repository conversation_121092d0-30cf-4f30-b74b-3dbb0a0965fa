package com.pearadmin.modules.standard.controller;

import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.session.HttpSessionContext;
import com.pearadmin.common.web.session.HttpSessionContextHolder;
import com.pearadmin.modules.sys.domain.SysOnlineUser;
import com.pearadmin.modules.sys.domain.SysUser;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.session.SessionInformation;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.core.session.SessionRegistryImpl;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.security.core.context.SecurityContextHolder;

import org.springframework.security.core.Authentication;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@Api(tags = {"在线用户"})
@RequestMapping("/customauth")
public class CustomLoginController extends BaseController {

    @Resource
    private SessionRegistry sessionRegistry;

    public List<SysOnlineUser> getLoginUser() {
        List<Object> allPrincipalsUser = sessionRegistry.getAllPrincipals();
        List<SysOnlineUser> onlineUser = new ArrayList<>();
        for (Object obj : allPrincipalsUser) {
            SysOnlineUser sysOnlineUser = new SysOnlineUser();
            SysUser objs = (SysUser) obj;
            sysOnlineUser.setUserId(objs.getUserId());
            sysOnlineUser.setUsername(objs.getUsername());
            sysOnlineUser.setRealName(objs.getRealName());
            sysOnlineUser.setLastTime(objs.getLastTime());
            sysOnlineUser.setOnlineTime(Duration.between(objs.getLastTime(), LocalDateTime.now()).toMinutes() + "分钟");
            onlineUser.add(sysOnlineUser);
        }
        return onlineUser;
    }

    @GetMapping("l")
    public Result<Boolean> customLogin(@RequestParam String username)
    {
        try {
            var list=getLoginUser();
            var exists=list.stream().filter(d->d.getUsername().equals(username)).isParallel();
            if(exists)
            {
                var res=new Result<Boolean>();
                res.setData(false);
                res.setSuccess(true);
                res.setMsg("当前用户已登录。");
                return res;
            }
            else
            {
                //没有当前用户的登录信息，可直接登录
                var res=new Result<Boolean>();
                res.setData(true);
                res.setSuccess(true);
                return res;
            }
        }
        catch (Exception ex)
        {
            log.error("customauth/l 异常:"+ex.getMessage());
            var res=new Result<Boolean>();
            res.setData(false);
            res.setSuccess(false);
            res.setMsg("发生异常，请重试！");
            return res;
        }
    }

    @GetMapping("/r")
    public Result<Boolean> clearSession(@RequestParam String username)
    {
        List<Object> allPrincipalsUser = sessionRegistry.getAllPrincipals();
        for (Object obj : allPrincipalsUser) {
            SysUser objs = (SysUser) obj;
            if(objs.getUsername().equals(username)) {
                for (SessionInformation sessionInformation : sessionRegistry.getAllSessions(obj, false)) {
                    sessionInformation.expireNow();
                    // 从sessionRegistry中清除session信息
                    sessionRegistry.removeSessionInformation(sessionInformation.getSessionId());
                    HttpSessionContext sessionContext = HttpSessionContextHolder.currentSessionContext();
                    // 销毁session
                    sessionContext.getSession(sessionInformation.getSessionId()).invalidate();
                }
                break;
            }
        }
        var res=new Result<Boolean>();
        res.setData(true);
        res.setSuccess(true);
        return res;
    }
}
