package com.pearadmin.modules.standard.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.StdClassification;
import com.pearadmin.modules.standard.domain.StdSystem;
import com.pearadmin.modules.standard.domain.StdSystemOpinion;
import com.pearadmin.modules.standard.service.StdClassificationService;
import com.pearadmin.modules.standard.service.StdSystemOpinionService;
import com.pearadmin.modules.standard.service.StdSystemService;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import io.swagger.annotations.Api;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.apache.xmlbeans.impl.store.QueryDelegate;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * Describe:  标准体系 控制器
 * Author: 就 眠 仪 式
 * CreateTime: 2022/10/23
 */
@RestController
@Api(tags = {"标准体系意见或建议"})
@RequestMapping(ControllerConstant.API_SYSTEM_OPINION_PREFIX)
public class StdSystemOpinionController extends BaseController {
    private final String MODULE_PATH = "stdsysopinion/";

    @Resource
    private StdSystemOpinionService stdSystemOpinionService;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private StringEncryptor stringEncryptor;
    //可使用总小时数
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;
    @Autowired
    private StdSystemService systemService;
    @Autowired
    private StdClassificationService classificationService;

    /**
     * 检查使用许可
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    public Boolean security() throws IOException {
        Boolean isSecurity=false;
        if(checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        }
        else
        {
            isSecurity=true;
        }
        return isSecurity;
    }

    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("main")
    public ModelAndView main(Model model, @RequestParam String systemId) {
        var system=systemService.getById(systemId);
        model.addAttribute("system",system);
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("data")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dicmtType:data')")
    public ResultTable data(StdSystemOpinion model, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
       {
           return null;
       }
        var query=new LambdaQueryWrapper<StdSystemOpinion>();
        if(StringUtils.isNotBlank(model.getSystemId()))
        {
            query.eq(StdSystemOpinion::getSystemId,model.getSystemId());
        }
        query.orderByDesc(StdSystemOpinion::getCreateTime);
        List<StdSystemOpinion> list = stdSystemOpinionService.list(query);
        return pageTable(list, list.size());
    }

    @GetMapping("list")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public List<StdSystemOpinion> list(StdSystemOpinion model, PageDomain pageDomain) throws IOException {
        if(!security())
        {
            return null;
        }
        var query=new LambdaQueryWrapper<StdSystemOpinion>();
        List<StdSystemOpinion> list = stdSystemOpinionService.list(query);
        return list;
    }

    /**
     * Describe: 数据字典类型新增视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("add")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public ModelAndView add(Model model,@RequestParam String systemId) {
        model.addAttribute("systemId",systemId);
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("save")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result save(@RequestBody StdSystemOpinion model) throws IOException {
        model.setQuestioner(UserContext.currentUser().getRealName());
        if(StringUtils.isNotBlank(model.getClassCodePath()) && model.getClassCodePath()!="-1")
        {
            var stdClass = classificationService.getOne(new LambdaQueryWrapper<StdClassification>().eq(StdClassification::getClassCodePath, model.getClassCodePath()).eq(StdClassification::getSystemId, model.getSystemId()).last("limit 1"));
            if (stdClass != null) {
                model.setClassName(stdClass.getClassName());
            }
        }
        boolean result = stdSystemOpinionService.save(model);
        return decide(result);
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("index")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView index(Model model) {
        return jumpPage(MODULE_PATH + "index");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("edit")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView edit(Model model,String id) {
        var data=stdSystemOpinionService.getById(id);
        model.addAttribute("model", data);
        return jumpPage(MODULE_PATH + "edit");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detail")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detail(Model model,String id) {
        var data=stdSystemOpinionService.getById(id);
        model.addAttribute("model",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PutMapping("update")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result update(@RequestBody StdSystemOpinion model) throws IOException {
        if(StringUtils.isNotBlank(model.getClassCodePath()) && model.getClassCodePath()!="-1")
        {
            var stdClass = classificationService.getOne(new LambdaQueryWrapper<StdClassification>().eq(StdClassification::getClassCodePath, model.getClassCodePath()).eq(StdClassification::getSystemId, model.getSystemId()).last("limit 1"));
            if (stdClass != null) {
                model.setClassName(stdClass.getClassName());
            }
        }
        boolean result = stdSystemOpinionService.updateById(model);
        return decide(result);
    }

    /**
     * Describe: 数据字典删除
     * Param: sysDictType
     * Return: ModelAndView
     */
    @DeleteMapping("remove/{id}")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result remove(@PathVariable("id") String id) throws IOException {
        Boolean result = stdSystemOpinionService.removeById(id);
        return decide(result);
    }

}
