package com.pearadmin.modules.standard.controller;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pearadmin.common.aop.annotation.Log;
import com.pearadmin.common.aop.enums.BusinessType;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.tools.*;
import com.pearadmin.common.tools.string.StringUtil;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.FileContent;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.domain.StdInfoOfSystem;
import com.pearadmin.modules.standard.domain.StdUploadProcess;
import com.pearadmin.modules.standard.mapper.StdInfoMapper;
import com.pearadmin.modules.standard.service.ChongQueryWrapper;
import com.pearadmin.modules.standard.service.StdInfoService;
import com.pearadmin.modules.standard.service.StdLogService;
import com.pearadmin.modules.sys.domain.SysUser;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import io.swagger.annotations.ApiOperation;
import lombok.var;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.util.WorkbookUtil;
import org.apache.poi.xssf.usermodel.*;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.HttpSolrClient;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.client.solrj.response.UpdateResponse;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.SolrDocumentList;
import org.apache.solr.common.SolrInputDocument;
import org.apache.solr.common.util.NamedList;
import org.apache.tomcat.jni.Directory;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.security.core.parameters.P;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.InetAddress;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.io.File;
import java.io.IOException;

@RestController
@RequestMapping("/inteligbusi")
public class InteligBusinessController extends BaseController {
   private final String MODULE_PATH = "intelligbusiness/";
    @Value(value="${pdf.source.china-path}")
    private String pdfPath;
    @Value(value="${pdf.encryptSource.path}")
    private String encryptPdfPath;
    @Autowired
    private StdInfoService stdInfoService;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private StringEncryptor stringEncryptor;
    //可使用总小时数
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;
    @Resource
    private StdInfoMapper stdInfoMapper;
    public static  String encryptExe;
    @Value("${std.encryptexe}")
    public void setAddress(String exe){
        encryptExe=exe;
    }
    /**
     * 检查使用许可
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    public Boolean security() throws IOException {
        Boolean isSecurity=false;
        if(checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (org.apache.commons.lang.StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        }
        else
        {
            isSecurity=true;
        }
        return isSecurity;
    }
    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("index")
    public ModelAndView main() {
        return jumpPage(MODULE_PATH + "index");
    }

    @PostMapping("export")
    public Result export(@RequestBody Map<String,String> dic) throws IOException {
        if(!security())
        {
            return new Result();
        }
        var query=new ChongQueryWrapper<StdInfo>();
        String sortSql="";
        var keys = dic.get("keywords");
        keys = keys.trim();
        query.eq("std_type", StandardTypeEnum.China);
        ResultTable pg = null;
        if (StringUtils.isNotBlank(keys) && keys != ",") {
            var arrTemp = keys.split(",");
            ArrayList<String> lst=new ArrayList<>();
            for(var idx=0;idx<arrTemp.length;idx++)
            {
                if(StringUtils.isNotBlank(arrTemp[idx]))
                {
                    lst.add(arrTemp[idx]);
                }
            }
            var hasEffectiveKey = false;
            if (lst.size()>0) {
                hasEffectiveKey = true;
            }
            //条件
            var intLogic = dic.get("condLogic");
            var logic = intLogic.equals("1") ? "And" : "Or";
            //左like 右like
            var leftLike = dic.get("leftlike").toUpperCase().equals("TRUE");
            var leftSpace = dic.get("leftSpace").toUpperCase().equals("TRUE");
            var rightLike = dic.get("rightlike").toUpperCase().equals("TRUE");
            var rightStrike =  dic.get("rightStrike").toUpperCase().equals("TRUE");
            var rightChar =dic.get("rightChar").toUpperCase().equals("TRUE");
            var rightBias=dic.get("rightBias").toUpperCase().equals("TRUE");
            var rightDot =dic.get("rightDot").toUpperCase().equals("TRUE");
            var queryFields=dic.get("q_field").split(",");
            //搜索条件默认必须有一个
            if(queryFields.length==0) {
                queryFields[0]="std_no";
            }
            if (hasEffectiveKey) {
                //用户录入的关键词数量少于50，则判断所有的like条件
                if(lst.size()<=50) {
                    for (var i = 0; i < lst.size(); i++) {
                        var item = lst.get(i);
                        if (StringUtils.isNotBlank(item)) {
                            if (StringUtil.isNumeric(item)) {
                                sortSql += " when sort_serial_no='" + item + "' then 10 when sort_serial_no like '" + item + "%' then 5 ";
                            }
                        }
                    }
                    query.and(qw -> {
                        // ---------------------1.精确查找--------------------------------------------------------------------------------
                        if(!leftLike && !rightLike)
                        {
                            for (var i = 0; i < lst.size(); i++) {
                                var item=lst.get(i);
                                qw.or(q -> {
                                    for (var j = 0; j < queryFields.length; j++) {
                                        q.or().eq(queryFields[j], item);
                                    }
                                });
                            }
                        }
                        //----------------------2.有左like 或者 左空格like----------------------------------------------
                        if(leftLike || leftSpace)
                        {
                            if(leftLike)
                            {
                                if (rightLike || rightBias || rightChar || rightDot || rightStrike) {
                                    if (rightLike) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item);
                                                }
                                            });
                                        }
                                    }
                                    if (rightStrike) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + "-");
                                                }
                                            });
                                        }
                                    }
                                    if (rightChar) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + "A").or().like(queryFields[j], item + "B").or().like(queryFields[j], item + "C").or().like(queryFields[j], item + "D").or().like(queryFields[j], item + "E").or().like(queryFields[j], item + "F").or().like(queryFields[j], item + "G");
                                                }
                                            });
                                        }
                                    }
                                    if (rightDot) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + ".");
                                                }
                                            });
                                        }
                                    }
                                    if (rightBias) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + "/");
                                                }
                                            });
                                        }
                                    }
                                }
                                else
                                {
                                    //只有左like，没有任何右like条件
                                    for (var i = 0; i < lst.size(); i++) {
                                        var item = lst.get(i);
                                        qw.or(q -> {
                                            for (var j = 0; j < queryFields.length; j++) {
                                                q.or().likeLeft(queryFields[j], item);
                                            }
                                        });
                                    }
                                }
                            }
                            if(leftSpace)
                            {
                                if (rightLike || rightBias || rightChar || rightDot || rightStrike) {
                                    if (rightLike) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = " " + lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item);
                                                }
                                            });
                                        }
                                    }
                                    if (rightStrike) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = " " + lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + "-");
                                                }
                                            });
                                        }
                                    }
                                    if (rightChar) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = " " + lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + "A").or().like(queryFields[j], item + "B").or().like(queryFields[j], item + "C").or().like(queryFields[j], item + "D").or().like(queryFields[j], item + "E").or().like(queryFields[j], item + "F").or().like(queryFields[j], item + "G");
                                                }
                                            });
                                        }
                                    }
                                    if (rightDot) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = " " + lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + ".");
                                                }
                                            });
                                        }
                                    }
                                    if (rightBias) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = " " + lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + "/");
                                                }
                                            });
                                        }
                                    }
                                }
                                else
                                {
                                    //只有左like，没有任何右like条件
                                    for (var i = 0; i < lst.size(); i++) {
                                        var item = " "+lst.get(i);
                                        qw.or(q -> {
                                            for (var j = 0; j < queryFields.length; j++) {
                                                q.or().likeLeft(queryFields[j], item);
                                            }
                                        });
                                    }
                                }
                            }
                        }
                        //----------------------2.没有左like---------------------------------------------
                        else if (rightLike || rightBias || rightChar || rightDot || rightStrike)
                        {
                            if(rightLike)
                            {
                                for (var i = 0; i < lst.size(); i++) {
                                    var item=lst.get(i);
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().likeRight(queryFields[j], item);
                                        }
                                    });
                                }
                            }
                            if(rightStrike)
                            {
                                for (var i = 0; i < lst.size(); i++) {
                                    var item=lst.get(i);
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().likeRight(queryFields[j], item+"-");
                                        }
                                    });
                                }
                            }
                            if(rightChar)
                            {
                                for (var i = 0; i < lst.size(); i++) {
                                    var item=lst.get(i);
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().likeRight(queryFields[j], item + "A").or().likeRight(queryFields[j], item + "B").or().likeRight(queryFields[j], item + "C").or().likeRight(queryFields[j], item + "D").or().likeRight(queryFields[j], item + "E").or().likeRight(queryFields[j], item + "F").or().likeRight(queryFields[j], item + "G");
                                        }
                                    });
                                }
                            }
                            if(rightDot)
                            {
                                for (var i = 0; i < lst.size(); i++) {
                                    var item=lst.get(i);
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().likeRight(queryFields[j], item+".");
                                        }
                                    });
                                }
                            }
                            if(rightBias)
                            {
                                for (var i = 0; i < lst.size(); i++) {
                                    var item=lst.get(i);
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().likeRight(queryFields[j], item+"/");
                                        }
                                    });
                                }
                            }
                        }
                    });
                }
                //用户录入的查询条件超过50条，则只判断左右like，其他不判断
                else {
                    for (var i = 0; i < lst.size(); i++) {
                        var item = lst.get(i);
                        if (StringUtils.isNotBlank(item)) {
                            if (StringUtil.isNumeric(item)) {
                                sortSql += " when sort_serial_no='" + item + "' then 10 when sort_serial_no like '" + item + "%' then 5 ";
                            }
                        }
                    }
                    query.and(qw -> {
                        for (var i = 0; i <  lst.size(); i++) {
                            var item = lst.get(i);
                            if (StringUtils.isNotBlank(item)) {
                                if (leftLike) {
                                    if (rightLike) {
                                        qw.or(q -> {
                                            for (var j = 0; j < queryFields.length; j++) {
                                                q.or().like(queryFields[j], item);
                                            }
                                        });
                                    }else {
                                        qw.or(q -> {
                                            for (var j = 0; j < queryFields.length; j++) {
                                                q.or().likeLeft(queryFields[j], item);
                                            }
                                        });
                                    }
                                } else if (rightLike) {
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().likeRight(queryFields[j], item);
                                        }
                                    });
                                } else {
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().eq(queryFields[j], item);
                                        }
                                    });
                                }
                            }
                        }
                    });
                }
            }
        }
        if(dic.containsKey("q_class") && StringUtils.isNotBlank(dic.get("q_class")))
        {
            var arr = dic.get("q_class").split(",");
            var list = (List<String>) Arrays.asList(arr);
            //检索全部Identification
            if (list.contains("0")) {
                //什么都不做,取所有标准
            } else if(list.size()>0)
            {
                query.and(qw-> {
                    for (var i = 0; i < list.size(); i++) {
                        if (list.get(i).equals("1")) {
                            qw.or(q -> {q.eq("std_class", "国家军用标准");});
                        } else if (list.get(i).equals("2")) {
                            qw.or(q -> {q.eq("std_class", "兵器行业标准");});
                        } else if (list.get(i).equals("3")) {
                            qw.or(q -> {q.eq("std_class", "集团公司标准");});
                        } else if (list.get(i).equals("4")) {
                            qw.or(q -> {q.eq("std_class", "国家标准");});
                        } else if (list.get(i).equals("5")) {
                            qw.or(q -> {q.eq("std_class", "航空行业标准");});
                            qw.or(q -> {q.eq("std_class", "化工行业标准");});
                            qw.or(q -> {q.eq("std_class", "机械行业标准");});
                            qw.or(q -> {q.eq("std_class", "计量技术规范");});
                            qw.or(q -> {q.eq("std_class", "计量检定规程");});
                            qw.or(q -> {q.eq("std_class", "汽车行业标准");});
                            qw.or(q -> {q.eq("std_class", "航天行业标准");});
                            qw.or(q -> {q.eq("std_class", "电子行业标准");});
                            qw.or(q -> {q.eq("std_class", "石油天然气行业标准");});
                            qw.or(q -> {q.eq("std_class", "铁道行业标准");});
                            qw.or(q -> {q.eq("std_class", "通信行业标准");});
                            qw.or(q -> {q.eq("std_class", "船舶行业标准");});
                            qw.or(q -> {q.eq("std_class", "电力行业标准");});
                        }
                    }
                });
            }
        }
        else
        {
            query.and(wq->{wq.eq("1","2");});
        }
        var maxCount=2000;
        var config=sysConfigMapper.selectByCode("qb_m_d_c");
        if(config!=null)
        {
            maxCount=Integer.parseInt(config.getConfigValue());
        }
        var counts=stdInfoMapper.selectCount(query);
        if(counts>maxCount)
        {
            var result=new Result();
            result.setSuccess(false);
            result.setMsg("平台无法一次导出"+maxCount+"条以上的数据，您要导出的数据量为"+counts+"条，超出了限制。如果要导出超过"+maxCount+"条的数据，请分批执行。");
            return result;
        }
        if(StringUtils.isNotBlank(sortSql)) {
            query.orderBy(true,false, "CASE "+sortSql+" ELSE 0 END");
        }
        //query.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_char").orderByAsc("sort_year");
        query.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_year");
        query.last("limit "+maxCount);
        var stdList = stdInfoMapper.selectList(query);
        //java 创建文件夹
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日HH时mm分");
            String outPath = "D:\\标准导出\\" + sdf.format(new Date());
            File file = new File(outPath);
            boolean newjavaTest = file.mkdirs();
            var sType = "china";
            XSSFWorkbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet(WorkbookUtil.createSafeSheetName("Sheet1"));
            XSSFRow rowObjHeader = (XSSFRow) sheet.createRow(0);
            XSSFCell cellHeader = rowObjHeader.createCell(0);
            cellHeader.setCellValue("序号");
            cellHeader = rowObjHeader.createCell(1);
            cellHeader.setCellValue("需求单位");
            cellHeader = rowObjHeader.createCell(2);
            cellHeader.setCellValue("标准号");
            cellHeader = rowObjHeader.createCell(3);
            cellHeader.setCellValue("标准形式");
            cellHeader = rowObjHeader.createCell(4);
            cellHeader.setCellValue("单页价格");
            cellHeader = rowObjHeader.createCell(5);
            cellHeader.setCellValue("标准页数");
            cellHeader = rowObjHeader.createCell(6);
            cellHeader.setCellValue("单本价格");
            cellHeader = rowObjHeader.createCell(7);
            cellHeader.setCellValue("采购数量");
            cellHeader = rowObjHeader.createCell(8);
            cellHeader.setCellValue("采购金额");
            cellHeader = rowObjHeader.createCell(9);
            cellHeader.setCellValue("折扣");
            cellHeader = rowObjHeader.createCell(10);
            cellHeader.setCellValue("开票金额");
            cellHeader = rowObjHeader.createCell(11);
            cellHeader.setCellValue("输出日期");
            cellHeader = rowObjHeader.createCell(12);
            cellHeader.setCellValue("备注");
            // var dateFormat = DEFAULT_DATE_FORMAT;
            var numberFormat = "#.0";
            SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");
            int idx = 0;
            //加密文件路径，可能包含多个路径，密文可能是分开存放的
            var sourcePath = getPdfPath(sType);
            for (var i = 0; i < stdList.size(); i++) {
                var stdInfo = stdList.get(i);
                boolean exportErr = false;
                String errReason = "";
                var filePath=getCorrespondingPath(sourcePath,stdInfo.getId());
                File f = new File(filePath + "\\" + stdInfo.getId());
                if (stdInfo.getPdfIsExists().equals("1") && f.exists()) {
                    var pdfName = stdInfo.getStdNo().replaceAll(" ", "_");
                    pdfName = pdfName.replaceAll("-", "_");
                    pdfName = pdfName.replaceAll("\\.", "_");
                    pdfName = pdfName + ".pdf";
                    var decryptFileName = outPath + "\\" + (StringUtils.isNotBlank(stdInfo.getPdfFileName()) ? stdInfo.getPdfFileName() : pdfName);
                    var decryptFile = new File(decryptFileName);

                    //大于100M的文件单独处理
                    try {
                        if (f.length() > 104857600) {
                            if (!decryptFile.exists()) {
                                var run = new SaveBigFileRun();
                                run.setSourcePath(filePath + "\\" + stdInfo.getId());
                                run.setOutPath(decryptFileName);
                                new Thread(run).start();
                                //导出大文件 sleep 10秒
                                Thread.sleep(10000);
                            }
                        } else {
                            //小于100M的文件的处理
                            var data = getDecyptData(filePath, stdInfo.getId());
                            saveBinaryFile(stdInfo.getStdOrgName(), data, decryptFileName);
                        }
                    } catch (Exception ex) {
                        exportErr = true;
                        errReason = ex.getStackTrace().toString();
                    }
                }
                else
                {
                    exportErr = true;
                    errReason = "文件不存在";
                }
                    //写入excel
                    XSSFRow rowObj = (XSSFRow) sheet.createRow(idx + 1);
                    XSSFCell cell = rowObj.createCell(0);
                    cell.setCellValue(idx + 1);
                    cell = rowObj.createCell(1);
                    cell.setCellValue("");
                    cell = rowObj.createCell(2);
                    cell.setCellValue(stdInfo.getStdNo());
                    cell = rowObj.createCell(3);
                    cell.setCellValue("电子版");
                    cell = rowObj.createCell(4);
                    cell.setCellValue(0);
                    cell = rowObj.createCell(5);
                    cell.setCellValue(stdInfo.getPageCount());
                    cell = rowObj.createCell(6);
                    cell.setCellValue(0.00);
                    cell = rowObj.createCell(7);
                    cell.setCellValue(1);
                    cell = rowObj.createCell(8);
                    cell.setCellValue(0.00);
                    cell = rowObj.createCell(9);
                    cell.setCellValue("100%");
                    cell = rowObj.createCell(10);
                    cell.setCellValue("0.00");
                    cell = rowObj.createCell(11);
                    cell.setCellValue(sdfDate.format(new Date()));
                    cell = rowObj.createCell(12);
                    if(exportErr) {
                        cell.setCellValue("导出异常："+errReason);
                    }
                    else {
                        cell.setCellValue("");
                    }
                    idx++;
            }
            sheet.autoSizeColumn(0,true);
            sheet.autoSizeColumn(1,true);
            sheet.autoSizeColumn(2,true);
            sheet.autoSizeColumn(3,true);
            sheet.autoSizeColumn(4,true);
            sheet.autoSizeColumn(5,true);
            sheet.autoSizeColumn(6,true);
            sheet.autoSizeColumn(7,true);
            sheet.autoSizeColumn(8,true);
            sheet.autoSizeColumn(9,true);
            sheet.autoSizeColumn(10,true);
            sheet.autoSizeColumn(11,true);
            sheet.autoSizeColumn(12,true);
            FileOutputStream fileOut = new FileOutputStream(outPath + "\\data.xlsx");
            workbook.write(fileOut);
            workbook.close();
            fileOut.flush();
            fileOut.close();

            var result = new Result();
            result.setSuccess(true);
            result.setData(outPath);
            return result;
        }
        catch (Exception ex)
        {
            var result = new Result();
            result.setSuccess(false);
            result.setMsg(ex.getMessage());
            return result;
        }
    }



    @PostMapping("exportcurrpage")
    public Result exportcurrpage(@RequestBody Map<String,String> resDic) throws IOException {
        var query=new QueryWrapper<StdInfo>();
        var ids="";
        var dic=new HashMap<Integer,String>();
        if(resDic.containsKey("ids"))
        {
            ids=resDic.get("ids");
            if(StringUtils.isNotBlank(ids))
            {
                var arr=ids.split(",");
                query.in("id",Arrays.asList(arr));
                var idx=0;
                for(var i=0;i<arr.length;i++)
                {
                    if(StringUtils.isNotBlank(arr[i])) {
                        dic.put(idx, arr[i]);
                        idx++;
                    }
                }
            }
        }
        var counts=stdInfoMapper.selectCount(query);
        if(counts>2000)
        {
            var result=new Result();
            result.setSuccess(false);
            result.setMsg("平台无法一次导出2000条以上的数据，您要导出的数据量为"+counts+"条，超出了限制。如果要导出超过2000条的数据，请分批执行。");
            return result;
        }
         var stdList = stdInfoMapper.selectList(query);
        //java 创建文件夹
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日HH时mm分");
            String outPath = "D:\\标准导出\\" + sdf.format(new Date());
            File file = new File(outPath);
            boolean newjavaTest = file.mkdirs();
            var sType = "china";
            XSSFWorkbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet(WorkbookUtil.createSafeSheetName("Sheet1"));
            XSSFCellStyle centerStyle = workbook.createCellStyle();
            centerStyle.setAlignment(HorizontalAlignment.CENTER);
            XSSFRow rowObjHeader = (XSSFRow) sheet.createRow(0);
            XSSFCell cellHeader = rowObjHeader.createCell(0);
            cellHeader.setCellValue("序号");
            cellHeader = rowObjHeader.createCell(1);
            cellHeader.setCellValue("需求单位");
            cellHeader = rowObjHeader.createCell(2);
            cellHeader.setCellValue("标准号");
            cellHeader = rowObjHeader.createCell(3);
            cellHeader.setCellValue("标准形式");
            cellHeader = rowObjHeader.createCell(4);
            cellHeader.setCellValue("单页价格");
            cellHeader = rowObjHeader.createCell(5);
            cellHeader.setCellValue("标准页数");
            cellHeader = rowObjHeader.createCell(6);
            cellHeader.setCellValue("单本价格");
            cellHeader = rowObjHeader.createCell(7);
            cellHeader.setCellValue("采购数量");
            cellHeader = rowObjHeader.createCell(8);
            cellHeader.setCellValue("采购金额");
            cellHeader = rowObjHeader.createCell(9);
            cellHeader.setCellValue("折扣");
            cellHeader = rowObjHeader.createCell(10);
            cellHeader.setCellValue("开票金额");
            cellHeader = rowObjHeader.createCell(11);
            cellHeader.setCellValue("输出日期");
            cellHeader = rowObjHeader.createCell(12);
            cellHeader.setCellValue("备注");
            // var dateFormat = DEFAULT_DATE_FORMAT;
            var numberFormat = "#.0";
            SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd");
            int idx = 0;
            //加密文件路径，可能包含多个路径，密文可能是分开存放的
            var sourcePath = getPdfPath(sType);
            for(var i=0;i<=100;i++)
            {
                if(dic.containsKey(i))
                {
                    var id=dic.get(i);
                    var sModel = stdList.stream().filter(d->d.getId().equals(id)).findFirst();
                    if(sModel.isPresent())
                    {
                        var stdInfo = sModel.get();
                        boolean exportErr = false;
                        String errReason = "";
                        var filePath=getCorrespondingPath(sourcePath,stdInfo.getId());
                        File f = new File(filePath + "\\" + stdInfo.getId());
                        if (stdInfo.getPdfIsExists().equals("1") && f.exists()) {
                            var pdfName = stdInfo.getStdNo().replaceAll(" ", "_");
                            pdfName = pdfName.replaceAll("-", "_");
                            pdfName = pdfName.replaceAll("\\.", "_");
                            pdfName = pdfName + ".pdf";
                            var decryptFileName = outPath + "\\" + (StringUtils.isNotBlank(stdInfo.getPdfFileName()) ? stdInfo.getPdfFileName() : pdfName);
                            var decryptFile = new File(decryptFileName);

                            //大于100M的文件单独处理
                            try {
                                if (f.length() > 104857600) {
                                    if (!decryptFile.exists()) {
                                        var run = new SaveBigFileRun();
                                        run.setSourcePath(filePath + "\\" + stdInfo.getId());
                                        run.setOutPath(decryptFileName);
                                        new Thread(run).start();
                                        //导出大文件 sleep 10秒
                                        Thread.sleep(10000);
                                    }
                                } else {
                                    //小于100M的文件的处理
                                    var data = getDecyptData(filePath, stdInfo.getId());
                                    saveBinaryFile(stdInfo.getStdOrgName(), data, decryptFileName);
                                }
                            } catch (Exception ex) {
                                exportErr = true;
                                errReason = ex.getStackTrace().toString();
                            }
                        }
                        else
                        {
                            exportErr = true;
                            errReason = "文件不存在";
                        }
                        //写入excel
                        XSSFRow rowObj = (XSSFRow) sheet.createRow(idx + 1);
                        XSSFCell cell = rowObj.createCell(0);
                        cell.setCellValue(idx + 1);
                        cell.setCellStyle(centerStyle);
                        cell = rowObj.createCell(1);
                        cell.setCellValue("");
                        cell = rowObj.createCell(2);
                        cell.setCellValue(stdInfo.getStdNo());
                        cell = rowObj.createCell(3);
                        cell.setCellValue("电子版");
                        cell = rowObj.createCell(4);
                        cell.setCellValue(0);
                        cell.setCellStyle(centerStyle);
                        cell = rowObj.createCell(5);
                        cell.setCellValue(stdInfo.getPageCount());
                        cell.setCellStyle(centerStyle);
                        cell = rowObj.createCell(6);
                        cell.setCellValue(0.00);
                        cell.setCellStyle(centerStyle);
                        cell = rowObj.createCell(7);
                        cell.setCellValue(1);
                        cell.setCellStyle(centerStyle);
                        cell = rowObj.createCell(8);
                        cell.setCellValue(0.00);
                        cell.setCellStyle(centerStyle);
                        cell = rowObj.createCell(9);
                        cell.setCellValue("100%");
                        cell.setCellStyle(centerStyle);
                        cell = rowObj.createCell(10);
                        cell.setCellValue("0.00");
                        cell.setCellStyle(centerStyle);
                        cell = rowObj.createCell(11);
                        cell.setCellValue(sdfDate.format(new Date()));
                        cell.setCellStyle(centerStyle);
                        cell = rowObj.createCell(12);
                        if(exportErr) {
                            cell.setCellValue("导出异常："+errReason);
                        }
                        else {
                            cell.setCellValue("");
                        }
                        idx++;
                    }
                }
            }
            sheet.autoSizeColumn(0,true);
            sheet.autoSizeColumn(1,true);
            sheet.autoSizeColumn(2,true);
            sheet.autoSizeColumn(3,true);
            sheet.autoSizeColumn(4,true);
            sheet.autoSizeColumn(5,true);
            sheet.autoSizeColumn(6,true);
            sheet.autoSizeColumn(7,true);
            sheet.autoSizeColumn(8,true);
            sheet.autoSizeColumn(9,true);
            sheet.autoSizeColumn(10,true);
            sheet.autoSizeColumn(11,true);
            sheet.autoSizeColumn(12,true);
            sheet.autoSizeColumn(13,true);
            FileOutputStream fileOut = new FileOutputStream(outPath + "\\data.xlsx");
            workbook.write(fileOut);
            workbook.close();
            fileOut.flush();
            fileOut.close();

            var result = new Result();
            result.setSuccess(true);
            result.setData(outPath);
            return result;
        }
        catch (Exception ex)
        {
            var result = new Result();
            result.setSuccess(false);
            result.setMsg(ex.getMessage());
            return result;
        }
    }


    public String getPdfPath(String sType)
    {
        return mergePath(encryptPdfPath);
    }

    /**
     * 从多个盘符对应的文件中找到加密文件的存放位置
     *
     * @param filePaths
     * @param id
     * @return java.lang.String
     * @throws
     */
    public String getCorrespondingPath(String filePaths,String id)
    {
        String path=filePaths;
        if(filePaths.contains(",")) {
            var paths = filePaths.split(",");
            for(var i=0;i<paths.length;i++)
            {
                File file=new File(paths[i]+"\\"+id);
                if(file.exists())
                {
                    path=paths[i];
                    break;
                }
            }
        }
        return path;
    }

    public class SaveBigFileRun implements Runnable {
        private String sourcePath;
        public void setSourcePath(String path)
        {
            this.sourcePath=path;
        }
        private String outPath;
        public void setOutPath(String path)
        {
            this.outPath=path;
        }
        private String pwd="20221103";
        @Override
        public void run() {
            Tool.decryptByExe(sourcePath, outPath, pwd);
        }
    }

    public String saveBinaryFile(String fileName,byte[] data,String filePath) throws IOException {
        if (data.length>0) {
            try {
                //写入相应的文件
                var out = new FileOutputStream(filePath);
                //var strData=String.valueOf(data);
                out.write(data);
                out.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     *从pdf加密后的数据中解密出真实的二进制数据
     *
     * @param id
     * @return byte[]
     * @throws
     */
    public byte[] getDecyptData(String filePath, String id) throws IOException {
        var convertData=new byte[0];
        File file=new File(filePath+"\\"+id);
        var byteData= Tool.getBinaryData(file);
        convertData= DESUtil.getDecryptBytes(byteData);
        return convertData;
    }

    /**
     *根据用户配置的文件存放盘符及软件默认的文件盘符合并起来作为最终的文件盘符
     *
     * @param basePath
     * @return java.lang.String
     * @throws
     */
    public String mergePath(String basePath)
    {
        var path="";
        var diskNo="";
        var config=sysConfigMapper.selectByCode("security_file_path");
        if(config!=null && com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(config.getConfigValue()))
        {
            diskNo=config.getConfigValue();
        }
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(diskNo))
        {
//            if(diskNo.contains(":"))
//            {
//                diskNo=diskNo.split(":")[0];
//            }
            var baseArr=basePath.split(":");
            // baseArr[0]=diskNo;
            if(diskNo.contains(","))
            {
                var arr=diskNo.split(",");
                for(var i=0;i<arr.length;i++)
                {
                    if(org.apache.commons.lang.StringUtils.isNotBlank(arr[i]))
                    {
                        var p=arr[i]+":"+baseArr[1];
                        path+=","+p;
                    }
                }
                if(path.length()>1) {
                    path = path.substring(1);
                }
            }
            else {
                path = diskNo + ":" + baseArr[1];
            }
        }
        System.out.println("系统配置的文件路径为："+path);
        return path;
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @PostMapping("data")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    @Log(title = "国内标准", describe = "获取数据列表", type = BusinessType.STD_Data)
    public ResultTable simpleQuery(StdInfo stdInfo, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return new ResultTable();
        }
        var query=new ChongQueryWrapper<StdInfo>();
        String sortSql="";
        query.eq("std_type", StandardTypeEnum.China);
        ResultTable pg=null;
        if(StringUtils.isNotBlank(stdInfo.getSimpleSearchKey())) {
            var jsonObj = (com.alibaba.fastjson.JSONObject) JSONArray.parse(stdInfo.getSimpleSearchKey());
            var keys = jsonObj.getString("keywords");
            keys = keys.trim();
            var arr = keys.split(",");
            ArrayList<String> lst=new ArrayList<>();
            for(var idx=0;idx<arr.length;idx++)
            {
                if(StringUtils.isNotBlank(arr[idx]))
                {
                    lst.add(arr[idx]);
                }
            }
            var hasEffectiveKey = false;
            if (lst.size()>0) {
                hasEffectiveKey = true;
            }
            //条件
            var intLogic = jsonObj.getInteger("condLogic");
            var logic = intLogic == 1 ? "And" : "Or";
            //左like 右like
            var leftLike = jsonObj.getBoolean("leftlike");
            var leftSpace = jsonObj.getBoolean("leftSpace");
            var rightLike = jsonObj.getBoolean("rightlike");
            var rightStrike = jsonObj.getBoolean("rightStrike");
            var rightChar = jsonObj.getBoolean("rightChar");
            var rightBias= jsonObj.getBoolean("rightBias");
            var rightDot = jsonObj.getBoolean("rightDot");
            var queryFields=jsonObj.get("q_field").toString().split(",");
            //搜索条件默认必须有一个
            if(queryFields.length==0) {
                queryFields[0]="std_no";
            }
            if (hasEffectiveKey) {
                //用户录入的关键词数量少于50，则判断所有的like条件
                if(lst.size()<=50) {
                    for (var i = 0; i < lst.size(); i++) {
                        var item = lst.get(i);
                        if (StringUtils.isNotBlank(item)) {
                            if (StringUtil.isNumeric(item)) {
                                sortSql += " when sort_serial_no='" + item + "' then 10 when sort_serial_no like '" + item + "%' then 5 ";
                            }
                        }
                    }
                    query.and(qw -> {
                        // ---------------------1.精确查找--------------------------------------------------------------------------------
                        if(!leftLike && !rightLike)
                        {
                            for (var i = 0; i < lst.size(); i++) {
                                var item=lst.get(i);
                                qw.or(q -> {
                                    for (var j = 0; j < queryFields.length; j++) {
                                        q.or().eq(queryFields[j], item);
                                    }
                                });
                            }
                        }
                        //----------------------2.有左like 或者 左空格like----------------------------------------------
                        if(leftLike || leftSpace)
                        {
                            if(leftLike)
                            {
                                if (rightLike || rightBias || rightChar || rightDot || rightStrike) {
                                    if (rightLike) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item);
                                                }
                                            });
                                        }
                                    }
                                    if (rightStrike) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + "-");
                                                }
                                            });
                                        }
                                    }
                                    if (rightChar) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + "A").or().like(queryFields[j], item + "B").or().like(queryFields[j], item + "C").or().like(queryFields[j], item + "D").or().like(queryFields[j], item + "E").or().like(queryFields[j], item + "F").or().like(queryFields[j], item + "G");
                                                }
                                            });
                                        }
                                    }
                                    if (rightDot) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + ".");
                                                }
                                            });
                                        }
                                    }
                                    if (rightBias) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + "/");
                                                }
                                            });
                                        }
                                    }
                                }
                                else
                                {
                                    //只有左like，没有任何右like条件
                                    for (var i = 0; i < lst.size(); i++) {
                                        var item = lst.get(i);
                                        qw.or(q -> {
                                            for (var j = 0; j < queryFields.length; j++) {
                                                q.or().likeLeft(queryFields[j], item);
                                            }
                                        });
                                    }
                                }
                            }
                            if(leftSpace)
                            {
                                if (rightLike || rightBias || rightChar || rightDot || rightStrike) {
                                    if (rightLike) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = " " + lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item);
                                                }
                                            });
                                        }
                                    }
                                    if (rightStrike) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = " " + lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + "-");
                                                }
                                            });
                                        }
                                    }
                                    if (rightChar) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = " " + lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + "A").or().like(queryFields[j], item + "B").or().like(queryFields[j], item + "C").or().like(queryFields[j], item + "D").or().like(queryFields[j], item + "E").or().like(queryFields[j], item + "F").or().like(queryFields[j], item + "G");
                                                }
                                            });
                                        }
                                    }
                                    if (rightDot) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = " " + lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + ".");
                                                }
                                            });
                                        }
                                    }
                                    if (rightBias) {
                                        for (var i = 0; i < lst.size(); i++) {
                                            var item = " " + lst.get(i);
                                            qw.or(q -> {
                                                for (var j = 0; j < queryFields.length; j++) {
                                                    q.or().like(queryFields[j], item + "/");
                                                }
                                            });
                                        }
                                    }
                                }
                                else
                                {
                                    //只有左like，没有任何右like条件
                                    for (var i = 0; i < lst.size(); i++) {
                                        var item = " "+lst.get(i);
                                        qw.or(q -> {
                                            for (var j = 0; j < queryFields.length; j++) {
                                                q.or().likeLeft(queryFields[j], item);
                                            }
                                        });
                                    }
                                }
                            }
                        }
                        //----------------------2.没有左like---------------------------------------------
                        else if (rightLike || rightBias || rightChar || rightDot || rightStrike)
                        {
                            if(rightLike)
                            {
                                for (var i = 0; i < lst.size(); i++) {
                                    var item=lst.get(i);
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().likeRight(queryFields[j], item);
                                        }
                                    });
                                }
                            }
                            if(rightStrike)
                            {
                                for (var i = 0; i < lst.size(); i++) {
                                    var item=lst.get(i);
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().likeRight(queryFields[j], item+"-");
                                        }
                                    });
                                }
                            }
                            if(rightChar)
                            {
                                for (var i = 0; i < lst.size(); i++) {
                                    var item=lst.get(i);
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().likeRight(queryFields[j], item + "A").or().likeRight(queryFields[j], item + "B").or().likeRight(queryFields[j], item + "C").or().likeRight(queryFields[j], item + "D").or().likeRight(queryFields[j], item + "E").or().likeRight(queryFields[j], item + "F").or().likeRight(queryFields[j], item + "G");
                                        }
                                    });
                                }
                            }
                            if(rightDot)
                            {
                                for (var i = 0; i < lst.size(); i++) {
                                    var item=lst.get(i);
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().likeRight(queryFields[j], item+".");
                                        }
                                    });
                                }
                            }
                            if(rightBias)
                            {
                                for (var i = 0; i < lst.size(); i++) {
                                    var item=lst.get(i);
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().likeRight(queryFields[j], item+"/");
                                        }
                                    });
                                }
                            }
                        }
                    });
                }
                //用户录入的查询条件超过50条，则只判断左右like，其他不判断
                else {
                    for (var i = 0; i < lst.size(); i++) {
                        var item = lst.get(i);
                        if (StringUtils.isNotBlank(item)) {
                            if (StringUtil.isNumeric(item)) {
                                sortSql += " when sort_serial_no='" + item + "' then 10 when sort_serial_no like '" + item + "%' then 5 ";
                            }
                        }
                    }
                    query.and(qw -> {
                        for (var i = 0; i <  lst.size(); i++) {
                            var item = lst.get(i);
                            if (StringUtils.isNotBlank(item)) {
                                if (leftLike) {
                                    if (rightLike) {
                                        qw.or(q -> {
                                            for (var j = 0; j < queryFields.length; j++) {
                                                q.or().like(queryFields[j], item);
                                            }
                                        });
                                    }else {
                                        qw.or(q -> {
                                            for (var j = 0; j < queryFields.length; j++) {
                                                q.or().likeLeft(queryFields[j], item);
                                            }
                                        });
                                    }
                                } else if (rightLike) {
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().likeRight(queryFields[j], item);
                                        }
                                    });
                                } else {
                                    qw.or(q -> {
                                        for (var j = 0; j < queryFields.length; j++) {
                                            q.or().eq(queryFields[j], item);
                                        }
                                    });
                                }
                            }
                        }
                    });
                }
            }
        }
        if(StringUtils.isNotBlank(stdInfo.getQueryIdenti())) {
            var arr = stdInfo.getQueryIdenti().split(",");
            var list = (List<String>) Arrays.asList(arr);
            //检索全部Identification
            if (list.contains("0")) {
                //什么都不做,取所有标准
            } else if(list.size()>0)
            {
                query.and(qw-> {
                    for (var i = 0; i < list.size(); i++) {
                        if (list.get(i).equals("1")) {
                            qw.or(q -> {q.eq("std_class", "国家军用标准");});
                        } else if (list.get(i).equals("2")) {
                            qw.or(q -> {q.eq("std_class", "兵器行业标准");});
                        } else if (list.get(i).equals("3")) {
                            qw.or(q -> {q.eq("std_class", "集团公司标准");});
                        } else if (list.get(i).equals("4")) {
                            qw.or(q -> {q.eq("std_class", "国家标准");});
                        } else if (list.get(i).equals("5")) {
                            qw.or(q -> {q.eq("std_class", "航空行业标准");});
                            qw.or(q -> {q.eq("std_class", "化工行业标准");});
                            qw.or(q -> {q.eq("std_class", "机械行业标准");});
                            qw.or(q -> {q.eq("std_class", "计量技术规范");});
                            qw.or(q -> {q.eq("std_class", "计量检定规程");});
                            qw.or(q -> {q.eq("std_class", "汽车行业标准");});
                            qw.or(q -> {q.eq("std_class", "航天行业标准");});
                            qw.or(q -> {q.eq("std_class", "电子行业标准");});
                            qw.or(q -> {q.eq("std_class", "石油天然气行业标准");});
                            qw.or(q -> {q.eq("std_class", "铁道行业标准");});
                            qw.or(q -> {q.eq("std_class", "通信行业标准");});
                            qw.or(q -> {q.eq("std_class", "船舶行业标准");});
                            qw.or(q -> {q.eq("std_class", "电力行业标准");});
                        }
                    }
                });
            }
        }
        else
        {
            query.and(wq->{wq.eq("1","2");});
        }
        if(StringUtils.isNotBlank(sortSql)) {
            query.orderBy(true,false, "CASE "+sortSql+" ELSE 0 END");
        }
        //query.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_char").orderByAsc("sort_year");
        query.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_year");
        var page = stdInfoMapper.selectPage(new Page<StdInfo>(pageDomain.getPage(), pageDomain.getLimit()), query);
        //获取可以下载的标准的数量
        query.eq("pdf_is_exists", "1");
        var pdfCanDownloads = stdInfoService.count(query);
        pg= pageTable(stdInfoService.fillIsCollected(page.getRecords()), page.getTotal());
        pg.setExtData(pdfCanDownloads);
        return pg;
    }

    /**
     *  excel文件上传
     */
    @PostMapping("/uploadcondition")
    @ApiOperation(value = "情报室导出条件上传")
    public CResult<?> fileUpload(HttpServletRequest req) throws Exception {
        var result=new CResult<String>();
        result.setResult("");
        result.setSuccess(true);
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) req;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        var resList="";
        try {
            var customSessionId=req.getParameter("customSessionId");
            InputStream fileContent = file.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(fileContent));
            String line;
            while ((line = reader.readLine()) != null) {
                String[] fields = line.split(",");
                for(var i=0;i<fields.length;i++)
                {
                    if(StringUtils.isNotBlank(fields[i]))
                    {
                        resList+=fields[i]+",";
                    }
                }
                // 在这里处理学生信息
            }
            //返回一个excel文件名称
            result.setMessage("");
            result.setSuccess(true);
            result.setResult(resList);
            return result;
        }
        catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
            result.setResult("");
            result.setSuccess(false);
            result.setMessage("上传失败，"+ex.getMessage());
            return result;
        }
    }

//    @GetMapping("testfile")
//    public Result testFile()
//    {
//        var total=13;
//        var size=10000;
//        for(var i=0;i<total;i++) {
//            var list = stdInfoService.list(new LambdaQueryWrapper<StdInfo>().last(" limit " +i*size+","+(i+1)*size));
//            for(var j=0;j<list.size();j++)
//            {
//                File f=new File(list.get(j).getId());
//            }
//        }
//    }

}
