package com.pearadmin.modules.standard.controller;

import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.modules.standard.task.PreviewDataCleanupTask;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 预览数据清理控制器
 * 仅用于测试手动触发清理任务
 *
 * <AUTHOR> Code
 * @date 2023/5/28
 */
@RestController
@Api(tags = {"预览数据清理"})
@RequestMapping(ControllerConstant.API_SYSTEM_PREFIX + "previewCleanup")
public class PreviewCleanupController extends BaseController {

    @Resource
    private PreviewDataCleanupTask previewDataCleanupTask;

    /**
     * 手动触发预览数据清理任务
     * 仅用于测试，生产环境应该由定时任务自动执行
     *
     * @return 执行结果
     */
    @GetMapping("manual")
    @ApiOperation(value = "手动触发预览数据清理")
    @PreAuthorize("hasPermission('/system/previewCleanup/manual','sys:previewCleanup:manual')")
    public Result<String> manualCleanup() {
        try {
            previewDataCleanupTask.manualCleanup();
            return Result.success("预览数据清理任务已手动触发");
        } catch (Exception e) {
            return Result.failure("预览数据清理任务触发失败: " + e.getMessage());
        }
    }
}
