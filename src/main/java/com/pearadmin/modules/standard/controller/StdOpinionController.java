package com.pearadmin.modules.standard.controller;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.tools.StandardTypeEnum;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.domain.StdOpinion;
import com.pearadmin.modules.standard.service.StdInfoService;
import com.pearadmin.modules.standard.service.StdOpinionService;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import io.swagger.annotations.Api;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;

/**
 * Describe:  对标准的意见或建议 控制器
 * Author: chongyibo
 * CreateTime: 2023/5/23
 */
@RestController
@Api(tags = {"对标准的意见或建议"})
@RequestMapping(ControllerConstant.API_STANDARD_OPINION_PREFIX)
public class StdOpinionController extends BaseController {
    private final String MODULE_PATH = "stdopinion/";

    @Resource
    private StdOpinionService stdOpinionService;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private StringEncryptor stringEncryptor;
    //可使用总小时数
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;
    @Autowired
    private StdInfoService stdInfoService;
    /**
     * 检查使用许可
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    public Boolean security() throws IOException {
        Boolean isSecurity=false;
        if(checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        }
        else
        {
            isSecurity=true;
        }
        return isSecurity;
    }

    /**
     * Describe: 标准意见列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("main")
    public ModelAndView main(Model model,@RequestParam String stdId) {
        model.addAttribute("stdId",stdId);
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 标准意见列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("data")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dicmtType:data')")
    public ResultTable data(StdOpinion model, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
       {
           return null;
       }
        var query=new QueryWrapper<StdOpinion>();
        if(StringUtils.isNotBlank(model.getStdId()))
        {
            query.eq("std_id",model.getStdId());
        }
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getSimpleSearchKey()))
        {
            var keys=model.getSimpleSearchKey();
            while(keys.contains("  "))
            {
                keys=keys.replaceFirst("  "," ");
            }
            var keyArr=keys.split(" ");
            for(String key : keyArr)
            {
                query.and(foo->foo.like("std_no",key).or().like("std_org_Name",key));
            }
        }
        query.orderByDesc("create_time");
        List<StdOpinion> list = stdOpinionService.list(query);
        return pageTable(list, list.size());
    }

    /**
     * Describe: 被提过意见的标准
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("dataofstdhasopinion")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable dataofstdhasopinion(StdInfo stdInfo, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return new ResultTable();
        }
        var query=new QueryWrapper<StdInfo>();
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(stdInfo.getSimpleSearchKey()))
        {
            var keys=stdInfo.getSimpleSearchKey();
            while(keys.contains("  "))
            {
                keys=keys.replaceFirst("  "," ");
            }
            var keyArr=keys.split(" ");
            for(String key : keyArr)
            {
                //var subQuery=new QueryWrapper<StdInfo>();
                //subQuery.like("std_no",key).or().like("std_org_Name",key);
                //单独的一种写法
                // Consumer<QueryWrapper<StdInfo>> c=foo->foo.like("std_no",key).or().like("std_org_Name",key);
                // c.accept(query);
                query.and(foo->foo.like("std_no",key).or().like("std_org_Name",key));
                //query.and((Consumer<QueryWrapper<StdInfo>>) subQuery);
            }
        }
        else if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(stdInfo.getAdvSearchKey()))
        {
            var jsonArray= (JSONArray)JSONArray.parse(stdInfo.getAdvSearchKey());
            for(Object item :jsonArray)
            {
                var obj=(com.alibaba.fastjson.JSONObject)item;
                var logic=obj.getString("logic");
                var field=obj.getString("field");
                var val=obj.getString("val");
                var type=obj.getString("type");
                if("AND".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.eq(field, val);
                    }
                    else {
                        //模糊查询
                        query.like(field,val);
                    }
                }
                else if("OR".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.or(qr->qr.eq(field, val));
                    }
                    else {
                        //模糊查询
                        query.or(qr->qr.like(field, val));
                    }
                }
                else if("NOT".equals(logic))
                {
                    //精确查找
                    if("exact".equals(type)) {
                        query.ne(field, val);
                    }
                    else {
                        //模糊查询
                        query.notLike(field,val);
                    }
                }
            }
        }
       // query.orderByAsc("sort_identifaction").orderByDesc("sort_year").orderByDesc("sort_serial_no").orderByAsc("sort_char");
       //用最新评价时间做为排序条件，让用户先看到最新评价的标准
        query.orderByDesc("opinion_create_time");
        var page= stdInfoService.pageByOpinion(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
        var pg= pageTable(stdInfoService.fillIsCollected(page.getRecords()), page.getTotal());
        //不统计可下载数量
        pg.setExtData(0L);
        return pg;
    }

    @GetMapping("list")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public List<StdOpinion> list(StdOpinion model, PageDomain pageDomain) throws IOException {
        if(!security())
        {
            return null;
        }
        var query=new LambdaQueryWrapper<StdOpinion>();
        List<StdOpinion> list = stdOpinionService.list(query);
        return list;
    }

    /**
     * Describe: 标准意见类型新增视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("add")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public ModelAndView add(Model model,@RequestParam String stdId) {
        var stdInfo=stdInfoService.getById(stdId);
        model.addAttribute("stdInfo",stdInfo);
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("save")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result save(@RequestBody StdOpinion model) throws IOException {
        model.setQuestioner(UserContext.currentUser().getRealName());
        boolean result = stdOpinionService.save(model);
        return decide(result);
    }

    /**
     * Describe: 标准意见类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("index")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView index(Model model) {
        return jumpPage(MODULE_PATH + "index");
    }

    /**
     * Describe: 被提过意见的标准主视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("stdindex")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView stdindex(Model model) {
        return jumpPage(MODULE_PATH + "stdindex");
    }

    /**
     * Describe: 标准意见类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("edit")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView edit(Model model,String id) {
        var data=stdOpinionService.getById(id);
        model.addAttribute("model", data);
        return jumpPage(MODULE_PATH + "edit");
    }

    /**
     * Describe: 标准意见类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detail")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detail(Model model,String id) {
        var data=stdOpinionService.getById(id);
        model.addAttribute("model",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    /**
     * Describe: 标准意见类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PutMapping("update")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result update(@RequestBody StdOpinion model) throws IOException {
        boolean result = stdOpinionService.updateById(model);
        return decide(result);
    }

    /**
     * Describe: 删除
     * Param: sysDictType
     * Return: ModelAndView
     */
    @DeleteMapping("remove/{id}")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result remove(@PathVariable("id") String id) throws IOException {
        Boolean result = stdOpinionService.removeById(id);
        return decide(result);
    }

}
