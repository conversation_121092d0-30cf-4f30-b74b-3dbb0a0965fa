package com.pearadmin.modules.standard.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.StdDownload;
import com.pearadmin.modules.standard.mapper.StdDownloadMapper;

import com.pearadmin.modules.standard.service.StdDownloadService;
import com.pearadmin.modules.sys.domain.SysUser;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import com.pearadmin.modules.sys.service.SysConfigService;
import io.swagger.annotations.Api;
import lombok.var;
import org.apache.commons.lang3.StringUtils;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Describe: 预览记录控制器
 * Author: 系统生成
 * CreateTime: 2023/5/20
 */
@RestController
@Api(tags = {"标准预览"})
@RequestMapping(ControllerConstant.API_PREVIEW_PREFIX)
public class StdPreviewController extends BaseController {
    private final String MODULE_PATH = "preview/";

    @Resource
    private StdDownloadService stdDownloadService;

    @Resource
    private StdDownloadMapper stdDownloadMapper;

    @Resource
    private SysConfigMapper sysConfigMapper;

    @Resource
    private SysConfigService sysConfigService;

    @Resource
    private StringEncryptor stringEncryptor;

    @Value("${stdsys.limithours}")
    private Integer hours;

    @Value("${stdsys.check-security}")
    private Integer checkSecurity;

    /**
     * 检查使用许可
     */
    public Boolean security() throws IOException {
        Boolean isSecurity = false;
        if (checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    String configData = config.getConfigValue();
                    if (StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        } else {
            isSecurity = true;
        }
        return isSecurity;
    }

    /**
     * Describe: 获取预览记录列表视图
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("index")
    public ModelAndView index() {
        // 准备最近7天的日期数据
        List<Map<String, String>> dateList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter displayFormatter = DateTimeFormatter.ofPattern("MM月dd日");

        for (int i = 0; i < 7; i++) {
            LocalDate date = LocalDate.now().minusDays(i);
            Map<String, String> dateMap = new HashMap<>();
            dateMap.put("value", date.format(formatter));
            dateMap.put("display", date.format(displayFormatter));
            dateList.add(dateMap);
        }

        ModelAndView mav = jumpPage(MODULE_PATH + "index");
        mav.addObject("dateList", dateList);
        return mav;
    }

    /**
     * Describe: 获取预览记录列表数据
     * Param: StdDownload, PageDomain
     * Return: ResultTable
     */
    @GetMapping("data")
    public ResultTable data(StdDownload model, PageDomain pageDomain) throws IOException {
        // 默认查询当天的数据
        LocalDate today = LocalDate.now();
        return getDataByDate(model, pageDomain, today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
    }

    /**
     * 根据日期获取预览记录列表数据
     *
     * @param model 查询模型
     * @param pageDomain 分页参数
     * @param dateStr 日期字符串，格式为 yyyy-MM-dd
     * @return 查询结果
     * @throws IOException 异常
     */
    @GetMapping("data/{date}")
    public ResultTable getDataByDate(StdDownload model, PageDomain pageDomain, @PathVariable("date") String dateStr) throws IOException {
        System.out.println("Fetching preview data for date: " + dateStr);
        if (!security()) {
            System.out.println("Security check failed");
            return pageTable(null, 0);
        }

        try {
            // 解析日期
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDateTime startOfDay = date.atStartOfDay();
            LocalDateTime endOfDay = date.plusDays(1).atStartOfDay();

            var query = new LambdaQueryWrapper<StdDownload>();
            SysUser sysUser = UserContext.currentUser();

            // 设置查询条件：当前用户且操作类型为预览(2)
            query.eq(StdDownload::getUserId, sysUser.getUserId());
            query.eq(StdDownload::getOpType, 2);

            // 添加日期范围条件
            query.ge(StdDownload::getCreateTime, startOfDay);
            query.lt(StdDownload::getCreateTime, endOfDay);

            // 添加搜索条件
            if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getSimpleSearchKey())) {
                var keys = model.getSimpleSearchKey();
                while (keys.contains("  ")) {
                    keys = keys.replaceFirst("  ", " ");
                }
                var keyArr = keys.split(" ");
                for (String key : keyArr) {
                    if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(key)) {
                        query.and(foo -> foo.like(StdDownload::getStdNo, "%" + key + "%")
                                .or().like(StdDownload::getStdOrgName, "%" + key + "%")
                                .or().like(StdDownload::getStdChineseName, "%" + key + "%")
                                .or().like(StdDownload::getPdfFileName, "%" + key + "%"));
                    }
                }
            }

            // 按创建时间降序排序
            query.orderByDesc(StdDownload::getCreateTime);

            // 分页查询
            // 防止分页参数为空
            int pageNum = pageDomain != null && pageDomain.getPage() != null ? pageDomain.getPage() : 1;
            int pageSize = pageDomain != null && pageDomain.getLimit() != null ? pageDomain.getLimit() : 10;

            var page = stdDownloadMapper.selectPage(
                    new Page<StdDownload>(pageNum, pageSize),
                    query);
            
            return pageTable(page.getRecords(), page.getTotal());
        } catch (Exception e) {
            System.err.println("Error while querying preview records: " + e.getMessage());
            e.printStackTrace();
            return pageTable(null, 0);
        }
    }

    /**
     * 获取最近7天的预览统计数据
     *
     * @return 统计数据
     * @throws IOException 异常
     */
    @GetMapping("stats")
    public Result<List<Map<String, Object>>> getPreviewStats() throws IOException {
        if (!security()) {
            return Result.failure("Security check failed");
        }

        try {
            List<Map<String, Object>> statsList = new ArrayList<>();
            SysUser sysUser = UserContext.currentUser();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            DateTimeFormatter displayFormatter = DateTimeFormatter.ofPattern("MM月dd日");

            for (int i = 0; i < 7; i++) {
                LocalDate date = LocalDate.now().minusDays(i);
                LocalDateTime startOfDay = date.atStartOfDay();
                LocalDateTime endOfDay = date.plusDays(1).atStartOfDay();

                // 查询当天的预览数量
                var query = new LambdaQueryWrapper<StdDownload>();
                query.eq(StdDownload::getUserId, sysUser.getUserId());
                query.eq(StdDownload::getOpType, 2);
                query.ge(StdDownload::getCreateTime, startOfDay);
                query.lt(StdDownload::getCreateTime, endOfDay);

                int count = Math.toIntExact(stdDownloadMapper.selectCount(query));

                Map<String, Object> dayStats = new HashMap<>();
                dayStats.put("date", date.format(formatter));
                dayStats.put("display", date.format(displayFormatter));
                dayStats.put("count", count);

                statsList.add(dayStats);
            }

            return Result.success("Success", statsList);
        } catch (Exception e) {
            System.err.println("Error while getting preview stats: " + e.getMessage());
            e.printStackTrace();
            return Result.failure("Error: " + e.getMessage());
        }
    }
}
