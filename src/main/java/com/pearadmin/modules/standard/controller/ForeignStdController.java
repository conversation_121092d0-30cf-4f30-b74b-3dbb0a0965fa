package com.pearadmin.modules.standard.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.tools.CResult;
import com.pearadmin.common.tools.DESUtil;
import com.pearadmin.common.tools.StandardTypeEnum;
import com.pearadmin.common.tools.Tool;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.FileContent;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.mapper.StdInfoMapper;
import com.pearadmin.modules.standard.service.StdInfoService;
import com.pearadmin.modules.sys.domain.SysUser;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import io.swagger.annotations.ApiOperation;
import lombok.var;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.HttpSolrClient;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.client.solrj.response.UpdateResponse;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.SolrDocumentList;
import org.apache.solr.common.SolrInputDocument;
import org.apache.solr.common.util.NamedList;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/foreignstd")
public class ForeignStdController extends BaseController {
    private final String MODULE_PATH = "stdforeign/";
    @Value(value="${pdf.source.foreign-path}")
    private String pdfPath;
    @Value(value="${pdf.encryptSource.path}")
    private String encryptPdfPath;
    @Autowired
    private StdInfoService stdInfoService;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private StringEncryptor stringEncryptor;
    //可使用总小时数
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;
    @Resource
    private StdInfoMapper stdInfoMapper;
    @Autowired
    private com.pearadmin.common.tools.UploadProcess process;

    /**
     * 检查使用许可
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    public Boolean security() throws IOException {
        Boolean isSecurity=false;
        if(checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (org.apache.commons.lang.StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        }
        else
        {
            isSecurity=true;
        }
        return isSecurity;
    }
    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("main")
    public ModelAndView main() {
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("myIndex")
    public ModelAndView index() {
        return jumpPage(MODULE_PATH + "index");
    }


    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("data")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable data(StdInfo stdInfo, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
        {
            return new ResultTable();
        }
        var query=new LambdaQueryWrapper<StdInfo>();
        query.eq(StdInfo::getStdType, StandardTypeEnum.Foreign);
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getStdOrgName()))
        {
            query.like(StdInfo::getStdOrgName,stdInfo.getStdOrgName());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getPrimaryCoverage()))
        {
            query.like(StdInfo::getPrimaryCoverage,stdInfo.getPrimaryCoverage());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getDrafter()))
        {
            query.like(StdInfo::getDrafter,stdInfo.getDrafter());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getDraftingUnit()))
        {
            query.like(StdInfo::getDraftingUnit,stdInfo.getDraftingUnit());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getStdNo()))
        {
            query.like(StdInfo::getStdNo,stdInfo.getStdNo());
        }
        var page= stdInfoMapper.selectPage(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
       // var page =stdInfoService.page(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
        //获取可以下载的标准的数量
        query.eq(StdInfo::getPdfIsExists,"1");
        var pdfCanDownloads=stdInfoService.count(query);
        var pg= pageTable(stdInfoService.fillIsCollected(page.getRecords()), page.getTotal());
        pg.setExtData(pdfCanDownloads);
        return pg;
    }

    @GetMapping("list")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable list(StdInfo stdInfo, PageDomain pageDomain) {
        var query=new LambdaQueryWrapper<StdInfo>();
        query.eq(StdInfo::getStdType, StandardTypeEnum.Foreign);
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getStdNo()))
        {
            query.like(StdInfo::getStdNo, stdInfo.getStdNo());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getStdOrgName()))
        {
            query.like(StdInfo::getStdOrgName,stdInfo.getStdOrgName());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getDraftingUnit()))
        {
            query.like(StdInfo::getDraftingUnit, stdInfo.getDraftingUnit());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getDrafter()))
        {
            query.like(StdInfo::getDrafter, stdInfo.getDrafter());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(stdInfo.getPrimaryCoverage()))
        {
            query.like(StdInfo::getPrimaryCoverage, stdInfo.getPrimaryCoverage());
        }
        var page =stdInfoService.page(new Page<StdInfo>(pageDomain.getPage(),pageDomain.getLimit()),query);
        //获取可以下载的标准的数量
        query.eq(StdInfo::getPdfIsExists,"1");
        var pdfCanDownloads=stdInfoService.count(query);
        var pg= pageTable(page.getRecords(), page.getTotal());
        pg.setExtData(pdfCanDownloads);
        return pg;
    }

    /**
     * Describe: 数据字典类型新增视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("add")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public ModelAndView add() {
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("save")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result save(@RequestBody StdInfo stdInfo) throws IOException, InterruptedException {
        stdInfo.setStdType(StandardTypeEnum.Foreign);
        stdInfo=stdInfoService.AnalysisSortCondition(stdInfo);
        if(StringUtils.isBlank(stdInfo.getId()))
        {
            if(!StringUtils.isNotBlank(stdInfo.getPdfFileName()))
            {
                stdInfo.setPdfIsExists("1");
            }
            else
            {
                stdInfo.setPdfIsExists("0");
            }
        }
        if(StringUtils.isNotBlank(stdInfo.getPdfFileName())) {
            File f = new File(pdfPath + "\\" + stdInfo.getPdfFileName());
            File encryptFile = new File(encryptPdfPath + "\\" + stdInfo.getId());
            var pdfIsExists = false;
            if (!encryptFile.exists() && f.exists()) {
                //大于100M的文件单独处理
                if (f.length() > 104857600) {
                    encryptPdfPath = mergePath(encryptPdfPath);
                    Tool.encryptByExe(pdfPath + "\\" + stdInfo.getPdfFileName(), encryptPdfPath + "\\" + stdInfo.getId(), "20221103");
                    Thread.sleep(Tool.estimatedEncryptTime(f.length()));
                } else {
                    var data = Tool.getBinaryData(f);
                    saveEncryptPdf(stdInfo.getId(), data);
                }
                pdfIsExists = true;
            } else if (encryptFile.exists()) {
                pdfIsExists = true;
            }
            stdInfo.setPdfIsExists(pdfIsExists ? "1" : "0");
        }
        boolean result = stdInfoService.save(stdInfo);
        return decide(result);
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("edit")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView edit(Model model,String stdID) {
        var data=stdInfoService.getById(stdID);
        model.addAttribute("stdInfo",data);
        return jumpPage(MODULE_PATH + "edit");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detail")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detail(Model model,String stdID) {
        var data=stdInfoService.getById(stdID);
        model.addAttribute("stdInfo",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detailbystdno")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detailbystdno(Model model,String stdNo) {
        var data=stdInfoService.getOne(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.Foreign).eq(StdInfo::getStdNo,stdNo).last("limit 1"));
        model.addAttribute("stdInfo",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PutMapping("update")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result update(@RequestBody StdInfo stdInfo) throws IOException, InterruptedException {
//        if(StringUtils.isNotBlank(stdInfo.getPdfFileName())) {
//            File f = new File(pdfPath + "\\" + stdInfo.getPdfFileName());
//            File encryptFile = new File(encryptPdfPath + "\\" + stdInfo.getId());
//            var pdfIsExists = false;
//            if (!encryptFile.exists() && f.exists()) {
//                //大于100M的文件单独处理
//                if (f.length() > 104857600) {
//                    encryptPdfPath = mergePath(encryptPdfPath);
//                    Tool.encryptByExe(pdfPath + "\\" + stdInfo.getPdfFileName(), encryptPdfPath + "\\" + stdInfo.getId(), "20221103");
//                    Thread.sleep(Tool.estimatedEncryptTime(f.length()));
//                } else {
//                    var data = Tool.getBinaryData(f);
//                    saveEncryptPdf(stdInfo.getId(), data);
//                }
//                pdfIsExists = true;
//            } else if (encryptFile.exists()) {
//                pdfIsExists = true;
//            }
//            stdInfo.setPdfIsExists(pdfIsExists ? "1" : "0");
//        }
        boolean result = stdInfoService.updateById(stdInfo);
        return decide(result);
    }

    /**
     * Describe: 批量生成加密文件
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("batchUpdateDoc")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result batchUpdateDoc() throws IOException, InterruptedException {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.Foreign));
        pdfPath=mergePath(pdfPath);
        for(var i=0;i<list.size();i++)
        {
            var model =list.get(i);
            if(StringUtils.isNotBlank(model.getPdfFileName())) {
                File f = new File(pdfPath + "\\" + model.getPdfFileName());
                File encryptFile = new File(encryptPdfPath + "\\" + model.getId());
                var pdfIsExists = false;
                if (!encryptFile.exists() && f.exists()) {
                    //大于100M的文件单独处理
                    if (f.length() > 104857600) {
                        encryptPdfPath = mergePath(encryptPdfPath);
                        Tool.encryptByExe(pdfPath + "\\" + model.getPdfFileName(), encryptPdfPath + "\\" + model.getId(), "20221103");
                        Thread.sleep(Tool.estimatedEncryptTime(f.length()));
                    } else {
                        var data = Tool.getBinaryData(f);
                        saveEncryptPdf(model.getId(), data);
                    }
                    pdfIsExists = true;
                } else if (encryptFile.exists()) {
                    pdfIsExists = true;
                }
                model.setPdfIsExists(pdfIsExists ? "1" : "0");
                stdInfoService.updateById(model);
            }
        }
        return decide(true);
    }

    /**
     * Describe: 数据字典删除
     * Param: sysDictType
     * Return: ModelAndView
     */
    @DeleteMapping("remove/{id}")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result remove(@PathVariable("id") String id) throws IOException {
        //var model=stdInfoService.getById(id);
        Boolean result = stdInfoService.removeById(id);
        if(result) {
            //删除磁盘上的文件
            Files.deleteIfExists(Paths.get(encryptPdfPath+"\\"+id));
            //Files.deleteIfExists(Paths.get(pdfPath+"\\"+model.getPdfFileName()));
            //删除solr对应对象
            HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_foreign").build();
            try {
                client.deleteById(id);
                System.out.print(id + " 在solr索引中删除成功");
            } catch (SolrServerException e) {
                e.printStackTrace();
                return decide(false);
            } catch (IOException e) {
                e.printStackTrace();
                try {
                    client.rollback();
                    return decide(false);
                } catch (SolrServerException ex) {
                    ex.printStackTrace();
                    return decide(false);
                } catch (IOException ex) {
                    ex.printStackTrace();
                    return decide(false);
                }
            }
        }
        return decide(result);
    }

    /**
     * Describe: 个人资料
     * Param: null
     * Return: ModelAndView
     */
    @PostMapping("createsolrindex")
    @ApiOperation(value = "为所有标准创建或更新solr索引")
    public CResult<Boolean> createSolorIndex(@RequestParam String id) throws IOException {
        var data=stdInfoService.getById(id);
        if(data==null)
        {
            System.out.println("无效的编号");
            var result=new CResult<Boolean>();
            result.setSuccess(false);
            result.setMessage("无效的编号");
            return result;
        }
        SolrInputDocument doc = new SolrInputDocument();
        var fileExists=false;
        if(StringUtils.isNotBlank(data.getPdfFileName()))
        {
            pdfPath=mergePath(pdfPath);
            File file=new File(pdfPath+"\\"+data.getPdfFileName());
            if(file.exists())
            {
                fileExists=true;
                var content=getText(file);
                doc.addField("content", content.getContent());
            }
        }
        if(!fileExists)
        {
            System.out.println("pdf文件不存在！");
            var result=new CResult<Boolean>();
            result.setSuccess(false);
            result.setMessage("pdf文件不存在");
            return result;
        }
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_foreign").build();
        //新增或更新。新增文档类型都是SolrInputDocument
        doc.addField("id",data.getId());
        doc.addField("stdNo",data.getStdNo());
        doc.addField("stdOrgName",data.getStdOrgName());
        doc.addField("stdIdentification",data.getStdIdentification());
        doc.addField("stdLangugage",data.getStdLangugage());
        doc.addField("catetoryNo",data.getCatetoryNo());
        doc.addField("stdIcs",data.getStdIcs());
        doc.addField("implementationDate",data.getImplementationDate());
        doc.addField("stdStatus",data.getStdStatus());
        doc.addField("supersededStdNo",data.getSupersededStdNo());
        doc.addField("stdOcr",data.getStdOcr());
        doc.addField("stdChineseName",data.getStdChineseName());
        doc.addField("stdEnglishName",data.getStdEnglishName());
        doc.addField("securityClass",data.getSecurityClass());
        doc.addField("pubDept",data.getPubDept());
        doc.addField("advanceDept",data.getAdvanceDept());
        doc.addField("draftingUnit",data.getDraftingUnit());
        doc.addField("drafter",data.getDrafter());
        doc.addField("primaryCoverage",data.getPrimaryCoverage());
        doc.addField("alternateStdNo",data.getAlternateStdNo());
        doc.addField("pdfFileName", data.getPdfFileName());
        doc.addField("stdClass", data.getStdClass());
        try {
            UpdateResponse response = client.add(doc);
            System.out.println(String.format("solor数据更新：status = %s ; QTime = %s",response.getStatus(),response.getQTime()));
        } catch (Exception e) {
            e.printStackTrace();
            try {
                client.rollback();
            } catch (SolrServerException ex) {
                ex.printStackTrace();
                var result=new CResult<Boolean>();
                result.setSuccess(false);
                return result;
            } catch (IOException ex) {
                ex.printStackTrace();
                var result=new CResult<Boolean>();
                result.setSuccess(false);
                return result;
            }
        }
        //在Solr服务中，数据的写操作也是有事务的，WEB管理平台默认一次操作一次提交。
        try {
            client.commit();
            client.close();
        } catch (SolrServerException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return CResult.OK(true);
    }

    /**
     * Describe: 为所有标准创建或更新solr索引
     * Param: null
     */
    @PostMapping("updateallsolrindex")
    @ApiOperation(value = "为所有标准创建或更新solr索引")
    public CResult<Boolean> updateAllSolorIndex() throws IOException, SolrServerException {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.Foreign));
        //先删除所有索引，不然更新不彻底
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_foreign").build();
        client.deleteByQuery("*:*");
        client.commit();
        pdfPath=mergePath(pdfPath);
        for (StdInfo data:list)
        {
            //新增或更新。新增文档类型都是SolrInputDocument
            SolrInputDocument doc = new SolrInputDocument();
            var fileExists=false;
            if(StringUtils.isNotBlank(data.getPdfFileName()))
            {
                File file=new File(pdfPath+"\\"+data.getPdfFileName());
                if(file.exists())
                {
                    fileExists=true;
                    var content=getText(file);
                    doc.addField("content", content.getContent());
                }
            }
            if(!fileExists)
            {
                continue;
            }
            doc.addField("id", data.getId());
            doc.addField("stdNo",data.getStdNo());
            doc.addField("stdOrgName",data.getStdOrgName());
            doc.addField("stdIdentification",data.getStdIdentification());
            doc.addField("stdLangugage",data.getStdLangugage());
            doc.addField("catetoryNo",data.getCatetoryNo());
            doc.addField("stdIcs",data.getStdIcs());
            doc.addField("implementationDate",data.getImplementationDate());
            doc.addField("stdStatus",data.getStdStatus());
            doc.addField("supersededStdNo",data.getSupersededStdNo());
            doc.addField("stdOcr",data.getStdOcr());
            doc.addField("stdChineseName",data.getStdChineseName());
            doc.addField("stdEnglishName",data.getStdEnglishName());
            doc.addField("securityClass",data.getSecurityClass());
            doc.addField("pubDept",data.getPubDept());
            doc.addField("advanceDept",data.getAdvanceDept());
            doc.addField("draftingUnit",data.getDraftingUnit());
            doc.addField("drafter",data.getDrafter());
            doc.addField("primaryCoverage",data.getPrimaryCoverage());
            doc.addField("alternateStdNo",data.getAlternateStdNo());
            doc.addField("pdfFileName", data.getPdfFileName());
            doc.addField("stdClass", data.getStdClass());
            try {
                UpdateResponse response = client.add(doc);
                System.out.println(String.format("solor数据更新：status = %s ; QTime = %s", response.getStatus(), response.getQTime()));
            } catch (Exception e) {
                e.printStackTrace();
                try {
                    client.rollback();
                } catch (SolrServerException ex) {
                    ex.printStackTrace();
                    var result = new CResult<Boolean>();
                    result.setSuccess(false);
                    return result;
                } catch (IOException ex) {
                    ex.printStackTrace();
                    var result = new CResult<Boolean>();
                    result.setSuccess(false);
                    return result;
                }
            }
            //在Solr服务中，数据的写操作也是有事务的，WEB管理平台默认一次操作一次提交。
            try {
                client.commit();
            } catch (SolrServerException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        client.close();
        return CResult.OK(true);
    }

    /**
     * Describe: 个人资料
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("solrsearch")
    @ApiOperation(value = "solr搜索")
    public CResult<?> solrsearch(@RequestParam String key,
                                 @RequestParam String value,
                                 @RequestParam(name = "start", defaultValue = "0",required = true) Integer start,
                                 @RequestParam(name = "rows", defaultValue = "15",required = true) Integer rows) throws ParseException, IOException {
        if(!security())
        {
            return CResult.error("");
        }
        var result=new CResult<>();
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_foreign").build();
        SolrQuery solrQuery = new SolrQuery();
        //设置默认搜索域
        solrQuery.set("df", key);
        solrQuery.setQuery(key+":"+value);
        solrQuery.set("q.op", "AND");
        solrQuery.set("defType","edismax");
        solrQuery.set("mm","90%");
        solrQuery.setHighlight(true);
        solrQuery.addHighlightField("content");
        solrQuery.setHighlightSnippets(10000);
        solrQuery.setFacetLimit(-1);
        solrQuery.setStart(start);
        solrQuery.setRows(rows);
        QueryResponse response;
        try {
            response = client.query(solrQuery);
            System.out.println(response.getResults());
            //响应头
            NamedList<Object> namedList = response.getHeader();
            Iterator iterator = namedList.iterator();
            while(iterator.hasNext()){
                System.out.println(iterator.next());
            }
            SolrDocumentList documentList = response.getResults();
            var total=documentList.getNumFound();
            System.out.println("总计数据行数"+documentList.getNumFound());
            ArrayList<String> ids=new ArrayList<>();
            for(SolrDocument solrDocument:documentList){
                System.out.print("id = "+solrDocument.get("id"));
                ids.add(solrDocument.get("id").toString());
                // System.out.println("; primaryCoverage = "+solrDocument.get("primaryCoverage"));
            }
            if(ids.size()>0)
            {
                HashMap<String,Integer> isCollectedMap= (HashMap<String, Integer>) stdInfoService.IsCollected(ids);
                if(isCollectedMap.size()>0)
                {
                    for(SolrDocument solrDocument:documentList){
                        String id=solrDocument.get("id").toString();
                        if(isCollectedMap.containsKey(id))
                        {
                            solrDocument.addField("isCollected",isCollectedMap.get(id));
                        }
                    }
                }
            }
            var highliting=response.getHighlighting();
            var obj=new com.alibaba.fastjson.JSONObject();
            obj.put("datalist",documentList);
            obj.put("highlight",highliting);
            obj.put("total",total);
            result.setResult(obj);
        } catch (SolrServerException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    @PostMapping("deleteRecordAndIndex")
    @ApiOperation(value = "删除")
    public CResult<Boolean> deleteRecordAndIndex(@RequestParam(name="data",required = true) String data){
        var result=new CResult<Boolean>();
        var idArr= Arrays.asList(data.split(","));
        if(idArr.size()>0) {
            HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_foreign").build();
            try {
                client.deleteById(idArr);
                System.out.print(data + " 在solr索引中删除成功");
            } catch (SolrServerException e) {
                e.printStackTrace();
                result.setSuccess(false);
                result.setResult(false);
                result.setMessage(e.getMessage());
                return result;
            } catch (IOException e) {
                e.printStackTrace();
                try {
                    client.rollback();
                    result.setSuccess(false);
                    result.setResult(false);
                    result.setMessage(e.getMessage());
                    return result;
                } catch (SolrServerException ex) {
                    ex.printStackTrace();
                    result.setSuccess(false);
                    result.setResult(false);
                    result.setMessage(e.getMessage());
                    return result;
                } catch (IOException ex) {
                    ex.printStackTrace();
                    result.setSuccess(false);
                    result.setResult(false);
                    result.setMessage(e.getMessage());
                    return result;
                }
            }
            try {
                client.commit();
                stdInfoService.removeByIds(idArr);
                for(var i=0;i<idArr.size();i++)
                {
                    deleteEncryptFile(idArr.get(i));
                }
                result.setSuccess(true);
                result.setResult(true);
                result.setMessage("");
            } catch (SolrServerException e) {
                e.printStackTrace();
                result.setSuccess(false);
                result.setResult(false);
                result.setMessage(e.getMessage());
                return result;
            } catch (IOException e) {
                e.printStackTrace();
                result.setSuccess(false);
                result.setResult(false);
                result.setMessage(e.getMessage());
                return result;
            }
        }
        else
        {
            result.setSuccess(false);
            result.setResult(false);
            result.setMessage("请输入要删除的记录编号！");
        }
        return result;
    }

    @PostMapping("deleteAll")
    @ApiOperation(value = "删除所有标准")
    public CResult<Boolean> deleteAll(){
        var result=new CResult<Boolean>();
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.Foreign));
        var idArr=list.stream().map(StdInfo::getId).collect(Collectors.toList());
        if(idArr.size()>0) {
            HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_foreign").build();
            try {
                client.deleteById(idArr);
                System.out.print(idArr + " 在solr索引中删除成功");
            } catch (SolrServerException e) {
                e.printStackTrace();
                result.setSuccess(false);
                result.setResult(false);
                result.setMessage(e.getMessage());
                return result;
            } catch (IOException e) {
                e.printStackTrace();
                try {
                    client.rollback();
                    result.setSuccess(false);
                    result.setResult(false);
                    result.setMessage(e.getMessage());
                    return result;
                } catch (SolrServerException ex) {
                    ex.printStackTrace();
                    result.setSuccess(false);
                    result.setResult(false);
                    result.setMessage(e.getMessage());
                    return result;
                } catch (IOException ex) {
                    ex.printStackTrace();
                    result.setSuccess(false);
                    result.setResult(false);
                    result.setMessage(e.getMessage());
                    return result;
                }
            }
            try {
                client.commit();
                stdInfoService.removeByIds(idArr);
                for(var i=0;i<idArr.size();i++)
                {
                    deleteEncryptFile(idArr.get(i));
                }
                result.setSuccess(true);
                result.setResult(true);
                result.setMessage("");
            } catch (SolrServerException e) {
                e.printStackTrace();
                result.setSuccess(false);
                result.setResult(false);
                result.setMessage(e.getMessage());
                return result;
            } catch (IOException e) {
                e.printStackTrace();
                result.setSuccess(false);
                result.setResult(false);
                result.setMessage(e.getMessage());
                return result;
            }
        }
        else
        {
            result.setSuccess(false);
            result.setResult(false);
            result.setMessage("请输入要删除的记录编号！");
        }
        return result;
    }

    @GetMapping("solrdelete")
    @ApiOperation(value = "删除")
    public void delete(@RequestParam String id){
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_foreign").build();
        try {
            client.deleteById(id);
            System.out.print(id+" 删除成功");
        } catch (SolrServerException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
            try {
                client.rollback();
            } catch (SolrServerException ex) {
                ex.printStackTrace();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        try {
            client.commit();
        } catch (SolrServerException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @PostMapping("updatesolrindex")
    @ApiOperation(value = "更新指定编号的记录")
    //包括新增和更新。 主键一致-更新。主键不存在-新增
    public  CResult<Boolean> update(@RequestParam(name="ids", required = true) String strIds) throws IOException, SolrServerException {
        if(StringUtils.isBlank(strIds))
        {
            var result = new CResult<Boolean>();
            result.setSuccess(false);
            return result;
        }
        var idArr=strIds.split(",");
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().in(StdInfo::getId,idArr));
        HttpSolrClient client = new HttpSolrClient.Builder("http://localhost:8983/solr/std_foreign").build();
        //先删除索引（指定id），不然更新不彻底
        client.deleteById(list.stream().map(StdInfo::getId).collect(Collectors.toList()));
        client.commit();
        //更新数据
        pdfPath=mergePath(pdfPath);
        for (StdInfo data:list)
        {
            //新增或更新。新增文档类型都是SolrInputDocument
            SolrInputDocument doc = new SolrInputDocument();
            var fileExists=false;
            if(StringUtils.isNotBlank(data.getPdfFileName()))
            {
                File file=new File(pdfPath+"\\"+data.getPdfFileName());
                if(file.exists())
                {
                    fileExists=true;
                    var content=getText(file);
                    doc.addField("content", content.getContent());
                }
            }
            if(!fileExists)
            {
                continue;
            }
            doc.addField("id", data.getId());
            doc.addField("stdNo",data.getStdNo());
            doc.addField("stdOrgName",data.getStdOrgName());
            doc.addField("stdIdentification",data.getStdIdentification());
            doc.addField("stdLangugage",data.getStdLangugage());
            doc.addField("catetoryNo",data.getCatetoryNo());
            doc.addField("stdIcs",data.getStdIcs());
            doc.addField("implementationDate",data.getImplementationDate());
            doc.addField("stdStatus",data.getStdStatus());
            doc.addField("supersededStdNo",data.getSupersededStdNo());
            doc.addField("stdOcr",data.getStdOcr());
            doc.addField("stdChineseName",data.getStdChineseName());
            doc.addField("stdEnglishName",data.getStdEnglishName());
            doc.addField("securityClass",data.getSecurityClass());
            doc.addField("pubDept",data.getPubDept());
            doc.addField("advanceDept",data.getAdvanceDept());
            doc.addField("draftingUnit",data.getDraftingUnit());
            doc.addField("drafter",data.getDrafter());
            doc.addField("primaryCoverage",data.getPrimaryCoverage());
            doc.addField("alternateStdNo",data.getAlternateStdNo());
            doc.addField("pdfFileName", data.getPdfFileName());
            doc.addField("stdClass", data.getStdClass());
            try {
                UpdateResponse response = client.add(doc);
                System.out.println(String.format("solor数据更新：status = %s ; QTime = %s", response.getStatus(), response.getQTime()));
            } catch (Exception e) {
                e.printStackTrace();
                try {
                    client.rollback();
                } catch (SolrServerException ex) {
                    ex.printStackTrace();
                    var result = new CResult<Boolean>();
                    result.setSuccess(false);
                    return result;
                } catch (IOException ex) {
                    ex.printStackTrace();
                    var result = new CResult<Boolean>();
                    result.setSuccess(false);
                    return result;
                }
            }
            //在Solr服务中，数据的写操作也是有事务的，WEB管理平台默认一次操作一次提交。
            try {
                client.commit();
            } catch (SolrServerException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
        client.close();
        return CResult.OK(true);
    }

    /**
     * Describe: 个人资料
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("index1")
    @ApiOperation(value = "搜索首页")
    public ModelAndView index1(Model model) {
        SysUser sysUser = UserContext.currentUser();
        //model.addAttribute("logs", sysLogService.selectTopLoginLog(sysUser.getUsername()));
        return jumpPage(MODULE_PATH+ "searchSolr");
    }

    /**
     * Describe: 个人资料
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("index")
    @ApiOperation(value = "搜索首页")
    public ModelAndView center(Model model) {
        SysUser sysUser = UserContext.currentUser();
        //model.addAttribute("logs", sysLogService.selectTopLoginLog(sysUser.getUsername()));
        return jumpPage(MODULE_PATH+ "main");
    }

    /**
     * Describe: 个人资料
     * Param: null
     * Return: ModelAndView
     */
    @GetMapping("db")
    @ApiOperation(value = "搜索首页")
    public ModelAndView dbIndex(Model model) throws ParseException {
        return jumpPage(MODULE_PATH+ "searchDB");
    }

    /**
     * 获取文本提取
     *
     * @param document
     * @param writer
     * @throws IOException
     */
    public void getTextStripper(PDDocument document, Writer writer)
            throws IOException {
        PDFTextStripper textStripper = new PDFTextStripper();
        textStripper.writeText(document, writer);
    }

    /**
     * 提取文本内容
     * @param  file 加载文档的路径
     * @return FileContent
     * @throws IOException
     */
    public FileContent getText(File file) throws IOException {
        String textString = "";
        PDDocument document = PDDocument.load(file);
        //将提取出来的字节流转换为字符流进行显示
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        OutputStreamWriter writer = new OutputStreamWriter(out);
        getTextStripper(document, writer);
        document.close();
        out.close();
        writer.close();
        byte[] con = out.toByteArray();
        textString = new String(con);
        var fileContent=new FileContent();
        fileContent.setContent(textString);
        fileContent.setBinContent(con);
        return fileContent;
    }

    /**
     *  excel文件上传
     */
    @PostMapping("/file-upload")
    @ApiOperation(value = "excel文件上传")
    public CResult<?> fileUpload(HttpServletRequest req) throws Exception,IOException {
        var result=new CResult<Boolean>();
        result.setResult(true);
        result.setSuccess(true);
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) req;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        var errList=new ArrayList<String>();
        try {
            var customSessionId=req.getParameter("customSessionId");
            var uploadResult= stdInfoService.upload(customSessionId,StandardTypeEnum.Foreign, file.getInputStream(),errList);
            //更新二进制文件存储
            if(uploadResult.getResult().getResult() && uploadResult.getList().size()>0)
            {
                process.setTotal(uploadResult.getList().size());
                process.setDealed(0);
                process.setTitle("加密标准文件");
                process.setHasNextProcess(0);
                var list=uploadResult.getList();
                pdfPath=mergePath(pdfPath);
                for(var i=0;i<list.size();i++)
                {
                    var model =list.get(i);
                    if(StringUtils.isNotBlank(model.getPdfFileName())) {
                        File f = new File(pdfPath + "\\" + model.getPdfFileName());
                        File encryptFile = new File(encryptPdfPath + "\\" + model.getId());
                        var pdfIsExists = false;
                        if (!encryptFile.exists() && f.exists()) {
                            //大于100M的文件单独处理
                            if (f.length() > 104857600) {
                                encryptPdfPath = mergePath(encryptPdfPath);
                                Tool.encryptByExe(pdfPath + "\\" + model.getPdfFileName(), encryptPdfPath + "\\" + model.getId(), "20221103");
                                Thread.sleep(Tool.estimatedEncryptTime(f.length()));
                            } else {
                                var data = Tool.getBinaryData(f);
                                saveEncryptPdf(model.getId(), data);
                            }
                            pdfIsExists = true;
                        } else if (encryptFile.exists()) {
                            pdfIsExists = true;
                        }
                        model.setPdfIsExists(pdfIsExists ? "1" : "0");
                        stdInfoService.updateById(model);
                    }
                    process.setDealed(process.getDealed()+1);
                }
                process.setDealed(process.getTotal());
            }
            else if(errList.size()>0) {
                var fileName= savelErrToExcel(errList);
                //返回一个excel文件名称
                result.setMessage(fileName);
                result.setSuccess(false);
                result.setResult(false);
                return result;
            }
        }
        catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
            result.setResult(false);
            result.setSuccess(false);
            result.setMessage("上传失败，"+ex.getMessage());
            return result;
        }
        return result;
    }

    /**
     * 获取jar包所在文件路径
     *
     * @param
     * @return java.lang.String
     * @throws
     */
    public String getJarFilePath() {
        ApplicationHome home = new ApplicationHome(getClass());
        File jarFile = home.getSource();
        return jarFile.getParentFile().toString();
    }

    public String savelErrToExcel(List<String> errArray) throws IOException {
        // create a new Workbook
        var sdf=new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss");
        var fileName="system_upload_err_"+sdf.format(new Date())+".xlsx";
        String fileFullPath=getJarFilePath()+"\\"+fileName;
        Workbook workbook = new XSSFWorkbook();
        // create a new sheet
        XSSFSheet sheet = (XSSFSheet) workbook.createSheet("Sheet1");
        sheet.setColumnWidth(0, 1000);// 设置第二列的宽度
        sheet.setColumnWidth(1, 10000);// 设置第二列的宽度
        // create some data rows
        Row row = sheet.createRow(0);
        row.createCell(0).setCellValue("序号");
        row.createCell(1).setCellValue("错误描述");
        Integer rowIndex=1;
        for(Integer i=0;i<errArray.size();i++)
        {
            if(org.apache.commons.lang.StringUtils.isNotBlank(errArray.get(i))) {
                row = sheet.createRow(rowIndex);
                row.createCell(0).setCellValue(rowIndex);
                row.createCell(1).setCellValue(errArray.get(i));
                rowIndex++;
            }
        }
        // write the Workbook to a ByteArrayOutputStream
        var cal= Calendar.getInstance();
        var outputStream=new FileOutputStream(fileFullPath);
        // set the headers for downloading the file
        try {
            workbook.write(outputStream);
            return fileName;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    /**
     * 数据加密后将pdf保存为二进制数据
     *
     * @param data
     * @return void
     * @throws
     */
    public void saveEncryptPdf(String id,byte[] data) throws IOException {
        encryptPdfPath=mergePath(encryptPdfPath);
        var encryptData= DESUtil.getEncryptBytes(data);
        File file=new File(encryptPdfPath+"\\"+id);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(encryptData);
        fos.flush();
        fos.close();
    }

    /**
     *统计可下载的pdf文件数量
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    @PostMapping("testpdfstatus")
    public Result statisticsPdfCount()
    {
        var list=stdInfoService.list(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getStdType,StandardTypeEnum.Foreign));
        encryptPdfPath=mergePath(encryptPdfPath);
        try {
            for (var i = 0; i < list.size(); i++) {
                var exists = false;
                var data = list.get(i);
                if (StringUtils.isNotBlank(data.getPdfFileName())) {
                    File file = new File(encryptPdfPath + "\\" + data.getId());
                    if (file.exists()) {
                        exists = true;
                    }
                }
                data.setPdfIsExists(exists ? "1" : "0");
                stdInfoService.updateById(data);
            }
        }catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
        }
        return decide(true);
    }

    /**
     * 删除加密文件
     *
     * @param fileName
     * @return void
     * @throws
     */
    public void deleteEncryptFile(String fileName){
        encryptPdfPath=mergePath(encryptPdfPath);
        try {
            File file = new File(encryptPdfPath + "\\" + fileName);
            if (file.exists()) {
                file.delete();
            }
        }catch (Exception ex)
        {

        }
    }

    /**
     *根据用户配置的文件存放盘符及软件默认的文件盘符合并起来作为最终的文件盘符
     *
     * @param basePath
     * @return java.lang.String
     * @throws
     */
    public String mergePath(String basePath)
    {
        var path="";
        var diskNo="";
        var config=sysConfigMapper.selectByCode("security_file_path");
        if(config!=null && StringUtils.isNotBlank(config.getConfigValue()))
        {
            diskNo=config.getConfigValue();
        }
        if(StringUtils.isNotBlank(diskNo))
        {
//            if(diskNo.contains(":"))
//            {
//                diskNo=diskNo.split(":")[0];
//            }
            var baseArr=basePath.split(":");
            // baseArr[0]=diskNo;
            if(diskNo.contains(","))
            {
                var arr=diskNo.split(",");
                for(var i=0;i<arr.length;i++)
                {
                    if(org.apache.commons.lang.StringUtils.isNotBlank(arr[i]))
                    {
                        path=arr[i]+":"+baseArr[1];
                        File f=new File(path);
                        if(!f.exists()) {
                            continue;
                        }
                        else
                        {
                            break;
                        }
                    }
                }
            }
            else {
                path = diskNo + ":" + baseArr[1];
            }
        }
        System.out.println("系统配置的文件路径为："+path);
        return path;
    }

}
