package com.pearadmin.modules.standard.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageInfo;
import com.google.common.primitives.Bytes;
import com.pearadmin.common.constant.ControllerConstant;
import com.pearadmin.common.tools.CResult;
import com.pearadmin.common.tools.DESUtil;
import com.pearadmin.common.tools.Tool;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.EquipmentAssessment;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.service.EquipmentAssessmentService;
import com.pearadmin.modules.standard.service.StdInfoService;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jasypt.encryption.StringEncryptor;
import org.omg.PortableInterceptor.SYSTEM_EXCEPTION;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Describe:  装备鉴定定型及在役考核文件模板 控制器
 * Author: 就 眠 仪 式
 * CreateTime: 2022/10/23
 */
@RestController
@Api(tags = {"装备鉴定定型及在役考核文件模板"})
@RequestMapping(ControllerConstant.API_Equip_ASSESS_PREFIX)
public class EquipmentAssessmentController extends BaseController {
    private final String MODULE_PATH = "equipassess/";
    private String docPath;
    private String encryptDocPath;
    @Resource
    private EquipmentAssessmentService equipmentAssessmentService;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Resource
    private StringEncryptor stringEncryptor;
    //可使用总小时数
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;

    /**
     * 检查使用许可
     *
     * @param
     * @return com.pearadmin.common.web.domain.response.Result
     * @throws
     */
    public Boolean security() throws IOException {
        Boolean isSecurity=false;
        if(checkSecurity.equals(1)) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        if (totalHours < hours) {
                            isSecurity = true;
                        }
                    }
                } catch (Exception ex) {
                    isSecurity = false;
                }
            }
        }
        else
        {
            isSecurity=true;
        }
        return isSecurity;
    }

    /**
     * Describe: 数据字典列表视图
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("main")
    public ModelAndView main() {
        return jumpPage(MODULE_PATH + "main");
    }

    /**
     * Describe: 给最终用户看的界面
     * Param: ModelAndView
     * Return: ModelAndView
     */
    @GetMapping("templist")
    public ModelAndView tempList() throws ParseException {
        /*var result=new CResult<>();
        var sdf=new SimpleDateFormat("yyyy-MM-dd");
        var dateLimit=sdf.parse("2022-09-20");
        if(dateLimit.compareTo(new Date())<=0)
        {
            return jumpPage(MODULE_PATH+"index");
        }*/
        return jumpPage(MODULE_PATH + "list");
    }

    /**
     * Describe: 数据字典列表数据
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("data")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public ResultTable data(EquipmentAssessment model, PageDomain pageDomain) throws ParseException, IOException {
        if(!security())
       {
           return null;
       }
        /* var sdf=new SimpleDateFormat("yyyy-MM-dd");
        var dateLimit=sdf.parse("2023-1-20");
        if(dateLimit.compareTo(new Date())<=0)
        {
            return new ResultTable();
        }*/
      //  PageInfo<EquipmentAssessment> pageInfo = equipmentAssessmentService.page(model, pageDomain);
       // return pageTable(pageInfo.getList(), pageInfo.getTotal());
        var query=new LambdaQueryWrapper<EquipmentAssessment>();
        if(org.apache.commons.lang.StringUtils.isNotBlank(model.getTemplateNo()))
        {
            query.like(EquipmentAssessment::getTemplateNo,model.getTemplateNo());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(model.getTemplateName()))
        {
            query.like(EquipmentAssessment::getTemplateName, model.getTemplateName());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(model.getBigClass()))
        {
            query.like(EquipmentAssessment::getBigClass, model.getBigClass());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(model.getSmallClass()))
        {
            query.like(EquipmentAssessment::getSmallClass, model.getSmallClass());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(model.getPreparationBasis()))
        {
            query.like(EquipmentAssessment::getPreparationBasis, model.getPreparationBasis());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(model.getResponsibleSubject()))
        {
            query.like(EquipmentAssessment::getResponsibleSubject, model.getResponsibleSubject());
        }
        if(StringUtils.isNotBlank(model.getNameOrRange()))
        {
            query.like(EquipmentAssessment::getNameOrRange, model.getNameOrRange());
        }
        //query.select(EquipmentAssessment.class,info-> !info.getColumn().equals("bin_content"));
        List<EquipmentAssessment> list = equipmentAssessmentService.list(query);
        return pageTable(list, list.size());
    }

    @GetMapping("list")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    public List<EquipmentAssessment> list(EquipmentAssessment model, PageDomain pageDomain) throws IOException {
        if(!security())
        {
            return null;
        }
        var query=new LambdaQueryWrapper<EquipmentAssessment>();
        if(org.apache.commons.lang.StringUtils.isNotBlank(model.getTemplateNo()))
        {
            query.like(EquipmentAssessment::getTemplateNo,model.getTemplateNo());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(model.getTemplateName()))
        {
            query.like(EquipmentAssessment::getTemplateName, model.getTemplateName());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(model.getBigClass()))
        {
            query.like(EquipmentAssessment::getBigClass, model.getBigClass());
        }
        if(org.apache.commons.lang.StringUtils.isNotBlank(model.getSmallClass()))
        {
            query.like(EquipmentAssessment::getSmallClass, model.getSmallClass());
        }
        if(StringUtils.isNotBlank(model.getNameOrRange()))
        {
            query.like(EquipmentAssessment::getNameOrRange, model.getNameOrRange());
        }
        //query.select(EquipmentAssessment.class,info-> !info.getColumn().equals("bin_content"));
        List<EquipmentAssessment> list = equipmentAssessmentService.list(query);
        return list;
    }

    /**
     * Describe: 数据字典类型新增视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("add")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public ModelAndView add() {
        return jumpPage(MODULE_PATH + "add");
    }

    /**
     * Describe: 新增字典类型接口
     * Param: sysDictType
     * Return: ResuBean
     */
    @PostMapping("save")
    //@PreAuthorize("hasPermission('/system/dictType/add','sys:dictType:add')")
    public Result save(@RequestBody EquipmentAssessment model) throws IOException {
        boolean result = equipmentAssessmentService.save(model);
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getDocFileName()))
        {
            File f=new File(docPath+"\\"+model.getDocFileName());
            if(f.exists())
            {
                var byteData=getWordBinaryData(f);
                saveEncryptDoc(model.getId(),byteData);
            }
        }
        return decide(result);
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("index")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView index(Model model) {
        return jumpPage(MODULE_PATH + "index");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("edit")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView edit(Model model,String id) {
        var data=equipmentAssessmentService.getById(id);
        model.addAttribute("equipAssess", data);
        return jumpPage(MODULE_PATH + "edit");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @GetMapping("detail")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public ModelAndView detail(Model model,String id) {
        var data=equipmentAssessmentService.getById(id);
        model.addAttribute("quipAssess",data);
        return jumpPage(MODULE_PATH + "detail");
    }

    /**
     * Describe: 数据字典类型修改视图
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PutMapping("update")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result update(@RequestBody EquipmentAssessment stdInfo) throws IOException {
        if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(stdInfo.getDocFileName()))
        {
            File f=new File(docPath+"\\"+stdInfo.getDocFileName());
            if(f.exists())
            {
                var byteData=getWordBinaryData(f);
                saveEncryptDoc(stdInfo.getId(),byteData);
            }
        }
        boolean result = equipmentAssessmentService.updateById(stdInfo);
        return decide(result);
    }

    /**
     * Describe: 批量生成加密文件
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("batchUpdateDoc")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result batchUpdateDoc() throws IOException {
        var list=equipmentAssessmentService.list();
        for(var i=0;i<list.size();i++)
        {
            if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(list.get(i).getDocFileName()))
            {
                File f=new File(docPath+"\\"+list.get(i).getDocFileName());
                if(f.exists())
                {
                    var byteData=getWordBinaryData(f);
                    saveEncryptDoc(list.get(i).getId(),byteData);
                }
            }
        }
        return decide(true);
    }

 /*   *//**
     * Describe: 批量生成加密文件
     * Param: sysDictType
     * Return: ModelAndView
     *//*
    @PostMapping("batchDecryptDoc")
    //@PreAuthorize("hasPermission('/system/dictType/edit','sys:dictType:edit')")
    public Result batchDecryptDoc() throws IOException {
        var list=equipmentAssessmentService.list();
        for(var i=0;i<list.size();i++)
        {
            var equipment=list.get(i);
            var data=getDecyptData(encryptDocPath,equipment.getId());
            saveFile(equipment.getDocFileName(),data);
        }
        return decide(true);
    }*/

    /**
     * 数据加密后将pdf保存为二进制数据
     *
     * @param data
     * @return void
     * @throws
     */
    public void saveFile(String fileName,byte[] data) throws IOException {
        File file=new File(docPath+"\\"+fileName);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(data);
        fos.flush();
        fos.close();
    }

    /**
     *从pdf加密后的数据中解密出真实的二进制数据
     *
     * @param id
     * @return byte[]
     * @throws
     */
    public byte[] getDecyptData(String filePath, String id) throws IOException {
        var convertData=new byte[0];
        File file=new File(filePath+"\\"+id);
        var byteData= Tool.getBinaryData(file);
        convertData= DESUtil.getDecryptBytes(byteData);
        return convertData;
    }

    /**
     * Describe: 数据字典删除
     * Param: sysDictType
     * Return: ModelAndView
     */
    @DeleteMapping("remove/{id}")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result remove(@PathVariable("id") String id) throws IOException {
        Boolean result = equipmentAssessmentService.removeById(id);
        Files.deleteIfExists(Paths.get(encryptDocPath+"\\"+id));
        return decide(result);
    }

    /**
     * Describe: 数据字典删除
     * Param: sysDictType
     * Return: ModelAndView
     */
    @PostMapping("deleteRecordAndIndex")
    //@PreAuthorize("hasPermission('/system/dictType/remove','sys:dictType:remove')")
    public Result deleteRecordAndIndex(@RequestParam String data) throws IOException {
        var ids=data.split(",");
        if(ids.length>0) {
            Boolean result = equipmentAssessmentService.removeBatchByIds(Arrays.asList(ids));
            for(var i=0;i<ids.length;i++)
            {
                Files.deleteIfExists(Paths.get(encryptDocPath+"\\"+ids[i]));
            }
            return decide(result);
        }
        else{
            return decide(false);
        }
    }

    /**
     *  excel文件上传
     */
    @PostMapping("/file-upload")
    @ApiOperation(value = "excel文件上传")
    public CResult<?> importExcel(HttpServletRequest req) throws Exception {
        var result=new CResult<Boolean>();
        result.setResult(true);
        result.setSuccess(true);
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) req;
        MultipartFile file = multipartRequest.getFile("file");// 获取上传文件对象
        try {
           var uploadResult= equipmentAssessmentService.upload(file.getInputStream());
           if(uploadResult.getResult().isSuccess() && uploadResult.getList().size()>0)
           {
                var list=uploadResult.getList();
                for(var i=0;i<list.size();i++)
                {
                    var model=list.get(i);
                    if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(model.getDocFileName()))
                    {
                        File f=new File(docPath+"\\"+model.getDocFileName());
                        if(f.exists())
                        {
                            var byteData=getWordBinaryData(f);
                            saveEncryptDoc(model.getId(),byteData);
                         }
                    }
                }
           }
        }
        catch (Exception ex)
        {
            System.out.println(ex.getStackTrace());
            result.setResult(false);
            result.setSuccess(false);
            result.setMessage("上传失败，"+ex.getMessage());
            return result;
        }
        return result;
    }


    public byte[] getWordBinaryData(File file) {
        byte[] result=new byte[0];
        //设置文件路径
        //ClassPathResource classPathResource = new ClassPathResource("statics/pdf/"+fileName);
        //String resource = classPathResource.getURL().getPath();
        if (file.exists()) {
            byte[] buffer = new byte[1024];
            FileInputStream fis = null;
            BufferedInputStream bis = null;
            try {
                fis = new FileInputStream(file);
                bis = new BufferedInputStream(fis);
                ArrayList<Byte> byteList = new ArrayList<Byte>();
                int i = bis.read(buffer);
                while (i != -1) {
                    for (var idx = 0; idx < i; idx++) {
                        byteList.add(buffer[idx]);
                    }
                    i = bis.read(buffer);
                }
                result = Bytes.toArray(byteList);
                System.out.println("success");
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (bis != null) {
                    try {
                        bis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (fis != null) {
                    try {
                        fis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return result;
    }

    /**
     * 数据加密后将word保存为二进制数据
     *
     * @param data
     * @return void
     * @throws
     */
    public void saveEncryptDoc(String id,byte[] data) throws IOException {
        var encryptData= DESUtil.getEncryptBytes(data);
        File file=new File(encryptDocPath+"\\"+id);
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(encryptData);
        fos.flush();
        fos.close();
    }

}
