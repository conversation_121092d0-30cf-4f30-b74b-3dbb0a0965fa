package com.pearadmin.modules.standard.controller;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.pearadmin.common.aop.annotation.Log;
import com.pearadmin.common.aop.enums.BusinessType;
import com.pearadmin.common.context.UserContext;
import com.pearadmin.common.tools.DESUtil;
import com.pearadmin.common.tools.SequenceUtil;
import com.pearadmin.common.tools.StandardTypeEnum;
import com.pearadmin.common.tools.Tool;
import com.pearadmin.common.tools.string.StringUtil;
import com.pearadmin.common.tools.velocity.StdUserOperateType;
import com.pearadmin.common.web.base.BaseController;
import com.pearadmin.common.web.domain.request.PageDomain;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.common.web.domain.response.module.ResultTable;
import com.pearadmin.modules.standard.domain.EquipmentAssessment;
import com.pearadmin.modules.standard.domain.StdDownload;
import com.pearadmin.modules.standard.domain.StdDownloadConfig;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.service.ChongQueryWrapper;
import com.pearadmin.modules.standard.service.EquipmentAssessmentService;
import com.pearadmin.modules.standard.service.StdDownloadConfigService;
import com.pearadmin.modules.standard.service.StdDownloadService;
import com.pearadmin.modules.standard.service.StdInfoService;
import com.pearadmin.modules.sys.domain.SysConfig;
import com.pearadmin.modules.sys.domain.SysUser;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import com.pearadmin.modules.sys.service.SysConfigService;
import lombok.var;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.WorkbookUtil;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.net.InetAddress;
import java.net.URLEncoder;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.List;

@RestController
@RequestMapping("/source")
public class DownLoadController extends BaseController {
    @Value(value = "${pdf.encryptSource.path}")
    private String pdfPath;
    @Autowired
    private StdInfoService stdInfoService;
    //系统检查
    @Resource
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private StdDownloadService stdDownloadService;
    @Resource
    private SysConfigService sysConfigService;
    @Resource
    private StdDownloadConfigService stdDownloadConfigService;

    //获取国内标准栏目下 允许导出的标准的最大数量
    @GetMapping("/std_m_d_c")
    public Result getChinaStdMaxDownloadCount() {
        var res = new Result<Integer>();
        res.setData(1000);
        res.setSuccess(true);
        var config = sysConfigMapper.selectByCode("c_std_m_d_c");
        if (config != null) {
            res.setData(Integer.parseInt(config.getConfigValue()));
        }
        return res;
    }

    //获取情报室栏目下 允许导出的标准的最大数量
    @GetMapping("/ib_m_d_c")
    public Result getInteligBusiMaxDownloadCount() {
        var res = new Result<Integer>();
        res.setData(2000);
        res.setSuccess(true);
        var config = sysConfigMapper.selectByCode("qb_m_d_c");
        if (config != null) {
            res.setData(Integer.parseInt(config.getConfigValue()));
        }
        return res;
    }

    //获取国内标准栏目下 上次用户选择的要导出的列
    @GetMapping("/g_user_d_col")
    public Result getChinaStdDownloadColumnConfig() {
        var user = UserContext.currentUser();
        var res = new Result<String>();
        res.setData("1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0");//默认前两列可以下载
        res.setSuccess(true);
        var config = sysConfigMapper.selectByCode("col_con_" + user.getUsername());
        if (config != null) {
            res.setData(config.getConfigValue());
        }
        return res;
    }

    //更新国内标准栏目下 用户选择的要导出的列
    @PostMapping("/u_user_d_col")
    public Result updateChinaStdDownloadColumnConfig(@RequestBody Map<String, String> map) {
        var data = "1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0";
        if (map != null && map.containsKey("config")) {
            data = map.get("config");
        }
        updateStdDownloadColumnConfig(data);
        var res = new Result<Boolean>();
        res.setData(true);//默认前两列可以下载
        res.setSuccess(true);
        return res;
    }

    //更新国内标准栏目下 用户选择的要导出的列
    public Boolean updateStdDownloadColumnConfig(String colConfig) {
        var user = UserContext.currentUser();
        var config = sysConfigMapper.selectByCode("col_con_" + user.getUsername());
        if (config != null) {
            config.setConfigValue(colConfig);
            sysConfigMapper.updateById(config);
        } else {
            config = new SysConfig();
            config.setConfigCode("col_con_" + user.getUsername());
            config.setConfigValue(colConfig);
            config.setConfigType("custom");
            config.setConfigName("用户配置");
            config.setCreateTime(LocalDateTime.now());
            sysConfigMapper.insert(config);
        }
        return true;
    }


    /**
     * Describe: 下载检索出的标准列表
     * Param: sysDictType
     * Return: ResuTable
     */
    @GetMapping("downloadStdByCond")
    //@PreAuthorize("hasPermission('/system/dictType/data','sys:dictType:data')")
    @Log(title = "下载检索出的标准列表", describe = "下载检索出的标准列表", type = BusinessType.STD_Data)
    public void simpleQuery(StdInfo stdInfo, HttpServletResponse response) throws ParseException, IOException, IllegalAccessException {
        //保存下载配置字段
        if (StringUtils.isNotBlank(stdInfo.getChooseCol())) {
            var data = "1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0";
            data = stdInfo.getChooseCol();
            updateStdDownloadColumnConfig(data);
        }
        var query = new ChongQueryWrapper<StdInfo>();
        query.eq("std_type", stdInfo.stdType);
        String sortSql = "";
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(stdInfo.getSimpleSearchKey())) {
            var keys = stdInfo.getSimpleSearchKey();
            while (keys.contains("  ")) {
                keys = keys.replaceFirst("  ", " ");
            }
            var keyArr = keys.split(" ");
            for (String key : keyArr) {
                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(key)) {
                    if (stdInfo.stdType.equals(StandardTypeEnum.China)) {
                        query.and(foo -> foo.like("std_no", key).or().like("std_org_Name", key));
                    } else if (stdInfo.stdType.equals(StandardTypeEnum.USA)) {
                        query.and(foo -> foo.like("std_no", key).or().like("std_org_Name", key).or().like("std_chinese_Name", key));
                    }
                    if (StringUtil.isNumeric(key)) {
                        sortSql += " when sort_serial_no='" + key + "' then 10 when sort_serial_no like '" + key + "%' then 5 ";
                    }
                }
            }
        } else if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(stdInfo.getAdvSearchKey())) {
            var jsonArray = (JSONArray) JSONArray.parse(stdInfo.getAdvSearchKey());
            for (Object item : jsonArray) {
                var obj = (com.alibaba.fastjson.JSONObject) item;
                var logic = obj.getString("logic");
                var field = obj.getString("field");
                var val = obj.getString("val");
                var type = obj.getString("type");
                if ("AND".equals(logic)) {
                    //精确查找
                    if ("exact".equals(type)) {
                        query.eq(field, val);
                    } else {
                        //模糊查询
                        query.like(field, val);
                    }
                } else if ("OR".equals(logic)) {
                    //精确查找
                    if ("exact".equals(type)) {
                        query.or(qr -> qr.eq(field, val));
                    } else {
                        //模糊查询
                        query.or(qr -> qr.like(field, val));
                    }
                } else if ("NOT".equals(logic)) {
                    //精确查找
                    if ("exact".equals(type)) {
                        query.ne(field, val);
                    } else {
                        //模糊查询
                        query.notLike(field, val);
                    }
                }
                //排序条件------------------------------
                //精确查找
                if ("exact".equals(type)) {
                    if (field.trim().equals("std_no")) {
                        sortSql += " when std_no = '" + val + "' then 10 ";
                    }
                } else {
                    //模糊查询
                    if (field.trim().equals("std_no")) {
                        if (StringUtil.isNumeric(val)) {
                            sortSql += " when sort_serial_no='" + val + "' then 10 when sort_serial_no like '" + val + "%' then 5 ";
                        }
                    }
                }
            }
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(stdInfo.getQueryIdenti())) {
            var arr = stdInfo.getQueryIdenti().split(",");
            var list = (List<String>) Arrays.asList(arr);
            //检索全部Identification
            if (list.contains("0")) {
                //什么都不做,取所有标准
            } else if (list.size() > 0) {
                if (stdInfo.stdType.equals("china")) {
                    query.and(qw -> {
                        for (var i = 0; i < list.size(); i++) {
                            if (list.contains("GJB")) {
                                qw.or(q -> {
                                    q.like("std_class", "国家军用标准");
                                });
                            }
                            if (list.contains("WJ")) {
                                qw.or(q -> {
                                    q.like("std_class", "兵器行业标准");
                                });
                            }
                            if (list.contains("Q/CNG")) {
                                qw.or(q -> {
                                    q.like("std_class", "集团公司标准");
                                });
                            }
                            if (list.contains("GB")) {
                                qw.or(q -> {
                                    q.like("std_class", "国家标准");
                                });
                            }
                            if (list.contains("HB")) {
                                qw.or(q -> {
                                    q.like("std_class", "航空行业标准");
                                });
                            }
                            if (list.contains("HG")) {
                                qw.or(q -> {
                                    q.like("std_class", "化工行业标准");
                                });
                            }
                            if (list.contains("JB")) {
                                qw.or(q -> {
                                    q.like("std_class", "机械行业标准");
                                });
                            }
                            if (list.contains("JJF")) {
                                qw.or(q -> {
                                    q.like("std_class", "计量技术规范");
                                });
                            }
                            if (list.contains("JJG")) {
                                qw.or(q -> {
                                    q.like("std_class", "计量检定规程");
                                });
                            }
                            if (list.contains("QC")) {
                                qw.or(q -> {
                                    q.like("std_class", "汽车行业标准");
                                });
                            }
                            if (list.contains("QJ")) {
                                qw.or(q -> {
                                    q.like("std_class", "航天行业标准");
                                });
                            }
                            if (list.contains("SJ")) {
                                qw.or(q -> {
                                    q.like("std_class", "电子行业标准");
                                });
                            }
                            if (list.contains("SY")) {
                                qw.or(q -> {
                                    q.like("std_class", "石油天然气行业标准");
                                });
                            }
                            if (list.contains("TB")) {
                                qw.or(q -> {
                                    q.like("std_class", "铁道行业标准");
                                });
                            }
                            if (list.contains("YD")) {
                                qw.or(q -> {
                                    q.like("std_class", "通信行业标准");
                                });
                            }
                            if (list.contains("CB")) {
                                qw.or(q -> {
                                    q.like("std_class", "船舶行业标准");
                                });
                            }
                            if (list.contains("DL")) {
                                qw.or(q -> {
                                    q.like("std_class", "电力行业标准");
                                });
                            }
                        }
                    });
                } else if (stdInfo.stdType.equals("usa")) {
                    query.and(qw -> {
                        for (var i = 0; i < list.size(); i++) {
                            switch (list.get(i)) {
                                case "AA":
                                    qw.or(q -> q.like("std_class", "美国铝协会标准"));
                                    break;
                                case "AASHTO":
                                    qw.or(q -> q.like("std_class", "美国国家公路与运输协会标准"));
                                    break;
                                case "ABS":
                                    qw.or(q -> q.like("std_class", "美国船舶局标准"));
                                    break;
                                case "AIA":
                                    qw.or(q -> q.like("std_class", "美国航空航天工业联合会标准"));
                                    break;
                                case "AIAA":
                                    qw.or(q -> q.like("std_class", "美国航空与航天协会标准"));
                                    break;
                                case "AIIM":
                                    qw.or(q -> q.like("std_class", "美国信息与图像管理协会标准"));
                                    break;
                                case "ARINC":
                                    qw.or(q -> q.like("std_class", "美国航空无线电通信公司标准"));
                                    break;
                                case "ASCE":
                                    qw.or(q -> q.like("std_class", "美国土木工程师协会标准"));
                                    break;
                                case "ASME":
                                    qw.or(q -> q.like("std_class", "美国机械工程师协会标准"));
                                    break;
                                case "ASQ":
                                    qw.or(q -> q.like("std_class", "美国质量协会标准"));
                                    break;
                                case "ASTM":
                                    qw.or(q -> q.like("std_class", "美国材料与试验协会标准"));
                                    break;
                                case "ATIS":
                                    qw.or(q -> q.like("std_class", "美国信息技术协会标准"));
                                    break;
                                case "AWS":
                                    qw.or(q -> q.like("std_class", "美国焊接协会标准"));
                                    break;
                                case "EIA":
                                    qw.or(q -> q.like("std_class", "美国电子工业协会标准"));
                                    break;
                                case "FM":
                                    qw.or(q -> q.like("std_class", "美国陆军野战手册"));
                                    break;
                                case "GPA":
                                    qw.or(q -> q.like("std_class", "美国气体处理协会标准"));
                                    break;
                                case "GSA":
                                    qw.or(q -> q.like("std_class", "美国总务管理局标准"));
                                    break;
                                case "IEEE":
                                    qw.or(q -> q.like("std_class", "美国电气与电子工程师协会标准"));
                                    break;
                                case "ISA":
                                    qw.or(q -> q.like("std_class", "美国仪器、系统与自动化协会标准"));
                                    break;
                                case "MIL":
                                    qw.or(q -> q.like("std_class", "美国军用标准"));
                                    break;
                                case "MSS":
                                    qw.or(q -> q.like("std_class", "美国阀门及配件工业制造商标准化协会标准"));
                                    break;
                                case "NASA":
                                    qw.or(q -> q.like("std_class", "美国宇航局标准"));
                                    break;
                                case "NATO":
                                    qw.or(q -> q.like("std_class", "北约标准"));
                                    break;
                                case "NEMA":
                                    qw.or(q -> q.like("std_class", "美国电气制造商协会标准"));
                                    break;
                                case "NISO":
                                    qw.or(q -> q.like("std_class", "美国国家信息标准协会标准"));
                                    break;
                                case "RTCA":
                                    qw.or(q -> q.like("std_class", "美国航空无线电技术委员会标准"));
                                    break;
                                case "RWMA":
                                    qw.or(q -> q.like("std_class", "美国电阻焊接机制造商协会标准"));
                                    break;
                                case "SAE":
                                    qw.or(q -> q.like("std_class", "美国汽车工程师学会标准"));
                                    break;
                                case "SSPC":
                                    qw.or(q -> q.like("std_class", "美国钢结构油漆委员会标准"));
                                    break;
                                case "NACE":
                                    qw.or(q -> q.like("std_class", "美国全国腐蚀工程师协会标准"));
                                    break;
                                case "TIA":
                                    qw.or(q -> q.like("std_class", "美国通信工业协会标准"));
                                    break;
                                case "TM":
                                    qw.or(q -> q.like("std_class", "美国陆军技术手册"));
                                    break;
                                case "TOP":
                                    qw.or(q -> q.like("std_class", "美国试验操作规则"));
                                    break;
                            }
                        }
                    });
                }
            }
        } else {
            query.and(wq -> {
                wq.eq("1", "2");
            });
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(sortSql)) {
            query.orderBy(true, false, "CASE " + sortSql + " ELSE 0 END");
        }
        //query.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_char").orderByAsc("sort_year");
        query.orderByAsc("sort_identifaction").orderByAsc("sort_serial_no").orderByAsc("sort_number").orderByAsc("sort_year");
        var maxCountRes = getChinaStdMaxDownloadCount();
        var maxCount = maxCountRes != null ? maxCountRes.getData() : 1000;
        query.last("limit " + maxCount);
        var list = stdInfoService.list(query);
        String[] colDownload = new String[]{"标准号", "标准名称(原文)", "标准名称(中文)", "标准名称(英文)", "标准类别", "分类号", "ICS 号", "发布机构", "发布日期", "实施日期", "提出单位", "起草单位", "起草人", "标准状态", "代替标准号(老标准)", "被替代标准号(新标准)", "页数", "标准年代", "标准主要内容"};
        String[] colFields = new String[]{"stdNo", "stdOrgName", "stdChineseName", "stdEnglishName", "stdClass", "catetoryNo", "stdIcs", "pubDept", "pubDate", "implementationDate", "advanceDept", "draftingUnit", "drafter", "stdStatus", "alternateStdNo", "supersededStdNo", "pageCount", "stdOcr", "primaryCoverage"};
        var colDownloadAllow = stdInfo.getChooseCol().split(",");

        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet(WorkbookUtil.createSafeSheetName("Sheet1"));
        XSSFRow rowObjHeader = (XSSFRow) sheet.createRow(0);
        XSSFCell cellHeader = rowObjHeader.createCell(0);
        cellHeader.setCellValue("序号");
        var hIdx = 1;
        for (var i = 0; i < colDownload.length; i++) {
            if (colDownloadAllow[i].equals("1")) {
                cellHeader = rowObjHeader.createCell(hIdx);
                cellHeader.setCellValue(colDownload[i]);
                hIdx++;
            }
        }
        XSSFCellStyle xhStyle = workbook.createCellStyle();
        xhStyle.setAlignment(HorizontalAlignment.CENTER);

        Class<?> stdInfoClass = stdInfo.getClass();
        Field[] classFields = stdInfoClass.getDeclaredFields();
        int idx = 0;
        for (var i = 0; i < list.size(); i++) {
            var stdModel = list.get(i);
            //写入excel
            XSSFRow rowObj = (XSSFRow) sheet.createRow(idx + 1);
            XSSFCell cell = rowObj.createCell(0);
            cell.setCellValue(idx + 1);
            cell.setCellStyle(xhStyle);
            var fIdx = 1;
            for (var z = 0; z < colFields.length; z++) {
                if (colDownloadAllow[z].equals("1")) {
                    cell = rowObj.createCell(fIdx);
                    // 遍历每一个Field对象
                    for (Field field : classFields) {
                        if (field.getName().equals(colFields[z])) {
                            // 暂时忽略私有字段
                            //if (field.isAccessible()) {
                            // 获取字段的值
                            Object value = field.get(stdModel);
                            cell.setCellValue(value != null ? value.toString() : "");

                            // }
                        }
                    }
                    fIdx++;
                }
            }
            idx++;
        }
        sheet.autoSizeColumn(0);
        var zIdx = 1;
        for (var z = 0; z < colFields.length; z++) {
            if (colDownloadAllow[z].equals("1")) {
                sheet.autoSizeColumn(zIdx);
                zIdx++;
            }
        }
        response.setContentType("application/force-download");// 设置强制下载不打开
        response.addHeader("Content-Disposition", "attachment;fileName=export.xlsx");// 设置文件名
        OutputStream out = response.getOutputStream();
        workbook.write(out);
        workbook.close();
        out.flush();
        out.close();
    }

    /**
     * 下载 装备鉴定定型及在役考核文件模板（下载的是上传excel模板）
     *
     * @param
     * @param response
     * @return void
     * @throws
     */
    @GetMapping("/downloadStandardTemplate")
    public void downloadStandardTemplate(HttpServletResponse response) throws Exception {
        var stream = this.getClass().getClassLoader().getResourceAsStream("static/admin/template/template_standardinfo.xlsx");
        String fileName = "template_standardinfo.xlsx";
        downloadFileFromStream(fileName, stream, response);
    }

    /**
     * 超过100M的文件
     * 同时会执行解密操作
     *
     * @param response
     * @param id
     * @return com.pearadmin.common.web.domain.response.Result<java.lang.Long>
     * @throws
     */
    @GetMapping("/isBigFile")
    public Result<Long> isBigFile(HttpServletResponse response, @RequestParam String id) throws Exception {
        var res = new Result<Long>();
        res.setData(0L);
        res.setSuccess(true);
        try {
            var sType = "china";
            if (StringUtils.isNotBlank(id)) {
                var stdInfo = stdInfoService.getById(id);
                if (stdInfo != null) {
                    var filePath = getCorrespondingPath(getPdfPath(sType), stdInfo.getId());
                    File file = new File(filePath + "\\" + stdInfo.getId());
                    if (file.exists()) {
                        var decryptFileName = filePath + "\\" + stdInfo.getId() + "1";
                        var decryptFile = new File(decryptFileName);
                        //大于100M的文件单独处理
                        if (file.length() > 104857600) {
                            //解密文件
                            if (!decryptFile.exists()) {
                                Tool.decryptByExe(filePath + "\\" + stdInfo.getId(), decryptFileName, "20221103");
                            }
                        }
                        if (decryptFile.exists()) {
                            //解压文件已经存在了，就直接跳过去显示，不用等待了，设置为1000意思是该文件是小文件
                            res.setData(1000L);
                        } else {
                            res.setData(file.length());
                        }
                        res.setSuccess(true);
                    }
                }
            }
        } catch (Exception ex) {
            res.setSuccess(false);
            res.setMsg("发生错误!");
        }
        return res;
    }

    /**
     * 从多个盘符对应的文件中找到加密文件的存放位置
     *
     * @param filePaths
     * @param id
     * @return java.lang.String
     * @throws
     */
    public String getCorrespondingPath(String filePaths, String id) {
        String path = filePaths;
        if (filePaths.contains(",")) {
            var paths = filePaths.split(",");
            for (var i = 0; i < paths.length; i++) {
                File file = new File(paths[i] + "\\" + id);
                if (file.exists()) {
                    path = paths[i];
                    break;
                }
            }
        }
        return path;
    }


    /**
     * 下载 装备鉴定定型及在役考核文件模板（下载的是上传excel模板）
     *
     * @param
     * @param response
     * @return void
     * @throws
     */
    @GetMapping("/downloadSystemTemplate")
    public void downloadSystemTemplate(HttpServletResponse response) throws Exception {
        var stream = this.getClass().getClassLoader().getResourceAsStream("static/admin/template/template_system.xlsx");
        String fileName = "template_system.xlsx";
        downloadFileFromStream(fileName, stream, response);
    }

    /**
     * 下载 岗位专题 导入模板
     *
     * @param
     * @param response
     * @return void
     * @throws
     */
    @GetMapping("/downloadSubjectTemplate")
    public void downloadSubjectTemplate(HttpServletResponse response) throws Exception {
        var stream = this.getClass().getClassLoader().getResourceAsStream("static/admin/template/template_special_subject.xlsx");
        String fileName = "template_special_subject.xlsx";
        downloadFileFromStream(fileName, stream, response);
    }


    @GetMapping("/stdsystemTemplate")
    public void downloadStdsystemTemplate(HttpServletResponse response) throws Exception {
        var stream = this.getClass().getClassLoader().getResourceAsStream("static/admin/template/template_stdsystem.xlsx");
        String fileName = "标准体系excel模板.xlsx";
        downloadFileFromStream(fileName, stream, response);
    }


    @GetMapping("/downloadUploadErr")
    public void downloadUploadErr(HttpServletResponse response, @RequestParam String fileName) throws Exception {
        var stream = new FileInputStream(getJarFilePath() + "\\" + fileName);
        if (StringUtils.isNotBlank(fileName)) {
            downloadFileFromStream(fileName, stream, response);
        }
    }

    /**
     * 获取jar包所在文件路径
     *
     * @param
     * @return java.lang.String
     * @throws
     */
    public String getJarFilePath() {
        ApplicationHome home = new ApplicationHome(getClass());
        File jarFile = home.getSource();
        return jarFile.getParentFile().toString();
    }


    /**
     * 下载标准
     *
     * @param fileName
     * @param response
     * @return void
     * @throws
     */
    @GetMapping("/downloadStandard")
    public void download(@RequestParam(name = "fileName", required = true) String fileName, @RequestParam(name = "stype", required = true) String sType, HttpServletResponse response) throws Exception {
        // var data= java.net.URLDecoder.decode(map."")
        if (StringUtils.isNotBlank(fileName)) {
            var stdInfo = stdInfoService.getOne(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getPdfFileName, fileName).last("limit 1"));
            if (stdInfo != null) {
                var data = getDecyptData(getCorrespondingPath(getPdfPath(sType), stdInfo.getId()), stdInfo.getId());
                downloadBinaryFile(fileName, data, response);
            }
        }
    }

    /**
     * pdf安装文件下载（在/resources/static/UdPdf下）
     *
     * @param fileName (需要包含资源文件夹下的路径及文件名称）
     * @param response
     * @return void
     * @throws
     */
    @GetMapping("/getPdfPlugIn")
    public void getPdfPlugIn(@RequestParam(name = "fileName", required = true) String fileName, HttpServletResponse response) throws Exception {
        if (StringUtils.isNotBlank(fileName)) {
            String fullPath = this.getClass().getClassLoader().getResource("").getPath();
            fullPath = fullPath + fileName;
            downloadFile(fullPath, response);
        }
    }

    public String getPdfPath(String sType) {

        return mergePath(pdfPath);

      /*  if("china".equals(sType))
        {
            return chinaPdfPath;
        }
        else  if("usa".equals(sType))
        {
            return usaPdfPath;
        }
        else
        {
            return foreignPdfPath;
        }*/
    }

    /**
     * 根据用户配置的文件存放盘符及软件默认的文件盘符合并起来作为最终的文件盘符
     *
     * @param basePath
     * @return java.lang.String
     * @throws
     */
    public String mergePath(String basePath) {
        var path = "";
        var diskNo = "";
        var config = sysConfigMapper.selectByCode("security_file_path");
        if (config != null && com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(config.getConfigValue())) {
            diskNo = config.getConfigValue();
        }
        if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(diskNo)) {
//            if(diskNo.contains(":"))
//            {
//                diskNo=diskNo.split(":")[0];
//            }
            var baseArr = basePath.split(":");
            // baseArr[0]=diskNo;
            if (diskNo.contains(",")) {
                var arr = diskNo.split(",");
                for (var i = 0; i < arr.length; i++) {
                    if (StringUtils.isNotBlank(arr[i])) {
                        var p = arr[i] + ":" + baseArr[1];
                        path += "," + p;
                    }
                }
                if (path.length() > 1) {
                    path = path.substring(1);
                }
            } else {
                path = diskNo + ":" + baseArr[1];
            }
        }
        //增加扩展文件夹，如系统配置可在两个盘符下存放密文（C，D），则扩展为8个位置，搜索顺序为：  C:\EncryptSources,D:\EncryptSources,C:\EncryptSources0,D:\EncryptSources0,C:\EncryptSources1,D:\EncryptSources1,C:\EncryptSources2,D:\EncryptSources2
        //多个盘符依次类推
        var pathArr = path.split(",");
        var list = new ArrayList<String>();
        for (var i = 0; i < pathArr.length; i++) {
            list.add(pathArr[i]);
        }
        for (var i = 0; i < pathArr.length; i++) {
            list.add(pathArr[i] + "0");
        }
        for (var i = 0; i < pathArr.length; i++) {
            list.add(pathArr[i] + "1");
        }
        for (var i = 0; i < pathArr.length; i++) {
            list.add(pathArr[i] + "2");
        }
        path = String.join(",", list);
        System.out.println("系统配置的文件路径为：" + path);
        return path;
    }

    public String downloadBinaryFile(String fileName, byte[] data, HttpServletResponse response) throws IOException {
        if (data.length > 0) {
            response.setContentType("application/force-download");// 设置强制下载不打开
            response.addHeader("Content-Disposition", "attachment;fileName=" + fileName);// 设置文件名
            OutputStream os = response.getOutputStream();
            try {
                os.write(data, 0, data.length);
                System.out.println("success");
                InetAddress addr = InetAddress.getLocalHost();
                String ip = addr.getHostAddress(); //获取本机ip
                replacTextContent(ip);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 大文件下载
     *
     * @param fileName
     * @param filePath
     * @param response
     * @return java.lang.String
     * @throws
     */
    public String downloadBinaryBigFile(String fileName, String filePath, HttpServletResponse response) throws IOException {
        var file = new File(filePath);
        var inputStream = new FileInputStream(file);
        if (file.length() > 0) {
            response.setContentType("application/force-download");// 设置强制下载不打开
            response.addHeader("Content-Disposition", "attachment;fileName=" + fileName);// 设置文件名
            OutputStream out = response.getOutputStream();
            try {
                var buf = new byte[8192];
                int bytesread = 0, bytesBuffered = 0;
                while ((bytesread = inputStream.read(buf)) > -1) {
                    out.write(buf, 0, bytesread);
                    bytesBuffered += bytesread;
                    if (bytesBuffered > 1024 * 1024) { //flush after 1MB
                        bytesBuffered = 0;
                        out.flush();
                    }
                }
                System.out.println("success");
                InetAddress addr = InetAddress.getLocalHost();
                String ip = addr.getHostAddress(); //获取本机ip
                replacTextContent(ip);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (out != null) {
                    out.flush();
                }
                inputStream.close();
            }
        }
        return null;
    }

    /*
    @GetMapping("/getStandardPdf/{fileName}")
    public void getStandardPdf(@PathVariable("fileName") String fileName, HttpServletResponse response, HttpServletRequest req) throws Exception {
        String referer = req.getHeader("referer");
        if(null == referer || referer.isEmpty())
            return;
        var sType = "china";
        if(StringUtils.isNotBlank(fileName)) {
            var stdInfo=stdInfoService.getOne(new LambdaQueryWrapper<StdInfo>().eq(StdInfo::getPdfFileName,fileName).last("limit 1"));
            if(stdInfo!=null)
            {
                var data=getDecyptData(getPdfPath(sType),stdInfo.getId());
                downloadBinaryFile(fileName,data, response);
            }
        }
    }*/

    /**
     * 预览PDF文件
     *
     * @param id       标准ID
     * @param response HTTP响应
     * @param req      HTTP请求
     * @throws Exception 异常
     */
    @GetMapping("/previewStandardPdf/{id}")
    public void previewStandardPdf(@PathVariable("id") String id, HttpServletResponse response, HttpServletRequest req) throws Exception {
        System.out.println("Previewing PDF with ID: " + id);
        try {
            String referer = req.getHeader("referer");
            if (null == referer || referer.isEmpty()) {
                System.out.println("Referer is empty, returning");
                return;
            }

            // 获取当前用户
            SysUser sysUser = UserContext.currentUser();
            if (sysUser == null) {
                System.err.println("Current user is null");
                OutputStream os = response.getOutputStream();
                os.write("用户未登录或会话已过期".getBytes());
                return;
            }
            System.out.println("Current user: " + sysUser.getUsername());

            // 检查用户当天的预览次数
            Integer previewCount = stdDownloadService.getPreviewCount(sysUser.getUserId());
            System.out.println("Current preview count: " + previewCount);

            // 获取预览次数限制配置
            SysConfig previewLimitConfig = sysConfigService.getByCode("preview_limit");
            Integer allowedPreviewCount = 10; // 默认值
            if (previewLimitConfig != null) {
                try {
                    allowedPreviewCount = Integer.parseInt(previewLimitConfig.getConfigValue());
                } catch (NumberFormatException e) {
                    System.err.println("Invalid preview limit value: " + previewLimitConfig.getConfigValue());
                }
            } else {
                System.out.println("Preview limit config not found, using default: " + allowedPreviewCount);
                // 创建默认配置
                previewLimitConfig = new SysConfig();
                previewLimitConfig.setConfigName("预览限制");
                previewLimitConfig.setConfigCode("preview_limit");
                previewLimitConfig.setConfigType("system");
                previewLimitConfig.setConfigValue(String.valueOf(allowedPreviewCount));
                sysConfigService.save(previewLimitConfig);
            }
            System.out.println("Allowed preview count: " + allowedPreviewCount);

            // 如果超过限制，返回错误信息
            if (previewCount >= allowedPreviewCount) {
                System.out.println("Preview limit exceeded");
                response.setContentType("text/html;charset=UTF-8");
                OutputStream os = response.getOutputStream();
                os.write(("你今天的预览次数已超过" + allowedPreviewCount + "次，无法继续预览").getBytes("UTF-8"));
                return;
            }

            var sType = "china";
            if (StringUtils.isNotBlank(id)) {
                var stdInfo = stdInfoService.getById(id);
                if (stdInfo != null) {
                    System.out.println("Found standard info: " + stdInfo.getStdNo());
                    var filePath = getCorrespondingPath(getPdfPath(sType), stdInfo.getId());
                    File file = new File(filePath + "\\" + stdInfo.getId());
                    if (file.exists()) {
                        System.out.println("File exists: " + file.getAbsolutePath() + ", size: " + file.length());
                        // 记录预览信息
                        try {
                            StdDownload stdDownload = new StdDownload();
                            stdDownload.setId(SequenceUtil.makeStringId());
                            stdDownload.setStdNo(stdInfo.getStdNo());
                            stdDownload.setStdOrgName(stdInfo.getStdOrgName());
                            stdDownload.setStdChineseName(stdInfo.getStdChineseName());
                            stdDownload.setStdInfoId(stdInfo.getId());
                            stdDownload.setPdfFileName(stdInfo.getPdfFileName());
                            stdDownload.setUserId(sysUser.getUserId());
                            stdDownload.setStdType(stdInfo.getStdType());
                            stdDownload.setOpType(StdUserOperateType.Preview);
                            boolean saveResult = stdDownloadService.save(stdDownload);
                            System.out.println("Preview record saved: " + saveResult);
                        } catch (Exception e) {
                            System.err.println("Error saving preview record: " + e.getMessage());
                            e.printStackTrace();
                        }

                        // 大于100M的文件单独处理
                        if (file.length() > 104857600) {
                            System.out.println("Large file, using special handling");
                            // 解密文件
                            var decryptFileName = filePath + "\\" + stdInfo.getId() + "1";
                            var decryptFile = new File(decryptFileName);
                            if (!decryptFile.exists()) {
                                System.out.println("Decrypting file...");
                                Tool.decryptByExe(filePath + "\\" + stdInfo.getId(), decryptFileName, "20221103");
                                Thread.sleep(Tool.estimatedEncryptTime(file.length()));
                            }
                            downloadBinaryBigFile(stdInfo.getPdfFileName(), decryptFileName, response);
                        } else {
                            System.out.println("Normal file, decrypting and serving");
                            var data = getDecyptData(filePath, stdInfo.getId());
                            onlineBinaryFile(stdInfo.getPdfFileName(), data, response);
                        }
                    } else {
                        System.err.println("File does not exist: " + file.getAbsolutePath());
                        response.setContentType("text/html;charset=UTF-8");
                        OutputStream os = response.getOutputStream();
                        os.write("文件不存在".getBytes("UTF-8"));
                    }
                } else {
                    System.err.println("Standard info not found for ID: " + id);
                    response.setContentType("text/html;charset=UTF-8");
                    OutputStream os = response.getOutputStream();
                    os.write("标准信息不存在".getBytes("UTF-8"));
                }
            } else {
                System.err.println("Invalid ID: " + id);
                response.setContentType("text/html;charset=UTF-8");
                OutputStream os = response.getOutputStream();
                os.write("无效的标准ID".getBytes("UTF-8"));
            }
        } catch (Exception e) {
            System.err.println("Error in previewStandardPdf: " + e.getMessage());
            e.printStackTrace();
            response.setContentType("text/html;charset=UTF-8");
            OutputStream os = response.getOutputStream();
            os.write(("预览时发生错误: " + e.getMessage()).getBytes("UTF-8"));
        }
    }

    @GetMapping("/getStandardPdf/{opType}/{id}")
    public void getStandardPdf(@PathVariable("opType") String opType, @PathVariable("id") String id, HttpServletResponse response, HttpServletRequest req) throws Exception {
        String referer = req.getHeader("referer");
        if (null == referer || referer.isEmpty()) {
            return;
        }
        var sType = "china";
        if (StringUtils.isNotBlank(id)) {
            var stdInfo = stdInfoService.getById(id);
            if (stdInfo != null) {
                var filePath = getCorrespondingPath(getPdfPath(sType), stdInfo.getId());
                File file = new File(filePath + "\\" + stdInfo.getId());
                if (file.exists()) {
                    //记录预览或下载信息
                    SysUser sysUser = UserContext.currentUser();
                    Integer counts = 0;
                    if (opType.equals("download")) {
                        //下载逻辑
                        boolean canDownload = false;
                        String errorMessage = "";
                        String configIdToUpdate = null;

                        try {
                            // 检查该文件是否已经下载过
                            var fileHasDownload = stdDownloadService.getOne(
                                new LambdaQueryWrapper<StdDownload>()
                                    .eq(StdDownload::getOpType, StdUserOperateType.Download)
                                    .eq(StdDownload::getStdInfoId, stdInfo.getId())
                                    .eq(StdDownload::getUserId, sysUser.getUserId())
                                    .last("limit 1")
                            );

                            // 如果已经下载过，直接允许
                            if (fileHasDownload != null && StringUtils.isNotBlank(fileHasDownload.getId())) {
                                canDownload = true;
                                // 更新下载时间
                                stdDownloadService.update(null,
                                    new LambdaUpdateWrapper<StdDownload>()
                                        .set(StdDownload::getUpdateTime, LocalDateTime.now())
                                        .eq(StdDownload::getId, fileHasDownload.getId())
                                );
                            } else {
                                // 检查用户是否有专门的下载配置
                                if (stdDownloadConfigService.hasUserDownloadConfig(sysUser.getUserId())) {
                                    // 使用方案2：针对单独用户的配置
                                    List<StdDownloadConfig> validConfigs = stdDownloadConfigService.getValidUserDownloadConfigs(sysUser.getUserId());

                                    if (validConfigs.isEmpty()) {
                                        // 有配置记录但没有有效配置，不允许下载
                                        canDownload = false;
                                        errorMessage = "您的下载配置已过期或被禁用";
                                    } else {
                                        // 检查是否有可用的配置（下载次数未超限）
                                        for (StdDownloadConfig config : validConfigs) {
                                            int configValue = Integer.parseInt(config.getConfigValue());
                                            int downloadCount = config.getDownloadCount() != null ? config.getDownloadCount() : 0;

                                            if (downloadCount < configValue) {
                                                canDownload = true;
                                                configIdToUpdate = config.getId();
                                                break;
                                            }
                                        }

                                        if (!canDownload) {
                                            errorMessage = "您的下载次数已达到配置上限";
                                        }
                                    }
                                } else {
                                    // 使用方案1：全局配置
                                    counts = stdDownloadService.getDownloadCount(sysUser.getUserId());
                                    SysConfig downloadLimitConfig = sysConfigService.getByCode("download_limit");

                                    if (downloadLimitConfig == null) {
                                        canDownload = false;
                                        errorMessage = "系统下载配置不存在";
                                    } else {
                                        Integer allowedDownloadCount = Integer.parseInt(downloadLimitConfig.getConfigValue());
                                        canDownload = counts < allowedDownloadCount;

                                        if (!canDownload) {
                                            errorMessage = "您当日下载量已达上限（" + allowedDownloadCount + "次）";
                                        }
                                    }
                                }

                                // 如果允许下载，记录下载信息
                                if (canDownload) {
                                    var downModel = new StdDownload();
                                    downModel.setStdInfoId(stdInfo.getId());
                                    downModel.setStdNo(stdInfo.getStdNo());
                                    downModel.setStdOrgName(stdInfo.getStdOrgName());
                                    downModel.setStdChineseName(stdInfo.getStdChineseName());
                                    downModel.setPdfFileName(stdInfo.getPdfFileName());
                                    downModel.setUpdateTime(LocalDateTime.now());
                                    downModel.setUserId(sysUser.getUserId());
                                    downModel.setOpType(StdUserOperateType.Download);
                                    downModel.setStdType(stdInfo.getStdType());
                                    stdDownloadService.save(downModel);

                                    // 如果使用方案2，更新配置的下载次数
                                    if (configIdToUpdate != null) {
                                        stdDownloadConfigService.incrementDownloadCount(configIdToUpdate, 1);
                                        System.out.println("Updated download count for config ID: " + configIdToUpdate);
                                    }
                                }
                            }
                        } catch (Exception e) {
                            System.err.println("Error in download logic: " + e.getMessage());
                            e.printStackTrace();
                            canDownload = false;
                            errorMessage = "检查下载权限时发生错误";
                        }

                        // 如果不允许下载，返回错误信息
                        if (!canDownload) {
                            OutputStream os = response.getOutputStream();
                            os.write(errorMessage.getBytes("UTF-8"));
                            return;
                        }
                    } else if (opType.equals("preview")) {
                        //预览
                        if (sysUser != null) {
                            counts = stdDownloadService.getPreviewCount(sysUser.getUserId());
                        }
                        SysConfig previewLimitConfig = sysConfigService.getByCode("preview_limit");
                        Integer allowedPreviewCount = Integer.parseInt(previewLimitConfig.getConfigValue());
                        if (counts < allowedPreviewCount) {
                            var downModel = new StdDownload();
                            downModel.setStdInfoId(stdInfo.getId());
                            downModel.setStdNo(stdInfo.getStdNo());
                            downModel.setStdOrgName(stdInfo.getStdOrgName());
                            downModel.setStdChineseName(stdInfo.getStdChineseName());
                            downModel.setPdfFileName(stdInfo.getPdfFileName());
                            downModel.setUpdateTime(LocalDateTime.now());
                            downModel.setUserId(UserContext.currentUser().getUserId());
                            downModel.setOpType(StdUserOperateType.Preview);
                            downModel.setStdType(stdInfo.getStdType());
                            stdDownloadService.save(downModel);
                        }
                        else {
                            OutputStream os = response.getOutputStream();
                            os.write("您当日预览次数超过平台限制。".getBytes());
                        }
                    }
                    //大于100M的文件单独处理
                    if (file.length() > 104857600) {
                        //解密文件
                        var decryptFileName = filePath + "\\" + stdInfo.getId() + "1";
                        var decryptFile = new File(decryptFileName);
                        if (!decryptFile.exists()) {
                            Tool.decryptByExe(filePath + "\\" + stdInfo.getId(), decryptFileName, "20221103");
                            Thread.sleep(Tool.estimatedEncryptTime(file.length()));
                        }
                        downloadBinaryBigFile(stdInfo.getPdfFileName(), decryptFileName, response);
                    } else {
                        var data = getDecyptData(filePath, stdInfo.getId());
                        downloadBinaryFile(stdInfo.getPdfFileName(), data, response);
                    }
                } else {
                    OutputStream os = response.getOutputStream();
                    os.write("文件不存在，请与管理员联系".getBytes());
                }
            } else {
                OutputStream os = response.getOutputStream();
                os.write("参数错误".getBytes());
            }
        } else {
            OutputStream os = response.getOutputStream();
            os.write("参数错误".getBytes());
        }
    }


    @GetMapping("/pdfReader/{id}")
    public Result pdfNameReader(@PathVariable("id") String id) {
        var stdInfo = stdInfoService.getById(id);
        return Result.success("", stdInfo == null ? "" : stdInfo.getPdfFileName());
    }

    /**
     * 二进制数据输出为pdf（在线查看pdf用）
     *
     * @param fileName
     * @param data
     * @param response
     * @return java.lang.String
     * @throws
     */
    public String onlineBinaryFile(String fileName, byte[] data, HttpServletResponse response) throws IOException {
        if (data.length > 0) {
            response.setContentType("application/pdf");
            String filedisplay = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            response.setHeader("Content-Length", Long.toString(data.length));
            OutputStream os = response.getOutputStream();
            try {
                os.write(data, 0, data.length);
                System.out.println("success");
                InetAddress addr = InetAddress.getLocalHost();
                String ip = addr.getHostAddress(); //获取本机ip
                replacTextContent(ip);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    public String downloadFile(String fileName, String filePath, HttpServletResponse response) throws IOException {
        if (fileName != null) {
            //设置文件路径
            //ClassPathResource classPathResource = new ClassPathResource("statics/pdf/"+fileName);
            //String resource = classPathResource.getURL().getPath();
            File file = new File(filePath + "/" + fileName);
            if (file.exists()) {
                response.setContentType("application/force-download");// 设置强制下载不打开
                response.addHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "utf-8"));// 设置文件名
                byte[] buffer = new byte[1024];
                FileInputStream fis = null;
                BufferedInputStream bis = null;
                try {
                    fis = new FileInputStream(file);
                    bis = new BufferedInputStream(fis);
                    OutputStream os = response.getOutputStream();
                    int i = bis.read(buffer);
                    while (i != -1) {
                        os.write(buffer, 0, i);
                        i = bis.read(buffer);
                    }
                    System.out.println("success");
                    InetAddress addr = InetAddress.getLocalHost();
                    String ip = addr.getHostAddress(); //获取本机ip
                    replacTextContent(ip);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    if (bis != null) {
                        try {
                            bis.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    if (fis != null) {
                        try {
                            fis.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        }
        return null;
    }

    public String downloadFile(String fileName, HttpServletResponse response) throws IOException {
        if (fileName != null) {
            //设置文件路径
            //ClassPathResource classPathResource = new ClassPathResource("statics/pdf/"+fileName);
            //String resource = classPathResource.getURL().getPath();
            File file = new File(fileName);
            if (file.exists()) {
                response.setContentType("application/force-download");// 设置强制下载不打开
                response.addHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "utf-8"));// 设置文件名
                byte[] buffer = new byte[1024];
                FileInputStream fis = null;
                BufferedInputStream bis = null;
                try {
                    fis = new FileInputStream(file);
                    bis = new BufferedInputStream(fis);
                    OutputStream os = response.getOutputStream();
                    int i = bis.read(buffer);
                    while (i != -1) {
                        os.write(buffer, 0, i);
                        i = bis.read(buffer);
                    }
                    System.out.println("success");
                    InetAddress addr = InetAddress.getLocalHost();
                    String ip = addr.getHostAddress(); //获取本机ip
                    replacTextContent(ip);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    if (bis != null) {
                        try {
                            bis.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    if (fis != null) {
                        try {
                            fis.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        }
        return null;
    }

    @PostMapping("pdf")
    public void outputPDF(@RequestBody Map<String, String> params, HttpServletResponse response) throws IOException {
        var isGetFile = params.get("uudoc_getfile");
        responsePdf("", "", response);
    }

    public boolean responsePdf(String fileName, String filePath, HttpServletResponse response) throws IOException {
        File file = new File(filePath + "/" + fileName);
        response.setContentType("application/pdf");
        String filedisplay = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + filedisplay);
        response.setHeader("Content-Length", Long.toString(file.length()));

        //可以使用http头的方式进行服务器设置
        //设置之后下列属性将不能再在客户端进行修改:License,HashKey,EnableCopy,EnablePrint,EnableCapture,ToolbarVisible,RemoveWaterMark
        //response.setHeader("UdPdf-Config", "EnableCopy=false&EnablePrint=false");

        //也可以使用加密方式进行配置:EnableCopy=false
        //response.setHeader("UdPdf-EncryptConfig", "74321C08CA9D4B127A06C1B07A78A8B1344B4A88E7F2517D90A04FFEB46F7A9FF99E3DD7B52282441B6A3FCC6E9BA168");


        OutputStream os = response.getOutputStream();
        FileInputStream fis = new FileInputStream(filePath);
        int n = 0;
        while ((n = fis.read()) != -1) {
            os.write(n);
        }
        fis.close();

          /*  out.clear();
            out = pageContext.pushBody();*/
        return true;
    }


    public String downloadFileFromStream(String fileName, InputStream stream, HttpServletResponse response) throws IOException {
        //设置文件路径
        //ClassPathResource classPathResource = new ClassPathResource("statics/pdf/"+fileName);
        //String resource = classPathResource.getURL().getPath();
        var len = stream.available();
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.addHeader("Content-Length", String.valueOf(len));
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/force-download");// 设置强制下载不打开
        response.addHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "utf-8"));// 设置文件名
        byte[] buffer = new byte[1024];
        BufferedInputStream bis = null;
        try {
            bis = new BufferedInputStream(stream);

            OutputStream os = response.getOutputStream();
            int i = bis.read(buffer);
            while (i != -1) {
                os.write(buffer, 0, i);
                i = bis.read(buffer);
            }
            System.out.println("success");
            InetAddress addr = InetAddress.getLocalHost();
            String ip = addr.getHostAddress(); //获取本机ip
            replacTextContent(ip);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }


    /**
     * 替换文本文件中的 指定字符串
     *
     * @param
     * @throws IOException
     */

    public void replacTextContent(String ip) {

        try {

            File file = new File("D:zabbix_agentd.conf");

            String content = FileUtils.readFileToString(file, "utf-8");

            //原有的内容

            String srcStr = "Hostname=*************";

            //要替换的内容

            String replaceStr = "Hostname" + ip;

            // 读

            FileReader in = new FileReader(content);

            BufferedReader bufIn = new BufferedReader(in);

            // 内存流, 作为临时流

            CharArrayWriter tempStream = new CharArrayWriter();

            // 替换

            String line = null;

            while ((line = bufIn.readLine()) != null) {

                // 替换每行中, 符合条件的字符串

                line = line.replaceAll(srcStr, replaceStr);

                // 将该行写入内存

                tempStream.write(line);

                // 添加换行符

                tempStream.append(System.getProperty("line.separator"));

            }

            // 关闭 输入流

            bufIn.close();

            // 将内存中的流 写入 文件

            FileWriter out = new FileWriter(file);

            tempStream.writeTo(out);

            out.close();

        } catch (Exception e) {

            e.printStackTrace();

        }

    }

    /**
     * 从pdf加密后的数据中解密出真实的二进制数据
     *
     * @param id
     * @return byte[]
     * @throws
     */
    public byte[] getDecyptData(String filePath, String id) throws IOException {
        String path = getCorrespondingPath(filePath, id);
        var convertData = new byte[0];
        File file = new File(path + "\\" + id);
        if (file.exists()) {
            var byteData = Tool.getBinaryData(file);
            convertData = DESUtil.getDecryptBytes(byteData);
        }
        return convertData;
    }
}
