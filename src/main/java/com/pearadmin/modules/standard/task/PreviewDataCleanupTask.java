package com.pearadmin.modules.standard.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.pearadmin.common.tools.StandardTypeEnum;
import com.pearadmin.common.web.domain.response.Result;
import com.pearadmin.modules.standard.domain.StdClassStatistics;
import com.pearadmin.modules.standard.domain.StdDownload;
import com.pearadmin.modules.standard.domain.StdInfo;
import com.pearadmin.modules.standard.mapper.StdClassStatisticsMapper;
import com.pearadmin.modules.standard.mapper.StdDownloadMapper;
import com.pearadmin.modules.standard.mapper.StdInfoMapper;
import com.pearadmin.modules.standard.service.StdClassStatisticsService;
import com.pearadmin.modules.standard.service.StdInfoService;
import lombok.var;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 预览数据清理定时任务
 * 每天执行一次，清除七天之外的标准浏览数据
 *同时每天凌晨2点执行分类下的标准数量统计
 * <AUTHOR> Code
 * @date 2023/5/28
 */
@Component
public class PreviewDataCleanupTask {

    private static final Logger logger = LoggerFactory.getLogger(PreviewDataCleanupTask.class);

    @Resource
    private StdDownloadMapper stdDownloadMapper;
    @Resource
    private StdClassStatisticsService stdClassStatisticsService;
    @Resource
    private StdInfoMapper stdInfoMapper;

    /**
     * 保留预览记录的天数，默认为7天
     */
    @Value("${stdsys.preview-retention-days:7}")
    private Integer previewRetentionDays;

    /**
     * 每天凌晨2点执行清理任务
     * cron表达式：秒 分 时 日 月 周
     */
    @Scheduled(cron = "20 * * * * ?")
    public void cleanupPreviewData() {
        statisticsStdInfoByClass();
        doCleanup();
    }

    /**
     * 手动触发清理任务，用于测试
     */
    public void manualCleanup() {
        doCleanup();
    }

    /**
     * 执行清理操作
     */
    private void doCleanup() {
        logger.info("开始执行预览数据清理任务...");

        try {
            // 计算需要保留的最早日期
            LocalDateTime cutoffDate = LocalDate.now().minusDays(previewRetentionDays).atStartOfDay();

            // 构建查询条件：op_type=2（预览记录）且创建时间早于截止日期
            LambdaQueryWrapper<StdDownload> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StdDownload::getOpType, 2)
                      .lt(StdDownload::getCreateTime, cutoffDate);

            // 查询符合条件的记录数量
            long count = stdDownloadMapper.selectCount(queryWrapper);

            if (count > 0) {
                // 删除符合条件的记录
                int deletedCount = stdDownloadMapper.delete(queryWrapper);

                logger.info("预览数据清理完成，共删除 {} 条{}天前的预览记录",
                        deletedCount, previewRetentionDays);
            } else {
                logger.info("没有需要清理的预览记录");
            }

        } catch (Exception e) {
            logger.error("预览数据清理任务执行失败", e);
        }

        logger.info("预览数据清理任务执行结束");
    }

    /**
     * 按照分类统计标准数量
     */
    public void statisticsStdInfoByClass()
    {
        // 创建LambdaQueryWrapper实例
        QueryWrapper<StdInfo> queryWrapper = new QueryWrapper<StdInfo>();
        // 设置查询字段，包括分组字段和聚合函数（这里使用COUNT统计每个部门的用户数量）
        queryWrapper.select("std_class","std_type", "COUNT(1) as counts").lambda().groupBy(StdInfo::getStdClass,StdInfo::getStdType);
        // 执行查询，结果存储在List<Map<String, Object>>中
        List<Map<String, Object>> list = stdInfoMapper.selectMaps(queryWrapper);
        // 遍历结果集
        for (Map<String, Object> map : list) {
            // 从Map中获取部门名称和用户数量
            String stdClass = (String) map.get("std_class");
            String stdType = (String) map.get("std_type");
            Long counts = (Long) map.get("counts");
            stdClassStatisticsService.updateByClassName(stdClass, stdType,counts.intValue());
        }
    }
}
