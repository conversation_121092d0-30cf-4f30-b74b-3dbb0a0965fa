package com.pearadmin.modules.job.schedule;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.pearadmin.common.quartz.base.BaseQuartz;
import com.pearadmin.common.tools.string.StringUtil;
import com.pearadmin.modules.sys.domain.SysConfig;
import com.pearadmin.modules.sys.mapper.SysConfigMapper;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.lang.StringUtils;
import org.jasypt.encryption.StringEncryptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

@Slf4j
@Component("commonTask")
public class CommonTask implements BaseQuartz {
    @Resource
    private SysConfigMapper sysConfigMapper;
    private static final SimpleDateFormat FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    @Resource
    private StringEncryptor stringEncryptor;
    @Value("${stdsys.limithours}")
    private Integer hours;
    //是否检查安全性
    @Value("${stdsys.check-security}")
    private Integer checkSecurity;
    /**
     * 任务实现
     */
    @Override
    public void run(String params) {
       // log.info("Params === >> " + params);
        //log.info("当前时间::::" + FORMAT.format(new Date()));
        if(checkSecurity==1) {
            var config = sysConfigMapper.selectByCode("main_sys_path");
            if (config != null) {
                try {
                    var configData = config.getConfigValue();
                    if (StringUtils.isNotBlank(configData)) {
                        var data = stringEncryptor.decrypt(configData);
                        var totalHours = Integer.parseInt(data);
                        totalHours += 1;
                        config.setConfigValue(stringEncryptor.encrypt(Integer.toString(totalHours)));
                        sysConfigMapper.updateById(config);
                        // log.info("totalHours === >> " + totalHours);
                        //  log.info("hours === >> " + hours);
                    } else {
                        config.setConfigValue(stringEncryptor.encrypt(Integer.toString(1)));
                        sysConfigMapper.updateById(config);
                    }
                } catch (Exception ex) {
                    config.setConfigValue(stringEncryptor.encrypt(Integer.toString(hours)));
                    sysConfigMapper.updateById(config);
                }
            } else {
                var cfg = new SysConfig();
                cfg.setConfigValue(stringEncryptor.encrypt(Integer.toString(hours)));
                cfg.setConfigCode("main_sys_path");
                cfg.setConfigName("系统配置");
                cfg.setConfigType("system");
                sysConfigMapper.insert(cfg);
            }
            //System.out.println("执行成功");
        }
    }
}
