package com.pearadmin.common.tools;
import com.google.common.primitives.Bytes;
import lombok.var;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.security.SecureRandom;
/**
 * pdf 和 word的加密解密
 *
 * @param
 * @return
 * @throws
 */
public class DESUtil {
    private static Key key;
    private static String KEY_STR="20221103";
    private static String CHARSETNAME="UTF-8";
    private static String ALGORITHM="DES";

    static {
        try {
            //生成DES算法对象
            KeyGenerator generator=KeyGenerator.getInstance(ALGORITHM);
            //运用SHA1安全策略
            SecureRandom secureRandom=SecureRandom.getInstance("SHA1PRNG");
            //设置上密钥种子
            secureRandom.setSeed(KEY_STR.getBytes());
            //初始化基于SHA1的算法对象
            generator.init(secureRandom);
            //生成密钥对象
            key=generator.generateKey();
            generator=null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    /***
     * 数据加密
     * @param bytes
     * @return
     */
    public static byte[] getEncryptBytes(byte[] bytes) {
        try {
            //填充为8的整数
            var rest=bytes.length % 8;
            var data=new byte[0];
            if(rest !=0) {
                var byteZero=new byte[8-rest];
                for(var i=0;i<8-rest;i++) {
                    byteZero[i]=0;
                }
                data=Bytes.concat(bytes,byteZero);
            }
            else
            {
                data=Bytes.concat(bytes);
            }
            //获取加密对象
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            //初始化密码信息
            cipher.init(Cipher.ENCRYPT_MODE, key);
            //加密
            byte[] doFinal = cipher.doFinal(data);
            byte[] zeros=new byte[1];
            zeros[0]=(byte)(8-rest);
            var result= Bytes.concat(zeros,doFinal);
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /***
     * 获取解密之后的信息
     * @param bytes
     * @return
     */
    public static byte[] getDecryptBytes(byte[] bytes) {
        try {
            //获取解密对象
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            //初始化解密信息
            cipher.init(Cipher.DECRYPT_MODE, key);
            var data=new byte[bytes.length-1];
            for(var i=1;i<bytes.length;i++) {
                data[i-1]=bytes[i];
            }
            //加密前尾部填充的零的个数
            var zeroLen=(int)bytes[0];
            //解密
            byte[] doFial = cipher.doFinal(data);
            var result=new byte[doFial.length-zeroLen];
            var dataLen=result.length;
            for(var i=0;i<dataLen;i++) {
                result[i]=doFial[i];
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
