package com.pearadmin.common.tools;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.primitives.Bytes;
import com.pearadmin.modules.standard.domain.StdClassification;
import com.pearadmin.modules.standard.service.StdClassificationService;
import lombok.var;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.mozilla.universalchardet.UniversalDetector;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.system.ApplicationHome;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;

@Component
public class Tool
{
    public static  String encryptExe;
    @Value("${std.encryptexe}")
    public void setAddress(String exe){
        encryptExe=exe;
    }

    /**
     * @param chinaStr 中文字符串
     * @return 中文字符串转拼音 其它字符不变
     */
    public static String getPinyin(String chinaStr){
        HanyuPinyinOutputFormat formart = new HanyuPinyinOutputFormat();
        formart.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        formart.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        formart.setVCharType(HanyuPinyinVCharType.WITH_V);
        char[] arrays = chinaStr.trim().toCharArray();
        String result = "";
        try {
            for (int i=0;i<arrays.length;i++) {
                char ti = arrays[i];
                if(Character.toString(ti).matches("[\\u4e00-\\u9fa5]")){ //匹配是否是中文
                    String[] temp = PinyinHelper.toHanyuPinyinStringArray(ti,formart);
                    result += temp[0];
                }else{
                    result += ti;
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            e.printStackTrace();
        }

        //判断如果包含重庆，则替换拼音中的zhongqing为chongqing
        if (chinaStr.indexOf("重庆") == -1) {
            //do nothing
        }  else {
            result = result.replace("zhongqing","chongqing");
        }
        return result;
    }
    /**
     * 文件加密
     *
     * @param infile
     * @param outfile
     * @param pwd
     * @return void
     * @throws
     */
    public static void encryptByExe(String infile,String outfile,String pwd)
    {
        try {
            String[] cmd = {encryptExe, "encrypt", pwd, infile, outfile};
            Runtime.getRuntime().exec(cmd);
            //方式二：
           /* Process p = new ProcessBuilder(encryptExe).start();
            br = new BufferedReader(new InputStreamReader(p.getInputStream()));
            brError = new BufferedReader(new InputStreamReader(p.getErrorStream()));
            while ((line = br.readLine()) != null || (line = brError.readLine()) != null) {
                //输出exe输出的信息以及错误信息
                System.out.println(line);
            }*/
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 预测pdf大文件加密或解密及保存称文件的时间
     * 因为是调用第三方软件来进行加密解密，所以无法预知时间
     *
     * @param fileSize
     * @return double
     * @throws
     */
    public static long estimatedEncryptTime(long fileSize)
    {
        //每秒加密或解密保存的字节数
        var speed=14898831;
        return (long)Math.ceil(fileSize*1.0/speed)*1000;
    }

    /**
     * 文件解密
     *
     * @param infile
     * @param outfile
     * @param pwd
     * @return void
     * @throws
     */
    public static void decryptByExe(String infile,String outfile,String pwd)
    {
        try {
            String[] cmd = {encryptExe, "decrypt", pwd, infile, outfile};
            Runtime.getRuntime().exec(cmd);
            //方式二：
           /* Process p = new ProcessBuilder(encryptExe).start();
            br = new BufferedReader(new InputStreamReader(p.getInputStream()));
            brError = new BufferedReader(new InputStreamReader(p.getErrorStream()));
            while ((line = br.readLine()) != null || (line = brError.readLine()) != null) {
                //输出exe输出的信息以及错误信息
                System.out.println(line);
            }*/
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /***
     * 获取文件编码
     *
     * @param file
     * @return java.lang.String
     * @throws
     */
    public static String detectedEncoding(File file) throws IOException {
        byte[] buf = new byte[4096];

        java.io.FileInputStream fis = new java.io.FileInputStream(file);

// (1)
        UniversalDetector detector = new UniversalDetector(null);

// (2)
        int nread;
        while ((nread = fis.read(buf)) > 0 && !detector.isDone()) {
            detector.handleData(buf, 0, nread);
        }
// (3)
        detector.dataEnd();

// (4)
        String encoding = detector.getDetectedCharset();
        if (encoding != null) {
            System.out.println("Detected encoding = " + encoding);
        } else {
            System.out.println("No encoding detected.");
        }

// (5)
        detector.reset();
        return encoding;
    }


    public static String getCellStringValue(XSSFCell cell) {
        try {
            if (null!=cell) {
                return String.valueOf(cell.getStringCellValue());
            }
        } catch (Exception e) {
            return String.valueOf(getCellIntValue(cell));
        }
        return "";
    }

    public static long getCellLongValue(XSSFCell cell) {
        try {
            if (null!=cell) {
                return Long.parseLong("" + (long) cell.getNumericCellValue());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0L;
    }

    public static int getCellIntValue(XSSFCell cell) {
        try {
            if (null!=cell) {
                return Integer.parseInt("" + (int) cell.getNumericCellValue());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }

    /**
     * 从给定文件中读取二进制数据
     *
     * @param file
     * @return byte[]
     * @throws
     */
//    public static byte[] getBinaryData(File file) {
//        byte[] result=new byte[0];
//        //设置文件路径
//        //ClassPathResource classPathResource = new ClassPathResource("statics/pdf/"+fileName);
//        //String resource = classPathResource.getURL().getPath();
//        if (file.exists()) {
//            byte[] buffer = new byte[1024];
//            FileInputStream fis = null;
//            BufferedInputStream bis = null;
//            try {
//                fis = new FileInputStream(file);
//                bis = new BufferedInputStream(fis);
//                ArrayList<Byte> byteList = new ArrayList<Byte>();
//                int i = bis.read(buffer);
//                while (i != -1) {
//                    for (var idx = 0; idx < i; idx++) {
//                        byteList.add(buffer[idx]);
//                    }
//                    i = bis.read(buffer);
//                }
//                result = Bytes.toArray(byteList);
//                System.out.println("success");
//            } catch (Exception e) {
//                e.printStackTrace();
//            } finally {
//                if (bis != null) {
//                    try {
//                        bis.close();
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
//                }
//                if (fis != null) {
//                    try {
//                        fis.close();
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
//                }
//            }
//        }
//        return result;
//    }

    public static byte[] getBinaryData(File file) throws IOException {
        if (!file.exists()) {
            throw new FileNotFoundException("File not found: " + file.getPath());
        }

        try (FileInputStream fis = new FileInputStream(file);
             BufferedInputStream bis = new BufferedInputStream(fis);
             ByteArrayOutputStream bos = new ByteArrayOutputStream()) {

            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = bis.read(buffer)) != -1) {
                bos.write(buffer, 0, bytesRead);
            }
            return bos.toByteArray();
        } catch (IOException e) {
            System.err.println("Error reading file: " + e.getMessage());
            throw e; // 抛出异常让调用方处理
        }
    }


    /**
     * 数据加密后将pdf保存为二进制数据
     *
     * @param data
     * @return void
     * @throws
     */
    public String encryptPDF(byte[] data) throws IOException {
        var result="";
        var org=new String(data,0,data.length,"UTF-8");

        return result;
    }

    @Resource
    private  StdClassificationService   stdClassificationService;

    public   void fillClassCode(StdClassification model)
    {
        //无父节点,说明是体系下边的一节子节点
        if(model.getParentFullCode().equals("-1"))
        {
            //找兄弟节点
            var maxBrother = stdClassificationService.getOne(new LambdaQueryWrapper<StdClassification>().eq(StdClassification::getSystemId,model.getSystemId()).eq(StdClassification::getParentFullCode, -1).eq(StdClassification::getStatus, "1").orderByDesc(StdClassification::getClassCode).last("limit 1"));
            var brotherInt=(int)'A';
            char newCode = 'A';
            if(maxBrother!=null && StringUtils.isNotBlank(maxBrother.getId())) {
                brotherInt = (int) (maxBrother.getClassCode().charAt(0));
                newCode = (char) (brotherInt + 1);
            }
            model.setClassCode(String.valueOf(newCode));
            model.setClassCodePath(String.valueOf(newCode));
        }
        else
        {
            var query = new LambdaQueryWrapper<StdClassification>();
            query.eq(StdClassification::getSystemId,model.getSystemId());
            query.eq(StdClassification::getClassCodePath, model.getParentFullCode()).last("limit 1");
            var parent = stdClassificationService.getOne(query);
            var maxBrother = stdClassificationService.getOne(new LambdaQueryWrapper<StdClassification>().eq(StdClassification::getSystemId,model.getSystemId()).eq(StdClassification::getParentFullCode, model.getParentFullCode()).eq(StdClassification::getStatus, "1").orderByDesc(StdClassification::getClassCode).last("limit 1"));
            var brotherInt=(int)'A';
            char newCode = 'A';
            if(maxBrother!=null && StringUtils.isNotBlank(maxBrother.getId())) {
                brotherInt = (int) (maxBrother.getClassCode().charAt(0));
                newCode = (char) (brotherInt + 1);
            }
            model.setClassCode(String.valueOf(newCode));
            model.setClassCodePath(parent.getClassCodePath() + String.valueOf(newCode));
            model.setParentId(parent.getId());
        }
    }

    /**
     * 获取jar包所在文件路径
     *
     * @param
     * @return java.lang.String
     * @throws
     */
    public String getJarFilePath() {
        ApplicationHome home = new ApplicationHome(getClass());
        File jarFile = home.getSource();
        return jarFile.getParentFile().toString();
    }

    public String savelErrToExcel(String err) throws IOException {
        // create a new Workbook
        var sdf=new SimpleDateFormat("yyyy_MM_dd_HH_mm_ss");
        var fileName="system_upload_err_"+sdf.format(new Date())+".xlsx";
        String fileFullPath=getJarFilePath()+"\\"+fileName;
        Workbook workbook = new XSSFWorkbook();
        // create a new sheet
        XSSFSheet sheet = (XSSFSheet) workbook.createSheet("Sheet1");
        sheet.setColumnWidth(0, 1000);// 设置第二列的宽度
        sheet.setColumnWidth(1, 10000);// 设置第二列的宽度
        // create some data rows
        Row row = sheet.createRow(0);
        row.createCell(0).setCellValue("序号");
        row.createCell(1).setCellValue("错误描述");
        var errArray=err.split(";;");
        Integer rowIndex=1;
        for(Integer i=0;i<errArray.length;i++)
        {
            if(StringUtils.isNotBlank(errArray[i])) {
                row = sheet.createRow(rowIndex);
                row.createCell(0).setCellValue(rowIndex);
                row.createCell(1).setCellValue(errArray[i]);
                rowIndex++;
            }
        }
        // write the Workbook to a ByteArrayOutputStream
        var cal= Calendar.getInstance();
        var outputStream=new FileOutputStream(fileFullPath);
        // set the headers for downloading the file
        try {
            workbook.write(outputStream);
            return fileName;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

}
