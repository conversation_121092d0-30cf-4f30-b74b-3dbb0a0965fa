package com.pearadmin.common.tools;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;

import java.util.Map;

public class SqlUtils {
    public static String getFullSql(QueryWrapper<?> wrapper, Class<?> entityClass) {
        TableInfo tableInfo = TableInfoHelper.getTableInfo(entityClass);
        String sqlSelect = tableInfo.getTableName();

        // 获取 SQL 片段和参数
        String sqlSegment = wrapper.getSqlSegment();
        Map<String, Object> params = wrapper.getParamNameValuePairs();

        // 构建完整 SQL
        StringBuilder sql = new StringBuilder(sqlSegment);
//                ("SELECT * FROM ")
//                .append(sqlSelect)
//                .append(" WHERE ")
//                .append(sqlSegment);


        // 替换参数
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String paramName = "#{ew.paramNameValuePairs." + entry.getKey() + "}";
            String paramValue = (entry.getValue() instanceof String)
                    ? "'" + entry.getValue() + "'"
                    : entry.getValue().toString();

            sql = new StringBuilder(sql.toString().replace(paramName, paramValue));
        }

        return sql.toString();
    }






//    public static String handleUserInput(String input) {
//        // 移除首尾空格
//        input = input.trim();
//
//        // 替换特殊字符
//        input = input.replace("\\", "\\\\");
//        input = input.replace("'", "\\'");
//        input = input.replace("%", "\\%");
//        input = input.replace("_", "\\_");
//
//        return input;
//    }

    //增加一个方法，消除用户输入的查询条件中会引起Sql注入的情况
    public static String handleUserInput(String input) {
        // 移除首尾空格
        input = input.trim();
        // 替换特殊字符
        input = input.replace("\\", "\\\\");
        input = input.replace("'", "\\'");
        input = input.replace("%", "\\%");
        input = input.replace("_", "\\_");
        return input;
    }


    //需要使用like进行查询的情况（不适合使用全文检索）
    public static boolean needLike(String key) {
        if(key.contains(".") || key.contains("-") || key.contains("/") || key.contains("(")  || key.contains(")"))
        {
            return true;
        }
        return false;
    }

}
