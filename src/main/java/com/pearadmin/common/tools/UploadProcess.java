package com.pearadmin.common.tools;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = UploadProcess.PREFIX)
public class UploadProcess {
    public static final String PREFIX = "upload";//这里注意前缀一律小写，这是规范
    private Integer total;
    private String title;
    private String err;
    private Integer dealed;
    private String msg;
    /**
     * 是否有下一个处理流程
     * （如上传标准分为两个流程：1.上传excel解析excel，将记录变为模型对象存入数据库
     *                      2.将excel对应的明码pdf进行加密
     *  在执行第一个流程的时候，hasNextProcess=1，表示上传完还需要加密文件
     * ）
     * @param null
     * @return
     * @throws
     */
    private Integer hasNextProcess=0;

    public String getMsg() {
        return msg;
    }
    public String getErr() {
        return err;
    }

    public void setErr(String err) {
        this.err = err;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getDealed() {
        return dealed;
    }

    public void setDealed(Integer dealed) {
        this.dealed = dealed;
    }

    public String getTitle() {
        return title;
    }
    public void setTitle(String title) {
        this.title = title;
    }
    public void setMsg(String msg) {
        this.msg = msg;
    }
    /**
     * 是否有下一个需要处理的流程
     *
     * @param
     * @return java.lang.Integer
     * @throws
     */
    public Integer getHasNextProcess() {
        return hasNextProcess;
    }
    public void setHasNextProcess(Integer hasNextProcess) {
        this.hasNextProcess = hasNextProcess;
    }
}
