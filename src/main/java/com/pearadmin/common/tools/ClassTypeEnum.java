package com.pearadmin.common.tools;

/**
 * 分类类型，对应 std_classification 的 class_type字段
 *
 * @param
 * @return
 * @throws
 */
public class ClassTypeEnum {
    //---如果是岗位专题 涵义如下--------------------------------
    //部门（不能绑定标准）
    public static String Department="0";
    //岗位（可以绑定标准）
    public static String Post="1";

    //---如果是标准体系，涵义树下--------------------------------
    //树枝（不能绑定标准）
    public static String Branch="0";
    //叶子节点（可以绑定标准）
    public static String Leaves="1";
}
