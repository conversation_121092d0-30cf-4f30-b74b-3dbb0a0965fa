package com.pearadmin.common.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 日期时间配置类
 * 用于处理前后端时间格式转换
 */
@Configuration
public class DateTimeConfig implements WebMvcConfigurer {

    /**
     * 添加自定义的时间格式转换器
     */
    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(new StringToLocalDateTimeConverter());
    }

    /**
     * 字符串到 LocalDateTime 的转换器
     * 支持多种时间格式
     */
    public static class StringToLocalDateTimeConverter implements Converter<String, LocalDateTime> {
        
        private static final DateTimeFormatter[] FORMATTERS = {
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm")
        };

        @Override
        public LocalDateTime convert(String source) {
            if (source == null || source.trim().isEmpty()) {
                return null;
            }

            source = source.trim();

            // 尝试不同的格式进行解析
            for (DateTimeFormatter formatter : FORMATTERS) {
                try {
                    return LocalDateTime.parse(source, formatter);
                } catch (DateTimeParseException e) {
                    // 继续尝试下一个格式
                }
            }

            // 如果格式是 yyyy-MM-ddTHH:mm，自动补全秒数
            if (source.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}")) {
                source += ":00";
                try {
                    return LocalDateTime.parse(source, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
                } catch (DateTimeParseException e) {
                    // 忽略
                }
            }

            // 如果所有尝试都失败，抛出异常
            throw new IllegalArgumentException("无法解析时间格式: " + source + 
                "，支持的格式: yyyy-MM-dd HH:mm:ss, yyyy-MM-dd HH:mm, yyyy-MM-ddTHH:mm:ss, yyyy-MM-ddTHH:mm");
        }
    }
}
