package com.pearadmin.common.constant;

/**
 * Describe: 接 口 静 态 常 量
 * Author: 就 眠 仪 式
 * CreateTime: 2019/10/23
 */
public class ControllerConstant {

    /**
     * 系统业务基础路径
     */
    public final static String API_SYSTEM_PREFIX = "/system/";

    /**
     * 代码生成基础路径
     */
    public final static String API_GENERATOR_PREFIX = "/generate/";

    /**
     * 定时任务基础路径
     */
    public final static String API_SCHEDULE_PREFIX = "/schedule/";

    /**
     * 工作流程基础路径
     */
    public final static String API_PROCESS_PREFIX = "/process/";
    /**
     * 标准文件基础路径
     */
    public final static String API_STANDARD_PREFIX = "/standard/";
    /**
     * 装备鉴定定型及在役考核文件模板基础路径
     */
    public final static String API_Equip_ASSESS_PREFIX = "/equipassess/";
    /**
     * 标准体系
     */
    public final static String API_STD_SYSTEM_PREFIX = "/stdsystem/";

    /**
     * 标准体系意见建议
     */
    public final static String API_SYSTEM_OPINION_PREFIX = "/sysopinion/";

    /**
     * 对标准的意见建议
     */
    public final static String API_STANDARD_OPINION_PREFIX = "/stdopinion/";

    /**
     * 标准分类
     */
    public final static String API_CLASSIFICATION_PREFIX = "/stdclassification/";

    /**
     * 岗位管理
     */
    public final static String API_POST_PREFIX = "/stdpost/";

    /**
     * 下载管理
     */
    public final static String API_DOWNLOAD_PREFIX = "/stddownload/";

    /**
     * 标准与分类的关联关系
     */
    public final static String API_CLASS_JOIN_STANDARD_PREFIX = "/classjoinstd/";

    /**
     * 岗位专题
     */
    public final static String API_STD_CORP_SUBJECT_PREFIX = "/stdcorpsubject/";

    /**
     * 个人收藏
     */
    public final static String API_PERSONAL_COLLECT_PREFIX = "/personalcoll/";

    /**
     * 部门
     */
    public final static String API_DEPARTMENT_PREFIX = "/stddepartment/";

    /**
     * 日志统计
     */
    public final static String API_STD_LOG_PREFIX = "/stdlog/";

    /**
     * 预览管理
     */
    public final static String API_PREVIEW_PREFIX = "/stdpreview/";

}
