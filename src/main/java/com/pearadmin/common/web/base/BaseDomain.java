package com.pearadmin.common.web.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 基础实体
 * <p>
 * 提供 createBy createTime updateBy updateTime remark公共字段
 *
 * @serial 2.0.0
 * <AUTHOR>
 */
@Data
public class BaseDomain implements Serializable {

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    public LocalDateTime createTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建人名称
     */
    @TableField(exist = false)
    private String createName;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 修改人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改人名称
     */
    @TableField(exist = false)
    private String updateName;

    /**
     * 备注
     */
    public String remark;

    /**
     * 请求参数
     */
    @TableField(exist = false)
    private Map<String, Object> params;
}
