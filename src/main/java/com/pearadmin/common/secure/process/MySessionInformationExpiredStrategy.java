package com.pearadmin.common.secure.process;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.session.SessionInformationExpiredEvent;
import org.springframework.security.web.session.SessionInformationExpiredStrategy;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;import java.io.IOException;
@Component
public class MySessionInformationExpiredStrategy //implements SessionInformationExpiredStrategy
{
    private RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();
    //Jackson JSON数据处理类
    private  static ObjectMapper objectMapper = new ObjectMapper();
    @Autowired
    SecureAuthenticationFailureHandler myAuthenticationFailureHandler;
    //@Override
    public void onExpiredSessionDetected(SessionInformationExpiredEvent event) throws IOException, ServletException
    {
        // 1. 获取用户名
        UserDetails userDetails =                (UserDetails)event.getSessionInformation().getPrincipal();
        AuthenticationException exception =  new AuthenticationServiceException(
                String.format("[%s] 用户在另外一台电脑登录,您已被下线", userDetails.getUsername()));
        try {
            // 当用户在另外一台电脑登录后,交给失败处理器回到认证页面
            event.getRequest().setAttribute("toAuthentication" , true);
            myAuthenticationFailureHandler
                    .onAuthenticationFailure(event.getRequest(), event.getResponse(), exception);
            redirectStrategy.sendRedirect(event.getRequest(), event.getResponse(), "/login?abnormalout=1");
        }
        catch (ServletException e)
        {
            e.printStackTrace();
        }
    }
}
