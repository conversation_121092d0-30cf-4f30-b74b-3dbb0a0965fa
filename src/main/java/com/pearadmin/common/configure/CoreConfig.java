package com.pearadmin.common.configure;

import cn.hutool.extra.mail.MailAccount;
import com.fasterxml.jackson.databind.Module;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.pearadmin.common.constant.ConfigurationConstant;
import com.pearadmin.common.web.interceptor.PreviewInterceptor;
import com.pearadmin.common.web.interceptor.RateLimitInterceptor;
import com.pearadmin.modules.sys.service.SysConfigService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * Core 核心配置
 * <p>
 * @serial 2.0.0
 * <AUTHOR>
 */
@Configuration
public class CoreConfig implements WebMvcConfigurer {

    @Resource
    private SysConfigService sysContext;

    @Resource
    private RateLimitInterceptor rateLimitInterceptor;

    @Resource
    private PreviewInterceptor previewInterceptor;

    @Bean
    public MailAccount mailAccount() {
        MailAccount mailAccount = new MailAccount();
        mailAccount.setHost(sysContext.getConfig(ConfigurationConstant.MAIN_HOST));
        mailAccount.setPort(sysContext.getConfig(ConfigurationConstant.MAIN_PORT) == "" ? 0000 : Integer.parseInt(sysContext.getConfig(ConfigurationConstant.MAIN_PORT)));
        mailAccount.setFrom(sysContext.getConfig(ConfigurationConstant.MAIN_FROM));
        mailAccount.setUser(sysContext.getConfig(ConfigurationConstant.MAIN_USER));
        mailAccount.setPass(sysContext.getConfig(ConfigurationConstant.MAIN_PASS));
        mailAccount.setCharset(StandardCharsets.UTF_8);
        mailAccount.setAuth(true);
        return mailAccount;
    }

    @Bean
    public Module dateTime() {
        JavaTimeModule javaTimeModule = new JavaTimeModule();

        // 序列化配置
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        javaTimeModule.addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern("HH:mm:ss")));

        // 反序列化配置 - 支持多种格式
        javaTimeModule.addDeserializer(LocalDateTime.class, new FlexibleLocalDateTimeDeserializer());

        return javaTimeModule;
    }

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.registerModule(new LongModule());
        objectMapper.registerModule(new SimpleModule());
        objectMapper.registerModule(dateTime());
        return objectMapper;
    }


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(rateLimitInterceptor).addPathPatterns("/**");
        registry.addInterceptor(previewInterceptor).addPathPatterns("/**");
    }

    /**
     * 灵活的 LocalDateTime 反序列化器
     * 支持多种时间格式
     */
    public static class FlexibleLocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {

        private static final DateTimeFormatter[] FORMATTERS = {
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm")
        };

        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
            String value = p.getValueAsString();

            if (value == null || value.trim().isEmpty()) {
                return null;
            }

            value = value.trim();

            // 尝试不同的格式进行解析
            for (DateTimeFormatter formatter : FORMATTERS) {
                try {
                    return LocalDateTime.parse(value, formatter);
                } catch (DateTimeParseException e) {
                    // 继续尝试下一个格式
                }
            }

            // 如果格式是 yyyy-MM-ddTHH:mm，自动补全秒数
            if (value.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}")) {
                value += ":00";
                try {
                    return LocalDateTime.parse(value, DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"));
                } catch (DateTimeParseException e) {
                    // 忽略
                }
            }

            // 如果所有尝试都失败，抛出异常
            throw new IOException("无法解析时间格式: " + value +
                "，支持的格式: yyyy-MM-dd HH:mm:ss, yyyy-MM-dd HH:mm, yyyy-MM-ddTHH:mm:ss, yyyy-MM-ddTHH:mm");
        }
    }
}
